<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$pengalaman_kerja = "";
$total_pengalaman_kerja = "";
$pengalaman_posisi_sama = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);

    $pengalaman_kerja = htmlspecialchars($row['pengalaman_kerja']);
    $total_pengalaman_kerja = htmlspecialchars($row['lama_pengalaman_kerja']);
    $pengalaman_posisi_sama = htmlspecialchars($row['lama_posisi_kerja']);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}
?>
<form id="myForm" class="needs-validation">
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Riwayat Pekerjaan') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="mb-3">
                <label for="pengalaman_kerja" class="form-label"><?= translate('Apakah Anda Memiliki Pengalaman Bekerja') ?>? <span style="color: red;">(*)</span></label>
                <select class="form-select" name="pengalaman_kerja" id="pengalaman_kerja" aria-label="Default select example" required>
                    <option value="" disabled selected><?= translate('Silakan pilih') ?></option>
                    <option value="Ya"><?= translate('Ada') ?></option>
                    <option value="Tidak"><?= translate('Tidak Ada') ?></option>
                    <option value="Fresh Graduate">Fresh Graduate</option>
                </select>
            </div>
        </div>
    </div>

    <div id="konten-input-pengalaman-kerja" style="display: none;">
        <div class="row mt-2 mb-2">
            <div class="col-md-4 mt-2">
                <label for="total_pengalaman_kerja" class="form-label"><?= translate('Total Pengalaman Kerja') ?> <a style="color: red;">(*)</a></label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="total_pengalaman_kerja" name="total_pengalaman_kerja" maxlength="2" value="<?= $total_pengalaman_kerja ?>" onkeypress="return hanyaAngka(event)">
                    <span class="input-group-text"><?= translate('Tahun') ?></span>
                </div>
            </div>
        </div>

        <div class="row mt-2">
            <h6><?= translate('Silakan masukan detail pekerjaan anda') ?></h6>
            <div class="col-md-6 mt-2">
                <label for="temp_nama" class="form-label"><?= translate('Nama Perusahaan') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control" minlength="3" id="temp_nama" name="temp_nama" placeholder="<?= translate('Silakan masukan nama perusahaan') ?>">
            </div>
            <div class="col-md-4 mt-2">
                <label for="temp_status" class="control-label"><?= translate('Status Kerja') ?> <a style="color: red;">(*)</a></label>
                <select class="form-select mt-2" name="temp_status" id="temp_status">
                    <option value="" selected disabled><?= translate('Pilih Status Kerja') ?> ...</option>
                    <option value="(Magang)"><?= translate('Magang') ?></option>
                    <option value="(Freelance)"><?= translate('Freelance (Pekerja Mandiri/Pekerja Tidak Terikat Kontrak)') ?></option>
                    <option value="(Full Time)"><?= translate('Full Time (Pekerja Yang Terikat Kontrak & Memiliki Waktu Kerja Tetap)') ?></option>
                </select>
            </div>
        </div>

        <div class="row mt-2">
            <div class="col-md-3 mt-2">
                <label for="temp_jabatan" class="form-label"><?= translate('Jabatan') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control" minlength="3" id="temp_jabatan" name="temp_jabatan" placeholder="<?= translate('Silakan masukan jabatan') ?>">
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_gaji" class="form-label"><?= translate('Gaji Net') ?> <a style="color: red;">(*)</a></label>
                <div class="input-group">
                    <span class="input-group-text">Rp.</span>
                    <input type="text" class="form-control" id="temp_gaji" name="temp_gaji" placeholder="<?= translate('Contoh') ?>: 5.000.000">
                </div>
                <span style="font-size: 12px;">* <?= translate('Isi 0 jika tidak ada gaji') ?></span>
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_tahun_masuk" class="form-label"><?= translate('Bulan Tahun Masuk') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control type-tahun-bulan" id="temp_tahun_masuk" name="temp_tahun_masuk">
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_tahun_selesai" class="form-label"><?= translate('Bulan Tahun Selesai') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control type-tahun-bulan" id="temp_tahun_selesai" name="temp_tahun_selesai">
            </div>
        </div>

        <div class="row mt-2">
            <div class="col-md-6 mt-2">
                <label for="temp_alasan" class="form-label"><?= translate('Alasan Berhenti') ?> <a style="color: red;">(*)</a></label>
                <textarea class="form-control" id="temp_alasan" name="temp_alasan" rows="3"></textarea>
            </div>
            <div class="col-md-3 mt-3">
                <label class="form-label" style="display: none;">#</label><br>
                <button type="button" class="btn btn-primary btn-sm" onclick="tambahRiwayatPekerjaan()"><i class="fas fa-plus-square"></i> <?= translate('Simpan') ?></button>
            </div>
        </div>

        <div class="row mt-3">
            <span><?= translate('Detail Riwayat Pekerja') ?></span>
            <?php
            // get riwayat kursus
            $sql = $conn->prepare("SELECT * FROM riwayat_pekerjaan WHERE id = ?");
            $sql->bind_param("s", $temp_pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                echo '<div class="col-md-12" id="konten-info-detail-pekerjaan" style="display: none;">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Riwayat pekerjaan belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-pekerjaan">';
                $no = 0;
                while ($row = mysqli_fetch_array($result)) {
                    if ($row['gaji'] > 0) {
                        $gaji = $row['gaji'];
                    } else {
                        $gaji = 0;
                    }

                    echo '<div class="col-md-4" id="detailPekerjaan-' . $no . '">
                                <div class="card">
                                    <div class="card-body">
                                    <span class="card-title" style="font-size: 14px;">' . htmlspecialchars($row['nama_perusahaan']) . '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailPekerjaan(' . $no . ')"></i></span>
                                    <p class="card-text mt-2" style="font-size: 12px;">
                                        ' . translate('Jabatan') . ' : ' . htmlspecialchars($row['jabatan']) . '<br>
                                        ' . translate('Status Kerja') . ' : ' . translate($row['status_kerja']) . '<br>
                                        ' . translate('Gaji') . ' : Rp. ' . number_format($gaji, 0, ".", ".") . '<br>
                                        ' . translate('Tahun') . ' : ' . htmlspecialchars($row['tahun_mulai']) . ' s/d ' . htmlspecialchars($row['tahun_selesai']) . '<br>
                                        ' . translate('Alasan Berhenti') . ' : ' . $row['alasan_berhenti'] . '
                                    </p>
                                    <input type="hidden" name="nama[]" value="' . htmlspecialchars($row['nama_perusahaan']) . '">
                                    <input type="hidden" name="jabatan[]" value="' . htmlspecialchars($row['jabatan']) . '">
                                    <input type="hidden" name="status[]" value="' . htmlspecialchars($row['status_kerja']) . '">
                                    <input type="hidden" name="gaji[]" value="' . htmlspecialchars($row['gaji']) . '">
                                    <input type="hidden" name="tahun_mulai[]" value="' . htmlspecialchars($row['tahun_mulai']) . '">
                                    <input type="hidden" name="tahun_selesai[]" value="' . htmlspecialchars($row['tahun_selesai']) . '">
                                    <input type="hidden" name="alasan[]" value="' . htmlspecialchars($row['alasan_berhenti']) . '">
                                </div>
                            </div>
                        </div>';
                    $no++;
                }
                echo '</div>';
            } else {
                echo '<div class="col-md-12" id="konten-info-detail-pekerjaan">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Riwayat pekerjaan belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-pekerjaan">
                    </div>';
            }
            ?>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-pelatihan-kursus.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('#temp_gaji').mask('000.000.000', {
            reverse: true
        }); // Format mata uang

        $('[name=pengalaman_kerja]').on('change', function() {
            var text = $(this).val();

            if (text == 'Ya') {
                $('#konten-input-pengalaman-kerja').show();
            } else {
                $('#konten-input-pengalaman-kerja').hide();
            }
        });

        $(".type-tahun-bulan").datepicker({
            format: "yyyy-mm",
            startView: "months",
            minViewMode: "months",
            autoclose: true,
            endDate: new Date() // opsional: batas maksimal hari ini
        });

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data riwayat pekerjaan
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var pengalaman_kerja = $("#pengalaman_kerja").val();
            total_pengalaman_kerja = $("#total_pengalaman_kerja").val();
            nama = [];
            jabatan = [];
            status = [];
            gaji = [];
            tahun_mulai = [];
            tahun_selesai = [];
            alasan = [];

            if (pengalaman_kerja == "Ya" && total_pengalaman_kerja == "") {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan total pengalaman kerja terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600
                });
            } else if (pengalaman_kerja == "Ya" && $("input[name='nama[]']").length == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan riwayat pekerjaan terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600
                });
            } else {
                if (pengalaman_kerja == "Ya") {
                    nama = $("input[name='nama[]']").map(function() {
                        return this.value;
                    }).get();
                    jabatan = $("input[name='jabatan[]']").map(function() {
                        return this.value;
                    }).get();
                    status = $("input[name='status[]']").map(function() {
                        return this.value;
                    }).get();
                    gaji = $("input[name='gaji[]']").map(function() {
                        return this.value;
                    }).get();
                    tahun_mulai = $("input[name='tahun_mulai[]']").map(function() {
                        return this.value;
                    }).get();
                    tahun_selesai = $("input[name='tahun_selesai[]']").map(function() {
                        return this.value;
                    }).get();
                    alasan = $("input[name='alasan[]']").map(function() {
                        return this.value;
                    }).get();
                }

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanRiwayatPekerjaan",
                            data: {
                                pengalaman_kerja: pengalaman_kerja,
                                total_pengalaman_kerja: total_pengalaman_kerja,
                                pengalaman_posisi_sama: '',
                                nama: nama,
                                jabatan: jabatan,
                                status: status,
                                gaji: gaji,
                                tahun_mulai: tahun_mulai,
                                tahun_selesai: tahun_selesai,
                                alasan: alasan,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-informasi-pekerjaan.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }
        });
        // End Fungsi simpan data riwayat pekerjaan

        // Auto input
        var pengalaman_kerja = '<?= $pengalaman_kerja ?>';

        $("#pengalaman_kerja").val(pengalaman_kerja).trigger('change');
        // End Auto input
    });

    function tambahRiwayatPekerjaan() {
        var temp_nama = htmlspecialchars($("#temp_nama").val());
        temp_status = htmlspecialchars($("#temp_status").val());
        temp_jabatan = htmlspecialchars($("#temp_jabatan").val());
        temp_gaji = htmlspecialchars($("#temp_gaji").val());
        temp_tahun_masuk = htmlspecialchars($("#temp_tahun_masuk").val());
        temp_tahun_selesai = htmlspecialchars($("#temp_tahun_selesai").val());
        temp_alasan = htmlspecialchars($("#temp_alasan").val());

        if (temp_nama == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan nama perusahaan terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_status == "" || temp_status === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih status kerja terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_jabatan == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan jabatan terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_gaji == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan gaji terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tahun_masuk == "" || temp_tahun_masuk === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih tahun masuk terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tahun_selesai == "" || temp_tahun_selesai === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih tahun selesai terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tahun_masuk > temp_tahun_selesai) {
            swal({
                icon: "warning",
                text: "<?= translate('Tahun masuk tidak boleh lebih besar dari tahun selesai.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_alasan == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan alasan berhenti terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else {
            $("#konten-info-detail-pekerjaan").hide();

            var id_input = Math.floor(Math.random() * 900) + 100;
            var content = $("#konten-detail-pekerjaan").html();

            let parts = temp_tahun_masuk.split("-");
            temp_tahun_masuk = parts[1] + "-" + parts[0]; // Ubah ke MM-YYYY

            let parts_2 = temp_tahun_selesai.split("-");
            temp_tahun_selesai = parts_2[1] + "-" + parts_2[0]; // Ubah ke MM-YYYY

            newContent = content + '<div class="col-md-4" id="detailPekerjaan-' + id_input + '">' +
                '<div class="card">' +
                '<div class="card-body">' +
                '<span class="card-title" style="font-size: 14px;">' + temp_nama + '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailPekerjaan(' + id_input + ')"></i></span>' +
                '<p class="card-text mt-2" style="font-size: 12px;">' +
                '<?= translate('Jabatan') ?> : ' + temp_jabatan + '<br>' +
                '<?= translate('Status Kerja') ?> : ' + $('#temp_status option:selected').text() + '<br>' +
                '<?= translate('Gaji') ?> : Rp. ' + temp_gaji + '<br>' +
                '<?= translate('Tahun') ?> : ' + temp_tahun_masuk + ' s/d ' + temp_tahun_selesai + '<br>' +
                '<?= translate('Alasan Berhenti') ?> : ' + temp_alasan + '' +
                '</p>' +
                '<input type="hidden" name="nama[]" value="' + temp_nama + '">' +
                '<input type="hidden" name="jabatan[]" value="' + temp_jabatan + '">' +
                '<input type="hidden" name="status[]" value="' + temp_status + '">' +
                '<input type="hidden" name="gaji[]" value="' + temp_gaji + '">' +
                '<input type="hidden" name="tahun_mulai[]" value="' + temp_tahun_masuk + '">' +
                '<input type="hidden" name="tahun_selesai[]" value="' + temp_tahun_selesai + '">' +
                '<input type="hidden" name="alasan[]" value="' + temp_alasan + '">' +
                '</div>' +
                '</div>' +
                '</div>';

            $("#konten-detail-pekerjaan").html(newContent);

            $("#temp_nama").val('');
            $("#temp_status").val('').trigger('change');
            $("#temp_jabatan").val('');
            $("#temp_gaji").val('');
            $("#temp_tahun_masuk").val('').trigger('change');
            $("#temp_tahun_selesai").val('').trigger('change');
            $("#temp_alasan").val('');
        }
    }

    function hapusDetailPekerjaan(id) {
        $("#detailPekerjaan-" + id).remove();

        // cek apakah html kosong atau tidak
        if ($("#konten-detail-pekerjaan").text().trim().length === 0) {
            $("#konten-info-detail-pekerjaan").show();
        }
    }
</script>