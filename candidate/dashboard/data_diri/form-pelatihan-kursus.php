<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$kursus = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $kursus = htmlspecialchars($row['kursus']);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Pelatihan / Kursus') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-12 mt-2">
            <div class="mb-3" id="cekdiplomas">
                <label class="form-label"><?= translate('Apakah Anda pernah mengikuti suatu pelatihan / kursus') ?>? <a style="color: red;">(*)</a></label>
                <br>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="kursus" id="kursus-y" value="Ya" required>
                    <label class="form-check-label" for="kursus-y">
                        <?= translate('Ya') ?>
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="kursus" id="kursus-t" value="Tidak">
                    <label class="form-check-label" for="kursus-t">
                        <?= translate('Tidak') ?>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div id="konten-input-pelatihan" style="display: none;">
        <div class="row mt-2">
            <div class="row">
                <div class="col-md-4 mt-2">
                    <label for="temp_nama" class="form-label"><?= translate('Nama Pelatihan/Kursus') ?> <a style="color: red;">(*)</a></label>
                    <input type="text" class="form-control" minlength="3" id="temp_nama" name="temp_nama" placeholder="<?= translate('Silakan Tuliskan Nama Pelatihan/Kursus') ?>">
                </div>
                <div class="col-md-4 mt-2">
                    <label for="temp_sertifikat" class="form-label"><?= translate('Apakah Ada Sertifikat') ?>? <a style="color: red;">(*)</a></label>
                    <select name="temp_sertifikat" id="temp_sertifikat" class="form-select">
                        <option value="" selected disabled><?= translate('Pilih') ?></option>
                        <option value="Ada"><?= translate('Ada') ?></option>
                        <option value="Tidak Ada"><?= translate('Tidak Ada') ?></option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mt-3">
                    <label for="temp_tempat" class="form-label"><?= translate('Tempat Pelatihan/Kursus') ?> <span style="color: red;">(*)</span></label>
                    <select class="form-select" id="temp_tempat" name="temp_tempat" style="width: 100%">
                    </select>
                </div>
                <div class="col-md-3 mt-3">
                    <label for="temp_tgl_mulai" class="form-label"><?= translate('Tanggal Mulai') ?> <span style="color: red;">(*)</span></label>
                    <input type="date" class="form-control" id="temp_tgl_mulai" name="temp_tgl_mulai" min="1900-01-01" max="<?= date("Y-m-d") ?>">
                </div>
                <div class="col-md-3 mt-3">
                    <label for="temp_tgl_selesai" class="form-label"><?= translate('Tanggal Selesai') ?> <span style="color: red;">(*)</span></label>
                    <input type="date" class="form-control" id="temp_tgl_selesai" name="temp_tgl_selesai" min="1900-01-01" max="<?= date("Y-m-d") ?>">
                </div>
                <div class="col-md-2 mt-5">
                    <label class="form-label" style="display: none;">#</label>
                    <button type="button" class="btn btn-primary btn-sm" onclick="tambahKursus()"><i class="fas fa-plus-square"></i> <?= translate('Simpan') ?></button>
                </div>
            </div>
        </div>

        <div class="row mt-2">
            <span><?= translate('Detail Riwayat Pelatihan/Kursus') ?></span>
            <?php
            // get riwayat kursus
            $sql = $conn->prepare("SELECT * FROM riwayat_kursus WHERE id = ?");
            $sql->bind_param("s", $temp_pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                echo '<div class="col-md-12" id="konten-info-detail-kursus" style="display: none;">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Riwayat pelatihan/kursus belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-kursus">';
                $no = 0;
                while ($row = mysqli_fetch_array($result)) {
                    echo '<div class="col-md-4" id="detailKursus-' . $no . '">
                            <div class="card">
                                <div class="card-body">
                                    <span class="card-title" style="font-size: 14px;">' . htmlspecialchars($row['nama']) . '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailKursus(' . $no . ')"></i></span>
                                    <p class="card-text mt-2" style="font-size: 12px;">
                                        ' . translate('Periode') . ' : ' . htmlspecialchars($row['tgl_mulai']) . ' s/d ' . htmlspecialchars($row['tgl_selesai']) . '<br>
                                        ' . translate('Tempat') . ' : ' . htmlspecialchars($row['tempat']) . '<br>
                                        ' . translate('Sertifikat') . ' : ' . translate($row['sertifikat']) . '
                                    </p>
                                    <input type="hidden" name="nama_kursus[]" value="' . htmlspecialchars($row['nama']) . '">
                                    <input type="hidden" name="sertifikat_kursus[]" value="' . htmlspecialchars($row['sertifikat']) . '">
                                    <input type="hidden" name="tempat_kursus[]" value="' . htmlspecialchars($row['tempat']) . '">
                                    <input type="hidden" name="tgl_mulai_kursus[]" value="' . htmlspecialchars($row['tgl_mulai']) . '">
                                    <input type="hidden" name="tgl_selesai_kursus[]" value="' . htmlspecialchars($row['tgl_selesai']) . '">
                                </div>
                            </div>
                        </div>';
                    $no++;
                }
                echo '</div>';
            } else {
                echo '<div class="col-md-12" id="konten-info-detail-kursus">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Riwayat pelatihan/kursus belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-kursus">
                    </div>';
            }
            ?>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-riwayat-pendidikan.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('#temp_tempat').select2({
            placeholder: "<?= translate('Silakan masukan nama kota') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getLokasi',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        // Fungsi onchange pelatihan dan kursus
        $('input[name="kursus"]').on('change', function() {
            var value = $(this).val(); // Ambil value dari radio yang dipilih

            if (value == 'Ya') {
                $("#konten-input-pelatihan").show();
            } else {
                $("#konten-input-pelatihan").hide();
            }
        });

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data riwayat pendidikan
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var kursus = $('input[name="kursus"]:checked').val();
            nama_kursus = [];
            sertifikat_kursus = [];
            tempat_kursus = [];
            tgl_mulai_kursus = [];
            tgl_selesai_kursus = [];

            if (kursus == 'Ya' && $("input[name='nama_kursus[]']").length == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan riwayat pelatihan/kursus terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600
                });
            } else {
                if ($("input[name='nama_kursus[]']").length > 0) {
                    nama_kursus = $("input[name='nama_kursus[]']").map(function() {
                        return this.value;
                    }).get();
                    sertifikat_kursus = $("input[name='sertifikat_kursus[]']").map(function() {
                        return this.value;
                    }).get();
                    tempat_kursus = $("input[name='tempat_kursus[]']").map(function() {
                        return this.value;
                    }).get();
                    tgl_mulai_kursus = $("input[name='tgl_mulai_kursus[]']").map(function() {
                        return this.value;
                    }).get();
                    tgl_selesai_kursus = $("input[name='tgl_selesai_kursus[]']").map(function() {
                        return this.value;
                    }).get();
                }

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanRiwayatKursus",
                            data: {
                                kursus: kursus,
                                nama_kursus: nama_kursus,
                                sertifikat_kursus: sertifikat_kursus,
                                tempat_kursus: tempat_kursus,
                                tgl_mulai_kursus: tgl_mulai_kursus,
                                tgl_selesai_kursus: tgl_selesai_kursus,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-riwayat-pekerjaan.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }
        });
        // End fungsi simpan data riwayat pendidikan

        // Auto input
        var kursus = '<?= $kursus ?>';
        $("input[name='kursus'][value='" + kursus + "']").prop("checked", true);

        if (kursus == "Ya") {
            $("#konten-input-pelatihan").show();
        }
        // End Auto input
    });

    function tambahKursus() {
        var temp_nama = htmlspecialchars($("#temp_nama").val());
        temp_sertifikat = htmlspecialchars($("#temp_sertifikat").val());
        temp_tempat = htmlspecialchars($("#temp_tempat").val());
        temp_tgl_mulai = htmlspecialchars($("#temp_tgl_mulai").val());
        temp_tgl_selesai = htmlspecialchars($("#temp_tgl_selesai").val());

        if (temp_nama == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan nama pelatihan/kursus terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_sertifikat == "" || temp_sertifikat === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih keterangan sertifikat terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tempat == "" || temp_tempat === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan tempat pelatihan/kursus terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tgl_mulai == "" || temp_tgl_mulai === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan tanggal masuk pelatihan/kursus terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tgl_selesai == "" || temp_tgl_selesai === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan tanggal selesai pelatihan/kursus terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tgl_mulai > temp_tgl_selesai) {
            swal({
                icon: "warning",
                text: "<?= translate('Tanggal mulai tidak boleh lebih besar dari tanggal selesai.') ?>",
                buttons: false,
                timer: 1600
            });
        } else {
            $("#konten-info-detail-kursus").hide();

            var id_input = Math.floor(Math.random() * 900) + 100;
            var content = $("#konten-detail-kursus").html();

            newContent = content + '<div class="col-md-4" id="detailKursus-' + id_input + '">' +
                '<div class="card">' +
                '<div class="card-body">' +
                '<span class="card-title" style="font-size: 14px;">' + temp_nama + '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailKursus(' + id_input + ')"></i></span>' +
                '<p class="card-text mt-2" style="font-size: 12px;">' +
                '<?= translate('Periode') ?> : ' + temp_tgl_mulai + ' s/d ' + temp_tgl_selesai + '<br>' +
                '<?= translate('Tempat') ?> : ' + temp_tempat + '<br>' +
                '<?= translate('Sertifikat') ?> : ' + $('#temp_sertifikat option:selected').text() + '' +
                '</p>' +
                '<input type="hidden" name="nama_kursus[]" value="' + temp_nama + '">' +
                '<input type="hidden" name="sertifikat_kursus[]" value="' + temp_sertifikat + '">' +
                '<input type="hidden" name="tempat_kursus[]" value="' + temp_tempat + '">' +
                '<input type="hidden" name="tgl_mulai_kursus[]" value="' + temp_tgl_mulai + '">' +
                '<input type="hidden" name="tgl_selesai_kursus[]" value="' + temp_tgl_selesai + '">' +
                '</div>' +
                '</div>' +
                '</div>';

            $("#konten-detail-kursus").html(newContent);

            $("#temp_nama").val('');
            $("#temp_sertifikat").val('').trigger('change');
            $("#temp_tempat").val(null).trigger('change');
            $("#temp_tgl_mulai").val('');
            $("#temp_tgl_selesai").val('');
        }
    }

    function hapusDetailKursus(id) {
        $("#detailKursus-" + id).remove();

        // cek apakah html kosong atau tidak
        if ($("#konten-detail-kursus").text().trim().length === 0) {
            $("#konten-info-detail-kursus").show();
        }
    }
</script>