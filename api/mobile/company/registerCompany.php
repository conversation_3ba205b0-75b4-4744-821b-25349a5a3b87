<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';

// fungsi untuk validasi password
function validatePassword($password)
{
    $errors = [];

    // Cek minimal 8 karakter
    if (strlen($password) < 8) {
        $errors[] = "Password minimal 8 karakter";
    }

    // Cek mengandung minimal 1 huruf besar
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 huruf besar";
    }

    // Cek mengandung minimal 1 angka
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 angka";
    }

    // Cek mengandung minimal 1 karakter khusus
    if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 karakter khusus (!@#$%^&*()_+-=[]{}|;:,.<>?)";
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

// Validasi input yang diperlukan
$requiredFields = ['nama_pic', 'no_hp_pic', 'email_pic', 'otp', 'password', 'nama_perusahaan', 'no_tlp_perusahaan', 'email_perusahaan', 'alamat_perusahaan', 'industri_perusahaan'];
$missingFields = [];

foreach ($requiredFields as $field) {
    if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
        $missingFields[] = $field;
    }
}

// if (!in_array('email_perusahaan', $missingFields)) {
//     $data['status'] = "gagal";
//     $data['message'] = "Field berikut harus diisi: " . implode(", ", $missingFields);
//     echo json_encode($data, JSON_PRETTY_PRINT);
//     exit();
// }

// get from post data perekrut
$nama_pic = trim($_POST['nama_pic']);
$no_hp_pic = trim($_POST['no_hp_pic']);
$email_pic = trim($_POST['email_pic']);
$otp = trim($_POST['otp']);

// Validasi format email
if (!filter_var($email_pic, FILTER_VALIDATE_EMAIL)) {
    $data['status'] = "gagal";
    $data['message'] = "Format email PIC tidak valid";
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

// Validasi password sebelum hashing
$passwordInput = $_POST['password'];
$passwordValidation = validatePassword($passwordInput);

if (!$passwordValidation['valid']) {
    $data['status'] = "gagal";
    $data['message'] = "Password tidak memenuhi syarat: " . implode(", ", $passwordValidation['errors']);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

$password = hashPassword($passwordInput);

// get from post data perusahaan
$nama_perusahaan = trim($_POST['nama_perusahaan']);
$no_tlp_perusahaan = trim($_POST['no_tlp_perusahaan']);
$email_perusahaan = trim($_POST['email_perusahaan']);
$alamat_perusahaan = trim($_POST['alamat_perusahaan']);
$industri_perusahaan = trim($_POST['industri_perusahaan']);
$website_perusahaan = isset($_POST['website_perusahaan']) ? trim($_POST['website_perusahaan']) : '';
$deskripsi_perusahaan = isset($_POST['deskripsi_perusahaan']) ? trim($_POST['deskripsi_perusahaan']) : '';

// Validasi format email perusahaan
if (!empty($email_perusahaan) && !filter_var($email_perusahaan, FILTER_VALIDATE_EMAIL)) {
    $data['status'] = "gagal";
    $data['message'] = "Format email perusahaan tidak valid";
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

// Validasi website jika diisi
if (!empty($website_perusahaan)) {
    // Tambahkan http:// jika tidak ada protokol
    if (!preg_match('/^https?:\/\//', $website_perusahaan)) {
        $website_perusahaan = 'http://' . $website_perusahaan;
    }
    
    // Validasi format URL
    if (!filter_var($website_perusahaan, FILTER_VALIDATE_URL)) {
        $data['status'] = "gagal";
        $data['message'] = "Format website perusahaan tidak valid";
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit();
    }
}

$fcm_token = "";
if (isset($_POST['fcm_token'])) {
    $fcm_token = trim($_POST['fcm_token']);
}

// tambahan
$tgl = date('Y-m-d H:i:s');

if (!function_exists('random_bytes')) {
    function random_bytes($length = 36)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $characters_length = strlen($characters);
        $output = '';
        for ($i = 0; $i < $length; $i++)
            $output .= $characters[rand(0, $characters_length - 1)];
        return $output;
    }
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

$status = "gagal";
$message = "Pendaftaran gagal.";

try {
    // Mulai proses
    $conn->begin_transaction();
    $timezone = "SET time_zone = '+07:00'";
    $conn->query($timezone);
    
    if ($otp != "appleGoogle") {
        //cek otp
        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
        $sql->bind_param("ss", $otp, $email_pic);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // Validasi waktu expired OTP (5 menit)
            $row = mysqli_fetch_array($result);
            $otpDate = new DateTime($row['date']);
            $currentDate = new DateTime();
            $interval = $currentDate->diff($otpDate);
            $minutesDiff = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

            if ($minutesDiff > 5) {
                throw new Exception("OTP sudah kadaluarsa. Silakan minta OTP baru.");
            }

            // Cek apakah OTP sudah pernah digunakan
            if ($row['status'] == 'Finish') {
                throw new Exception("OTP sudah pernah digunakan. Silakan minta OTP baru.");
            }

            // Update Status OTP
            $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
            $update->bind_param("ss", $otp, $email_pic);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("OTP tidak sesuai. Silakan coba lagi.");
            }
        } else {
            throw new Exception("OTP Salah.");
        }
    }

    // cek jika perusahaan sudah ada
    $sql = $conn->prepare("SELECT id_koordinator FROM koordinator WHERE label = ?");
    $sql->bind_param("s", $nama_perusahaan);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        throw new Exception("Perusahaan sudah terdaftar.");
    }

    // generate id koordinator
    $sqlCek = "SELECT id_koordinator FROM koordinator";
    $queryCek = $conn->query($sqlCek);

    do {
        $id_koordinator = "KOOR-" . generateUUID();
    } while ($id_koordinator == $queryCek);

    // Proses penyimpanan data koordinator pic
    $insert = $conn->prepare("INSERT INTO `koordinator` (`id_koordinator`,`label`,`alias`,`alamat`,`deskripsi`,`link_perusahaan`,`tipe`,`tlp`,`warna`,`created_at`,`email`,`paket`) 
                            VALUES 
                            (?, ?, ?, ?, ?, ?, ?, ?,'#fcae1a', ?, ?, '')");
    $insert->bind_param("ssssssssss", $id_koordinator, $nama_perusahaan, $nama_perusahaan, $alamat_perusahaan, $deskripsi_perusahaan, $website_perusahaan, $industri_perusahaan, $no_tlp_perusahaan, $tgl, $email_perusahaan);

    if ($insert->execute()) {
        $insert->close();
    } else {
        throw new Exception("Pendaftaran Gagal.");
    }

    // cek jika email dan no handphone koordinator pic sudah ada
    $sql = $conn->prepare("SELECT
                            GROUP_CONCAT(DISTINCT flag) as flag
                        FROM
                        (
                            SELECT id_pic, 'email' as flag FROM koordinator_pic WHERE email = ? GROUP BY id_pic
                        ) h");
    $sql->bind_param("s", $email_pic);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);

        if ($row['flag'] == 'email,no_hp') {
            throw new Exception("E-mail dan No. Handphone sudah terdaftar.");
        } elseif ($row['flag'] == 'email') {
            throw new Exception("E-mail sudah terdaftar.");
        } elseif ($row['flag'] == 'no_hp') {
            throw new Exception("No. Handphone sudah terdaftar.");
        }
    }

    // generate id pic
    $sqlCek = "SELECT id_pic FROM koordinator_pic";
    $queryCek = $conn->query($sqlCek);

    do {
        $id_pic = "PIC-" . generateUUID();
    } while ($id_pic == $queryCek);

    // Proses penyimpanan data koordinator pic
    $insert = $conn->prepare("INSERT INTO `koordinator_pic` (`id_pic`, `id_koordinator`, `nama`, `email`, `password`, `no_hp`, `status`, `create_at`, `role`, `fcm_token`) 
                            VALUES (?, ?, ?, ?, ?, ?, 'Active', ?, 'master', ?)");
    $insert->bind_param("ssssssss", $id_pic, $id_koordinator, $nama_pic, $email_pic, $password, $no_hp_pic, $tgl, $fcm_token);

    if ($insert->execute()) {
        $insert->close();

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Pendaftaran berhasil.";
    } else {
        throw new Exception("Pendaftaran Gagal.");
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = "gagal";
    $message = $e->getMessage();
}

$data['status'] = $status;
$data['message'] = $message;

$output = json_encode($data, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit();
