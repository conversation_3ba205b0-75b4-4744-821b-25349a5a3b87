name: Deploy PHP App to EC2

on:
  push:
    branches:
      - server_dev

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy via SSH
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ubuntu
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            echo ">>> Masuk ke folder project"
            cd /var/www/html/digitalcv_dev

            echo ">>> Update code dari GitHub"
            git fetch --all
            git reset --hard origin/server_dev

            echo ">>> Install dependencies via Composer"
            composer install --no-interaction --prefer-dist --optimize-autoloader

            echo ">>> Deploy selesai!"
