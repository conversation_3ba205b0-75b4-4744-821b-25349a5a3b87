# JWT Authentication Implementation for get_locations.php

## Overview

This document describes the implementation of JWT-based authentication for the `get_locations.php` endpoint, which provides Indonesian location data (provinces, regencies/cities, districts) with search and pagination functionality.

## Changes Made

### 1. JWT Authentication Integration

- Added `include '../../jwt_helper.php'` to include JWT helper functions
- Added `$userData = requireAuth()` to enforce JWT authentication
- Added comprehensive activity logging for all location data access

### 2. Security Implementation

**Before (No authentication):**

```php
<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
// No authentication - endpoint was publicly accessible
```

**After (JWT authentication required):**

```php
<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();
```

### 3. Activity Logging Implementation

Added specific activity logging for each function:

**getLokasi (Regencies):**

```php
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$messages = "Mengakses data lokasi/kabupaten $searchLog (halaman $page, $totalRows total data)";
logActivity($conn, $userData->id, $level, $messages, $extra_info);
```

**getProvinsi (Provinces):**

```php
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$messages = "Mengakses data provinsi $searchLog (halaman $page, $totalRows total data)";
logActivity($conn, $userData->id, $level, $messages, $extra_info);
```

**getKota (Cities/Regencies by Province):**

```php
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$provinceLog = $provinceId ? "provinsi ID: $provinceId" : "semua provinsi";
$messages = "Mengakses data kota/kabupaten dari $provinceLog $searchLog (halaman $page, $totalRows total data)";
logActivity($conn, $userData->id, $level, $messages, $extra_info);
```

**getKecamatan (Districts by Regency):**

```php
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$regencyLog = $provinceId ? "kabupaten ID: $provinceId" : "semua kabupaten";
$messages = "Mengakses data kecamatan dari $regencyLog $searchLog (halaman $page, $totalRows total data)";
logActivity($conn, $userData->id, $level, $messages, $extra_info);
```

## API Usage

### Request Format

```
GET /api/mobile/company/get_locations.php?func={function}&{additional_params}
Headers:
    Authorization: Bearer {jwt_token}
    Content-Type: application/json
```

### Available Functions

#### 1. getLokasi (Get All Regencies)

```
GET /api/mobile/company/get_locations.php?func=getLokasi&q={search}&page={page}&page_size={size}
```

#### 2. getProvinsi (Get All Provinces)

```
GET /api/mobile/company/get_locations.php?func=getProvinsi&q={search}&page={page}&page_size={size}
```

#### 3. getKota (Get Cities by Province)

```
GET /api/mobile/company/get_locations.php?func=getKota&province_id={id}&q={search}&page={page}&page_size={size}
```

#### 4. getKecamatan (Get Districts by Regency)

```
GET /api/mobile/company/get_locations.php?func=getKecamatan&regency_id={id}&q={search}&page={page}&page_size={size}
```

### Parameters

- **func** (required): Function name (`getLokasi`, `getProvinsi`, `getKota`, `getKecamatan`)
- **q** (optional): Search term for filtering locations
- **page** (optional): Page number for pagination (default: 1)
- **page_size** (optional): Number of items per page (default: 5)
- **province_id** (optional): Province ID for getKota function
- **regency_id** (optional): Regency ID for getKecamatan function
- **JWT Token** (required): Valid access token containing user authentication data

### JWT Payload Structure

The JWT token must contain:

```json
{
    "id_koordinator": "string",
    "divisi": "string",
    "id": "string",
    "company": "string",
    "iat": timestamp,
    "exp": timestamp
}
```

### Response Format

**Success (HTTP 200):**

_For getLokasi and getKota:_

```json
{
  "status": true,
  "message": "fetch data berhasil",
  "data": [
    {
      "id": "3201",
      "text": "BOGOR"
    }
  ],
  "page": 1,
  "page_size": 5,
  "total_data": 514
}
```

_For getProvinsi and getKecamatan:_

```json
{
  "success": true,
  "message": "fetch data berhasil",
  "data": [
    {
      "id": "32",
      "nama": "JAWA BARAT"
    }
  ],
  "page": 1,
  "page_size": 5,
  "total_data": 34
}
```

**Authentication Error (HTTP 401):**

```json
{
  "success": false,
  "message": "Unauthorized: Invalid or missing token"
}
```

**Validation Error (HTTP 400):**

```json
{
  "success": false,
  "message": "Harus lebih dari 3 karakter",
  "data": [],
  "page": 1,
  "page_size": 5,
  "total_data": 0
}
```

## Security Features

### 1. JWT Token Validation

- Validates token signature using secret key
- Checks token expiration
- Verifies token structure and required claims
- Ensures user has valid authentication

### 2. Access Control

- Restricts all location data access to authenticated users only
- Prevents unauthorized geographical data access
- Maintains user context throughout request

### 3. Activity Logging

- Logs all location data access requests
- Records search terms, filter parameters, and pagination details
- Tracks user ID from JWT token
- Maintains audit trail for location data usage

### 4. Input Validation

- Sanitizes search input to prevent SQL injection
- Validates function parameters
- Implements proper parameter binding
- Enforces minimum search length for some functions

## Functionality Preserved

### 1. Multi-Function Support

- **getLokasi**: Get all regencies/cities
- **getProvinsi**: Get all provinces
- **getKota**: Get cities filtered by province
- **getKecamatan**: Get districts filtered by regency

### 2. Search Capability

- Location name search using LIKE queries
- Case-insensitive search functionality
- Maintains existing search logic across all functions

### 3. Hierarchical Filtering

- Province-based city filtering
- Regency-based district filtering
- Maintains relational data integrity

### 4. Pagination

- Configurable page size per function
- Proper offset calculation
- Total data count for all functions
- Maintains pagination metadata

### 5. Response Format Consistency

- Different response formats preserved for different functions
- Maintains compatibility with existing frontend components
- Consistent JSON response structure

## Migration Benefits

### 1. Enhanced Security

- **Authentication Required**: Prevents unauthorized location data access
- **Token-based Security**: More secure than session-based authentication
- **User Context**: Maintains user identity throughout request lifecycle

### 2. Comprehensive Audit Trail

- **Activity Logging**: Track all location data access
- **Search Tracking**: Record what locations users are searching for
- **Filter Tracking**: Log hierarchical filtering usage
- **Usage Analytics**: Enable analysis of geographical data usage patterns

### 3. Consistency

- **Standardized Authentication**: Same pattern as other endpoints
- **Unified Error Handling**: Consistent response format
- **Maintainable Code**: Centralized authentication logic

## Error Handling

### 1. Authentication Errors

- **Invalid Token**: Returns 401 with descriptive message
- **Expired Token**: Prompts for token refresh
- **Missing Token**: Clear unauthorized response

### 2. Function Parameter Errors

- **Missing func Parameter**: Returns appropriate error
- **Invalid Function Name**: Graceful error handling
- **Invalid Filter Parameters**: Proper validation

### 3. Search Validation

- **Minimum Search Length**: Enforced for getKota and getKecamatan
- **SQL Injection Prevention**: Proper parameter binding
- **Empty Search Handling**: Appropriate default behavior

### 4. Database Errors

- **Connection Issues**: Graceful error handling
- **Query Failures**: Proper error responses
- **Data Validation**: Input sanitization

## Performance Considerations

### 1. Database Optimization

- Uses prepared statements for security and performance
- Implements proper pagination to limit result sets
- Efficient LIKE queries for search functionality
- Proper indexing on location tables

### 2. Memory Management

- Processes results in chunks via pagination
- Avoids loading large datasets into memory
- Proper resource cleanup

### 3. Response Times

- Lightweight JWT validation
- Minimal processing overhead per function
- Fast JSON encoding

## Testing

### Manual Testing Examples

```bash
# Test getLokasi with authentication
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/api/mobile/company/get_locations.php?func=getLokasi&q=jakarta&page=1&page_size=10"

# Test getProvinsi
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/api/mobile/company/get_locations.php?func=getProvinsi&q=jawa&page=1&page_size=5"

# Test getKota by province
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/api/mobile/company/get_locations.php?func=getKota&province_id=32&q=bandung"

# Test getKecamatan by regency
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/api/mobile/company/get_locations.php?func=getKecamatan&regency_id=3273&q=coblong"

# Test without token (should fail)
curl "http://localhost/api/mobile/company/get_locations.php?func=getLokasi"
```

### Integration Testing

The endpoint can be tested as part of complete location selection flow:

1. Login to get JWT token
2. Get provinces list
3. Get cities by selected province
4. Get districts by selected city
5. Verify search functionality at each level
6. Check activity logs are created

## Backward Compatibility

**Breaking Changes:**

- Endpoint now requires JWT authentication
- Previously public endpoint is now secured
- Unauthenticated requests will be rejected

**Migration Required:**

- Client applications must obtain JWT tokens before accessing endpoint
- Update API calls to include Authorization header
- Handle 401 Unauthorized responses appropriately

## Maintenance Notes

### 1. Database Dependencies

- Requires `provinces`, `regencies`, and `districts` tables
- Uses `koordinator_pic` table for user authentication (via JWT)
- Activity logging depends on logActivity function

### 2. Location Data Structure

- **provinces**: id, name
- **regencies**: id, name, province_id
- **districts**: id, name, regency_id

### 3. Configuration

- JWT secret key in `.env` file
- Configurable pagination limits per function
- Search functionality can be tuned per function

### 4. Monitoring

- Monitor search patterns for optimization opportunities
- Track authentication failure rates
- Analyze location data usage patterns
- Monitor hierarchical filtering usage

## Usage Examples

### Frontend JavaScript (Location Picker)

```javascript
class LocationPicker {
  constructor(token) {
    this.token = token;
    this.baseUrl = "/api/mobile/company/get_locations.php";
  }

  async getProvinces(search = "", page = 1) {
    const params = new URLSearchParams({
      func: "getProvinsi",
      q: search,
      page: page,
      page_size: 10,
    });

    const response = await fetch(`${this.baseUrl}?${params}`, {
      headers: {
        Authorization: `Bearer ${this.token}`,
        "Content-Type": "application/json",
      },
    });

    return await response.json();
  }

  async getCitiesByProvince(provinceId, search = "", page = 1) {
    const params = new URLSearchParams({
      func: "getKota",
      province_id: provinceId,
      q: search,
      page: page,
      page_size: 10,
    });

    const response = await fetch(`${this.baseUrl}?${params}`, {
      headers: {
        Authorization: `Bearer ${this.token}`,
        "Content-Type": "application/json",
      },
    });

    return await response.json();
  }

  async getDistrictsByRegency(regencyId, search = "", page = 1) {
    const params = new URLSearchParams({
      func: "getKecamatan",
      regency_id: regencyId,
      q: search,
      page: page,
      page_size: 10,
    });

    const response = await fetch(`${this.baseUrl}?${params}`, {
      headers: {
        Authorization: `Bearer ${this.token}`,
        "Content-Type": "application/json",
      },
    });

    return await response.json();
  }
}

// Usage
const token = localStorage.getItem("jwt_token");
const locationPicker = new LocationPicker(token);

// Setup cascading dropdowns
document
  .getElementById("province-select")
  .addEventListener("change", async function () {
    const provinceId = this.value;
    if (provinceId) {
      const cities = await locationPicker.getCitiesByProvince(provinceId);
      populateCityDropdown(cities.data);
    }
  });
```

### PHP Client Example

```php
class LocationService {
    private $baseUrl;
    private $token;

    public function __construct($token) {
        $this->baseUrl = 'http://localhost/api/mobile/company/get_locations.php';
        $this->token = $token;
    }

    private function makeRequest($params) {
        $url = $this->baseUrl . '?' . http_build_query($params);

        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'Authorization: Bearer ' . $this->token,
                    'Content-Type: application/json'
                ]
            ]
        ]);

        $response = file_get_contents($url, false, $context);
        return json_decode($response, true);
    }

    public function getProvinces($search = '', $page = 1, $pageSize = 10) {
        return $this->makeRequest([
            'func' => 'getProvinsi',
            'q' => $search,
            'page' => $page,
            'page_size' => $pageSize
        ]);
    }

    public function getCitiesByProvince($provinceId, $search = '', $page = 1, $pageSize = 10) {
        return $this->makeRequest([
            'func' => 'getKota',
            'province_id' => $provinceId,
            'q' => $search,
            'page' => $page,
            'page_size' => $pageSize
        ]);
    }

    public function getDistrictsByRegency($regencyId, $search = '', $page = 1, $pageSize = 10) {
        return $this->makeRequest([
            'func' => 'getKecamatan',
            'regency_id' => $regencyId,
            'q' => $search,
            'page' => $page,
            'page_size' => $pageSize
        ]);
    }
}
```

## Conclusion

The JWT authentication implementation for `get_locations.php` successfully secures all location data endpoints while maintaining their existing functionality. The endpoint now requires proper authentication, provides comprehensive activity logging for all location data access, and follows the established security patterns used throughout the application.

All four location functions (`getLokasi`, `getProvinsi`, `getKota`, `getKecamatan`) are now properly secured and logged, making this a comprehensive solution for authenticated geographical data access in the recruitment system.
