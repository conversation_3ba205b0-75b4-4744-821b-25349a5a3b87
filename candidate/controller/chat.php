<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../model/database.php';
require '../../vendor/autoload.php';

$userData = null;

if (isset($_SESSION['users'])) {
    $userData = $_SESSION['users'];
}

function decrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $data = strtr($data, '-_,', '+/=');
    $decoded = base64_decode($data);
    $iv = substr($decoded, 0, 16);
    $encrypted = substr($decoded, 16);
    return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
}

function konversiTanggal($tanggalInput)
{
    // pastikan format tanggal valid
    if (empty($tanggalInput) || $tanggalInput == '0000-00-00') {
        return '-';
    }

    // ubah ke format DateTime
    $tanggal = new DateTime($tanggalInput);
    $today   = new DateTime();
    $kemarin = (new DateTime())->modify('-1 day');
    $limaHariLalu = (new DateTime())->modify('-5 days');

    // format dasar bahasa Indonesia
    $namaHari = array(
        'Sunday' => 'Minggu',
        'Monday' => 'Senin',
        'Tuesday' => 'Selasa',
        'Wednesday' => 'Rabu',
        'Thursday' => 'Kamis',
        'Friday' => 'Jumat',
        'Saturday' => 'Sabtu'
    );

    $namaBulan = array(
        1 => 'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    // konversi ke format tanggal Y-m-d untuk perbandingan mudah
    $tgl = $tanggal->format('Y-m-d');
    $tglToday = $today->format('Y-m-d');
    $tglKemarin = $kemarin->format('Y-m-d');
    $tglLimaHariLalu = $limaHariLalu->format('Y-m-d');

    // aturan
    if ($tgl === $tglToday) {
        return 'Hari Ini';
    } elseif ($tgl === $tglKemarin) {
        return 'Kemarin';
    } elseif ($tgl <= $tglKemarin && $tgl >= $tglLimaHariLalu) {
        $hariInggris = $tanggal->format('l');
        return $namaHari[$hariInggris];
    } else {
        $tglNum = (int)$tanggal->format('j');
        $blnNum = (int)$tanggal->format('n');
        $thnNum = $tanggal->format('Y');
        return $tglNum . ' ' . $namaBulan[$blnNum] . ' ' . $thnNum;
    }
}

if ($userData != null && (isset($_GET['func']) || isset($_POST['func']))) {
    if (isset($_GET['func'])) {
        $func = $_GET['func'];
    } else {
        $func = $_POST['func'];
    }

    $pin = addslashes($userData['pin']);
    $nama_user = $userData['nama'];
    $email_user = $userData['email'];

    if ($func == 'send') {
        $data = array();

        $status = false;
        $message = 'Pesan tidak dapat dikirim. Silakan hubungi administrator';
        $html = '';
        $created_at = date("Y-m-d H:i:s");

        try {
            // Mulai proses
            $conn->begin_transaction();

            // Cek parameter
            if (!isset($_POST['index']) || !isset($_POST['message'])) {
                throw new Exception('Pesan tidak dapat dikirim.');
            }

            $index = $_POST['index'];
            $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
            $arr = explode("|", decrypt_url($index, $key));

            if (!$arr[0] || !$arr[1]) {
                throw new Exception('Pesan tidak dapat dikirim.');
            }

            $id_koordinator = $arr[0];
            $id_lamar = $arr[1];
            $message = $_POST['message'];

            // Cek apakah tujuan ada
            $cek = $conn->prepare("SELECT * FROM users_lamar WHERE id_lamar = ? AND id_koordinator = ?");
            $cek->bind_param("ss", $id_lamar, $id_koordinator);
            $cek->execute();
            $result = $cek->get_result();
            $cek->close();

            if ($result->num_rows == 0) {
                throw new Exception('Tujuan pengiriman pesan tidak tersedia.');
            }

            // Cek apakah ini chat baru di hari ini
            $cek = $conn->prepare("SELECT * FROM chat WHERE (`from` = ? OR `to` = ?) AND reference_id = ? AND SUBSTRING(created_at, 1, 10) = ?");
            $cek->bind_param("ssss", $pin, $pin, $id_lamar, date("Y-m-d"));
            $cek->execute();
            $result = $cek->get_result();
            $cek->close();

            $chatAwal = false;
            if ($result->num_rows == 0) {
                $chatAwal = true;
                $html .= '<div class="chat-group" data-tanggal="' . date("Y-m-d") . '">
                            <div class="row">
                                <div class="col-12 chat-badge" style="text-align: center;">
                                    <span class="badge bg-light text-dark">' . konversiTanggal(date("Y-m-d")) . '</span>
                                </div>
                            </div>';
            }

            // insert data chat
            $insert = $conn->prepare("INSERT INTO `chat`(`from`, `to`, `message`, `reference_id`, `status`, `created_at`) 
                VALUES (?, ?, ?, ?, 'send', ?)");
            $insert->bind_param("sssss", $pin, $id_koordinator, $message, $id_lamar, $created_at);

            if ($insert->execute()) {
                $insert->close();

                $apiKeyWS = '57pOr213C&^XM701%(*U4';
                $urlWS = $baseURL . 'api/WebSocket/send.php';

                $dataWS = [
                    'to' => urlencode($id_koordinator),
                    'tipe' => 'chat',
                    'reference_id' => $id_lamar,
                    'message' => $message
                ];

                $optionsWS = [
                    'http' => [
                        'method'  => 'POST',
                        'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                        'content' => http_build_query($dataWS)
                    ],
                    'ssl' => [
                        'verify_peer' => true,
                        'verify_peer_name' => true
                    ]
                ];

                $contextWS = stream_context_create($optionsWS);
                $response = @file_get_contents($urlWS, false, $contextWS);

                if ($response === false) {
                    throw new Exception('Pesan tidak dapat dikirim. Silakan hubungi administrator');
                }

                $html .= '<div class="msg-row right">
                            <div class="bubble">
                                ' . str_replace("\n", "<br>", $message) . '
                                <span class="meta">' . date("H:i", strtotime($created_at)) . '</span>
                            </div>
                        </div>';

                if ($chatAwal) {
                    $html .= '</div>';
                }

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = translate('Pesan terkirim.');
            } else {
                throw new Exception('Pesan tidak dapat dikirim. Silakan hubungi administrator');
            }
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = false;
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;
        $data['html'] = $html;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } elseif ($func == 'riwayatChat') {
        $data = array();

        $status = false;
        $message = 'Tidak ada riwayat chat.';
        $html = "";
        $id_lamar = "";

        try {
            // Mulai proses
            $conn->begin_transaction();

            // Cek parameter
            if (!isset($_POST['index'])) {
                throw new Exception('Riwayat chat gagal dimuat.');
            }

            $index = $_POST['index'];
            $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
            $arr = explode("|", decrypt_url($index, $key));

            if (!$arr[0] || !$arr[1]) {
                throw new Exception('Riwayat chat gagal dimuat.');
            }

            $id_koordinator = $arr[0];
            $id_lamar = $arr[1];

            // Cek apakah tujuan ada
            $cek = $conn->prepare("SELECT * FROM users_lamar WHERE id_lamar = ? AND id_koordinator = ?");
            $cek->bind_param("ss", $id_lamar, $id_koordinator);
            $cek->execute();
            $result = $cek->get_result();
            $cek->close();

            if ($result->num_rows > 0) {
                $hari_ini = date("Y-m-d");
                $tglAwal = date("Y-m-d 00:00:00", strtotime($hari_ini . ' -3 month'));
                $tglAkhir = date("Y-m-d 23:59:59");

                // Get data chat
                $get = $conn->prepare("SELECT * FROM chat WHERE (`from` = ? OR `to` = ?) AND reference_id = ? AND created_at BETWEEN '$tglAwal' AND '$tglAkhir' ORDER BY created_at");
                $get->bind_param("sss", $pin, $pin, $id_lamar);
                $get->execute();
                $result = $get->get_result();
                $get->close();

                if ($result->num_rows > 0) {
                    $temp_date = "";
                    while ($row = mysqli_fetch_array($result)) {
                        $pesanHtml = str_replace("\n", "<br>", $row['message']);
                        $jam = date('H:i', strtotime($row['created_at']));
                        $data_date = date("Y-m-d", strtotime($row['created_at']));

                        if ($temp_date != $data_date) {
                            if ($temp_date != "") {
                                $html .= '</div>';
                            }

                            $temp_date = $data_date;

                            $html .= '<div class="chat-group" data-tanggal="' . $data_date . '">
                                        <div class="row mb-2">
                                            <div class="col-12 chat-badge" style="text-align: center;">
                                                <span class="badge bg-light text-dark">' . konversiTanggal($data_date) . '</span>
                                            </div>
                                        </div>';
                        }

                        // Pesan pengirim
                        if ($row['from'] == $pin) {
                            $html .= '<div class="msg-row right">
                                        <div class="bubble">
                                            ' . $pesanHtml . '
                                            <span class="meta">' . $jam . '</span>
                                        </div>
                                    </div>';
                        } else {
                            $html .= '<div class="msg-row left">
                                        <div class="bubble">
                                            ' . $pesanHtml . '
                                            <span class="meta">' . $jam . '</span>
                                        </div>
                                    </div>';
                        }
                    }
                    $html .= '</div>';

                    // Update status chat
                    $update = $conn->prepare("UPDATE chat SET `status` = 'read' WHERE `to` = ? AND reference_id = ? AND `status` = 'send'");
                    $update->bind_param("ss", $pin, $id_lamar);

                    if ($update->execute()) {
                        $update->close();

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();
                    } else {
                        throw new Exception('Riwayat chat gagal dimuat.');
                    }

                    $status = true;
                    $message = 'Riwayat chat berhasil dimuat.';
                }
            }
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = false;
            $message = $e->getMessage();
            $id_lamar = "";
        }

        $data['status'] = $status;
        $data['message'] = $message;
        $data['html'] = $html;
        $data['reference_id'] = $id_lamar;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } elseif ($func == 'cekNotifChat') {
        $data = array();

        $status = false;
        $message = 'Tidak ada notifikasi chat.';

        $notifChatAll = 0;
        $notifChatRef = 0;

        try {
            // Mulai proses
            $conn->begin_transaction();

            // Cek parameter
            if (!isset($_POST['reference_id'])) {
                throw new Exception('Notifikasi chat gagal dimuat.');
            }

            $reference_id = $_POST['reference_id'];

            // Get all data chat
            $get = $conn->prepare("SELECT * FROM chat WHERE `to` = ? AND `status` = 'send' ORDER BY created_at");
            $get->bind_param("s", $pin);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            $notifChatAll = $result->num_rows;

            // Get data chat by reference_id
            $get = $conn->prepare("SELECT * FROM chat WHERE `to` = ? AND reference_id = ? AND `status` = 'send' ORDER BY created_at");
            $get->bind_param("ss", $pin, $reference_id);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            $notifChatRef = $result->num_rows;

            $status = true;
            $message = 'Riwayat chat berhasil dimuat.';
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = false;
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;
        $data['data'] = array("notifChatAll" => $notifChatAll, "notifChatRef" => $notifChatRef);

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } elseif ($func == 'autoRead') {
        $data = array();

        $status = false;
        $message = 'Status gagal diubah.';

        try {
            // Mulai proses
            $conn->begin_transaction();

            // Cek parameter
            if (!isset($_POST['reference_id'])) {
                throw new Exception('Status gagal diubah.');
            }

            $reference_id = $_POST['reference_id'];

            // Update status chat
            $update = $conn->prepare("UPDATE chat SET `status` = 'read' WHERE `to` = ? AND reference_id = ? AND `status` = 'send'");
            $update->bind_param("ss", $pin, $reference_id);

            if ($update->execute()) {
                $update->close();

                // Jika semua query berhasil, commit transaksi
                $conn->commit();
            } else {
                throw new Exception('Status gagal diubah.');
            }

            $status = true;
            $message = 'Status berhasil diubah.';
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = false;
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } else {
        $data = array();
        $data['status'] = "Failed";
        $data['message'] = "You don't have access";

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    }
} else {
    $data = array();
    $data['status'] = "Failed";
    $data['message'] = "You don't have access";

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

$conn->close();
