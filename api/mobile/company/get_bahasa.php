<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';


function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

$search = isset($_GET['q']) ? trim($_GET['q']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
$offset = ($page - 1) * $perPage;

// Jika search diisi tetapi kurang dari 3 karakter, kembalikan error
// if (!empty($search) && strlen($search) < 3) {
//     header('Content-Type: application/json');
//     echo json_encode([
//         "success" => false,
//         "message" => "Harus lebih dari 3 karakter",
//         "data" => [],
//         "page" => $page,
//         "page_size" => $perPage,
//         "total_data" => 0
//     ], JSON_PRETTY_PRINT);
//     exit;
// }

$sql = "SELECT `bahasa` FROM bahasa";
$countQuery = "SELECT COUNT(*) as total FROM bahasa";

// Tambahkan kondisi WHERE jika ada pencarian minimal 3 karakter
if (!empty($search)) {
    $sql .= " WHERE `bahasa` LIKE ?";
    $countQuery .= " WHERE `bahasa` LIKE ?";
}

// Tambahkan limit dan offset untuk pagination
$sql .= " ORDER BY id LIMIT ?, ?";

// Persiapkan statement untuk query utama
$stmt = $conn->prepare($sql);
if (!empty($search)) {
    $searchTerm = "%$search%";
    $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
} else {
    $stmt->bind_param("ii", $offset, $perPage);
}
$stmt->execute();
$result = $stmt->get_result();

$bahasa = [];
while ($row = $result->fetch_assoc()) {
    $bahasa[] = ['id' => $row['bahasa'], 'text' => $row['bahasa']];
}

$stmt = $conn->prepare($countQuery);
if (!empty($search)) {
    $stmt->bind_param("s", $searchTerm);
}
$stmt->execute();
$countResult = $stmt->get_result()->fetch_assoc();
$totalRows = isset($countResult['total']) ? $countResult['total'] : 0;
// Hitung total halaman
$totalPage = ceil($totalRows / $perPage);

// Log activity
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$messages = "Mengakses data bahasa $searchLog (halaman $page dari $totalPage)";
$extra_info = "API";
$level = "INFO";
logActivity($conn, $userData->id, $level, $messages, $extra_info);

// Format response JSON
$response = [
    "status" => true,
    "message" => "fetch data berhasil",
    "data" => $bahasa,
    "page" => $page,
    "page_size" => $perPage,
    "total_page" => $totalPage,
    "total_data" => $totalRows
];

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

$conn->close();
exit;
