<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');
include '../model/database.php';

$create_at = date("Y-m-d H:i:s");

// Cek koneksi
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $conn->connect_error
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] != "POST") {
    // simpan log error
    $pesan = 'Method not allowed. Only POST is permitted.';
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $pesan);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(405); // Method Not Allowed
    echo json_encode([
        "status" => "error",
        "message" => "Method not allowed. Only POST is permitted."
    ]);
    exit;
}

// Set API Key valid
$valid_api_key = "xiAheLul2MLQb4MZKs0MLrG5FcKOtg";

// Ambil API Key dari request header
$headers = apache_request_headers();
$api_key = isset($headers['key']) ? $headers['key'] : null;

// Alternatif kalau apache_request_headers tidak ada
if (!$api_key && isset($_SERVER['HTTP_X_API_KEY'])) {
    $api_key = $_SERVER['HTTP_X_API_KEY'];
}

// Cek API Key
if ($api_key !== $valid_api_key) {
    // simpan log error
    $pesan = 'Unauthorized: Invalid API Key';
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $pesan);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized: Invalid API Key'
    ]);
    exit;
} else {
    $created_at = date('Y-m-d H:i:s');
    function validasiTanggal($tanggal, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $tanggal);
        return $d && $d->format($format) === $tanggal;
    }

    $data = json_decode(file_get_contents("php://input"), true);

    // Jika tidak ada body JSON, fallback ke $_POST
    if (!$data) {
        $data = $_POST;
    }

    // Cek parameter wajib
    $required = [
        "email",
        "nama",
        "tempat_lahir",
        "tgl_lahir",
        "jk",
        "status_pernikahan",
        "no_telepon",
        "sim",
        "propinsi",
        "kota_tinggal",
        "kec_tinggal",
        "rt_tinggal",
        "rw_tinggal",
        "alamat_tinggal",
        "pos_tinggal",
        "cek_diploma",
        "pendidikan_tinggi",
        "jenjang_pendidikan",
        "nama_sekolah_pendidikan",
        "jurusan_pendidikan",
        "tahun_mulai_pendidikan",
        "tahun_selesai_pendidikan",
        "ket_pendidikan",
        "kursus",
        "nama_kursus",
        "sertifikat_kursus",
        "tempat_kursus",
        "tgl_mulai_kursus",
        "tgl_selesai_kursus",
        "pengalaman_kerja",
        "total_pengalaman_kerja",
        "rp_nama_perusahaan",
        "rp_jabatan",
        "rp_status",
        "rp_gaji",
        "rp_tahun_mulai",
        "rp_tahun_selesai",
        "rp_alasan",
        "perjalanan_dinas",
        "minat_lokasi_kerja",
        "minat_gaji",
        "penguasaan_bhs",
        "bahasa",
        "membaca_bhs",
        "menulis_bhs",
        "mendengar_bhs",
        "berbicara_bhs",
        "kelebihan",
        "kekurangan",
        "kk",
        "pimpin_tim",
        "kemampuan_persentasi",
        "rlp",
        "organisasi",
        "po_nama",
        "po_jabatan",
        "po_tahun",
        "po_tempat"
    ];
    $missing = [];

    foreach ($required as $param) {
        if (!isset($data[$param])) {
            // Kalau tidak ada sama sekali
            $missing[] = $param;
        } else {
            $value = $data[$param];

            if (is_array($value)) {
            } else {
                // Kalau value berupa string / scalar biasa
                if (trim($value) === "") {
                    $missing[] = $param;
                }
            }
        }
    }

    // Jika ada param yang hilang
    if (!empty($missing)) {
        // simpan log error
        $pesan = 'Missing required parameters.';
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => "Missing required parameters.",
            "missing" => $missing
        ]);
        exit;
    }

    $email = $data['email'];
    // validasi data diri
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        // simpan log error
        $pesan = 'Invalid email. ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => "Invalid email."
        ]);
        exit;
    }

    // Cek apakah user terdaftar
    $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE email = ? GROUP BY pin");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    // Jika user tidak terdaftar
    if ($result->num_rows == 0) {
        // simpan log error
        $pesan = 'Unregistered candidate. ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(403);
        echo json_encode([
            "status" => "error",
            "message" => "Unregistered candidate."
        ]);
        exit;
    }

    // Ambil data kandidat
    $row = mysqli_fetch_array($result);
    $pin = $row['pin'];

    // cek apakah data rh sudah ada atau tidak
    $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        // simpan log error
        $pesan = 'Personal data is already available. ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(403);
        echo json_encode([
            "status" => "error",
            "message" => "Personal data is already available."
        ]);
        exit;
    }

    $status = "error";
    try {
        // Mulai proses
        $conn->begin_transaction();

        // data diri        
        $nama = $data['nama'];
        $tempat_lahir = ucwords(strtolower($data['tempat_lahir']));
        $tgl_lahir = date("Y-m-d", strtotime($data['tgl_lahir']));
        $jk = $data['jk'];
        $status_pernikahan = $data['status_pernikahan'];
        $no_telepon = $data['no_telepon'];
        if (is_array($data['sim'])) {
            $sim = implode(",", $data['sim']);
        } else {
            $sim = "Tidak Ada";
        }

        $propinsi = ucwords(strtolower($data['propinsi']));
        $kota_tinggal = ucwords(strtolower($data['kota_tinggal']));
        $kec_tinggal = ucwords(strtolower($data['kec_tinggal']));
        $rt_tinggal = $data['rt_tinggal'];
        $rw_tinggal = $data['rw_tinggal'];
        $alamat_tinggal = $data['alamat_tinggal'];
        $pos_tinggal = $data['pos_tinggal'];

        if (!validasiTanggal($tgl_lahir)) {
            throw new Exception("Invalid date of birth format.");
        }

        if ($jk != "Laki-Laki" && $jk != "Perempuan") {
            throw new Exception("Invalid gender.");
        }

        if (!preg_match('/^\+?[0-9]+$/', $no_telepon)) {
            throw new Exception("Invalid phone number.");
        }

        if (!preg_match('/^[0-9]+$/', $pos_tinggal)) {
            throw new Exception("Invalid postal code.");
        }

        // insert data identitas diri
        $insert = $conn->prepare("INSERT INTO `rh`(`id`, `nama`, `tempat_lahir`, `tgl_lahir`, `jenis_kelamin`, `status_pernikahan`, `no_telepon`, `email`, `sim`, `provinsi`, `kota`, `kecamatan`, `rt`, `rw`, `alamat`, `kode_pos`, `created_at`) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $insert->bind_param("sssssssssssssssss", $pin, $nama, $tempat_lahir, $tgl_lahir, $jk, $status_pernikahan, $no_telepon, $email, $sim, $propinsi, $kota_tinggal, $kec_tinggal, $rt_tinggal, $rw_tinggal, $alamat_tinggal, $pos_tinggal, $created_at);

        if ($insert->execute()) {
            $insert->close();

            // update user kandidat
            $update_user_kandidat = $conn->prepare("UPDATE `users_kandidat` SET `nama_lengkap` = ? WHERE `pin` = ?");
            $update_user_kandidat->bind_param("ss", $nama, $pin);
            if ($update_user_kandidat->execute()) {
                $update_user_kandidat->close();
            } else {
                throw new Exception("Personal identity data failed to be saved.");
            }
        } else {
            throw new Exception("Personal identity data failed to be saved.");
        }
        // end insert data identitas diri

        // data riwayat pendidikan
        $pendidikan_terakhir = $data['pendidikan_tinggi'];
        if ($pendidikan_terakhir == 'S1' || $pendidikan_terakhir == 'S2' || $pendidikan_terakhir == 'S3') {
            $diploma = $data['cek_diploma'];
            if ($diploma != "Ya" && $diploma != "Tidak") {
                throw new Exception("The value 'cek_diploma' is not valid.");
            }
        } elseif ($pendidikan_terakhir == 'Diploma') {
            $diploma = "Ya";
        } else {
            $diploma = "Tidak";
        }

        $jenjang_pendidikan = array();
        if (is_array($data['jenjang_pendidikan'])) {
            $jenjang_pendidikan = $data['jenjang_pendidikan'];
        }

        $nama_sekolah_pendidikan = array();
        if (is_array($data['nama_sekolah_pendidikan'])) {
            $nama_sekolah_pendidikan = $data['nama_sekolah_pendidikan'];
        }

        $jurusan_pendidikan = array();
        if (is_array($data['jurusan_pendidikan'])) {
            $jurusan_pendidikan = $data['jurusan_pendidikan'];
        }

        $tahun_mulai_pendidikan = array();
        if (is_array($data['tahun_mulai_pendidikan'])) {
            $tahun_mulai_pendidikan = $data['tahun_mulai_pendidikan'];
        }

        $tahun_selesai_pendidikan = array();
        if (is_array($data['tahun_selesai_pendidikan'])) {
            $tahun_selesai_pendidikan = $data['tahun_selesai_pendidikan'];
        }

        $ket_pendidikan = array();
        if (is_array($data['ket_pendidikan'])) {
            $ket_pendidikan = $data['ket_pendidikan'];
        }

        // update data pendidikan
        $update = $conn->prepare("UPDATE rh SET pendidikan_terakhir = ?, diploma = ? WHERE id = ?");
        $update->bind_param("sss", $pendidikan_terakhir, $diploma, $pin);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Education history failed to save.");
        }

        // cek jika data kosong
        if (count($jenjang_pendidikan) == 0 && count($nama_sekolah_pendidikan) == 0 && count($jurusan_pendidikan) == 0 && count($tahun_mulai_pendidikan) == 0 && count($tahun_selesai_pendidikan) == 0 && count($ket_pendidikan) == 0) {
            throw new Exception("The education history field cannot be empty.");
        }

        $temp_jenjang = array();
        // simpan detail riwayat pendidikan
        for ($i = 0; $i < count($jenjang_pendidikan); $i++) {
            $jenjang = $jenjang_pendidikan[$i];
            $nama_sekolah = $nama_sekolah_pendidikan[$i];
            $jurusan = $jurusan_pendidikan[$i];
            $tahun_mulai = date("Y-m-d", strtotime($tahun_mulai_pendidikan[$i]));
            $tahun_selesai = date("Y-m-d", strtotime($tahun_selesai_pendidikan[$i]));
            $ket = $ket_pendidikan[$i];

            if (!validasiTanggal($tahun_mulai) || !validasiTanggal($tahun_selesai)) {
                throw new Exception("The start date or end date of education is invalid.");
            }

            if ($tahun_mulai > $tahun_selesai) {
                throw new Exception("The start date of the education cannot be later than the end date.");
            }

            // cek apakah jenjang pendidikan sudah ada
            $sql = $conn->prepare("SELECT id FROM riwayat_pendidikan WHERE id = ? AND jenjang = ?");
            $sql->bind_param("ss", $pin, $jenjang);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update detail riwayat pendidikan
                $update = $conn->prepare("UPDATE `riwayat_pendidikan` SET `nama_sekolah` = ?, `tahun_mulai` = ?, `tahun_selesai` = ?, `jurusan` = ?, `ket` = ?, `updated_at` = ? WHERE `id` = ? AND jenjang = ?");
                $update->bind_param("ssssssss",  $nama_sekolah, $tahun_mulai, $tahun_selesai, $jurusan, $ket, $created_at, $pin, $jenjang);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception("Education history failed to save.");
                }
            } else {
                // insert detail riwayat pendidikan
                $insert = $conn->prepare("INSERT INTO `riwayat_pendidikan`(`id`, `jenjang`, `nama_sekolah`, `tahun_mulai`, `tahun_selesai`, `jurusan`, `ket`, `created_at`) 
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("ssssssss", $pin, $jenjang, $nama_sekolah, $tahun_mulai, $tahun_selesai, $jurusan, $ket, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Education history failed to save.");
                }
            }

            $temp_jenjang[] = $jenjang;
        }
        // end simpan detail riwayat pendidikan

        // hapus jenjang yang tidak ada
        if (count($temp_jenjang) > 0) {
            // Buat placeholder (?) sebanyak jumlah elemen dalam array
            $placeholders = implode(",", array_fill(0, count($temp_jenjang), "?"));

            $del = $conn->prepare("DELETE FROM riwayat_pendidikan WHERE id = ? AND jenjang NOT IN ($placeholders)");

            // Buat string tipe data
            $types = "s" . str_repeat("s", count($temp_jenjang));

            // Gabungkan parameter (ID + Status) untuk bind_param()
            $params = array_merge([$pin], $temp_jenjang);

            // Bind parameter menggunakan reference (karena bind_param membutuhkan reference)
            $del->bind_param($types, ...$params);

            if ($del->execute()) {
                $del->close();
            } else {
                throw new Exception("Education history failed to save.");
            }
        }
        // end insert data pendidikan

        // update data pelatihan/kursus
        $kursus = $data['kursus'];
        if ($kursus != "Ya" && $kursus != "Tidak") {
            throw new Exception("The value 'kursus' is not valid.");
        }

        $update = $conn->prepare("UPDATE `rh` SET kursus = ? WHERE id = ?");
        $update->bind_param("ss", $kursus, $pin);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Course/training history failed to save.");
        }

        $nama_kursus = array();
        if (isset($data['nama_kursus'])) {
            $nama_kursus = $data['nama_kursus'];
        }

        // cek keterangan kursus
        if ($kursus == 'Ya' && count($nama_kursus) > 0) {
            $nama_kursus = array();
            if (is_array($data['nama_kursus'])) {
                $nama_kursus = $data['nama_kursus'];
            }

            $sertifikat_kursus = array();
            if (is_array($data['sertifikat_kursus'])) {
                $sertifikat_kursus = $data['sertifikat_kursus'];
            }

            $tempat_kursus = array();
            if (is_array($data['tempat_kursus'])) {
                $tempat_kursus = $data['tempat_kursus'];
            }

            $tgl_mulai_kursus = array();
            if (is_array($data['tgl_mulai_kursus'])) {
                $tgl_mulai_kursus = $data['tgl_mulai_kursus'];
            }

            $tgl_selesai_kursus = array();
            if (is_array($data['tgl_selesai_kursus'])) {
                $tgl_selesai_kursus = $data['tgl_selesai_kursus'];
            }

            // cek jika data kosong
            if (count($nama_kursus) == 0 && count($sertifikat_kursus) == 0 && count($tempat_kursus) == 0 && count($tgl_mulai_kursus) == 0 && count($tgl_selesai_kursus) == 0) {
                throw new Exception("Course/training history data cannot be empty.");
            }

            // simpan detail riwayat pelatihan/kursus
            for ($i = 0; $i < count($nama_kursus); $i++) {
                $nama = $nama_kursus[$i];
                $sertifikat = $sertifikat_kursus[$i];
                $tempat = $tempat_kursus[$i];
                $tgl_mulai = $tgl_mulai_kursus[$i];
                $tgl_selesai = $tgl_selesai_kursus[$i];

                if (!validasiTanggal($tgl_mulai) || !validasiTanggal($tgl_selesai)) {
                    throw new Exception("The start date or end date of the course are invalid.");
                }

                if ($tgl_mulai > $tgl_selesai) {
                    throw new Exception("The start date of the course cannot be later than the end date.");
                }

                // insert detail riwayat pendidikan
                $insert = $conn->prepare("INSERT INTO `riwayat_kursus`(`id`, `nama`, `tempat`, `sertifikat`, `tgl_mulai`, `tgl_selesai`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssss", $pin, $nama, $tempat, $sertifikat, $tgl_mulai, $tgl_selesai, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Course/training history failed to save.");
                }
            }
            // end simpan detail riwayat pelatihan/kursus
        }
        // end insert data pelatihan/kursus

        // insert data pekerjaan
        $pengalaman_kerja = $data['pengalaman_kerja'];
        if ($pengalaman_kerja != "Ya" && $pengalaman_kerja != "Tidak" && $pengalaman_kerja != "Fresh Graduate") {
            throw new Exception("The value 'pengalaman_kerja' is not valid.");
        }

        $total_pengalaman_kerja = $data['total_pengalaman_kerja'];
        $pengalaman_posisi_sama = "";

        // validasi pengalaman kerja
        if (!preg_match('/^[0-9]+$/', $total_pengalaman_kerja)) {
            throw new Exception("Length of work experience is invalid.");
        }

        if ($pengalaman_kerja != "Ya") {
            $total_pengalaman_kerja = "";
            $pengalaman_posisi_sama = "";
            $nama = array();
            $jabatan = array();
            $status = array();
            $gaji = array();
            $tahun_mulai = array();
            $tahun_selesai = array();
            $alasan = array();
        }

        $update = $conn->prepare("UPDATE `rh` SET `pengalaman_kerja` = ?, `lama_pengalaman_kerja` = ?, `lama_posisi_kerja` = ? WHERE id = ?");
        $update->bind_param("ssss", $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $pin);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Work experience failed to save.");
        }

        $nama = array();
        if (isset($data['rp_nama_perusahaan'])) {
            if (is_array($data['rp_nama_perusahaan'])) {
                $nama = $data['rp_nama_perusahaan'];
            }
        }

        // cek keterangan pengalaman kerja
        if ($pengalaman_kerja == 'Ya' && count($nama) > 0) {
            $jabatan = array();
            if (is_array($data['rp_jabatan'])) {
                $jabatan = $data['rp_jabatan'];
            }

            $status = array();
            if (is_array($data['rp_status'])) {
                $status = $data['rp_status'];
            }

            $gaji = array();
            if (is_array($data['rp_gaji'])) {
                $gaji = $data['rp_gaji'];
            }

            $tahun_mulai = array();
            if (is_array($data['rp_tahun_mulai'])) {
                $tahun_mulai = $data['rp_tahun_mulai'];
            }

            $tahun_selesai = array();
            if (is_array($data['rp_tahun_selesai'])) {
                $tahun_selesai = $data['rp_tahun_selesai'];
            }

            $alasan = array();
            if (is_array($data['rp_alasan'])) {
                $alasan = $data['rp_alasan'];
            }

            // cek jika data kosong
            if (count($nama) == 0 && count($jabatan) == 0 && count($status) == 0 && count($gaji) == 0 && count($tahun_mulai) == 0 && count($tahun_selesai) == 0 && count($alasan) == 0) {
                throw new Exception("Work history data cannot be left blank.");
            }

            // simpan detail riwayat pekerjaan
            for ($i = 0; $i < count($nama); $i++) {
                $temp_nama = $nama[$i];
                $temp_jabatan = $jabatan[$i];
                $temp_status = $status[$i];
                $temp_gaji = str_replace(".", "", $gaji[$i]);

                if ($temp_gaji == "") {
                    $temp_gaji = 0;
                }

                if ($temp_gaji >= 0) {
                    $temp_tahun_mulai = date("m-Y", strtotime($tahun_mulai[$i]));
                    if ($temp_tahun_mulai == "01-1970") {
                        throw new Exception("The value 'rp_tahun_mulai' is not valid.");
                    }

                    $temp_tahun_selesai = date("m-Y", strtotime($tahun_selesai[$i]));
                    if ($temp_tahun_selesai == "01-1970") {
                        throw new Exception("The value 'rp_tahun_selesai' is not valid.");
                    }

                    if (date("Y-m-d", strtotime($tahun_mulai[$i])) > date("Y-m-d", strtotime($tahun_selesai[$i]))) {
                        throw new Exception("The start date of the work experience cannot be later than the end date.");
                    }

                    $temp_alasan = $alasan[$i];

                    // insert detail riwayat pekerjaan
                    $insert = $conn->prepare("INSERT INTO `riwayat_pekerjaan`(`id`, `nama_perusahaan`, `jabatan`, `status_kerja`, `gaji`, `tahun_mulai`, `tahun_selesai`, `alasan_berhenti`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("sssssssss", $pin, $temp_nama, $temp_jabatan, $temp_status, $temp_gaji, $temp_tahun_mulai, $temp_tahun_selesai, $temp_alasan, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception("Work experience failed to save.");
                    }
                } else {
                    throw new Exception("Salary history does not match.");
                }
            }
            // end simpan detail riwayat pekerjaan
        }
        // end insert data pekerjaan

        // insert informasi pekerjaan
        $perjalanan_dinas = $data['perjalanan_dinas'];
        if ($perjalanan_dinas != "Ya" && $perjalanan_dinas != "Tidak") {
            throw new Exception("The value 'perjalanan_dinas' is not valid.");
        }

        $minat_lokasi_kerja = $data['minat_lokasi_kerja'];
        $temp_gaji = str_replace(".", "", $data['minat_gaji']);
        if ($temp_gaji >= 0) {
            $update = $conn->prepare("UPDATE `rh` SET `perjalanan_dinas` = ?, `minat_lokasi_kerja` = ?, `minat_gaji` = ? WHERE id = ?");
            $update->bind_param("ssss", $perjalanan_dinas, $minat_lokasi_kerja, $temp_gaji, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Job information failed to be saved.");
            }
        } else {
            throw new Exception("The value 'minat_gaji' is not valid.");
        }
        // end insert informasi pekerjaan

        // insert minat dan konsep
        $penguasaan_bhs = $data['penguasaan_bhs'];
        if ($penguasaan_bhs != "Ya" && $penguasaan_bhs != "Tidak") {
            throw new Exception("The value 'penguasaan_bhs' is not valid.");
        }

        // validasi input penuasaan bahasa
        $bahasa = array();
        if (is_array($data['bahasa'])) {
            $bahasa = $data['bahasa'];
        }

        $membaca_bhs = array();
        if (is_array($data['membaca_bhs'])) {
            $membaca_bhs = $data['membaca_bhs'];
        }

        $menulis_bhs = array();
        if (is_array($data['menulis_bhs'])) {
            $menulis_bhs = $data['menulis_bhs'];
        }

        $mendengar_bhs = array();
        if (is_array($data['mendengar_bhs'])) {
            $mendengar_bhs = $data['mendengar_bhs'];
        }

        $berbicara_bhs = array();
        if (is_array($data['berbicara_bhs'])) {
            $berbicara_bhs = $data['berbicara_bhs'];
        }

        if (count($bahasa) > 0 && count($membaca_bhs) > 0 && count($menulis_bhs) > 0 && count($mendengar_bhs) > 0 && count($berbicara_bhs) > 0) {
            $bahasa = $data['bahasa'];
            $membaca_bhs = $data['membaca_bhs'];
            $menulis_bhs = $data['menulis_bhs'];
            $mendengar_bhs = $data['mendengar_bhs'];
            $berbicara_bhs = $data['berbicara_bhs'];
        } else {
            $bahasa = array();
            $membaca_bhs = array();
            $menulis_bhs = array();
            $mendengar_bhs = array();
            $berbicara_bhs = array();
        }
        $kelebihan = implode(",", $data['kelebihan']);
        $kekurangan = implode(",", $data['kekurangan']);
        $kk = $data['kk'];
        // validasi kemampuan komputer
        if (!preg_match('/^[0-9]+$/', $kk)) {
            throw new Exception("The value 'kk' is not valid.");
        }

        if ($data['pimpin_tim'] > 50) {
            $pimpin_tim = 5;
        } elseif ($data['pimpin_tim'] >= 11 && $data['pimpin_tim'] <= 50) {
            $pimpin_tim = 4;
        } elseif ($data['pimpin_tim'] >= 4 && $data['pimpin_tim'] <= 10) {
            $pimpin_tim = 3;
        } elseif ($data['pimpin_tim'] >= 1 && $data['pimpin_tim'] <= 3) {
            $pimpin_tim = 2;
        } else {
            $pimpin_tim = 1;
        }

        // validasi kemampuan komputer
        if (!preg_match('/^[0-9]+$/', $pimpin_tim)) {
            throw new Exception("The value 'pimpin_tim' is not valid.");
        }

        $kemampuan_persentasi = $data['kemampuan_persentasi'];
        // validasi kemampuan komputer
        if (!preg_match('/^[0-9]+$/', $kemampuan_persentasi)) {
            throw new Exception("The value 'kemampuan_persentasi' is not valid.");
        }

        $rlp = implode(",", $data['rlp']);

        // cek keterangan penguasaan bahasa
        if ($penguasaan_bhs == 'Ya' && count($bahasa) > 0) {
            // cek jika data kosong
            if (count($bahasa) == 0 && count($membaca_bhs) == 0 && count($menulis_bhs) == 0 && count($mendengar_bhs) == 0 && count($berbicara_bhs) == 0) {
                throw new Exception("Language proficiency data must not be empty.");
            }

            // simpan detail penguasaan bahasa
            for ($i = 0; $i < count($bahasa); $i++) {
                $temp_bahasa = $bahasa[$i];
                $temp_membaca_bhs = $membaca_bhs[$i];
                $temp_menulis_bhs = $menulis_bhs[$i];
                $temp_mendengar_bhs = $mendengar_bhs[$i];
                $temp_berbicara_bhs = $berbicara_bhs[$i];

                //validasi keterampilan bahasa
                $temp_validasi = ["BS", "B", "C", "K", "KS"];

                if (!in_array($temp_membaca_bhs, $temp_validasi)) {
                    throw new Exception("The value 'membaca_bhs' is not valid.");
                }

                if (!in_array($temp_menulis_bhs, $temp_validasi)) {
                    throw new Exception("The value 'menulis_bhs' is not valid.");
                }

                if (!in_array($temp_mendengar_bhs, $temp_validasi)) {
                    throw new Exception("The value 'mendengar_bhs' is not valid.");
                }

                if (!in_array($temp_berbicara_bhs, $temp_validasi)) {
                    throw new Exception("The value 'berbicara_bhs' is not valid.");
                }

                // insert detail penguasaan bahasa
                $insert = $conn->prepare("INSERT INTO `penguasaan_bahasa`(`id`, `bahasa`, `membaca`, `menulis`, `mendengar`, `berbicara`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssss", $pin, $temp_bahasa, $temp_membaca_bhs, $temp_menulis_bhs, $temp_mendengar_bhs, $temp_berbicara_bhs, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Personal interests and concepts failed to be saved.");
                }
            }
            // end simpan detail penguasaan bahasa
        }
        // end cek keterangan penguasaan bahasa

        $update = $conn->prepare("UPDATE `rh` SET `bahasa_asing` = ?, `kelebihan` = ?, `kekurangan` = ?, `ilmu_komputerisasi` = ?, `memimpin_tim` = ?, `kemampuan_presentasi` = ?, `lingkup_pekerjaan` = ? WHERE id = ?");
        $update->bind_param("ssssssss", $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $pin);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Personal interests and concepts failed to be saved.");
        }
        // end insert minat dan konsep

        // insert data organisasi
        $organisasi = $data['organisasi'];
        if ($organisasi != "Ya" && $organisasi != "Tidak") {
            throw new Exception("The value 'organisasi' is not valid.");
        }

        $update = $conn->prepare("UPDATE `rh` SET `organisasi` = ? WHERE id = ?");
        $update->bind_param("ss", $organisasi, $pin);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Organizational experience was not retained.");
        }

        $nama = array();
        if (isset($data['po_nama'])) {
            $nama = $data['po_nama'];
        }

        // cek keterangan organisasi
        if ($organisasi == 'Ya' && count($nama) > 0) {
            $nama = array();
            if (is_array($data['po_nama'])) {
                $nama = $data['po_nama'];
            }

            $jabatan = array();
            if (is_array($data['po_jabatan'])) {
                $jabatan = $data['po_jabatan'];
            }

            $tahun = array();
            if (is_array($data['po_tahun'])) {
                $tahun = $data['po_tahun'];
            }

            $tempat = array();
            if (is_array($data['po_tempat'])) {
                $tempat = $data['po_tempat'];
            }

            // cek jika data kosong
            if (count($nama) == 0 && count($jabatan) == 0 && count($tahun) == 0 && count($tempat) == 0) {
                throw new Exception("Organization history data cannot be empty.");
            }

            // simpan detail riwayat organisasi
            for ($i = 0; $i < count($nama); $i++) {
                $temp_nama = $nama[$i];
                $temp_jabatan = $jabatan[$i];
                $temp_tahun = $tahun[$i];
                $temp_tempat = $tempat[$i];

                // insert detail riwayat organisasi
                $insert = $conn->prepare("INSERT INTO `riwayat_organisasi`(`id`, `nama`, `jabatan`, `tempat`, `tahun`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?)");
                $insert->bind_param("ssssss", $pin, $temp_nama, $temp_jabatan, $temp_tempat, $temp_tahun, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Organizational experience was not retained.");
                }
            }
            // end simpan detail riwayat organisasi
        }
        // end insert data organisasi

        $conn->commit();

        $status = "success";
        $message = "Personal data has been successfully saved.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "error";
        $message = $e->getMessage();
    }

    if ($status == 'success') {
        http_response_code(201);
        echo json_encode([
            "status" => "success",
            "message" => $message
        ]);
        exit;
    } else {
        // simpan log error
        $pesan = $message . ' ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert RH', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => $message
        ]);
        exit;
    }
}
