<?php

/**
 * API Mobile Candidate - Get CV Data
 * 
 * Endpoint untuk mendapatkan data CV kandidat (Riwayat Hidup)
 * Menggunakan JWT Authentication untuk keamanan
 * 
 * Authentication: <PERSON><PERSON> (JWT)
 * Header yang diperlukan:
 * - Authorization: Bearer <access_token>
 * 
 * Functions yang tersedia:
 * - getRH: Mendapatkan data riwayat hidup lengkap
 * - getRiwayatPendidikan: Mendapatkan riwayat pendidikan
 * - getRiwayatKursus: Mendapatkan riwayat kursus
 * - getRiwayatPekerjaan: Mendapatkan riwayat pekerjaan
 * - getRiwayatOrganisasi: Mendapatkan riwayat organisasi
 * - getPenguasaanBahasa: Mendapatkan data penguasaan bahasa
 * 
 * Usage:
 * GET /api/mobile/candidate/get_cv.php?func=getRH
 * Headers:
 *   Authorization: Bearer <jwt_access_token>
 */

header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

// Middleware untuk autentikasi JWT
$userData = requireAuth();

// Debug: pastikan userData tidak null dan memiliki properti yang diperlukan
if (!$userData) {
    echo json_encode([
        'success' => false,
        'message' => 'Data user tidak tersedia dari token'
    ]);
    exit;
}

$pin = $userData->pin;
$nama_user = $userData->nama;
$email_user = $userData->email;

// Validasi dan whitelist function parameter
$allowedFunctions = [
    'getRH',
    'getRiwayatPendidikan',
    'getRiwayatKursus',
    'getRiwayatPekerjaan',
    'getRiwayatOrganisasi',
    'getPenguasaanBahasa'
];

$func = isset($_GET['func']) ? trim($_GET['func']) : '';

// Validasi function parameter
if (empty($func) || !in_array($func, $allowedFunctions)) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Function tidak valid atau tidak diizinkan',
        'allowed_functions' => $allowedFunctions
    ], JSON_PRETTY_PRINT);
    exit;
}

function sanitizeOutput($data)
{
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeOutput($value);
        }
    } elseif (is_string($data)) {
        return $data;
    }
    return $data;
}

function executeSecureQuery($conn, $query, $params, $types)
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Failed to prepare statement");
        }

        if (!empty($params)) {
            if (!$stmt->bind_param($types, ...$params)) {
                throw new Exception("Failed to bind parameters");
            }
        }

        if (!$stmt->execute()) {
            throw new Exception("Failed to execute query");
        }

        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Failed to get result");
        }

        $stmt->close();
        return $result;
    } catch (Exception $e) {
        if (isset($stmt)) {
            $stmt->close();
        }
        return false;
    }
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}
function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}
if ($func == 'getRH') {
    $response['data'] = [];

    // Query dengan security yang ditingkatkan
    $query = "SELECT 
                rh.*, 
                CONCAT(alamat, ' RT ', rt, ' RW ', rw, ' ', kecamatan, ' ', kota, ' ', provinsi, '. ', kode_pos) AS alamat_lengkap,
                GROUP_CONCAT(rlp.name ORDER BY rlp.id SEPARATOR ', ') AS lingkup_pekerjaan_nama
                FROM rh
                LEFT JOIN ruang_lingkup_pekerjaan rlp 
                ON FIND_IN_SET(rlp.id, rh.lingkup_pekerjaan)
                WHERE rh.id = ?
                GROUP BY rh.id";

    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Cek dan format tgl_lahir
            if (isset($row['tgl_lahir']) && $row['tgl_lahir'] != "") {
                $row['tgl_lahir_formatted'] = formatTanggal($row['tgl_lahir']);
            } else {
                $row['tgl_lahir_formatted'] = '';
            }
            if (isset($row['tempat_lahir']) && $row['tempat_lahir'] != "") {
                $row['tempat_lahir_formatted'] = ucwords(strtolower($row['tempat_lahir']));
            } else {
                $row['tempat_lahir_formatted'] = '';
            }
            // Sanitasi output
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getRiwayatPendidikan') {
    $response['data'] = [];

    $query = "SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD','SMP','SMA','Diploma','S1','S2','S3')";
    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getRiwayatKursus') {
    $response['data'] = [];

    $query = "SELECT * FROM riwayat_kursus WHERE id = ?";
    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getRiwayatPekerjaan') {
    $response['data'] = [];

    $query = "SELECT * FROM riwayat_pekerjaan WHERE id = ?";
    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getRiwayatOrganisasi') {
    $response['data'] = [];

    $query = "SELECT * FROM riwayat_organisasi WHERE id = ?";
    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getPenguasaanBahasa') {
    $response['data'] = [];

    $query = "SELECT * FROM penguasaan_bahasa WHERE id = ?";
    $result = executeSecureQuery($conn, $query, [$pin], "s");

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan dalam mengambil data'
        ], JSON_PRETTY_PRINT);
        exit;
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

// Jika sampai sini berarti function tidak valid
header('Content-Type: application/json');
echo json_encode([
    'success' => false,
    'message' => 'Function tidak ditemukan'
], JSON_PRETTY_PRINT);

$conn->close();
exit;
