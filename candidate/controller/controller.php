<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../model/database.php';
include '../../api/s3.php';
require '../../vendor/autoload.php';
include '../../api/ses.php';
require '../../api/fcm_helper.php';

$pin = "-";
$nama_user = "-";
$email_user = "-";

// get data user
if (isset($_SESSION['users']['pin'])) {
    $pin = addslashes($_SESSION['users']['pin']);
}

if (isset($_SESSION['users']['nama'])) {
    $nama_user = $_SESSION['users']['nama'];
}

if (isset($_SESSION['users']['email'])) {
    $email_user = $_SESSION['users']['email'];
}

if (isset($_GET['func'])) {
    $func = $_GET['func'];
} else {
    $func = $_POST['func'];
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        if ($diff->y > 1) {
            return $diff->y . translate(' tahun yang lalu');
        } else {
            return $diff->y . " " . translate('tahun yang lalu');
        }
    } elseif ($diff->m > 0) {
        if ($diff->m > 1) {
            return $diff->m . translate(' bulan yang lalu');
        } else {
            return $diff->m . " " . translate('bulan yang lalu');
        }
    } elseif ($diff->d > 0) {
        if ($diff->d > 1) {
            return $diff->d . translate(' hari yang lalu');
        } else {
            return $diff->d . " " . translate('hari yang lalu');
        }
    } elseif ($diff->h > 0) {
        if ($diff->h > 1) {
            return $diff->h . translate(' jam yang lalu');
        } else {
            return $diff->h . " " . translate('jam yang lalu');
        }
    } elseif ($diff->i > 0) {
        if ($diff->i > 1) {
            return $diff->i . translate(' menit yang lalu');
        } else {
            return $diff->i . " " . translate('menit yang lalu');
        }
    } else {
        return translate('Baru saja');
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = translate($bulanIndo[(int)$tanggalObj->format('m') - 1]); // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

function getIDLamar($length = 10)
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

if (!function_exists('getIDScreening')) {
    // function buat id screening
    function getIDScreening($length)
    {
        $data = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyz';
        $string = '';
        for ($i = 0; $i < $length; $i++) {
            $pos = rand(0, strlen($data) - 1);
            $string .= $data[$pos];
        }
        return $string;
    }
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

// fungsi untuk memverifikasi password
function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

function decrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $data = strtr($data, '-_,', '+/=');
    $decoded = base64_decode($data);
    $iv = substr($decoded, 0, 16);
    $encrypted = substr($decoded, 16);
    return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
}

function kirim_apply_lamaran($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient)
{
    $emailParams = [
        'Destination' => [
            'ToAddresses' => [$emailKandidat],
        ],
        'Message' => [
            'Body' => [
                'Html' => ['Data' => '
                                    <!DOCTYPE html
                                        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                    <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                    <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                    <head>
                                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                        <!--[if !mso]><!-->
                                        <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                        <!--<![endif]-->
                                        <meta name="viewport" content="width=device-width">
                                        <style type="text/css">
                                            @media only screen and (min-width: 620px) {
                                                .wrapper {
                                                    min-width: 600px !important
                                                }

                                                .wrapper h1 {}

                                                .wrapper h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper h2 {}

                                                .wrapper h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper h3 {}

                                                .column {}

                                                .wrapper .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important
                                                }

                                                .wrapper .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important
                                                }

                                                .wrapper .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important
                                                }

                                                .wrapper .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important
                                                }

                                                .wrapper .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important
                                                }

                                                .wrapper .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important
                                                }

                                                .wrapper .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important
                                                }

                                                .wrapper .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important
                                                }

                                                .wrapper .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important
                                                }

                                                .wrapper .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important
                                                }

                                                .wrapper .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important
                                                }

                                                .wrapper .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important
                                                }

                                                .wrapper .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important
                                                }

                                                .wrapper .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important
                                                }

                                                .wrapper .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important
                                                }
                                            }
                                        </style>
                                        <style type="text/css">
                                            body {
                                                margin: 0;
                                                padding: 0;
                                            }

                                            table {
                                                border-collapse: collapse;
                                                table-layout: fixed;
                                            }

                                            * {
                                                line-height: inherit;
                                            }

                                            [x-apple-data-detectors],
                                            [href^="tel"],
                                            [href^="sms"] {
                                                color: inherit !important;
                                                text-decoration: none !important;
                                            }

                                            .wrapper .footer__share-button a:hover,
                                            .wrapper .footer__share-button a:focus {
                                                color: #ffffff !important;
                                            }

                                            .btn a:hover,
                                            .btn a:focus,
                                            .footer__share-button a:hover,
                                            .footer__share-button a:focus,
                                            .email-footer__links a:hover,
                                            .email-footer__links a:focus {
                                                opacity: 0.8;
                                            }

                                            .preheader,
                                            .header,
                                            .layout,
                                            .column {
                                                transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                            }

                                            .preheader td {
                                                padding-bottom: 8px;
                                            }

                                            .layout,
                                            div.header {
                                                max-width: 400px !important;
                                                -fallback-width: 95% !important;
                                                width: calc(100% - 20px) !important;
                                            }

                                            div.preheader {
                                                max-width: 360px !important;
                                                -fallback-width: 90% !important;
                                                width: calc(100% - 60px) !important;
                                            }

                                            .snippet,
                                            .webversion {
                                                Float: none !important;
                                            }

                                            .column {
                                                max-width: 400px !important;
                                                width: 100% !important;
                                            }

                                            .fixed-width.has-border {
                                                max-width: 402px !important;
                                            }

                                            .fixed-width.has-border .layout__inner {
                                                box-sizing: border-box;
                                            }

                                            .snippet,
                                            .webversion {
                                                width: 50% !important;
                                            }

                                            .ie .btn {
                                                width: 100%;
                                            }

                                            [owa] .column div,
                                            [owa] .column button {
                                                display: block !important;
                                            }

                                            .ie .column,
                                            [owa] .column,
                                            .ie .gutter,
                                            [owa] .gutter {
                                                display: table-cell;
                                                float: none !important;
                                                vertical-align: top;
                                            }

                                            .ie div.preheader,
                                            [owa] div.preheader,
                                            .ie .email-footer,
                                            [owa] .email-footer {
                                                max-width: 560px !important;
                                                width: 560px !important;
                                            }

                                            .ie .snippet,
                                            [owa] .snippet,
                                            .ie .webversion,
                                            [owa] .webversion {
                                                width: 280px !important;
                                            }

                                            .ie div.header,
                                            [owa] div.header,
                                            .ie .layout,
                                            [owa] .layout,
                                            .ie .one-col .column,
                                            [owa] .one-col .column {
                                                max-width: 600px !important;
                                                width: 600px !important;
                                            }

                                            .ie .fixed-width.has-border,
                                            [owa] .fixed-width.has-border,
                                            .ie .has-gutter.has-border,
                                            [owa] .has-gutter.has-border {
                                                max-width: 602px !important;
                                                width: 602px !important;
                                            }

                                            .ie .two-col .column,
                                            [owa] .two-col .column {
                                                max-width: 300px !important;
                                                width: 300px !important;
                                            }

                                            .ie .three-col .column,
                                            [owa] .three-col .column,
                                            .ie .narrow,
                                            [owa] .narrow {
                                                max-width: 200px !important;
                                                width: 200px !important;
                                            }

                                            .ie .wide,
                                            [owa] .wide {
                                                width: 400px !important;
                                            }

                                            .ie .two-col.has-gutter .column,
                                            [owa] .two-col.x_has-gutter .column {
                                                max-width: 290px !important;
                                                width: 290px !important;
                                            }

                                            .ie .three-col.has-gutter .column,
                                            [owa] .three-col.x_has-gutter .column,
                                            .ie .has-gutter .narrow,
                                            [owa] .has-gutter .narrow {
                                                max-width: 188px !important;
                                                width: 188px !important;
                                            }

                                            .ie .has-gutter .wide,
                                            [owa] .has-gutter .wide {
                                                max-width: 394px !important;
                                                width: 394px !important;
                                            }

                                            .ie .two-col.has-gutter.has-border .column,
                                            [owa] .two-col.x_has-gutter.x_has-border .column {
                                                max-width: 292px !important;
                                                width: 292px !important;
                                            }

                                            .ie .three-col.has-gutter.has-border .column,
                                            [owa] .three-col.x_has-gutter.x_has-border .column,
                                            .ie .has-gutter.has-border .narrow,
                                            [owa] .has-gutter.x_has-border .narrow {
                                                max-width: 190px !important;
                                                width: 190px !important;
                                            }

                                            .ie .has-gutter.has-border .wide,
                                            [owa] .has-gutter.x_has-border .wide {
                                                max-width: 396px !important;
                                                width: 396px !important;
                                            }

                                            .ie .fixed-width .layout__inner {
                                                border-left: 0 none white !important;
                                                border-right: 0 none white !important;
                                            }

                                            .ie .layout__edges {
                                                display: none;
                                            }

                                            .mso .layout__edges {
                                                font-size: 0;
                                            }

                                            .layout-fixed-width,
                                            .mso .layout-full-width {
                                                background-color: #ffffff;
                                            }

                                            @media only screen and (min-width: 620px) {

                                                .column,
                                                .gutter {
                                                    display: table-cell;
                                                    Float: none !important;
                                                    vertical-align: top;
                                                }

                                                div.preheader,
                                                .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 280px !important;
                                                }

                                                div.header,
                                                .layout,
                                                .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .fixed-width.has-border,
                                                .fixed-width.ecxhas-border,
                                                .has-gutter.has-border,
                                                .has-gutter.ecxhas-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .three-col .column,
                                                .column.narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .column.wide {
                                                    width: 400px !important;
                                                }

                                                .two-col.has-gutter .column,
                                                .two-col.ecxhas-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .three-col.has-gutter .column,
                                                .three-col.ecxhas-gutter .column,
                                                .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .two-col.has-gutter.has-border .column,
                                                .two-col.ecxhas-gutter.ecxhas-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .three-col.has-gutter.has-border .column,
                                                .three-col.ecxhas-gutter.ecxhas-border .column,
                                                .has-gutter.has-border .narrow,
                                                .has-gutter.ecxhas-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .has-gutter.has-border .wide,
                                                .has-gutter.ecxhas-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }
                                            }

                                            @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                            only screen and (min--moz-device-pixel-ratio: 2),
                                            only screen and (-o-min-device-pixel-ratio: 2/1),
                                            only screen and (min-device-pixel-ratio: 2),
                                            only screen and (min-resolution: 192dpi),
                                            only screen and (min-resolution: 2dppx) {
                                                .fblike {
                                                    background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .tweet {
                                                    background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .linkedinshare {
                                                    background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .forwardtoafriend {
                                                    background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }
                                            }

                                            @media (max-width: 321px) {
                                                .fixed-width.has-border .layout__inner {
                                                    border-width: 1px 0 !important;
                                                }

                                                .layout,
                                                .column {
                                                    min-width: 320px !important;
                                                    width: 320px !important;
                                                }

                                                .border {
                                                    display: none;
                                                }
                                            }

                                            .mso div {
                                                border: 0 none white !important;
                                            }

                                            .mso .w560 .divider {
                                                Margin-left: 260px !important;
                                                Margin-right: 260px !important;
                                            }

                                            .mso .w360 .divider {
                                                Margin-left: 160px !important;
                                                Margin-right: 160px !important;
                                            }

                                            .mso .w260 .divider {
                                                Margin-left: 110px !important;
                                                Margin-right: 110px !important;
                                            }

                                            .mso .w160 .divider {
                                                Margin-left: 60px !important;
                                                Margin-right: 60px !important;
                                            }

                                            .mso .w354 .divider {
                                                Margin-left: 157px !important;
                                                Margin-right: 157px !important;
                                            }

                                            .mso .w250 .divider {
                                                Margin-left: 105px !important;
                                                Margin-right: 105px !important;
                                            }

                                            .mso .w148 .divider {
                                                Margin-left: 54px !important;
                                                Margin-right: 54px !important;
                                            }

                                            .mso .size-8,
                                            .ie .size-8 {
                                                font-size: 8px !important;
                                                line-height: 14px !important;
                                            }

                                            .mso .size-9,
                                            .ie .size-9 {
                                                font-size: 9px !important;
                                                line-height: 16px !important;
                                            }

                                            .mso .size-10,
                                            .ie .size-10 {
                                                font-size: 10px !important;
                                                line-height: 18px !important;
                                            }

                                            .mso .size-11,
                                            .ie .size-11 {
                                                font-size: 11px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-12,
                                            .ie .size-12 {
                                                font-size: 12px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-13,
                                            .ie .size-13 {
                                                font-size: 13px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-14,
                                            .ie .size-14 {
                                                font-size: 14px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-15,
                                            .ie .size-15 {
                                                font-size: 15px !important;
                                                line-height: 23px !important;
                                            }

                                            .mso .size-16,
                                            .ie .size-16 {
                                                font-size: 16px !important;
                                                line-height: 24px !important;
                                            }

                                            .mso .size-17,
                                            .ie .size-17 {
                                                font-size: 17px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-18,
                                            .ie .size-18 {
                                                font-size: 18px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-20,
                                            .ie .size-20 {
                                                font-size: 20px !important;
                                                line-height: 28px !important;
                                            }

                                            .mso .size-22,
                                            .ie .size-22 {
                                                font-size: 22px !important;
                                                line-height: 31px !important;
                                            }

                                            .mso .size-24,
                                            .ie .size-24 {
                                                font-size: 24px !important;
                                                line-height: 32px !important;
                                            }

                                            .mso .size-26,
                                            .ie .size-26 {
                                                font-size: 26px !important;
                                                line-height: 34px !important;
                                            }

                                            .mso .size-28,
                                            .ie .size-28 {
                                                font-size: 28px !important;
                                                line-height: 36px !important;
                                            }

                                            .mso .size-30,
                                            .ie .size-30 {
                                                font-size: 30px !important;
                                                line-height: 38px !important;
                                            }

                                            .mso .size-32,
                                            .ie .size-32 {
                                                font-size: 32px !important;
                                                line-height: 40px !important;
                                            }

                                            .mso .size-34,
                                            .ie .size-34 {
                                                font-size: 34px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-36,
                                            .ie .size-36 {
                                                font-size: 36px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-40,
                                            .ie .size-40 {
                                                font-size: 40px !important;
                                                line-height: 47px !important;
                                            }

                                            .mso .size-44,
                                            .ie .size-44 {
                                                font-size: 44px !important;
                                                line-height: 50px !important;
                                            }

                                            .mso .size-48,
                                            .ie .size-48 {
                                                font-size: 48px !important;
                                                line-height: 54px !important;
                                            }

                                            .mso .size-56,
                                            .ie .size-56 {
                                                font-size: 56px !important;
                                                line-height: 60px !important;
                                            }

                                            .mso .size-64,
                                            .ie .size-64 {
                                                font-size: 64px !important;
                                                line-height: 63px !important;
                                            }
                                        </style>

                                        <!--[if !mso]><!-->
                                        <style type="text/css">
                                            @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                        </style>
                                        <style type="text/css">
                                            body {
                                                background-color: #fff
                                            }

                                            .logo a:hover,
                                            .logo a:focus {
                                                color: #1e2e3b !important
                                            }

                                            .mso .layout-has-border {
                                                border-top: 1px solid #ccc;
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .layout-has-bottom-border {
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .border,
                                            .ie .border {
                                                background-color: #ccc
                                            }

                                            .mso h1,
                                            .ie h1 {}

                                            .mso h1,
                                            .ie h1 {
                                                font-size: 26px !important;
                                                line-height: 34px !important
                                            }

                                            .mso h2,
                                            .ie h2 {}

                                            .mso h2,
                                            .ie h2 {
                                                font-size: 20px !important;
                                                line-height: 28px !important
                                            }

                                            .mso h3,
                                            .ie h3 {}

                                            .mso .layout__inner,
                                            .ie .layout__inner {}

                                            .mso .footer__share-button p {}

                                            .mso .footer__share-button p {
                                                font-family: Cabin, Avenir, sans-serif
                                            }
                                        </style>
                                        <meta name="robots" content="noindex,nofollow">
                                        </meta>
                                        <meta property="og:title" content="Mail v.01">
                                        </meta>
                                    </head>

                                    <body
                                        style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                        <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                            <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                <tr style="border-collapse:collapse;">
                                                    <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                        <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                    bgcolor="transparent">
                                                                    <div>
                                                                        <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="560" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <a target="_blank" href="https://digitalcv.id"
                                                                                                                style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                <img src="https://digitalcv.id/assets/images/logo/logoDcv2.png"
                                                                                                                    alt
                                                                                                                    style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                    height="131">
                                                                                                            </a>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="42"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center" bgcolor="transparent"
                                                                    style="padding:0;Margin:0;background-color:transparent;">
                                                                    <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="left" style="padding:0;Margin:0;">
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center"
                                                                                                        style="padding:0;Margin:0;padding-top:20px;">
                                                                                                        <p
                                                                                                            style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" style="padding:0;Margin:0;">
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Hai, ' . $namaKandidat . '
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Lamaran Anda untuk posisi <b style="color:#fbb116">' . $posisiLamar . '</b> sudah berhasil dikirimkan ke perusahaan <b style="color:#fbb116">' . $namaPerusahaan . '</b>. Setiap perusahaan memiliki serangkaian prosesnya sendiri, jadi mungkin perusahaan akan memberikan tanggapan atau tidak kepada Anda.
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Ikuti dan pantau terus pekerjaan yang telah dilamar serta cari dan temukan lebih banyak lagi peluang pekerjaan serupa.
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Recruitment Team
                                                                                                            <br>
                                                                                                            <b style="color:#fbb116">' . $namaPerusahaan . '</b>
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                    background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                    <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="left" style="padding:0;Margin:0;">
                                                                                                        <hr>
                                                                                                        <div>digitalcv<br>
                                                                                                            Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                            Barat<br>
                                                                                                            Telepon: 022-32097159<br>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="600" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" height="40"
                                                                                                        style="padding:0;Margin:0;"></td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </body>
                                    </table>
                                    <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                        src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                    </body>

                                    </html>'],
            ],
            'Subject' => ['Data' => 'Lamaran Posisi ' . $posisiLamar . ' di ' . $namaPerusahaan . '| digitalcv'],
        ],
        'Source' => 'digitalcv <<EMAIL>>',
    ];

    try {
        $result = $SesClient->sendEmail($emailParams);
        return true;
    } catch (AwsException $e) {
        return false;
    }
}

function getPaginationForListJob($page, $per_page, $slot_dataUltra, $total_dataUltra, $total_dataInternal)
{
    // ---------- Hitung Data 1 ----------
    $offset_data1 = ($page - 1) * $slot_dataUltra;
    $available_data1 = max(0, $total_dataUltra - $offset_data1);
    $take_data1 = min($slot_dataUltra, $available_data1);

    // ---------- Hitung Data 2 ----------
    // Akumulasi pemakaian data2 sebelum page sekarang
    $used_data2 = 0;
    for ($i = 1; $i < $page; $i++) {
        $off1 = ($i - 1) * $slot_dataUltra;
        $avail1 = max(0, $total_dataUltra - $off1);
        $take1  = min($slot_dataUltra, $avail1);
        $take2  = $per_page - $take1;
        $used_data2 += $take2;
    }

    $offset_data2 = $used_data2;
    $take_data2   = $per_page - $take_data1;
    $take_data2   = min($take_data2, max(0, $total_dataInternal - $offset_data2));

    return [
        'dataUltra' => [
            'offset' => $offset_data1,
            'limit'  => $take_data1
        ],
        'dataInternal' => [
            'offset' => $offset_data2,
            'limit'  => $take_data2
        ]
    ];
}

if (!function_exists('ProsesScreening')) {
    // function buat pincode
    function ProsesScreening($conn, $pin, $id_req)
    {
        $getRule = $conn->prepare("SELECT * FROM `list_request` where id_req = ?");
        $getRule->bind_param("s", $id_req);
        $getRule->execute();
        $queryRule = $getRule->get_result();
        $getRule->close();
        $rowRule = mysqli_fetch_assoc($queryRule);

        //get data from databse
        $id_koordinator           = $rowRule['id_koordinator'];
        $cekScreening             = explode("|", $rowRule['check_screening']);
        $lokasiKerjaRule          = explode(",", $rowRule['lokasi_kerja']);
        $usiaRule                 = explode("-", $rowRule['k_usia']);
        $minUsia                  = $usiaRule[0];
        $maxUsia                  = $usiaRule[1];
        $jkRule                   = $rowRule['k_jk'];
        $pengalamanTotalRule      = $rowRule['k_pengalaman'];
        $pengalamanSamaRule       = $rowRule['k_pengalaman_sama'];
        $statusRule               = $rowRule['k_status'];
        $pendidikanRule           = $rowRule['k_pendidikan'];
        $jurusanPendidikan        = explode("|", $rowRule['k_jurusan']);
        $sekolahRule              = explode("|", $rowRule['k_sekolah']);
        $simRule                  = explode(",", $rowRule['k_sim']);
        $sistem_kerja             = $rowRule['sistem_kerja'];
        $statusJurusan = '';

        $vPengalaman              = $rowRule['v_pengalaman'];
        $vMinPendidikan           = $rowRule['v_min_pendidikan'];
        $vUsiaLebih               = $rowRule['v_usia_lebih'];
        $vUsiaKurang              = $rowRule['v_usia_kurang'];
        $vJurusan                 = $rowRule['v_jurusan'];
        $vSekolah                 = $rowRule['v_sekolah'];

        $getRule2 = "SELECT * FROM `list_kriteria` WHERE id_req = '$id_req'";
        $queryRule2 = $conn->query($getRule2);
        $rowRule2 = mysqli_fetch_assoc($queryRule2);

        //get data from databasae
        $kkRule       = $rowRule2['kk'];
        $kbRule       = explode(",", $rowRule2['kb']);
        $kmRule       = $rowRule2['km'];
        $kbduRule     = $rowRule2['kbdu'];
        $rlpRule      = explode(',', $rowRule2['rlp']);

        $data = array();
        $statusPendidikan = '';
        $statusPengalaman = '';
        $isUmur = '';

        // cek apakah kandidat sudah perah screening
        $get = $conn->prepare("SELECT * FROM `hasil_screening` where id_gestalt = ? AND id_req = ?");
        $get->bind_param("ss", $pin, $id_req);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $rowHasilScreening = mysqli_fetch_array($result);
            $idScreening = $rowHasilScreening['id_screening'];
        } else {
            $sqlCek = "SELECT id_screening FROM hasil_screening";
            $queryCek = $conn->query($sqlCek);

            do {
                $idScreening = getIDScreening(10);
            } while ($idScreening == $queryCek);
        }

        // get data kandidat
        $get = $conn->prepare("SELECT
                                uk.pin as id_akun,
                                lr.id_req,
                                lr.posisi as posisi_lamar,
                                rh.nama as nama_lengkap,
                                rh.tempat_lahir,
                                rh.tgl_lahir,
                                rh.kota as kota_domisili,
                                rh.kecamatan as kec_domisili,
                                rh.pendidikan_terakhir,
                                IF(rp.jurusan IS NULL,'', GROUP_CONCAT(rp.jurusan)) as jurusan,
                                IF(rp.nama_sekolah IS NULL,'', GROUP_CONCAT(rp.nama_sekolah)) as nama_sekolah,
                                rh.status_pernikahan as status_marital,
                                rh.jenis_kelamin as gender,
                                rh.sim,
                                rh.lama_pengalaman_kerja as total_pengalaman,
                                rh.lama_posisi_kerja as total_pengalama_sama,
                                rh.minat_lokasi_kerja as penempatan,
                                rh.ilmu_komputerisasi as skill_komputer,
                                IF(pb.bahasa IS NULL,'',pb.bahasa) as skill_bahasa,
                                rh.memimpin_tim as skill_pimpin,
                                rh.kemampuan_presentasi as skill_public,
                                rh.lingkup_pekerjaan as skill_lingkup_pekerjaan,
                                rh.minat_lokasi_kerja as lokasi_kerja,
                                rh.perjalanan_dinas as kebutuhan_traveling
                            FROM
                                users_kandidat uk
                                JOIN users_lamar ul ON ul.id_gestalt = uk.pin
                                JOIN list_request lr ON ul.id_req = lr.id_req
                                JOIN rh ON rh.id = uk.pin
                                LEFT JOIN (SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')) as rp ON rp.id = uk.pin
                                LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE id = ? GROUP BY id) pb ON pb.id = uk.pin
                            WHERE
                                uk.pin = ?
                                AND ul.id_req = ?");
        $get->bind_param("ssss", $pin, $pin, $pin, $id_req);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $poinScreening = 0;
        $poinValidasi = 0;

        while ($row = mysqli_fetch_array($result)) {
            $id_lamar           = $idScreening;
            $namaLengkap        = $row['nama_lengkap'];
            $tgl_lahir          = $row['tgl_lahir'];
            $kota_domisili      = $row['kota_domisili'];
            $pendidkan_terakhir = $row['pendidikan_terakhir'];
            $jurusan            = explode(",", $row['jurusan']);
            $nama_sekolah       = explode(",", $row['nama_sekolah']);
            $status_marital     = $row['status_marital'];
            $gender             = $row['gender'];
            $sim                = explode(",", $row['sim']);
            $lokasi             = explode("|", $row['penempatan']);
            $total_pengalaman   = $row['total_pengalaman'];
            $skill_komputer     = $row['skill_komputer'];
            $skill_bahasa       = explode(",", $row['skill_bahasa']);
            $skill_pimpin       = $row['skill_pimpin'];
            $skill_public       = $row['skill_public'];
            $skill_lingkup      = explode(",", $row['skill_lingkup_pekerjaan']);
            $namaAkun           = $row['nama_lengkap'];
            $lokasi_kerja       = $row['lokasi_kerja'];
            $kebutuhan_traveling = $row['kebutuhan_traveling'];

            $data_riwayat_screening = array();

            for ($i = 0; $i < count($cekScreening); $i++) {
                $arrValidasi = $cekScreening[$i];
                $poin_validasi = 1;
                $poin_screening = 0;

                // cek screening untuk kebutuhan traveling
                if ($arrValidasi == 'Aktifitas Kerja') {
                    $poinValidasi++;

                    if ($kebutuhan_traveling == 'Ya') {
                        $poinScreening++;
                        $poin_screening = 1;
                    } elseif ($sistem_kerja == $kebutuhan_traveling) {
                        $poinScreening++;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Aktifitas Kerja', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk kebutuhan traveling

                // cek screening untuk lokasi kerja
                if ($arrValidasi == 'Lokasi Kerja') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($lokasi_kerja == 'DIMANA SAJA') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $resultLokasi = array();
                        //valdasi lokasi kerja
                        for ($k = 0; $k < count($lokasiKerjaRule); $k++) {
                            $arrLokasiKerja = $lokasiKerjaRule[$k];

                            for ($j = 0; $j < count($lokasi); $j++) {
                                $arrPenempatan = $lokasi[$j];

                                if ($arrLokasiKerja == $arrPenempatan) {
                                    $resultLokasi[] = "Yes";
                                } else {
                                    $resultLokasi[] = "No";
                                }
                            }
                        }

                        if (in_array("Yes", $resultLokasi)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Lokasi Kerja', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk lokasi kerja

                // cek screening untuk umur
                if ($arrValidasi == 'Umur Kandidat') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi Usia
                    $dateLahir = new DateTime($tgl_lahir);
                    $dateToday = new DateTime();
                    $diff = $dateToday->diff($dateLahir);
                    $umurKandidat = $diff->y;

                    if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                        $isUmur = 'true1';
                    } else if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Tidak Izinkan') {
                        if ($minUsia <= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true2';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false2';
                        }
                    } else if ($vUsiaLebih == 'Tidak Izinkan' && $vUsiaKurang == 'Izinkan') {
                        if ($maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true3';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false3';
                        }
                    } else {
                        if ($minUsia <= $umurKandidat && $maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true4';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false4';
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Umur Kandidat', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk umur

                // cek screening untuk jenis kelamin
                if ($arrValidasi == 'Jenis Kelamin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi gender
                    if ($jkRule == 'Keduanya' || $jkRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($jkRule == 'Laki-Laki') {
                        if ($gender == 'Laki-Laki') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($jkRule == 'Perempuan') {
                        if ($gender == 'Perempuan') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jenis Kelamin', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk jenis kelamin

                // cek screening untuk status pernikahan
                if ($arrValidasi == 'Status Pernikahan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi status
                    if ($statusRule == 'Tidak' || $statusRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($statusRule == 'Belum Menikah') {
                        if ($status_marital == 'Belum Menikah') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($statusRule == 'Menikah') {
                        if ($status_marital == 'Menikah' || $status_marital == 'Duda' || $status_marital == 'Janda') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Status Pernikahan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk status pernikahan

                // cek screening untuk bahasa yang dikuasai
                if ($arrValidasi == 'Bahasa yang dikuasai') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi skill bahasa
                    $resultBahasa = array();
                    for ($h = 0; $h < count($kbRule); $h++) {
                        $arrKriteriaKb = $kbRule[$h];

                        for ($l = 0; $l < count($skill_bahasa); $l++) {
                            $arrskillBahasa = $skill_bahasa[$l];

                            if ($arrKriteriaKb == $arrskillBahasa) {
                                $resultBahasa[] = "Yes";
                            } else {
                                $resultBahasa[] = "No";
                            }
                        }
                    }

                    if (in_array("Yes", $resultBahasa)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Bahasa yang dikuasai', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk bahasa yang dikuasai

                // cek screening untuk sim
                if ($arrValidasi == 'SIM') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi SIM
                    if (in_array('Tidak Ada', $simRule)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $countyes = 0;
                        for ($j = 0; $j < count($sim); $j++) {
                            $arrsim = $sim[$j];

                            if (in_array($arrsim, $simRule)) {
                                $countyes++;
                            }
                        }

                        if ($countyes == count($simRule)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $poinScreening += 0;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'SIM', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk sim

                // cek screening untuk pendidikan
                if ($arrValidasi == 'Pendidikan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi pendidikan
                    if ($vMinPendidikan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if ($pendidikanRule == 'SD') {
                            if ($pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMP') {
                            if ($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMA' || $pendidikanRule == 'SMK') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'Diploma') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S1') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S2') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S3') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1' && $pendidkan_terakhir != 'S2') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Pendidikan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk pendidikan

                // cek screening untuk jurusan pendidikan
                if ($arrValidasi == 'Jurusan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi jurusan
                    if ($vJurusan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if (in_array('Semua Jurusan', $jurusanPendidikan)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $temp_arr_jurusan = array_intersect($jurusanPendidikan, $jurusan);

                            if (!empty($temp_arr_jurusan)) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jurusan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk jurusan pendidikan

                // cek screening untuk nama sekolah
                if ($arrValidasi == 'Sekolah') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi sekolah
                    if ($vSekolah == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $temp_arr_sekolah = array_intersect($sekolahRule, $nama_sekolah);

                        if (!empty($temp_arr_sekolah)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Sekolah', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk nama sekolah

                // cek screening untuk pengalaman
                if ($arrValidasi == 'Pengalaman') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($total_pengalaman >= $pengalamanTotalRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Pengalaman', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk pengalaman

                // cek screening untuk Kemampuan Komputer
                if ($arrValidasi == 'Kemampuan Komputer') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi skill komputer
                    if ($kkRule == 6) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if ($skill_komputer < 6) {
                            if ($skill_komputer >= $kkRule) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Komputer', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Komputer

                // cek screening untuk Kemampuan Memimpin
                if ($arrValidasi == 'Kemampuan Memimpin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_pimpin >= $kmRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Memimpin', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Memimpin

                // cek screening untuk Kemampuan berbicara didepan umum
                if ($arrValidasi == 'Kemampuan berbicara didepan umum') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_public >= $kbduRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan berbicara didepan umum', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // cek screening untuk Kemampuan berbicara didepan umum

                // cek screening untuk Ruang lingkup pekerjaan
                if ($arrValidasi == 'Ruang lingkup pekerjaan' && count($rlpRule) > 0) {
                    $poinValidasi += count($rlpRule);
                    $poin_validasi = count($rlpRule);
                    $poin_screening = 0;

                    //validasi skill komputer
                    $temp_arr_rlp = array_intersect($rlpRule, $skill_lingkup);

                    if (count($temp_arr_rlp) > 0) {
                        $poinScreening += count($temp_arr_rlp);
                        $poin_screening += count($temp_arr_rlp);
                    }

                    $data_riwayat_screening[] = array('title' => 'Ruang lingkup pekerjaan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Ruang lingkup pekerjaan
            }
        }

        $presentase = round(($poinScreening / $poinValidasi) * 100);

        if ($presentase < 0) {
            $presentase = 0;
        }

        // hapus data screening sebelumnya
        $conn->query("DELETE FROM hasil_screening WHERE id_screening = '$idScreening'");
        $conn->query("DELETE FROM screening_recruitment WHERE id_screening = '$idScreening'");
        $conn->query("DELETE FROM riwayat_screening WHERE id_screening = '$idScreening'");

        $created_at = date("Y-m-d H:i:s");

        // insert hasil screening
        $insert = $conn->prepare("INSERT INTO `hasil_screening`(`id_screening`, `id_gestalt`, `id_req`, `id_koordinator`, `percentase`, `tgl`) VALUES (?, ?, ?, ?, ?, ?)");
        $insert->bind_param("ssssss", $idScreening, $pin, $id_req, $id_koordinator, $presentase, $created_at);

        if ($insert->execute()) {
            $insert->close();

            // insert screening recruitment
            $insert = $conn->prepare("INSERT INTO `screening_recruitment`(`id_screening`, `id_akun`, `id_req`, `posisi_lamar`, `nama_lengkap`, `tempat_lahir`, `tgl_lahir`, `kota_domisili`, `kel_domisili`, `kec_domisili`, `pendidikan_terakhir`, `nama_sekolah`, `jurusan`, `tgl_lulus`, `status_marital`, `gender`, `sim`, `total_pengalaman`, `total_pengalama_sama`, `penempatan`, `skill_komputer`, `skill_bahasa`, `skill_pimpin`, `skill_public`, `skill_lingkup_pekerjaan`, `tgl`, `sekolah`)
            SELECT
                ? as id_screening,
                uk.pin as id_akun,
                lr.id_req,
                lr.posisi as posisi_lamar,
                rh.nama as nama_lengkap,
                rh.tempat_lahir,
                rh.tgl_lahir,
                rh.kota as kota_domisili,
                '' as kel_domisili,
                rh.kecamatan as kec_domisili,
                rh.pendidikan_terakhir,
                IF(rp.nama_sekolah IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.nama_sekolah <> '' THEN rp.nama_sekolah END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as nama_sekolah,
                IF(rp.jurusan IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.jurusan <> '' THEN rp.jurusan END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as jurusan,
                IF(rp.tahun_selesai IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.tahun_selesai <> '' THEN rp.tahun_selesai END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as tgl_lulus,
                rh.status_pernikahan as status_marital,
                rh.jenis_kelamin as gender,
                rh.sim,
                rh.lama_pengalaman_kerja as total_pengalaman,
                rh.lama_posisi_kerja as total_pengalama_sama,
                ul.lokasi as penempatan,
                rh.ilmu_komputerisasi as skill_komputer,
                IF(pb.bahasa IS NULL,'',pb.bahasa) as skill_bahasa,
                rh.memimpin_tim as skill_pimpin,
                rh.kemampuan_presentasi as skill_public,
                rh.lingkup_pekerjaan as skill_lingkup_pekerjaan,
                ? as tgl,
                IF(rp.nama_sekolah IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.nama_sekolah <> '' THEN rp.nama_sekolah END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as sekolah
            FROM
                users_kandidat uk
                JOIN users_lamar ul ON ul.id_gestalt = uk.pin
                JOIN list_request lr ON ul.id_req = lr.id_req
                JOIN rh ON rh.id = uk.pin
                LEFT JOIN (SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')) as rp ON rp.id = uk.pin
                LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE id = ? GROUP BY id) pb ON pb.id = uk.pin
            WHERE
                uk.pin = ?
                AND ul.id_req = ?");
            $insert->bind_param("ssssss", $idScreening, $created_at, $pin, $pin, $pin, $id_req);

            if ($insert->execute()) {
                $insert->close();

                // simpan riwayat screening
                $temp_cek = 'true';
                if (count($data_riwayat_screening) > 0) {
                    for ($i = 0; $i < count($data_riwayat_screening); $i++) {
                        $title = $data_riwayat_screening[$i]['title'];
                        $arr_poin = explode("/", $data_riwayat_screening[$i]['poin']);
                        $temp_poin_screening = $arr_poin[0];
                        $temp_poin_validasi = $arr_poin[1];

                        // simpan riwayat screening
                        $insert = $conn->prepare("INSERT `riwayat_screening` (`id_screening`, `id_gestalt`, `id_req`, `title`, `poin_screening`, `poin_validasi`, `created_at`) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $insert->bind_param("sssssss", $idScreening, $pin, $id_req, $title, $temp_poin_screening, $temp_poin_validasi, $created_at);

                        if ($insert->execute()) {
                            $insert->close();
                        } else {
                            $temp_cek = 'false';
                            break;
                        }
                    }
                }

                if ($temp_cek == 'true') {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}

if ($func == "cekNotif") {
    $data = array();

    $status = "false";
    $message = "Notifikasi gagal";
    $konten = array('notification' => 0, 'titleNotif' => 'Anda tidak memiliki notfikasi', 'contentNotif' => '');

    // Get notifikasi
    $get = $conn->prepare("SELECT
                            ns.*,
                            k.label,
                            k.img
                        FROM
                            notif_summary ns
                            LEFT JOIN koordinator k ON k.id_koordinator = ns.pengirim
                        WHERE
                            ns.penerima = ? 
                            AND ns.`status` = 'Dikirim' 
                        ORDER BY
                            ns.create_at DESC");
    $get->bind_param("s", $pin);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $contentNotif = '';
        $jml_notif = 0;
        while ($row = mysqli_fetch_array($result)) {
            if ($row['img'] == "") {
                $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" alt="Img Profile" />';
            } else {
                if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'perusahaan/logo/' . $row['img']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                    $logoURL = (string) $request->getUri();

                    $logo_perusahaan = '<img src="' . $logoURL . '" alt="Img Profile" />';
                } else {
                    $logo_perusahaan = '<img src="' . $row['img'] . '" referrerpolicy="no-referrer" alt="Img Profile" />';
                }
            }

            $penerima = $row['penerima'];
            $pengirim = $row['pengirim'];
            $tglKirim = $row['create_at'];

            $waktuawal  = date_create($row['create_at']); //waktu di setting
            $waktuakhir = date_create(); //2019-02-21 09:35 waktu sekarang
            $diff  = date_diff($waktuawal, $waktuakhir);

            if ($diff->d != '0') {
                if ($diff->d > 1) {
                    $jam = $diff->d . translate(' hari yang lalu');
                } else {
                    $jam = $diff->d . ' ' . translate('hari yang lalu');
                }
            } elseif ($diff->h != '0') {
                if ($diff->h > 1) {
                    $jam = $diff->h . translate(' jam yang lalu');
                } else {
                    $jam = $diff->h . ' ' . translate('jam yang lalu');
                }
            } elseif ($diff->i != '0') {
                if ($diff->i > 1) {
                    $jam = $diff->i . translate(' menit yang lalu');
                } else {
                    $jam = $diff->i . ' ' . translate('menit yang lalu');
                }
            } else {
                if ($diff->s > 1) {
                    $jam = $diff->s . translate(' detik yang lalu');
                } else {
                    $jam = $diff->s . ' ' . translate('detik yang lalu');
                }
            }

            $link = "javascript:void(0)";
            if ($row['judul'] == 'Pengumuman Hasil Seleksi') {
                $link = $baseURL . "candidate/dashboard/riwayat/timeline?q=" . base64_encode($row['id_referensi']);
            }

            $contentNotif .= '<a href="' . $link . '" onclick="updateNotif(\'' . $penerima . '\',\'' . $pengirim . '\',\'' . $tglKirim . '\')">
                                <div class="notif-img" style="width: 80px; height: 50px;">
                                    ' . $logo_perusahaan . '
                                </div>
                                <div class="notif-content" style="width: 400px;">
                                    <b>' . htmlspecialchars($row['label']) . '</b>
                                    <span class="block">
                                        ' . htmlspecialchars($row['isi']) . '
                                    </span>
                                    <span class="time">' . $jam . '</span>
                                </div>
                            </a>';

            $jml_notif++;
        }

        $status = "true";
        $message = "Notifikasi berhasil";
        if ($jml_notif > 0) {
            if ($jml_notif > 1) {
                $titleNotif = translate('Anda memiliki') . ' ' . $jml_notif . translate(' notfikasi baru');
            } else {
                $titleNotif = translate('Anda memiliki') . ' ' . $jml_notif . ' ' . translate('notfikasi baru');
            }

            $konten = array('notification' => $jml_notif, 'titleNotif' => $titleNotif, 'contentNotif' => $contentNotif);
        }
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['konten'] = $konten;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateNotif') {
    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    if (isset($_POST['penerima']) && isset($_POST['pengirim']) && isset($_POST['tgl'])) {
        $penerima = $_POST['penerima'];
        $pengirim = $_POST['pengirim'];
        $tgl = $_POST['tgl'];

        $update = $conn->prepare("UPDATE notif_summary SET `status` = 'Dibaca' WHERE penerima = ? AND pengirim = ? AND create_at = ?");
        $update->bind_param("sss", $pin, $pengirim, $tgl);
        if ($update->execute()) {
            $update->close();

            $status = 'true';
            $message = 'Update notifikasi berhasil';
        }
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateAllNotif') {
    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    $update = $conn->prepare("UPDATE notif_summary SET `status` = 'Dibaca' WHERE penerima = ? AND (`status` = 'Diterima' OR `status` = 'Dikirim')");
    $update->bind_param("s", $pin);
    if ($update->execute()) {
        $update->close();

        $status = 'true';
        $message = 'Update notifikasi berhasil';
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'paginationListJob') {
    // Total data per halaman
    $total_limit_per_page = 9;
    $ultra_limit_per_page = 3;

    $page = isset($_POST['page']) ? (int) $_POST['page'] : 1;
    $start_of_page = ($page - 1) * $total_limit_per_page;

    // Filter parameters
    $lokasi = "";
    $api_filter_lokasi = "";
    $temp_lokasi = $_POST['lokasi'];
    if (isset($temp_lokasi) && $temp_lokasi != "") {
        $lokasi = "AND lr.lokasi_kerja LIKE '%" . addslashes($temp_lokasi) . "%'";
        $api_filter_lokasi = $temp_lokasi;
    }

    $pencarian = "";
    $api_filter_pencarian = "";
    if (isset($_POST['pencarian']) && $_POST['pencarian'] != "") {
        $pencarian = "AND (lr.perusahaan LIKE '%" . addslashes($_POST['pencarian']) . "%' OR lr.posisi LIKE '%" . addslashes($_POST['pencarian']) . "%')";
        $api_filter_pencarian = addslashes($_POST['pencarian']);
    }

    $jenis_pekerjaan = "";
    $api_filter_jenis_pekerjaan = "Full-Time";
    if (isset($_POST['jenis_pekerjaan']) && count($_POST['jenis_pekerjaan']) > 0) {
        $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['jenis_pekerjaan']);
        $jenis_pekerjaan = "AND lr.tipe_pekerjaan IN (" . implode(",", $quotedArray) . ")";
        if (!in_array("'Full-Time'", $quotedArray)) {
            $api_filter_jenis_pekerjaan = "";
        }
    }

    $spesialisasi = "";
    if (isset($_POST['spesialisasi']) && count($_POST['spesialisasi']) > 0) {
        $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['spesialisasi']);
        $spesialisasi = "AND k.tipe IN (" . implode(",", $quotedArray) . ")";
    }

    $pendidikan = "";
    $api_filter_pendidikan = '';
    if (isset($_POST['pendidikan']) && count($_POST['pendidikan']) > 0) {
        $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['pendidikan']);
        $pendidikan = "AND lr.k_pendidikan IN (" . implode(",", $quotedArray) . ")";
        $api_filter_pendidikan = str_replace("'", "", end($quotedArray));
    }

    $waktu_posting = "";
    if (isset($_POST['waktu_posting']) && count($_POST['waktu_posting']) > 0) {
        $temp = $_POST['waktu_posting'][0];
        $temp_tgl_awal = "";
        $temp_tgl_akhir = "";
        if ($temp == "Hari Ini") {
            $temp_tgl_awal = date("Y-m-d") . " 00:00:00";
            $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
        } elseif ($temp == "3 Hari Ini Terakhir") {
            $temp_tgl = new DateTime();
            $temp_tgl->modify("-3 days");
            $temp_tgl->format("Y-m-d");
            $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
            $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
        } elseif ($temp == "7 Hari Ini Terakhir") {
            $temp_tgl = new DateTime();
            $temp_tgl->modify("-7 days");
            $temp_tgl->format("Y-m-d");
            $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
            $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
        } elseif ($temp == "14 Hari Ini Terakhir") {
            $temp_tgl = new DateTime();
            $temp_tgl->modify("-14 days");
            $temp_tgl->format("Y-m-d");
            $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
            $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
        } elseif ($temp == "30 Hari Ini Terakhir") {
            $temp_tgl = new DateTime();
            $temp_tgl->modify("-30 days");
            $temp_tgl->format("Y-m-d");
            $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
            $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
        }
        $waktu_posting = "AND lr.create_at BETWEEN '$temp_tgl_awal' AND '$temp_tgl_akhir'";
        if ($temp == "Kapan Saja") {
            $waktu_posting = "";
        }
    }

    // Step 1: Get total count from internal database
    $total_query_internal = "SELECT COUNT(lr.id) AS total FROM list_request lr JOIN koordinator k ON lr.id_koordinator = k.id_koordinator JOIN JSON_TABLE(
                                CONCAT('[\"', REPLACE(lokasi_kerja, ',', '\",\"'), '\"]'),
                                '$[*]' COLUMNS (lokasi_kerja VARCHAR(100) PATH '$')
                            ) AS jt WHERE lr.`status` = 'On Proccess' $lokasi $pencarian $jenis_pekerjaan $spesialisasi $pendidikan $waktu_posting";
    $total_result_internal = $conn->query($total_query_internal);
    $total_row_internal = $total_result_internal->fetch_assoc();
    $total_data_internal = $total_row_internal['total'];

    // Step 2: Get TOTAL count for Ultrajaya jobs with a separate API call
    $total_dataLokerUltra = 0;
    if ($api_filter_jenis_pekerjaan == 'Full-Time') {
        $token = "FAJB87548LKGDL";
        $ultra_total_url = "https://ultrajaya.digitalcv.id/recruitment/api/recruitment.php?nama_perusahaan=" . urlencode($api_filter_pencarian) . "&posisi=" . urlencode($api_filter_pencarian) . "&pendidikan=" . urlencode($api_filter_pendidikan) . "&tanggal_posting&lokasi=" . urlencode($api_filter_lokasi) . "&limit=1&offset=0";
        $ch_total = curl_init($ultra_total_url);
        curl_setopt($ch_total, CURLOPT_HTTPHEADER, ["Authorization: Bearer $token", "Content-Type: application/json"]);
        curl_setopt($ch_total, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch_total, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch_total, CURLOPT_SSL_VERIFYHOST, false);
        if (!curl_errno($ch_total)) {
            $response_total = curl_exec($ch_total);
            $dataArray_total = json_decode($response_total, true);
            if (isset($dataArray_total['total'])) {
                $total_dataLokerUltra = $dataArray_total['total'];
            }
        }
        curl_close($ch_total);
    }

    // Step 3: Calculate total pages based on combined counts
    $total_data_all = $total_data_internal + $total_dataLokerUltra;
    $total_pages = ceil($total_data_all / $total_limit_per_page);

    $arrPagination = getPaginationForListJob($page, $total_limit_per_page, $ultra_limit_per_page, $total_dataLokerUltra, $total_data_internal);

    // Step 4: Fetch paginated data for the current page
    $dataLokerUltra = array();
    $dataLokerInternal = array();

    // Calculate start and limit for Ultrajaya based on the page number
    $ultra_start = $arrPagination['dataUltra']['offset'];
    $ultra_limit = $arrPagination['dataUltra']['limit'];

    // Make API call for paginated data
    if ($ultra_start < $total_dataLokerUltra) {
        $token = "FAJB87548LKGDL";
        $ultra_data_url = "https://ultrajaya.digitalcv.id/recruitment/api/recruitment.php?nama_perusahaan=" . urlencode($api_filter_pencarian) . "&posisi=" . urlencode($api_filter_pencarian) . "&pendidikan=" . urlencode($api_filter_pendidikan) . "&tanggal_posting&lokasi=" . urlencode($api_filter_lokasi) . "&limit=" . urlencode($ultra_limit) . "&offset=" . urlencode($ultra_start);
        $ch_data = curl_init($ultra_data_url);
        curl_setopt($ch_data, CURLOPT_HTTPHEADER, ["Authorization: Bearer $token", "Content-Type: application/json"]);
        curl_setopt($ch_data, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch_data, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch_data, CURLOPT_SSL_VERIFYHOST, false);
        if (!curl_errno($ch_data)) {
            $response_data = curl_exec($ch_data);
            $dataArray_data = json_decode($response_data, true);
            if (isset($dataArray_data['data'])) {
                // The fix: explicitly limit the number of jobs from Ultrajaya
                $dataLokerUltra = array_slice($dataArray_data['data'], 0, $ultra_limit_per_page);
            }
        }
        curl_close($ch_data);
    }

    // Calculate how many internal jobs to display
    $internal_jobs_per_page = $arrPagination['dataInternal']['limit'];

    // Calculate the correct offset for the internal database query
    $internal_start = $arrPagination['dataInternal']['offset'];

    if ($internal_jobs_per_page > 0) {
        $sql = "SELECT lr.id,
                lr.id_req,
                lr.perusahaan,
                lr.department,
                lr.posisi,
                lr.atasan,
                lr.bawahan,
                lr.jmlh_kandidat,
                lr.salary,
                TRIM(jt.lokasi_kerja) AS lokasi_kerja,
                lr.sistem_kerja,
                lr.lama_kerja,
                lr.k_pendidikan,
                lr.k_usia,
                lr.k_sim,
                lr.k_jk,
                lr.k_pengalaman,
                lr.k_pengalaman_sama,
                lr.k_status,
                lr.k_khusus,
                lr.note,
                lr.status,
                lr.create_at,
                lr.update_at,
                lr.ket_update,
                lr.id_koordinator,
                lr.k_jurusan,
                lr.v_pengalaman,
                lr.v_pendidikan,
                lr.v_jurusan,
                lr.v_sekolah,
                lr.v_usia_lebih,
                lr.v_usia_kurang,
                lr.v_min_pendidikan,
                lr.expired_date,
                lr.pembuat,
                lr.divisi,
                lr.secret_id,
                lr.check_screening,
                lr.persetujuan,
                lr.alasan_persetujuan,
                lr.check_lowongan,
                lr.k_sekolah,
                lr.tipe_pekerjaan, k.img FROM list_request lr JOIN koordinator k ON lr.id_koordinator = k.id_koordinator JOIN JSON_TABLE(
                    CONCAT('[\"', REPLACE(lokasi_kerja, ',', '\",\"'), '\"]'),
                    '$[*]' COLUMNS (lokasi_kerja VARCHAR(100) PATH '$')
                ) AS jt WHERE lr.`status` = 'On Proccess' $lokasi $pencarian $jenis_pekerjaan $spesialisasi $pendidikan $waktu_posting ORDER BY lr.create_at DESC LIMIT $internal_start, $internal_jobs_per_page";
        $result = $conn->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $dataLokerInternal[] = $row;
            }
        }
    }

    // Step 5: Combine data and display
    $combined_data = array_merge($dataLokerUltra, $dataLokerInternal);

    function convertDateUltra($raw)
    {
        $raw = str_replace(",", "", trim($raw));
        $hasAM = stripos($raw, "AM") !== false;
        $hasPM = stripos($raw, "PM") !== false;
        if (preg_match('/\s([0-9]{1,2}):/', $raw, $m)) {
            $hour = (int)$m[1];
            if ($hasPM && $hour >= 13) {
                $raw = str_ireplace("PM", "", $raw);
            }
            if ($hasAM && $hour >= 12) {
                $raw = str_ireplace("AM", "", $raw);
            }
        }
        $timestamp = strtotime(trim($raw));
        if ($timestamp === false) {
            return null;
        }
        return date('Y-m-d H:i:s', $timestamp);
    }

    $output = '';
    if (count($combined_data) > 0) {
        $output .= '<p class="mb-3">' . translate('Pekerjaan yang disarankan untuk Anda') . '</p>
                    <div class="row">';

        foreach ($combined_data as $row) {
            if (isset($row['link'])) {
                $posisi = $row['posisi'];
                $nama_perusahaan = mb_strimwidth($row['perusahaan'], 0, 37, "...");
                $tgl_buat = convertDateUltra($row['create_at']);
                $waktu_lalu = waktuLalu($tgl_buat);
                $tgl_posting = formatTanggalIndonesia($tgl_buat);
                $tipe_pekerjaan = "Full-Time";
                $img = $row['logo'];
                $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
                $link = $row['link'];
                if ($img == "") {
                    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                } else {
                    $logo_perusahaan = '<img src="' . $img . '" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                }
                $lokasi_kerja = '-';
                if (count($arr_lokasi_kerja) > 0) {
                    $lokasi_kerja = str_replace("Kota ", "", str_replace("Kabupaten ", "Kab. ", $arr_lokasi_kerja[0]));
                    if (strpos($lokasi_kerja, 'REMOTE') !== false) {
                        $lokasi_kerja = 'Remote';
                    }
                }
                $output .= '<div class="col-md-4"><div class="card"><div class="card-body"><table style="width: 100%;"><tr><td>' . $logo_perusahaan . '</td><td style="text-align: right;">';
                $shareLink = $link;
                $shareTwitter = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($posisi) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                $shareWA = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($posisi) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                $id_salin_link = rand(100, 999);
                $output .= '<div class="dropdown"><i class="bi bi-share" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 20px; float: right; cursor: pointer; padding-left: 10px;"></i><div class="dropdown-menu dropdown-for-share"><table style="width: 100%"><tr><td style="background-color: white;padding: 5px;"><a href="https://x.com/intent/tweet?text=' . urlencode($shareTwitter) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/twitter-x.svg" alt="twitter-x" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($shareLink) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/facebook.svg" alt="facebook" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $shareLink . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/linkedin.svg" alt="linkedin" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://wa.me/?text=' . urlencode($shareWA) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/whatsapp.svg" alt="whatsapp" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="javascript:void(0)" id="btn-salin-' . $id_salin_link . '" onclick="salinLink(event, \'' . $shareLink . '\',\'' . $id_salin_link . '\')" tabindex="0" data-toggle="tooltip" data-bs-original-title="' . translate('Salin Link') . '"><i class="fas fa-link" style="font-size: 16px; color: black; vertical-align: middle;"></i></a></td></tr></table></div></div>';
                $output .= '</td></tr><tr style="height: 10px;"><td></td><td></td></tr><tr><td style="color: #023047; font-size: 18px;"><a href="' . $link . '" style="color:black" target="_blank"><b>' . htmlspecialchars($posisi) . '</b></a></td><td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $waktu_lalu . '</td></tr><tr><td style="color: #023047; font-size: 13px;"><a href="' . $link . '" target="_blank">' . htmlspecialchars($nama_perusahaan) . '</a></td><td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $tgl_posting . '</td></tr></table><table class="mt-4" style="width: 100%;"><tr><td><i class="bi bi-clock"></i> ' . htmlspecialchars($tipe_pekerjaan) . '</td><td><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi_kerja))) . '</td><td style="text-align: right;"><a href="' . $link . '" target="_blank" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important;">' . translate('Lamar') . '</a></td></tr></table></div></div></div>';
            } else {
                $id_req = $row['id_req'];
                $nama_perusahaan = mb_strimwidth($row['perusahaan'], 0, 37, "...");
                $waktu_lalu = waktuLalu($row['create_at']);
                $tgl_posting = formatTanggalIndonesia($row['create_at']);
                $tipe_pekerjaan = translate($row['tipe_pekerjaan']);
                $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
                $id_koordinator = $row['id_koordinator'];
                if ($row['img'] == "") {
                    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                } else {
                    if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                        $cmd = $s3->getCommand('GetObject', ['Bucket' => $bucket, 'Key' => 'perusahaan/logo/' . $row['img']]);
                        $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                        $logoURL = (string) $request->getUri();
                        $logo_perusahaan = '<img src="' . $logoURL . '" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                    } else {
                        $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                    }
                }
                $lokasi_kerja = '-';
                if (count($arr_lokasi_kerja) > 0) {
                    $lokasi_kerja = str_replace("Kota ", "", str_replace("Kabupaten ", "Kab. ", $arr_lokasi_kerja[0]));
                    if (strpos($lokasi_kerja, 'REMOTE') !== false) {
                        $lokasi_kerja = 'Remote';
                    }
                }
                $q = base64_encode($id_req . "|" . $arr_lokasi_kerja[0]);
                $id_fav = rand(0, 999);
                $output .= '<div class="col-md-4"><div class="card"><div class="card-body"><table style="width: 100%;"><tr><td>' . $logo_perusahaan . '</td><td style="text-align: right;">';
                $cekFav = $conn->prepare("SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?");
                $cekFav->bind_param("sss", $pin, $id_req, $arr_lokasi_kerja[0]);
                $cekFav->execute();
                $resultFav = $cekFav->get_result();
                $cekFav->close();
                $shareLink = $baseURL . 'candidate/dashboard/beranda/detailJob?q=' . $q;
                $shareTwitter = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                $shareWA = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                $output .= '<div class="dropdown">';
                if ($resultFav->num_rows > 0) {
                    $output .= '<input class="form-check-input" type="checkbox" style="display: none;" value="' . $q . '" id="check-fav-' . $id_fav . '" checked><i class="bi bi-heart-fill" style="font-size: 20px; cursor: pointer;" onclick="simpanFavLowongan(' . $id_fav . ')" id="btn-fav-' . $id_fav . '"></i>';
                } else {
                    $output .= '<input class="form-check-input" type="checkbox" style="display: none;" value="' . $q . '" id="check-fav-' . $id_fav . '"><i class="bi bi-heart" style="font-size: 20px; cursor: pointer;" onclick="simpanFavLowongan(' . $id_fav . ')" id="btn-fav-' . $id_fav . '"></i>';
                }
                $id_salin_link = rand(100, 999);
                $output .= '<i class="bi bi-share" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 20px; float: right; cursor: pointer; padding-left: 10px;"></i><div class="dropdown-menu dropdown-for-share"><table style="width: 100%"><tr><td style="background-color: white;padding: 5px;"><a href="https://x.com/intent/tweet?text=' . urlencode($shareTwitter) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/twitter-x.svg" alt="twitter-x" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($shareLink) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/facebook.svg" alt="facebook" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $shareLink . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/linkedin.svg" alt="linkedin" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="https://wa.me/?text=' . urlencode($shareWA) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/whatsapp.svg" alt="whatsapp" width="16" height="16"></a></td><td style="background-color: white;padding: 5px;"><a href="javascript:void(0)" id="btn-salin-' . $id_salin_link . '" onclick="salinLink(event, \'' . $shareLink . '\',\'' . $id_salin_link . '\')" tabindex="0" data-toggle="tooltip" data-bs-original-title="' . translate('Salin Link') . '"><i class="fas fa-link" style="font-size: 16px; color: black; vertical-align: middle;"></i></a></td></tr></table></div></div>';
                $output .= '</td></tr><tr style="height: 10px;"><td></td><td></td></tr><tr><td style="color: #023047; font-size: 18px;"><a href="detailJob?q=' . $q . '" style="color:black"><b>' . htmlspecialchars($row['posisi']) . '</b></a></td><td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $waktu_lalu . '</td></tr><tr><td style="color: #023047; font-size: 13px;"><a href="detailPerusahaan?q=' . base64_encode($id_koordinator) . '">' . htmlspecialchars($nama_perusahaan) . '</a></td><td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $tgl_posting . '</td></tr></table><table class="mt-4" style="width: 100%;"><tr><td><i class="bi bi-clock"></i> ' . htmlspecialchars($tipe_pekerjaan) . '</td><td><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi_kerja))) . '</td><td style="text-align: right;"><a href="detailJob?q=' . $q . '" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important;">' . translate('Lamar') . '</a></td></tr></table></div></div></div>';
            }
        }
        $output .= "</div>";
        $max_links = 5;
        $start_page = max(1, $page - floor($max_links / 2));
        $end_page = min($total_pages, $start_page + $max_links - 1);
        $start_page = max(1, $end_page - $max_links + 1);
        $output .= '<div class="row"><div class="col-md-12"><nav aria-label="Page navigation"><ul class="pagination d-flex justify-content-center">';
        if ($page > 1) {
            $prev = $page - 1;
            $output .= "<li class='page-item'><button class='page-link' aria-label='Previous' data-page='{$prev}'><span aria-hidden='true'>&laquo;</span></button></li>";
        }
        for ($i = $start_page; $i <= $end_page; $i++) {
            $active = ($i == $page) ? 'active' : '';
            $output .= "<li class='page-item {$active}'><button class='page-link' data-page='{$i}'>{$i}</button></li>";
        }
        if ($page < $total_pages) {
            $next = $page + 1;
            $output .= "<li class='page-item'><button class='page-link' aria-label='Next' data-page='{$next}'><span aria-hidden='true'>&raquo;</span></button></li>";
        }
        $output .= '</ul></nav></div></div>';
    } else {
        $output = '<div class="row"><div class="col-md-12"><div class="alert alert-info" role="alert"><i class="fas fa-info-circle"></i> ' . translate('Tidak ada lowongan pekerjaan') . '.</div></div></div>';
    }

    $data = array();
    $data['html'] = $output;
    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'getLokasi') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 3 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    // Query untuk mencari lokasi berdasarkan pencarian
    $sql = "SELECT id, `name` FROM regencies WHERE `name` LIKE ? LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    if (isset($_GET['ketForm'])) {
        if ($_GET['ketForm'] == 'formInformasiPekerjaan') {
            $lokasi[] = ['id' => 'DIMANA SAJA', 'text' => 'DIMANA SAJA'];
        }
    }

    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ['id' => htmlspecialchars($row['name']), 'text' => htmlspecialchars($row['name'])];
    }

    // Cek apakah masih ada data selanjutnya untuk paginasi
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM regencies WHERE `name` LIKE ?");
    $stmt->bind_param("s", $searchTerm);
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = $countResult['total'];
    $more = ($offset + $perPage) < $totalRows;

    echo json_encode(["results" => $lokasi, "more" => $more]);
}

if ($func == 'getProvinsi') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 3 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    // Query untuk mencari lokasi berdasarkan pencarian
    $sql = "SELECT id, `name` FROM provinces WHERE `name` LIKE ? LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ['id' => htmlspecialchars($row['id']), 'text' => htmlspecialchars($row['name'])];
    }

    // Cek apakah masih ada data selanjutnya untuk paginasi
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM provinces WHERE `name` LIKE ?");
    $stmt->bind_param("s", $searchTerm);
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = $countResult['total'];
    $more = ($offset + $perPage) < $totalRows;

    echo json_encode(["results" => $lokasi, "more" => $more]);
}

if ($func == 'getKota') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 3 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    if (isset($_GET['province_id'])) {
        $province_id = $_GET['province_id'];

        // Query untuk mencari lokasi berdasarkan pencarian
        $sql = "SELECT id, `name` FROM regencies WHERE `name` LIKE ? AND province_id = ? LIMIT ?, ?";
        $stmt = $conn->prepare($sql);
        $searchTerm = "%$search%";
        $stmt->bind_param("ssii", $searchTerm, $province_id, $offset, $perPage);
        $stmt->execute();
        $result = $stmt->get_result();

        $lokasi = [];
        while ($row = $result->fetch_assoc()) {
            $lokasi[] = ['id' => htmlspecialchars($row['id']), 'text' => htmlspecialchars($row['name'])];
        }

        // Cek apakah masih ada data selanjutnya untuk paginasi
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM regencies WHERE `name` LIKE ?");
        $stmt->bind_param("s", $searchTerm);
        $stmt->execute();
        $countResult = $stmt->get_result()->fetch_assoc();
        $totalRows = $countResult['total'];
        $more = ($offset + $perPage) < $totalRows;
    } else {
        // Query untuk mencari lokasi berdasarkan pencarian
        $sql = "SELECT id, `name` FROM regencies WHERE `name` LIKE ? LIMIT ?, ?";
        $stmt = $conn->prepare($sql);
        $searchTerm = "%$search%";
        $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
        $stmt->execute();
        $result = $stmt->get_result();

        $lokasi = [];
        while ($row = $result->fetch_assoc()) {
            $lokasi[] = ['id' => htmlspecialchars($row['id']), 'text' => htmlspecialchars($row['name'])];
        }

        // Cek apakah masih ada data selanjutnya untuk paginasi
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM regencies WHERE `name` LIKE ?");
        $stmt->bind_param("s", $searchTerm);
        $stmt->execute();
        $countResult = $stmt->get_result()->fetch_assoc();
        $totalRows = $countResult['total'];
        $more = ($offset + $perPage) < $totalRows;
    }

    echo json_encode(["results" => $lokasi, "more" => $more]);
}

if ($func == 'getKecamatan') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 3 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    if (isset($_GET['regency_id'])) {
        $regency_id = $_GET['regency_id'];

        // Query untuk mencari lokasi berdasarkan pencarian
        $sql = "SELECT id, `name` FROM districts WHERE `name` LIKE ? AND regency_id = ? LIMIT ?, ?";
        $stmt = $conn->prepare($sql);
        $searchTerm = "%$search%";
        $stmt->bind_param("ssii", $searchTerm, $regency_id, $offset, $perPage);
        $stmt->execute();
        $result = $stmt->get_result();

        $lokasi = [];
        while ($row = $result->fetch_assoc()) {
            $lokasi[] = ['id' => htmlspecialchars($row['id']), 'text' => htmlspecialchars($row['name'])];
        }

        // Cek apakah masih ada data selanjutnya untuk paginasi
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM districts WHERE `name` LIKE ?");
        $stmt->bind_param("s", $searchTerm);
        $stmt->execute();
        $countResult = $stmt->get_result()->fetch_assoc();
        $totalRows = $countResult['total'];
        $more = ($offset + $perPage) < $totalRows;
    } else {
        // Query untuk mencari lokasi berdasarkan pencarian
        $sql = "SELECT id, `name` FROM districts WHERE `name` LIKE ? LIMIT ?, ?";
        $stmt = $conn->prepare($sql);
        $searchTerm = "%$search%";
        $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
        $stmt->execute();
        $result = $stmt->get_result();

        $lokasi = [];
        while ($row = $result->fetch_assoc()) {
            $lokasi[] = ['id' => htmlspecialchars($row['id']), 'text' => htmlspecialchars($row['name'])];
        }

        // Cek apakah masih ada data selanjutnya untuk paginasi
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM districts WHERE `name` LIKE ?");
        $stmt->bind_param("s", $searchTerm);
        $stmt->execute();
        $countResult = $stmt->get_result()->fetch_assoc();
        $totalRows = $countResult['total'];
        $more = ($offset + $perPage) < $totalRows;
    }

    echo json_encode(["results" => $lokasi, "more" => $more]);
}

if ($func == 'getBahasa') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 3 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    // Query untuk mencari bahasa berdasarkan pencarian
    $sql = "SELECT `bahasa` FROM bahasa WHERE `bahasa` LIKE ? LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ['id' => htmlspecialchars($row['bahasa']), 'text' => htmlspecialchars($row['bahasa'])];
    }

    // Cek apakah masih ada data selanjutnya untuk paginasi
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM bahasa WHERE `bahasa` LIKE ?");
    $stmt->bind_param("s", $searchTerm);
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = $countResult['total'];
    $more = ($offset + $perPage) < $totalRows;

    echo json_encode(["results" => $lokasi, "more" => $more]);
}

if ($func == 'getSekolah') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Jika input kurang dari 4 huruf, kembalikan array kosong
    if (strlen($search) < 4) {
        echo json_encode(["results" => [], "more" => false]);
    }

    // Query untuk mencari sekolah berdasarkan jenjang dan pencarian
    $sql = "SELECT id, nama FROM list_sekolah WHERE nama LIKE ? LIMIT ?, ?";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    $stmt->execute();
    $result = $stmt->get_result();

    $sekolah = [];
    while ($row = $result->fetch_assoc()) {
        $sekolah[] = ['id' => htmlspecialchars($row['nama']), 'text' => htmlspecialchars($row['nama'])];
    }

    // Cek apakah masih ada data selanjutnya untuk paginasi
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM list_sekolah WHERE nama LIKE ?");
    $stmt->bind_param("s", $searchTerm);
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = $countResult['total'];
    $more = ($offset + $perPage) < $totalRows;

    echo json_encode(["results" => $sekolah, "more" => $more]);
}

if ($func == 'getJurusan') {
    $pendidikan = isset($_GET['pendidikan']) ? $_GET['pendidikan'] : '';
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Pastikan page adalah integer

    $perPage = 20; // Jumlah data per halaman
    $offset = ($page - 1) * $perPage;

    // Menyesuaikan kategori sekolah
    $sekolah = in_array($pendidikan, ['D1', 'D2', 'D3', 'D4']) ? 'Diploma' : $pendidikan;

    // Jika input kurang dari 4 huruf, kembalikan array kosong
    if (strlen($search) < 3) {
        echo json_encode(["results" => [], "more" => false]);
    }

    if ($sekolah == 'SMA') {
        // Query untuk mencari jurusan berdasarkan sekolah & pencarian
        $sql = "SELECT id, jurusan FROM jurusan_sekolah WHERE (sekolah = ? OR sekolah = 'SMK') AND jurusan LIKE ? LIMIT ?, ?";
    } else {
        // Query untuk mencari jurusan berdasarkan sekolah & pencarian
        $sql = "SELECT id, jurusan FROM jurusan_sekolah WHERE sekolah = ? AND jurusan LIKE ? LIMIT ?, ?";
    }

    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("ssii", $sekolah, $searchTerm, $offset, $perPage);
    $stmt->execute();
    $result = $stmt->get_result();

    // echo "SELECT id, jurusan FROM jurusan_sekolah WHERE sekolah = '$sekolah' AND jurusan LIKE '%$search%' LIMIT '$offset', '$perPage'";

    $jurusanList = [];
    while ($row = $result->fetch_assoc()) {
        $jurusanList[] = ['id' => htmlspecialchars($row['jurusan']), 'text' => htmlspecialchars($row['jurusan'])];
    }

    // Cek apakah masih ada data selanjutnya untuk paginasi
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM jurusan_sekolah WHERE sekolah = ? AND jurusan LIKE ?");
    $stmt->bind_param("ss", $sekolah, $searchTerm);
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = $countResult['total'];
    $more = ($offset + $perPage) < $totalRows;

    echo json_encode(["results" => $jurusanList, "more" => $more]);
}

if ($func == 'paginationListJobCompany') {
    $limit = 4; // Jumlah data per halaman
    $page = isset($_POST['page']) ? (int) $_POST['page'] : 1;
    $start = ($page - 1) * $limit;
    $q = $_POST['q'];

    // Ambil data dari database
    $sql = $conn->prepare("SELECT
                                lr.*,
                                k.img
                            FROM
                                list_request lr
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                            WHERE
                                lr.`status` = 'On Proccess' 
                                AND lr.id_koordinator = ?
                            ORDER BY
                                lr.create_at DESC
                            LIMIT ? , ?");
    $sql->bind_param("sii", $q, $start, $limit);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    // Hitung total data
    $total_query = $conn->prepare("SELECT COUNT(id) AS total FROM list_request WHERE `status` = 'On Proccess' AND id_koordinator = ?");
    $total_query->bind_param("s", $q);
    $total_query->execute();
    $total_result = $total_query->get_result();
    $total_query->close();

    $total_row = $total_result->fetch_assoc();
    $total_data = $total_row['total'];
    $total_pages = ceil($total_data / $limit);

    if ($result->num_rows > 0) {
        $output = '';

        while ($row = $result->fetch_assoc()) {
            $id_req = $row['id_req'];
            $nama_perusahaan = $row['perusahaan'];
            $waktu_lalu = waktuLalu($row['create_at']);
            $tgl_posting = formatTanggalIndonesia($row['create_at']);
            $tipe_pekerjaan = translate($row['tipe_pekerjaan']);
            $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
            $id_koordinator = $row['id_koordinator'];


            if ($row['img'] == "") {
                $logo_perusahaan_desktop = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="100" alt="logo-perusahaan">';
                $logo_perusahaan_smartphone = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
            } else {
                if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'perusahaan/logo/' . $row['img']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                    $logoURL = (string) $request->getUri();

                    $logo_perusahaan_desktop = '<img src="' . $logoURL . '" class="profile-pic-2" width="100" alt="logo-perusahaan">';
                    $logo_perusahaan_smartphone = '<img src="' . $logoURL . '" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                } else {
                    $logo_perusahaan_desktop = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="100" alt="logo-perusahaan">';
                    $logo_perusahaan_smartphone = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                }
            }

            if (count($arr_lokasi_kerja) > 0) {
                for ($i = 0; $i < count($arr_lokasi_kerja); $i++) {
                    if (isset($arr_lokasi_kerja[$i])) {
                        $lokasi_kerja = str_replace("Kota ", "", str_replace("Kabupaten ", "Kab. ", $arr_lokasi_kerja[$i]));
                    } else {
                        $lokasi_kerja = "-";
                    }

                    if (strpos($lokasi_kerja, 'REMOTE') !== false) {
                        $lokasi_kerja = 'Remote';
                    }

                    $temp_kode = base64_encode($id_req . "|" . $arr_lokasi_kerja[$i]);
                    $id_fav = rand(0, 999);

                    // cek fav
                    $cekFav = $conn->prepare("SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?");
                    $cekFav->bind_param("sss", $pin, $id_req, $arr_lokasi_kerja[$i]);
                    $cekFav->execute();
                    $resultFav = $cekFav->get_result();
                    $cekFav->close();

                    if ($resultFav->num_rows > 0) {
                        $fav_btn = '<input class="form-check-input" type="checkbox" style="display: none;" value="' . $temp_kode . '" name="check-fav-' . $id_fav . '[]" checked>
                                    <i class="bi bi-heart-fill btn-fav-' . $id_fav . '" style="font-size: 20px; cursor: pointer;" onclick="simpanFavLowongan(' . $id_fav . ')"></i>';
                    } else {
                        $fav_btn = '<input class="form-check-input" type="checkbox" style="display: none;" value="' . $temp_kode . '" name="check-fav-' . $id_fav . '[]">
                                    <i class="bi bi-heart btn-fav-' . $id_fav . '" style="font-size: 20px; cursor: pointer;" onclick="simpanFavLowongan(' . $id_fav . ')"></i>';
                    }

                    $shareLink = $baseURL . 'candidate/dashboard/beranda/detailJob?q=' . $temp_kode;
                    $shareTwitter = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                    $shareWA = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;

                    $id_salin_link = rand(100, 999);
                    $btnShare = '<div class="dropdown mt-2">
                                    ' . $fav_btn . '
                                    <i class="bi bi-share" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 20px; float: right; cursor: pointer; padding-left: 10px;"></i>
                                    <div class="dropdown-menu dropdown-for-share">
                                        <table style="width: 100%">
                                            <tr>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://x.com/intent/tweet?text=' . urlencode($shareTwitter) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/twitter-x.svg" alt="twitter-x" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($shareLink) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/facebook.svg" alt="facebook" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $shareLink . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/linkedin.svg" alt="linkedin" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://wa.me/?text=' . urlencode($shareWA) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/whatsapp.svg" alt="whatsapp" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="javascript:void(0)" id="btn-salin-' . $id_salin_link . '" onclick="salinLink(event, \'' . $shareLink . '\',\'' . $id_salin_link . '\')" tabindex="0" data-toggle="tooltip" data-bs-original-title="' . translate('Salin Link') . '"><i class="fas fa-link" style="font-size: 16px; color: black; vertical-align: middle;"></i></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>';

                    $output .= '<li class="list-group-item">
                                    <table style="width: 100%;" class="job-list-for-desktop">
                                        <tr>
                                            <td rowspan="3" style="width: 15%;">' . $logo_perusahaan_desktop . '</td>
                                            <td colspan="2" style="color: #023047; font-size: 16px; vertical-align: top; width: 65%;">
                                                <b>' . htmlspecialchars($row['posisi']) . '​</b><br>
                                                <span style="font-size: 14px;">' . htmlspecialchars($nama_perusahaan) . '</span>
                                            </td>
                                            <td style="text-align: right; vertical-align: top;">' . $btnShare . '</td>
                                        </tr>
                                        <tr>
                                            <td colspan="4" style="height: 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td style="vertical-align: top; width: 30%;"><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi_kerja))) . '</td>
                                            <td>
                                                <i class="bi bi-clock"></i> ' . htmlspecialchars($tipe_pekerjaan) . '
                                                <p style="font-size: 10px;">' . translate('Diunggah') . ' ' . $waktu_lalu . '</p>
                                            </td>
                                            <td colspan="2"><a href="detailJob?q=' . $temp_kode . '" class="btn btn-secondary" style="padding: 3px 13px !important; background-color: #0D3B72 !important;">' . translate('Lihat Detail') . '</a></td>
                                        </tr>
                                    </table>
                                    <table style="width: 100%;" class="job-list-for-smartphone">
                                        <tr>
                                            <td>' . $logo_perusahaan_smartphone . '</td>
                                            <td></td>
                                            <td style="text-align: right;">' . $btnShare . '</td>
                                        </tr>
                                        <tr style="height: 10px;">
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="color: #023047; font-size: 12px;"><b>' . htmlspecialchars($row['posisi']) . '</b></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="color: #023047; font-size: 10px;">' . htmlspecialchars($nama_perusahaan) . '</td>
                                        </tr>
                                        <tr style="height: 10px;">
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td style="font-size: 12px;"><i class="bi bi-clock"></i> ' . htmlspecialchars($tipe_pekerjaan) . '</td>
                                            <td style="text-wrap: nowrap; font-size: 12px; text-align: center;"><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi_kerja))) . '</td>
                                            <td style="text-wrap: nowrap; font-size: 12px; text-align: right;">' . $waktu_lalu . '</td>
                                        </tr>
                                        <tr style="height: 10px;">
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="text-align: right;"><a href="detailJob?q=' . $temp_kode . '" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important; background-color: #0D3B72 !important;">' . translate('Lihat Detail') . '</a></td>
                                        </tr>
                                    </table>
                                </li>';
                }
            }
        }

        // Pagination Range (tampilkan hanya 5 tombol halaman)
        $max_links = 5;
        $start_page = max(1, $page - floor($max_links / 2));
        $end_page = min($total_pages, $start_page + $max_links - 1);

        // Jika halaman awal terlalu dekat dengan halaman terakhir, sesuaikan rentang
        $start_page = max(1, $end_page - $max_links + 1);

        // Tombol Pagination
        $output .= '<div class="row mt-3 mb-3">
                        <div class="col-md-12">
                            <nav aria-label="Page navigation">
                                <ul class="pagination d-flex justify-content-center">';
        if ($page > 1) {
            $prev = $page - 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' aria-label='Previous' data-page='{$prev}'><span aria-hidden='true'>&laquo;</span></button>
                        </li>";
        }

        // Tampilkan rentang halaman (hanya 5 halaman maksimal)
        for ($i = $start_page; $i <= $end_page; $i++) {
            $active = ($i == $page) ? 'active' : '';
            $output .= "<li class='page-item {$active}'>
                            <button class='page-link' data-page='{$i}'>{$i}</button>
                        </li>";
        }

        if ($page < $total_pages) {
            $next = $page + 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' data-page='{$next}'><span aria-hidden='true'>&raquo;</span></button>
                        </li>";
        }

        $output .= '    </ul>
                    </nav>
                </div>
            </div>';
    } else {
        $output = '<div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i> ' . translate('Tidak ada lowongan pekerjaan') . '.
                            </div>
                        </div>
                    </div>';
    }

    $data = array();
    $data['html'] = $output;
    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'getProgresPengisian') {
    $data = array();

    // get progress pengisian data diri
    $sql = $conn->prepare("SELECT
                            ROUND(((p_1 + p_2 + p_3 + p_4 + p_5 + p_6 + p_7) / 7) * 100) as persentase
                        FROM
                        (
                        SELECT
                            IF(alamat = '',0,1) as p_1,
                            IF(pendidikan_terakhir = '',0,1) as p_2,
                            IF(kursus = '',0,1) as p_3,
                            IF(pengalaman_kerja = '',0,1) as p_4,
                            IF(minat_gaji = '',0,1) as p_5,
                            IF(kelebihan = '',0,1) as p_6,
                            IF(organisasi = '',0,1) as p_7
                        FROM
                            `rh`
                        WHERE
                            id = ?
                        ) h");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $persentase_progres = 0;
    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        if ($row['persentase'] > 100) {
            $persentase_progres = 100;
        } else {
            $persentase_progres = $row['persentase'];
        }
    }

    $status = "success";
    $value = $persentase_progres;

    $data['status'] = $status;
    $data['value'] = $value;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanIdentitasDiri') {
    $data = array();

    $status = "gagal";
    $message = translate('Identitas diri gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // Cek parameter
            if (!isset($_POST['nama']) || !isset($_POST['tempat_lahir']) || !isset($_POST['tgl_lahir']) || !isset($_POST['jk']) || !isset($_POST['status_pernikahan']) || !isset($_POST['ktp']) || !isset($_POST['no_telepon']) || !isset($_POST['sim']) || !isset($_POST['propinsi']) || !isset($_POST['kota_tinggal']) || !isset($_POST['kec_tinggal']) || !isset($_POST['rt_tinggal']) || !isset($_POST['rw_tinggal']) || !isset($_POST['alamat_tinggal']) || !isset($_POST['pos_tinggal'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $nama = $_POST['nama'];
            $tempat_lahir = $_POST['tempat_lahir'];
            $tgl_lahir = date("Y-m-d", strtotime($_POST['tgl_lahir']));
            $jk = $_POST['jk'];
            $status_pernikahan = $_POST['status_pernikahan'];
            $ktp = $_POST['ktp'];
            $no_telepon = $_POST['no_telepon'];
            $sim = implode(",", $_POST['sim']);
            $propinsi = $_POST['propinsi'];
            $kota_tinggal = $_POST['kota_tinggal'];
            $kec_tinggal = $_POST['kec_tinggal'];
            $rt_tinggal = $_POST['rt_tinggal'];
            $rw_tinggal = $_POST['rw_tinggal'];
            $alamat_tinggal = $_POST['alamat_tinggal'];
            $pos_tinggal = $_POST['pos_tinggal'];

            // get data provinsi
            $get = $conn->prepare("SELECT `name` as nama, 'provinsi' as ket FROM `provinces` WHERE id = ?
            UNION
            SELECT `name` as nama, 'kota' as ket FROM `regencies` WHERE id = ?
            UNION
            SELECT `name` as nama, 'kecamatan' as ket FROM `districts` WHERE id = ?");
            $get->bind_param("sss", $propinsi, $kota_tinggal, $kec_tinggal);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            $propinsi = "-";
            $kota_tinggal = "-";
            $kec_tinggal = "-";

            while ($row = mysqli_fetch_array($result)) {
                if ($row['ket'] == 'provinsi') {
                    $propinsi = $row['nama'];
                } elseif ($row['ket'] == 'kota') {
                    $kota_tinggal = $row['nama'];
                } elseif ($row['ket'] == 'kecamatan') {
                    $kec_tinggal = $row['nama'];
                }
            }

            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin, email FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // get data users
            $row = mysqli_fetch_array($result);
            $email = $row['email'];

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data identitas diri
                $update = $conn->prepare("UPDATE `rh` SET `nama` = ?, `tempat_lahir` = ?, `tgl_lahir` = ?, `jenis_kelamin` = ?, `status_pernikahan` = ?, `ktp` = ?, `no_telepon` = ?, `email` = ?, `sim` = ?, `provinsi` = ?, `kota` = ?, `kecamatan` = ?, `rt` = ?, `rw` = ?, `alamat` = ?, `kode_pos` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("ssssssssssssssssss", $nama, $tempat_lahir, $tgl_lahir, $jk, $status_pernikahan, $ktp, $no_telepon, $email, $sim, $propinsi, $kota_tinggal, $kec_tinggal, $rt_tinggal, $rw_tinggal, $alamat_tinggal, $pos_tinggal, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan update data identitas diri.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = translate('Data identitas diri berhasil disimpan.');
                } else {
                    throw new Exception(translate('Data identitas diri gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data identitas diri
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `nama`, `tempat_lahir`, `tgl_lahir`, `jenis_kelamin`, `status_pernikahan`, `ktp`, `no_telepon`, `email`, `sim`, `provinsi`, `kota`, `kecamatan`, `rt`, `rw`, `alamat`, `kode_pos`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("ssssssssssssssssss", $pin, $nama, $tempat_lahir, $tgl_lahir, $jk, $status_pernikahan, $ktp, $no_telepon, $email, $sim, $propinsi, $kota_tinggal, $kec_tinggal, $rt_tinggal, $rw_tinggal, $alamat_tinggal, $pos_tinggal, $created_at);

                if ($insert->execute()) {
                    $insert->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan input data identitas diri.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = translate('Data identitas diri berhasil disimpan.');
                } else {
                    throw new Exception(translate('Data identitas diri gagal disimpan. Silakan hubungi administrator'));
                }
            }

            // update user kandidat
            $update_user_kandidat = $conn->prepare("UPDATE `users_kandidat` SET `nama_lengkap` = ? WHERE `pin` = ?");
            $update_user_kandidat->bind_param("ss", $nama, $pin);

            if ($update_user_kandidat->execute()) {
                $update_user_kandidat->close();
                $conn->commit();
            } else {
                throw new Exception(translate('Data identitas diri gagal disimpan. Silakan hubungi administrator'));
            }
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatPendidikan') {
    $data = array();

    $status = "gagal";
    $message = translate('Riwayat pendidikan gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek parameter
            if (!isset($_POST['pendidikan_tinggi']) || !isset($_POST['jenjang_pendidikan']) || !isset($_POST['nama_sekolah_pendidikan']) || !isset($_POST['jurusan_pendidikan']) || !isset($_POST['tahun_mulai_pendidikan']) || !isset($_POST['tahun_selesai_pendidikan']) || !isset($_POST['ket_pendidikan'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $pendidikan_terakhir = $_POST['pendidikan_tinggi'];
            if ($pendidikan_terakhir == 'S1' || $pendidikan_terakhir == 'S2' || $pendidikan_terakhir == 'S3') {
                if (isset($_POST['cek_diploma'])) {
                    $diploma = $_POST['cek_diploma'];
                } else {
                    $diploma = "Tidak";
                }
            } elseif ($pendidikan_terakhir == 'Diploma') {
                $diploma = "Ya";
            } else {
                $diploma = "Tidak";
            }
            $jenjang_pendidikan = $_POST['jenjang_pendidikan'];
            $nama_sekolah_pendidikan = $_POST['nama_sekolah_pendidikan'];
            $jurusan_pendidikan = $_POST['jurusan_pendidikan'];
            $tahun_mulai_pendidikan = $_POST['tahun_mulai_pendidikan'];
            $tahun_selesai_pendidikan = $_POST['tahun_selesai_pendidikan'];
            $ket_pendidikan = $_POST['ket_pendidikan'];

            // cek jika data kosong
            if (count($jenjang_pendidikan) == 0 && count($nama_sekolah_pendidikan) == 0 && count($jurusan_pendidikan) == 0 && count($tahun_mulai_pendidikan) == 0 && count($tahun_selesai_pendidikan) == 0 && count($ket_pendidikan) == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pendidikan
                $update = $conn->prepare("UPDATE `rh` SET `pendidikan_terakhir` = ?, `diploma` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("ssss",  $pendidikan_terakhir, $diploma, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Riwayat pendidikan gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data pendidikan
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pendidikan_terakhir`, `diploma`, `created_at`) 
                VALUES (?, ?, ?, ?)");
                $insert->bind_param("ssss", $pin, $pendidikan_terakhir, $diploma, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Riwayat pendidikan gagal disimpan. Silakan hubungi administrator'));
                }
            }

            $temp_jenjang = array();
            // simpan detail riwayat pendidikan
            for ($i = 0; $i < count($jenjang_pendidikan); $i++) {
                $jenjang = $jenjang_pendidikan[$i];
                $nama_sekolah = $nama_sekolah_pendidikan[$i];
                $jurusan = $jurusan_pendidikan[$i];
                $tahun_mulai = $tahun_mulai_pendidikan[$i];
                $tahun_selesai = $tahun_selesai_pendidikan[$i];
                $ket = $ket_pendidikan[$i];

                // cek apakah jenjang pendidikan sudah ada
                $sql = $conn->prepare("SELECT id FROM riwayat_pendidikan WHERE id = ? AND jenjang = ?");
                $sql->bind_param("ss", $pin, $jenjang);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    // update detail riwayat pendidikan
                    $update = $conn->prepare("UPDATE `riwayat_pendidikan` SET `nama_sekolah` = ?, `tahun_mulai` = ?, `tahun_selesai` = ?, `jurusan` = ?, `ket` = ?, `updated_at` = ? WHERE `id` = ? AND jenjang = ?");
                    $update->bind_param("ssssssss",  $nama_sekolah, $tahun_mulai, $tahun_selesai, $jurusan, $ket, $created_at, $pin, $jenjang);

                    if ($update->execute()) {
                        $update->close();
                    } else {
                        throw new Exception(translate('Riwayat pendidikan gagal disimpan. Silakan hubungi administrator'));
                    }
                } else {
                    // insert detail riwayat pendidikan
                    $insert = $conn->prepare("INSERT INTO `riwayat_pendidikan`(`id`, `jenjang`, `nama_sekolah`, `tahun_mulai`, `tahun_selesai`, `jurusan`, `ket`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("ssssssss", $pin, $jenjang, $nama_sekolah, $tahun_mulai, $tahun_selesai, $jurusan, $ket, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception(translate('Riwayat pendidikan gagal disimpan. Silakan hubungi administrator'));
                    }
                }

                $temp_jenjang[] = $jenjang;
            }
            // end simpan detail riwayat pendidikan

            // hapus jenjang yang tidak ada
            if (count($temp_jenjang) > 0) {
                // Buat placeholder (?) sebanyak jumlah elemen dalam array
                $placeholders = implode(",", array_fill(0, count($temp_jenjang), "?"));

                $del = $conn->prepare("DELETE FROM riwayat_pendidikan WHERE id = ? AND jenjang NOT IN ($placeholders)");

                // Buat string tipe data
                $types = "s" . str_repeat("s", count($temp_jenjang));

                // Gabungkan parameter (ID + Status) untuk bind_param()
                $params = array_merge([$pin], $temp_jenjang);

                // Bind parameter menggunakan reference (karena bind_param membutuhkan reference)
                $del->bind_param($types, ...$params);

                if ($del->execute()) {
                    $del->close();
                } else {
                    throw new Exception(translate('Riwayat pendidikan gagal disimpan. Silakan hubungi administrator'));
                }
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwyat pendidikan.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Riwayat pendidikan berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatKursus') {
    $data = array();

    $status = "gagal";
    $message = translate('Riwayat pelatihan/kursus gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek Parameter
            if (!isset($_POST['kursus'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }
            $kursus = $_POST['kursus'];

            // hapus data kursus yang sudah ada
            $del = $conn->prepare("DELETE FROM `riwayat_kursus` WHERE id = ?");
            $del->bind_param("s", $pin);
            if ($del->execute()) {
                $del->close();
            } else {
                throw new Exception(translate('Riwayat pelatihan/kurses gagal disimpan. Silakan hubungi administrator'));
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pelatihan/kursus
                $update = $conn->prepare("UPDATE `rh` SET `kursus` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sss",  $kursus, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Riwayat pelatihan/kurses gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data pelatihan/kursus
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `kursus`, `created_at`) 
                VALUES (?, ?, ?)");
                $insert->bind_param("sss", $pin, $kursus, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Riwayat pelatihan/kurses gagal disimpan. Silakan hubungi administrator'));
                }
            }

            $nama_kursus = array();
            if (isset($_POST['nama_kursus'])) {
                $nama_kursus = $_POST['nama_kursus'];
            }

            // cek keterangan kursus
            if ($kursus == 'Ya' && count($nama_kursus) > 0) {
                $nama_kursus = $_POST['nama_kursus'];
                $sertifikat_kursus = $_POST['sertifikat_kursus'];
                $tempat_kursus = $_POST['tempat_kursus'];
                $tgl_mulai_kursus = $_POST['tgl_mulai_kursus'];
                $tgl_selesai_kursus = $_POST['tgl_selesai_kursus'];

                // cek jika data kosong
                if (count($nama_kursus) == 0 && count($sertifikat_kursus) == 0 && count($tempat_kursus) == 0 && count($tgl_mulai_kursus) == 0 && count($tgl_selesai_kursus) == 0) {
                    throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
                }

                // simpan detail riwayat pelatihan/kursus
                for ($i = 0; $i < count($nama_kursus); $i++) {
                    $nama = $nama_kursus[$i];
                    $sertifikat = $sertifikat_kursus[$i];
                    $tempat = $tempat_kursus[$i];
                    $tgl_mulai = $tgl_mulai_kursus[$i];
                    $tgl_selesai = $tgl_selesai_kursus[$i];

                    // insert detail riwayat pendidikan
                    $insert = $conn->prepare("INSERT INTO `riwayat_kursus`(`id`, `nama`, `tempat`, `sertifikat`, `tgl_mulai`, `tgl_selesai`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("sssssss", $pin, $nama, $tempat, $sertifikat, $tgl_mulai, $tgl_selesai, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception(translate('Riwayat pelatihan/kurses gagal disimpan. Silakan hubungi administrator'));
                    }
                }
                // end simpan detail riwayat pelatihan/kursus
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwayat pelatihan/kursus.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Riwayat pelatihan/kursus berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = translate('Riwayat pekerjaan gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek parameter
            if (!isset($_POST['pengalaman_kerja']) || !isset($_POST['total_pengalaman_kerja']) || !isset($_POST['pengalaman_posisi_sama'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $pengalaman_kerja = $_POST['pengalaman_kerja'];
            $total_pengalaman_kerja = $_POST['total_pengalaman_kerja'];
            $pengalaman_posisi_sama = $_POST['pengalaman_posisi_sama'];

            if ($pengalaman_kerja != "Ya") {
                $total_pengalaman_kerja = "";
                $pengalaman_posisi_sama = "";
                $nama = array();
                $jabatan = array();
                $status = array();
                $gaji = array();
                $tahun_mulai = array();
                $tahun_selesai = array();
                $alasan = array();
            }

            // hapus data riwayat pekerjaan yang sudah ada
            $del = $conn->prepare("DELETE FROM `riwayat_pekerjaan` WHERE id = ?");
            $del->bind_param("s", $pin);
            if ($del->execute()) {
                $del->close();
            } else {
                throw new Exception(translate('Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator'));
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pekerjaan
                $update = $conn->prepare("UPDATE `rh` SET `pengalaman_kerja` = ?, `lama_pengalaman_kerja` = ?, `lama_posisi_kerja` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sssss",  $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data pekerjaan
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pengalaman_kerja`, `lama_pengalaman_kerja`, `lama_posisi_kerja`, `created_at`) 
                VALUES (?, ?, ?, ?, ?)");
                $insert->bind_param("sssss", $pin, $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator'));
                }
            }

            $nama = array();
            if (isset($_POST['nama'])) {
                $nama = $_POST['nama'];
            }

            // cek keterangan pengalaman kerja
            if ($pengalaman_kerja == 'Ya' && count($nama) > 0) {
                $jabatan = $_POST['jabatan'];
                $status = explode(",", $_POST['status']);
                $gaji = $_POST['gaji'];
                $tahun_mulai = $_POST['tahun_mulai'];
                $tahun_selesai = $_POST['tahun_selesai'];
                $alasan = $_POST['alasan'];

                // cek jika data kosong
                if (count($nama) == 0 && count($jabatan) == 0 && count($status) == 0 && count($gaji) == 0 && count($tahun_mulai) == 0 && count($tahun_selesai) == 0 && count($alasan) == 0) {
                    throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
                }

                // simpan detail riwayat pekerjaan
                for ($i = 0; $i < count($nama); $i++) {
                    $temp_nama = $nama[$i];
                    $temp_jabatan = $jabatan[$i];
                    $temp_status = $status[$i];
                    $temp_gaji = str_replace(".", "", $gaji[$i]);
                    $temp_tahun_mulai = $tahun_mulai[$i];
                    $temp_tahun_selesai = $tahun_selesai[$i];
                    $temp_alasan = $alasan[$i];

                    // insert detail riwayat pekerjaan
                    $insert = $conn->prepare("INSERT INTO `riwayat_pekerjaan`(`id`, `nama_perusahaan`, `jabatan`, `status_kerja`, `gaji`, `tahun_mulai`, `tahun_selesai`, `alasan_berhenti`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("sssssssss", $pin, $temp_nama, $temp_jabatan, $temp_status, $temp_gaji, $temp_tahun_mulai, $temp_tahun_selesai, $temp_alasan, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception(translate('Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator'));
                    }
                }
                // end simpan detail riwayat pekerjaan
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwayat pekerjaan.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Riwayat pekerjaan berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanInformasiPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = translate('Informasi pekerjaan gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek parameter
            if (!isset($_POST['perjalanan_dinas']) || !isset($_POST['minat_lokasi_kerja']) || !isset($_POST['temp_gaji'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $perjalanan_dinas = $_POST['perjalanan_dinas'];
            $minat_lokasi_kerja = $_POST['minat_lokasi_kerja'];
            $temp_gaji = str_replace(".", "", $_POST['temp_gaji']);

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data informasi pekerjaan
                $update = $conn->prepare("UPDATE `rh` SET `perjalanan_dinas` = ?, `minat_lokasi_kerja` = ?, `minat_gaji` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sssss",  $perjalanan_dinas, $minat_lokasi_kerja, $temp_gaji, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Informasi pekerjaan gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data informasi pekerjaan
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `perjalanan_dinas`, `minat_lokasi_kerja`, `minat_gaji`, `created_at`) 
                VALUES (?, ?, ?, ?, ?)");
                $insert->bind_param("sssss", $pin, $perjalanan_dinas, $minat_lokasi_kerja, $temp_gaji, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Informasi pekerjaan gagal disimpan. Silakan hubungi administrator'));
                }
            }

            // simpan log aktivitas
            $messages = 'Melakukan input informasi pekerjaan.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Informasi pekerjaan berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanMinatKonsep') {
    $data = array();

    $status = "gagal";
    $message = translate('Minat dan konsep pribadi gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek parameter
            if (!isset($_POST['penguasaan_bhs']) || !isset($_POST['kelebihan']) || !isset($_POST['kekurangan']) || !isset($_POST['kk']) || !isset($_POST['pimpin_tim']) || !isset($_POST['kemampuan_persentasi']) || !isset($_POST['rlp'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $penguasaan_bhs = $_POST['penguasaan_bhs'];
            if (isset($_POST['bahasa']) && isset($_POST['membaca_bhs']) && isset($_POST['menulis_bhs']) && isset($_POST['mendengar_bhs']) && isset($_POST['berbicara_bhs'])) {
                $bahasa = $_POST['bahasa'];
                $membaca_bhs = $_POST['membaca_bhs'];
                $menulis_bhs = $_POST['menulis_bhs'];
                $mendengar_bhs = $_POST['mendengar_bhs'];
                $berbicara_bhs = $_POST['berbicara_bhs'];
            } else {
                $bahasa = array();
                $membaca_bhs = array();
                $menulis_bhs = array();
                $mendengar_bhs = array();
                $berbicara_bhs = array();
            }
            $kelebihan = implode(",", $_POST['kelebihan']);
            $kekurangan = implode(",", $_POST['kekurangan']);
            $kk = $_POST['kk'];
            $pimpin_tim = $_POST['pimpin_tim'];
            $kemampuan_persentasi = $_POST['kemampuan_persentasi'];
            $rlp = implode(",", $_POST['rlp']);

            // hapus data penguasaan bahasa yang sudah ada
            $del = $conn->prepare("DELETE FROM `penguasaan_bahasa` WHERE id = ?");
            $del->bind_param("s", $pin);
            if ($del->execute()) {
                $del->close();
            } else {
                throw new Exception(translate('Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator'));
            }

            // cek keterangan penguasaan bahasa
            if ($penguasaan_bhs == 'Ya' && count($bahasa) > 0) {
                // cek jika data kosong
                if (count($bahasa) == 0 && count($membaca_bhs) == 0 && count($menulis_bhs) == 0 && count($mendengar_bhs) == 0 && count($berbicara_bhs) == 0) {
                    throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
                }

                // simpan detail penguasaan bahasa
                for ($i = 0; $i < count($bahasa); $i++) {
                    $temp_bahasa = $bahasa[$i];
                    $temp_membaca_bhs = $membaca_bhs[$i];
                    $temp_menulis_bhs = $menulis_bhs[$i];
                    $temp_mendengar_bhs = $mendengar_bhs[$i];
                    $temp_berbicara_bhs = $berbicara_bhs[$i];

                    // insert detail penguasaan bahasa
                    $insert = $conn->prepare("INSERT INTO `penguasaan_bahasa`(`id`, `bahasa`, `membaca`, `menulis`, `mendengar`, `berbicara`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("sssssss", $pin, $temp_bahasa, $temp_membaca_bhs, $temp_menulis_bhs, $temp_mendengar_bhs, $temp_berbicara_bhs, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception(translate('Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator'));
                    }
                }
                // end simpan detail penguasaan bahasa
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data minat dan konsep pribadi
                $update = $conn->prepare("UPDATE `rh` SET `bahasa_asing` = ?, `kelebihan` = ?, `kekurangan` = ?, `ilmu_komputerisasi` = ?, `memimpin_tim` = ?, `kemampuan_presentasi` = ?, `lingkup_pekerjaan` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sssssssss",  $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data minat dan konsep pribadi
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `bahasa_asing`, `kelebihan`, `kekurangan`, `ilmu_komputerisasi`, `memimpin_tim`, `kemampuan_presentasi`, `lingkup_pekerjaan`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssssss", $pin, $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator'));
                }
            }

            // simpan log aktivitas
            $messages = 'Melakukan input minat dan konsep.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Minat dan konsep pribadi berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatOrganisasi') {
    $data = array();

    $status = "gagal";
    $message = translate('Pengalaman organisasi gagal disimpan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            // Cek parameter
            if (!isset($_POST['organisasi'])) {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan untuk login kembali.'));
            }

            $organisasi = $_POST['organisasi'];

            // hapus data organisasi yang sudah ada
            $del = $conn->prepare("DELETE FROM `riwayat_organisasi` WHERE id = ?");
            $del->bind_param("s", $pin);
            if ($del->execute()) {
                $del->close();
            } else {
                throw new Exception(translate('Pengalaman organisasi gagal disimpan. Silakan hubungi administrator'));
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data organisasi
                $update = $conn->prepare("UPDATE `rh` SET `organisasi` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sss",  $organisasi, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Pengalaman organisasi gagal disimpan. Silakan hubungi administrator'));
                }
            } else {
                // insert data organisasi
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `organisasi`, `created_at`) 
                VALUES (?, ?, ?)");
                $insert->bind_param("sss", $pin, $organisasi, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception(translate('Pengalaman organisasi gagal disimpan. Silakan hubungi administrator'));
                }
            }

            $nama = array();
            if (isset($_POST['nama'])) {
                $nama = $_POST['nama'];
            }

            // cek keterangan kursus
            if ($organisasi == 'Ya' && count($nama) > 0) {
                $nama = $_POST['nama'];
                $jabatan = $_POST['jabatan'];
                $tahun = $_POST['tahun'];
                $tempat = $_POST['tempat'];

                // cek jika data kosong
                if (count($nama) == 0 && count($jabatan) == 0 && count($tahun) == 0 && count($tempat) == 0) {
                    throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
                }

                // simpan detail riwayat organisasi
                for ($i = 0; $i < count($nama); $i++) {
                    $temp_nama = $nama[$i];
                    $temp_jabatan = $jabatan[$i];
                    $temp_tahun = $tahun[$i];
                    $temp_tempat = $tempat[$i];

                    // insert detail riwayat organisasi
                    $insert = $conn->prepare("INSERT INTO `riwayat_organisasi`(`id`, `nama`, `jabatan`, `tempat`, `tahun`, `created_at`) 
                    VALUES (?, ?, ?, ?, ?, ?)");
                    $insert->bind_param("ssssss", $pin, $temp_nama, $temp_jabatan, $temp_tempat, $temp_tahun, $created_at);

                    if ($insert->execute()) {
                        $insert->close();
                    } else {
                        throw new Exception(translate('Pengalaman organisasi gagal disimpan. Silakan hubungi administrator'));
                    }
                }
                // end simpan detail riwayat organisasi
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwayat organisasi.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Pengalaman organisasi berhasil disimpan.');
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'prosesLamar') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses lamaran gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);
        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin, nama_lengkap, email FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan untuk login kembali.'));
            }

            $row = mysqli_fetch_array($result);
            $nama_kandidat = ucwords(strtolower($row['nama_lengkap']));
            $email_kandidat = $row['email'];

            $id_req = "-";
            $lokasi = "-";
            $pertanyaan_khusus = "";
            $where_lokasi = "";
            // cek parameter
            if (isset($_POST['id_req']) && isset($_POST['lokasi'])) {
                $id_req = $_POST['id_req'];
                $lokasi = strtoupper(str_replace("KAB. ", "KABUPATEN ", $_POST['lokasi']));
                $where_lokasi = "%" . $lokasi . "%";
                if (isset($_POST['pertanyaan_khusus'])) {
                    if ($_POST['pertanyaan_khusus'] != "") {
                        $pertanyaan_khusus = implode("|", $_POST['pertanyaan_khusus']);
                    }
                }
            }

            // cek apakah lowongan ada
            $get = $conn->prepare("SELECT id_koordinator, perusahaan, posisi, `status` FROM list_request WHERE id_req = ? AND lokasi_kerja LIKE ?");
            $get->bind_param("ss", $id_req, $where_lokasi);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            $id_koordinator = "-";
            $perusahaan = "-";
            $posisi = "-";
            if ($result->num_rows > 0) {
                $rowData = mysqli_fetch_array($result);
                if ($rowData['status'] == 'On Proccess') {
                    $id_koordinator = $rowData['id_koordinator'];
                    $perusahaan = $rowData['perusahaan'];
                    $posisi = $rowData['posisi'];
                } else {
                    throw new Exception(translate('Lowongan sudah tidak tersedia.'));
                }
            } else {
                throw new Exception(translate('Proses lamaran gagal dilakukan.'));
            }

            // cek apakah user sudah melakukan proses lamaran pada lowongan
            $get = $conn->prepare("SELECT id_koordinator FROM users_lamar WHERE id_gestalt = ? AND id_req = ? AND lokasi = ?");
            $get->bind_param("sss", $pin, $id_req, $lokasi);
            echo $get->error;
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows > 0) {
                $status = "exist";
                throw new Exception(translate('Anda sudah melamar lowongan ini.'));
            }

            // Generate id lamar
            $sqlCek = "SELECT id_lamar FROM users_lamar";
            $queryCek = $conn->query($sqlCek);

            do {
                $id_lamar = getIDLamar();
            } while ($id_lamar == $queryCek);

            // insert proses lamar
            $insert = $conn->prepare("INSERT INTO `users_lamar`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`, `lokasi`, `jawaban_k_khusus`) 
            VALUES (?, ?, ?, ?, 'On Proccess Digitalcv', ?, ?, ?)");
            $insert->bind_param("sssssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at, $lokasi, $pertanyaan_khusus);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
            }

            // insert riwayat lamar
            $insert = $conn->prepare("INSERT INTO `users_lamar_history`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
            VALUES (?, ?, ?, ?, 'Lamaran Dikirim', ?)");
            $insert->bind_param("sssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
            }

            // lakukan proses screening
            $hasil_screening = ProsesScreening($conn, $pin, $id_req);
            if ($hasil_screening) {
                if (canSendEmail($conn, $email_kandidat, 'Apply Lamaran')) {
                    if (kirim_apply_lamaran($email_kandidat, $posisi, $perusahaan, $nama_kandidat, $SesClient)) {
                        // simpan log aktivitas
                        $messages = 'Melamar lowongan di ' . $perusahaan . ' dengan posisi ' . $posisi . ' [' . $id_req . '].';
                        $extra_info = "Kandidat";
                        $level = "INFO";
                        logActivity($conn, $pin, $level, $messages, $extra_info);

                        // kirim notifikasi
                        $pesan_notif = $nama_kandidat . " melamar pekerjaan diposisi " . $posisi . ".";
                        $id_referensi = base64_encode($id_req);
                        $kirim_notif = $conn->prepare("INSERT INTO `notif_summary`(`pengirim`, `penerima`, `judul`, `isi`, `id_referensi`, `status`, `link`, `create_at`) VALUES (?, ?, 'Melamar Pekerjaan', ?, ?, 'Dikirim', '', ?)");
                        $kirim_notif->bind_param("sssss", $pin, $id_koordinator, $pesan_notif, $id_referensi, $created_at);
                        if ($kirim_notif->execute()) {
                            $kirim_notif->close();

                            $dataPayload = [
                                'tipe' => 'ajukan',
                                'id_req' => $id_req,
                                'id_lamar' => $id_lamar,
                                'posisi' => $posisi,
                                'perusahaan' => $perusahaan,
                                'nama_pelamar' => $nama_kandidat,
                                'pin' => $pin,
                            ];
                            sendNotifikasiFromCandidate($id_koordinator, "Lamar Pekerjaan", $nama_kandidat . " melamar pekerjaan Anda untuk posisi " . $posisi . ".", $dataPayload, $conn);

                            $apiKeyWS = '57pOr213C&^XM701%(*U4';
                            $urlWS = $baseURL . 'api/WebSocket/send.php';

                            $dataWS = [
                                'to' => urlencode($id_koordinator),
                                'tipe' => 'notif',
                                'message' => $pesan_notif
                            ];

                            $optionsWS = [
                                'http' => [
                                    'method'  => 'POST',
                                    'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                                    'content' => http_build_query($dataWS)
                                ],
                                'ssl' => [
                                    'verify_peer' => true,
                                    'verify_peer_name' => true
                                ]
                            ];

                            $contextWS = stream_context_create($optionsWS);
                            file_get_contents($urlWS, false, $contextWS);
                        }

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = translate('Proses lamaran berhasil dilakukan.');
                    } else {
                        throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
                    }
                } else {
                    throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
                }
            } else {
                throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
            }
        } else {
            // Failed reCAPTCHA verification
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanFavLowongan') {
    $data = array();

    $status = "gagal";
    $message = "Proses gagal dilakukan.";
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception("Proses gagal dilakukan. Silakan untuk login kembali.");
            }

            $q = "-";
            $check = "-";
            $id_req = "-";
            $lokasi = "-";
            // cek parameter
            if (isset($_POST['q']) && isset($_POST['check'])) {
                $q = base64_decode($_POST['q']);
                $arr = explode("|", $q);

                if (isset($arr[0])) {
                    $id_req = $arr[0];
                }

                if (isset($arr[1])) {
                    $lokasi = $arr[1];
                }

                $check = $_POST['check'];
            } else {
                throw new Exception("Proses gagal dilakukan. Parameter tidak ditemukan.");
            }

            if ($check == "-" || $id_req == "-" || $lokasi == "-") {
                throw new Exception("Proses gagal dilakukan. Parameter tidak ditemukan.");
            }

            // Update lowongan favorite
            if ($check == "true") {
                // simpan lowongan favorite
                $insert = $conn->prepare("INSERT INTO `list_favorite`(`id`, `id_req`, `lokasi`, `created_at`) 
                VALUES (?, ?, ?, ?)");
                $insert->bind_param("ssss", $pin, $id_req, $lokasi, $created_at);

                if ($insert->execute()) {
                    $insert->close();

                    $messages = 'Menyimpan lowongan favorite dengan id lowongan ' . $id_req . ' lokasi ' . $lokasi . '.';
                } else {
                    throw new Exception("Proses gagal dilakukan. Silakan hubungi administrator");
                }
            } else {
                // hapus lowongan favorite
                $delete = $conn->prepare("DELETE FROM `list_favorite` WHERE id = ? AND id_req = ? AND lokasi = ?");
                $delete->bind_param("sss", $pin, $id_req, $lokasi);

                if ($delete->execute()) {
                    $delete->close();

                    $messages = 'Membatalkan lowongan favorite dengan id lowongan ' . $id_req . ' lokasi ' . $lokasi . '.';
                } else {
                    throw new Exception("Proses gagal dilakukan. Silakan hubungi administrator");
                }
            }

            // simpan log aktivitas
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Proses berhasil dilakukan.";
        } else {
            throw new Exception("Verifikasi reCAPTCHA gagal, silakan coba kembali nanti.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'riwayatLamaranPekerjaan') {
    $limit = 6; // Jumlah data per halaman
    $page = isset($_POST['page']) ? (int) $_POST['page'] : 1;
    $start = ($page - 1) * $limit;

    $pencarian = "";
    $types = "sii";
    $vars = [$pin, $start, $limit];

    $types2 = "s";
    $vars2 = [$pin];
    if (isset($_POST['pencarian'])) {
        if ($_POST['pencarian'] != "") {
            $temp_pencarian = "%" . $_POST['pencarian'] . "%";
            $pencarian = "AND (lr.perusahaan LIKE ? OR lr.posisi LIKE ?)";
            $types = "sssii";
            $vars = [$pin, $temp_pencarian, $temp_pencarian, $start, $limit];

            $types2 = "sss";
            $vars2 = [$pin, $temp_pencarian, $temp_pencarian];
        }
    }

    // Ambil data dari database
    $sql = $conn->prepare("SELECT
                                ul.id_lamar,
                                ul.id_req,
                                lr.perusahaan,
                                lr.posisi,
                                ul.lokasi,
                                ulh.tgl,
                                ul.`status`,
                                lr.id_koordinator,
                                k.img,
                                COUNT(c.id) as unread_chat
                            FROM
                                users_lamar ul
                                JOIN list_request lr ON lr.id_req = ul.id_req
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                                JOIN users_lamar_history ulh ON ul.id_lamar = ulh.id_lamar
                                LEFT JOIN chat c ON c.reference_id = ul.id_lamar AND c.`status` = 'send' AND c.`to` = ul.id_gestalt
                            WHERE
                                ul.id_gestalt = ?
                                AND ulh.`status` = 'Lamaran Dikirim'
                                $pencarian
                            GROUP BY
                                ul.id_req, ul.lokasi
                            ORDER BY
                                ul.tgl
                            DESC
                            LIMIT ? , ?");
    $sql->bind_param($types, ...$vars);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    // Hitung total data
    $total_query = $conn->prepare("SELECT
                                    COUNT(*) as total
                                FROM
                                    (
                                    SELECT
                                        ul.id_lamar
                                    FROM
                                        users_lamar ul
                                        JOIN list_request lr ON lr.id_req = ul.id_req
                                        JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                                        JOIN users_lamar_history ulh ON ul.id_lamar = ulh.id_lamar
                                    WHERE
                                        ul.id_gestalt = ?
                                        AND ulh.`status` = 'Lamaran Dikirim'
                                        $pencarian
                                    GROUP BY
                                        ul.id_req, ul.lokasi
                                    ) h");
    $total_query->bind_param($types2, ...$vars2);
    $total_query->execute();
    $total_result = $total_query->get_result();
    $total_query->close();

    if ($total_result->num_rows > 0) {
        $total_row = $total_result->fetch_assoc();
        $total_data = $total_row['total'];
        $total_pages = ceil($total_data / $limit);
    } else {
        $total_row = 0;
        $total_data = 0;
        $total_pages = 0;
    }

    if ($result->num_rows > 0) {
        $output = '';

        while ($row = $result->fetch_assoc()) {
            $id_lamar = $row['id_lamar'];
            $id_req = $row['id_req'];
            $nama_perusahaan = mb_strimwidth($row['perusahaan'], 0, 40, "...");
            $posisi = $row['posisi'];
            $lokasi = $row['lokasi'];
            $waktu_lalu = waktuLalu($row['tgl']);
            $id_koordinator = $row['id_koordinator'];
            $unread_chat = $row['unread_chat'];

            if (strpos($lokasi, 'REMOTE') !== false) {
                $lokasi = 'Remote';
            }

            // get logo perusahaan
            if ($row['img'] == "") {
                $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
            } else {
                if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'perusahaan/logo/' . $row['img']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                    $logoURL = (string) $request->getUri();

                    $logo_perusahaan = '<img src="' . $logoURL . '" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                } else {
                    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                }
            }

            // get last histori
            $get = $conn->query("SELECT `status` FROM users_lamar_history WHERE id_lamar = '$id_lamar' ORDER BY tgl DESC LIMIT 1");
            $row = mysqli_fetch_array($get);
            $status = $row['status'];
            if (strpos(strtolower($status), 'tolak') !== false) {
                $class_text = "text-danger";
            } else {
                $class_text = "text-success";
            }

            if ($status == 'Tolak Rekrutmen') {
                $status = 'Tidak Lulus Seleksi';
            }

            $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter         
            $tujuanChat = encrypt_url($id_koordinator . '|' . $id_lamar, $key);
            $idBadgeChat = preg_replace('/[^a-zA-Z0-9_]/', '', base64_encode($id_lamar));
            $titleModalChat = 'HRD ' . $nama_perusahaan;

            // Cek jumlah chat yang belum dibaca
            $badgeChat = '<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success" id="badge-chat-notif-' . $idBadgeChat . '" style="display: none;">
                            0
                        </span>';
            if ($unread_chat > 0) {
                $count_unread_chat = $unread_chat;
                if ($unread_chat > 99) {
                    $count_unread_chat = '99+';
                }

                $badgeChat = '<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success" id="badge-chat-notif-' . $idBadgeChat . '">
                                ' . $count_unread_chat . '
                            </span>';
            }

            $output .= '<div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <table style="width: 100%;" class="job-list-for-desktop">
                                            <tr>
                                                <td colspan="4" style="text-align: right;">
                                                    <button type="button" class="btn btn-info btn-xs position-relative" data-bs-toggle="modal" data-bs-target="#modal-chat" onclick="openModalChat(\'' . $tujuanChat . '\',\'' . $titleModalChat . '\')">
                                                        Chat HRD
                                                        ' . $badgeChat . '
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td rowspan="3" style="width: 20%; text-align: left;">' . $logo_perusahaan . '</td>
                                                <td colspan="2" style="color: #023047; font-size: 16px; vertical-align: top; width: 65%; padding-left: 20px;">
                                                    <b>' . htmlspecialchars($posisi) . '​</b><br>
                                                    <span style="font-size: 14px;"><a href="../beranda/detailPerusahaan?q=' . base64_encode($id_koordinator) . '">' . htmlspecialchars($nama_perusahaan) . '</a></span><br>
                                                    <span style="font-size: 12px;"><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower($lokasi)) . '</span>
                                                </td>
                                                <td style="text-align: right; vertical-align: middle;"><a href="timeline?q=' . base64_encode($id_lamar) . '" class="btn btn-secondary" style="padding: 3px 13px !important; background-color: #0D3B72 !important;">' . strtoupper(translate('Timeline')) . '</a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="4" style="height: 10px;"></td>
                                            </tr>
                                            <tr>
                                                <td style="vertical-align: top; width: 30%; padding-left: 20px;" class="' . $class_text . '">' . translate(htmlspecialchars($status)) . '</td>
                                                <td colspan="3" style="text-align: right; font-size: 12px;">' . translate('Dilamar') . ' ' . $waktu_lalu . '</td>
                                            </tr>
                                        </table>
                                        <table style="width: 100%;" class="job-list-for-smartphone">
                                            <tr>
                                                <td colspan="3" style="text-align: right;">
                                                    <button type="button" class="btn btn-info btn-xs position-relative" data-bs-toggle="modal" data-bs-target="#modal-chat" onclick="openModalChat(\'' . $tujuanChat . '\')">
                                                        Chat HRD
                                                        ' . $badgeChat . '
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>' . $logo_perusahaan . '</td>
                                                <td></td>
                                                <td style="text-align: right; font-size: 12px;"><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi))) . '</td>
                                            </tr>
                                            <tr style="height: 10px;">
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td colspan="2" style="color: #023047; font-size: 14px;"><b>' . htmlspecialchars($posisi) . '</b></td>
                                                <td style="text-align: right;"><a href="timeline?q=' . base64_encode($id_lamar) . '" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important; background-color: #0D3B72 !important;">' . strtoupper(translate('Timeline')) . '</a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="3" style="color: #023047; font-size: 12px;"><a href="../beranda/detailPerusahaan?q=' . base64_encode($id_koordinator) . '">' . htmlspecialchars($nama_perusahaan) . '</a></td>
                                            </tr>
                                            <tr style="height: 10px;">
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td class="text-success">' . translate(htmlspecialchars($status)) . '</td>
                                                <td colspan="2" style="text-align: right; font-size: 10px;">' . translate('Dilamar') . ' ' . $waktu_lalu . '</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>';
        }

        // Pagination Range (tampilkan hanya 5 tombol halaman)
        $max_links = 5;
        $start_page = max(1, $page - floor($max_links / 2));
        $end_page = min($total_pages, $start_page + $max_links - 1);

        // Jika halaman awal terlalu dekat dengan halaman terakhir, sesuaikan rentang
        $start_page = max(1, $end_page - $max_links + 1);

        // Tombol Pagination
        $output .= '<div class="row">
                        <div class="col-md-12">
                            <nav aria-label="Page navigation">
                                <ul class="pagination d-flex justify-content-center">';
        if ($page > 1) {
            $prev = $page - 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' aria-label='Previous' data-page='{$prev}'><span aria-hidden='true'>&laquo;</span></button>
                        </li>";
        }

        // Tampilkan rentang halaman (hanya 5 halaman maksimal)
        for ($i = $start_page; $i <= $end_page; $i++) {
            $active = ($i == $page) ? 'active' : '';
            $output .= "<li class='page-item {$active}'>
                            <button class='page-link' data-page='{$i}'>{$i}</button>
                        </li>";
        }

        if ($page < $total_pages) {
            $next = $page + 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' data-page='{$next}'><span aria-hidden='true'>&raquo;</span></button>
                        </li>";
        }

        $output .= '    </ul>
                    </nav>
                </div>
            </div>';
    } else {
        $output = '<div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i> ' . translate('Tidak ada riwayat lamaran pekerjaan.') . '
                            </div>
                        </div>
                    </div>';
    }

    $data = array();
    $data['html'] = $output;
    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'paginationJobFavorite') {
    $limit = 6; // Jumlah data per halaman
    $page = isset($_POST['page']) ? (int) $_POST['page'] : 1;
    $start = ($page - 1) * $limit;

    $lokasi = "";
    $types = "s";
    $vars = [$pin];

    $temp_lokasi = $_POST['lokasi'];
    if (isset($temp_lokasi)) {
        if ($temp_lokasi != "") {
            $temp = "%" . $temp_lokasi . "%";
            $lokasi = "AND lr.lokasi_kerja LIKE ?";

            $types .= "s";
            $vars[] = $temp;
        }
    }

    $pencarian = "";
    if (isset($_POST['pencarian'])) {
        if ($_POST['pencarian'] != "") {
            $temp_pencarian = "%" . $_POST['pencarian'] . "%";
            $pencarian = "AND (lr.perusahaan LIKE ? OR lr.posisi LIKE ?)";

            $types .= "ss";
            $vars[] = $temp_pencarian;
            $vars[] = $temp_pencarian;
        }
    }

    $types .= $types;
    $vars = array_merge($vars, $vars);

    $types .= "ii";
    $vars = array_merge($vars, [$start, $limit]);

    // Ambil data dari database
    $sql = $conn->prepare("SELECT
                                lr.*,
                                k.img
                            FROM
                                list_request lr
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                JOIN list_favorite lf ON lf.id_req = lr.id_req
                            WHERE
                                lr.`status` = 'On Proccess'
                                AND lf.id = ?
                                $lokasi
                                $pencarian
                            GROUP BY
                                lr.id_req
                            UNION
                            SELECT
                                lr.*,
                                k.img
                            FROM
                                list_request lr
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                JOIN list_favorite lf ON lf.id_req = lr.id_req
                            WHERE
                                lr.`status` = 'Selesai'
                                AND lf.id = ?
                                $lokasi
                                $pencarian
                            GROUP BY
                                lr.id_req
                            LIMIT ? , ?");
    $sql->bind_param($types, ...$vars);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $types = substr($types, 0, -2);
    $vars = array_slice($vars, 0, -2);

    // Hitung total data
    $total_query = $conn->prepare("SELECT
                                        COUNT(h.id) AS total
                                    FROM
                                        (
                                        SELECT
                                            lr.id
                                        FROM
                                            list_request lr
                                            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                            JOIN list_favorite lf ON lf.id_req = lr.id_req
                                        WHERE
                                            lr.`status` = 'On Proccess'
                                            AND lf.id = ?
                                            $lokasi
                                            $pencarian
                                        GROUP BY
                                            lr.id_req
                                        UNION
                                        SELECT
                                            lr.id
                                        FROM
                                            list_request lr
                                            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                            JOIN list_favorite lf ON lf.id_req = lr.id_req
                                        WHERE
                                            lr.`status` = 'Selesai'
                                            AND lf.id = ?
                                            $lokasi
                                            $pencarian
                                        GROUP BY
                                            lr.id_req
                                        ) h");
    $total_query->bind_param($types, ...$vars);
    $total_query->execute();
    $total_result = $total_query->get_result();
    $total_query->close();

    $total_row = $total_result->fetch_assoc();
    $total_data = $total_row['total'];
    $total_pages = ceil($total_data / $limit);

    if ($result->num_rows > 0) {
        $output = '<p class="mb-3">' . translate('Lowongan pekerjaan yang Anda simpan') . '</p>
                    <div class="row">';

        while ($row = $result->fetch_assoc()) {
            $id_req = $row['id_req'];
            $nama_perusahaan = mb_strimwidth($row['perusahaan'], 0, 40, "...");
            $waktu_lalu = waktuLalu($row['create_at']);
            $tgl_posting = formatTanggalIndonesia($row['create_at']);
            $tipe_pekerjaan = $row['tipe_pekerjaan'];
            $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
            $id_koordinator = $row['id_koordinator'];

            if ($row['img'] == "") {
                $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
            } else {
                if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'perusahaan/logo/' . $row['img']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                    $logoURL = (string) $request->getUri();

                    $logo_perusahaan = '<img src="' . $logoURL . '" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                } else {
                    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
                }
            }

            $card_disabled = "";
            $hari_ini = date("Y-m-d");
            $tgl_expired = date("Y-m-d", strtotime($row['expired_date']));
            if ($row['status'] == 'Selesai' || strtotime($tgl_expired) < strtotime($hari_ini)) {
                $card_disabled = "disabled";
            }

            if ($temp_lokasi != "") {
                if (in_array($temp_lokasi, $arr_lokasi_kerja)) {
                    // Filter array untuk hanya menyisakan lokasi yang sama dengan $temp_lokasi
                    $arr_lokasi_kerja = array_filter($arr_lokasi_kerja, function ($value) use ($temp_lokasi) {
                        return $value === $temp_lokasi;
                    });

                    // Reset indeks array agar mulai dari 0
                    $arr_lokasi_kerja = array_values($arr_lokasi_kerja);
                } else {
                    $arr_lokasi_kerja = array();
                }
            }

            if (count($arr_lokasi_kerja) > 0) {
                for ($i = 0; $i < count($arr_lokasi_kerja); $i++) {
                    if (isset($arr_lokasi_kerja[$i])) {
                        $lokasi_kerja = str_replace("Kota ", "", str_replace("Kabupaten ", "Kab. ", $arr_lokasi_kerja[$i]));
                    } else {
                        $lokasi_kerja = "-";
                    }

                    if (strpos($lokasi_kerja, 'REMOTE') !== false) {
                        $lokasi_kerja = 'Remote';
                    }

                    $q = base64_encode($id_req . "|" . $arr_lokasi_kerja[$i]);
                    $id_fav = rand(0, 999);

                    // cek fav
                    $cekFav = $conn->prepare("SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?");
                    $cekFav->bind_param("sss", $pin, $id_req, $arr_lokasi_kerja[$i]);
                    $cekFav->execute();
                    $resultFav = $cekFav->get_result();
                    $cekFav->close();

                    $shareLink = $baseURL . 'candidate/dashboard/beranda/detailJob?q=' . $q;
                    $shareTwitter = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
                    $shareWA = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($row['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;

                    $id_salin_link = rand(100, 999);
                    $btnShare = '<div class="dropdown mt-2">
                                    <i class="bi bi-heart-fill" style="font-size: 20px; cursor: pointer;" onclick="simpanFavLowongan(' . $id_fav . ')" id="btn-fav-' . $id_fav . '"></i>
                                    <i class="bi bi-share" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 20px; float: right; cursor: pointer; padding-left: 10px;"></i>
                                    <div class="dropdown-menu dropdown-for-share">
                                        <table style="width: 100%">
                                            <tr>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://x.com/intent/tweet?text=' . urlencode($shareTwitter) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/twitter-x.svg" alt="twitter-x" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($shareLink) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/facebook.svg" alt="facebook" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $shareLink . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/linkedin.svg" alt="linkedin" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="https://wa.me/?text=' . urlencode($shareWA) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/whatsapp.svg" alt="whatsapp" width="16" height="16"></a>
                                                </td>
                                                <td style="background-color: white;padding: 5px;">
                                                    <a href="javascript:void(0)" id="btn-salin-' . $id_salin_link . '" onclick="salinLink(event, \'' . $shareLink . '\',\'' . $id_salin_link . '\')" tabindex="0" data-toggle="tooltip" data-bs-original-title="' . translate('Salin Link') . '"><i class="fas fa-link" style="font-size: 16px; color: black; vertical-align: middle;"></i></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>';

                    if ($resultFav->num_rows > 0) {
                        $output .= '<div class="col-md-4">
                                        <div class="card ' . $card_disabled . '">
                                            <div class="card-body">
                                                <table style="width: 100%;">
                                                    <tr>
                                                        <td>' . $logo_perusahaan . '</td>
                                                        <td style="text-align: right;">
                                                            <input class="form-check-input" type="checkbox" style="display: none;" value="' . $q . '" id="check-fav-' . $id_fav . '" checked>
                                                            ' . $btnShare . '
                                                        </td>
                                                    </tr>
                                                    <tr style="height: 10px;">
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="color: #023047; font-size: 18px;"><a href="../beranda/detailJob?q=' . $q . '" style="color:black"><b>' . htmlspecialchars($row['posisi']) . '</b></a></td>
                                                        <td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $waktu_lalu . '</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="color: #023047; font-size: 13px;"><a href="../beranda/detailPerusahaan?q=' . base64_encode($id_koordinator) . '">' . htmlspecialchars($nama_perusahaan) . '</a></td>
                                                        <td style="color: #0C1422; text-align: right; font-size: 13px; white-space: nowrap;">' . $tgl_posting . '</td>
                                                    </tr>
                                                </table>
                                                <table class="mt-4" style="width: 100%;">
                                                    <tr>
                                                        <td><i class="bi bi-clock"></i> ' . htmlspecialchars($tipe_pekerjaan) . '</td>
                                                        <td><i class="fas fa-map-marker-alt"></i> ' . ucwords(strtolower(htmlspecialchars($lokasi_kerja))) . '</td>
                                                        <td style="text-align: right;"><a href="../beranda/detailJob?q=' . $q . '" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important;">' . translate('Lamar') . '</a></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>';
                    }
                }
            }
        }
        $output .= "</div>";

        // Pagination Range (tampilkan hanya 5 tombol halaman)
        $max_links = 5;
        $start_page = max(1, $page - floor($max_links / 2));
        $end_page = min($total_pages, $start_page + $max_links - 1);

        // Jika halaman awal terlalu dekat dengan halaman terakhir, sesuaikan rentang
        $start_page = max(1, $end_page - $max_links + 1);

        // Tombol Pagination
        $output .= '<div class="row">
                        <div class="col-md-12">
                            <nav aria-label="Page navigation">
                                <ul class="pagination d-flex justify-content-center">';
        if ($page > 1) {
            $prev = $page - 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' aria-label='Previous' data-page='{$prev}'><span aria-hidden='true'>&laquo;</span></button>
                        </li>";
        }

        // Tampilkan rentang halaman (hanya 5 halaman maksimal)
        for ($i = $start_page; $i <= $end_page; $i++) {
            $active = ($i == $page) ? 'active' : '';
            $output .= "<li class='page-item {$active}'>
                            <button class='page-link' data-page='{$i}'>{$i}</button>
                        </li>";
        }

        if ($page < $total_pages) {
            $next = $page + 1;
            $output .= "<li class='page-item'>
                            <button class='page-link' data-page='{$next}'><span aria-hidden='true'>&raquo;</span></button>
                        </li>";
        }

        $output .= '    </ul>
                    </nav>
                </div>
            </div>';
    } else {
        $output = '<div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i> ' . translate('Tidak ada lowongan pekerjaan') . '.
                            </div>
                        </div>
                    </div>';
    }

    $data = array();
    $data['html'] = $output;
    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'uploadFotoProfil') {
    $data = array();

    $status = "gagal";
    $message = translate('Upload foto profil gagal dilakukan.');
    $urlFoto = "";
    $created_at = date("Y-m-d H:i:s");

    $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
    $recaptcha_response = $_POST['recaptcha_response'];

    // Verify token with Google
    $verify_url = "https://www.google.com/recaptcha/api/siteverify";
    $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
    $response_data = json_decode($response);

    if ($response_data->success && $response_data->score >= 0.5) {
        if (isset($_FILES['file']['name'])) {
            if (file_exists($_FILES['file']['tmp_name']) || is_uploaded_file($_FILES['file']['tmp_name'])) {
                try {
                    // Mulai proses
                    $conn->begin_transaction();

                    // cek apakah user terdaftar atau tidak
                    $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
                    $sql->bind_param("s", $pin);
                    $sql->execute();
                    $result = $sql->get_result();
                    $sql->close();

                    if ($result->num_rows == 0) {
                        throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
                    }

                    // get file profil yang lama
                    $row = mysqli_fetch_array($result);
                    $img = $row['foto'];

                    if ($img != "") {
                        if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $img)) {
                            // hapus file profil yang lama
                            $result = $s3->deleteObject([
                                'Bucket' => $bucket,
                                'Key'    => 'kandidat/foto-profil/' . $img
                            ]);
                        }
                    }

                    // upload file
                    $file_tmp = $_FILES['file']['tmp_name'];
                    $fileName = $_FILES['file']['name'];
                    $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                    $nama_file = 'FP-' . $pin . '.' . $ext;

                    $s3->putObject([
                        'Bucket' => $bucket,
                        'Key'    => 'kandidat/foto-profil/' . $nama_file,
                        'SourceFile' => $file_tmp
                    ]);

                    // cek apakah file sudah ke upload atau belum
                    if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $nama_file)) {
                        // update nama file di table kandidat
                        $update = $conn->prepare("UPDATE `users_kandidat` SET `foto` = ?, `update_at` = ? WHERE `pin` = ?");
                        $update->bind_param("sss",  $nama_file, $created_at, $pin);

                        if ($update->execute()) {
                            $update->close();

                            // buat link untuk foto profile
                            $cmd = $s3->getCommand('GetObject', [
                                'Bucket' => $bucket,
                                'Key'    => 'kandidat/foto-profil/' . $nama_file
                            ]);

                            $request = $s3->createPresignedRequest($cmd, '+24 hours');
                            $urlFoto = (string) $request->getUri();

                            // Mengubah nilai img pada session
                            $_SESSION['users']['img'] = $urlFoto;

                            // simpan log aktivitas
                            $messages = 'Melakukan update foto profil.';
                            $extra_info = "Kandidat";
                            $level = "INFO";
                            logActivity($conn, $pin, $level, $messages, $extra_info);

                            // Jika semua query berhasil, commit transaksi
                            $conn->commit();

                            $status = "success";
                            $message = translate('Foto profil berhasil disimpan.');
                        } else {
                            throw new Exception(translate('Upload foto profil gagal dilakukan.'));
                        }
                    } else {
                        throw new Exception(translate('Upload foto profil gagal dilakukan.'));
                    }
                } catch (Exception $e) {
                    // Jika ada error, rollback proses
                    $conn->rollback();

                    $message = $e->getMessage();
                }
            }
        }
    } else {
        $message = translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti');
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['data'] = $urlFoto;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateProfile') {
    $data = array();

    $status = "gagal";
    $message = translate('Update profil gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
            }

            $row = mysqli_fetch_array($result);
            $email_lama = $row['email'];

            // cek keterangan update
            if (!isset($_POST['ket'])) {
                throw new Exception(translate('Update profil gagal dilakukan.'));
            }

            if ($_POST['ket'] == 'updateNoTelepon') {
                if (isset($_POST['no_telepon'])) {
                    $no_telepon = $_POST['no_telepon'];

                    // cek apakah no telepon sudah terdaftar atau belum
                    $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE no_telp = ?");
                    $sql->bind_param("s", $no_telepon);
                    $sql->execute();
                    $result = $sql->get_result();
                    $sql->close();

                    if ($result->num_rows > 0) {
                        throw new Exception(translate('Nomor telepon sudah terdaftar.'));
                    }

                    // update no telepon di table users kandidat
                    $update = $conn->prepare("UPDATE `users_kandidat` SET `no_telp` = ?, `update_at` = ? WHERE `pin` = ?");
                    $update->bind_param("sss",  $no_telepon, $created_at, $pin);

                    if ($update->execute()) {
                        $update->close();

                        // update no telepon di table rh
                        $update = $conn->prepare("UPDATE `rh` SET `no_telepon` = ?, `updated_at` = ? WHERE `id` = ?");
                        $update->bind_param("sss",  $no_telepon, $created_at, $pin);

                        if ($update->execute()) {
                            $update->close();

                            // simpan log aktivitas
                            $messages = 'Melakukan update nomor telepon.';
                            $extra_info = "Kandidat";
                            $level = "INFO";
                            logActivity($conn, $pin, $level, $messages, $extra_info);

                            // Jika semua query berhasil, commit transaksi
                            $conn->commit();

                            $status = "success";
                            $message = translate('Nomor telepon berhasil disimpan.');
                        } else {
                            throw new Exception(translate('Update nomor telepon gagal dilakukan.'));
                        }
                    } else {
                        throw new Exception(translate('Update nomor telepon gagal dilakukan.'));
                    }
                } else {
                    throw new Exception(translate('Update profil gagal dilakukan.'));
                }
            } elseif ($_POST['ket'] == 'updateEmail') {
                if (isset($_POST['email']) && isset($_POST['otp'])) {
                    $email = $_POST['email'];
                    $otp = $_POST['otp'];

                    // cek apakah email sudah terdaftar atau belum
                    $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE email = ?");
                    $sql->bind_param("s", $email);
                    $sql->execute();
                    $result = $sql->get_result();
                    $sql->close();

                    if ($result->num_rows > 0) {
                        throw new Exception(translate('Email sudah terdaftar.'));
                    }

                    // cek otp
                    $timezone = "SET time_zone = '+07:00'";
                    $conn->query($timezone);

                    $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? AND email_lama = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
                    $sql->bind_param("sss", $otp, $email, $email_lama);
                    $sql->execute();
                    $result = $sql->get_result();
                    $sql->close();

                    if ($result->num_rows > 0) {
                        // Update Status OTP
                        $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                        $update->bind_param("ss", $otp, $email);

                        if ($update->execute()) {
                            $update->close();
                        } else {
                            throw new Exception(translate('Update profil gagal dilakukan.'));
                        }
                    } else {
                        throw new Exception(translate('OTP Salah.'));
                    }

                    // update email di table users kandidat
                    $update = $conn->prepare("UPDATE `users_kandidat` SET `email` = ?, `update_at` = ? WHERE `pin` = ?");
                    $update->bind_param("sss",  $email, $created_at, $pin);

                    if ($update->execute()) {
                        $update->close();

                        // update email di table rh
                        $update = $conn->prepare("UPDATE `rh` SET `email` = ?, `updated_at` = ? WHERE `id` = ?");
                        $update->bind_param("sss",  $email, $created_at, $pin);

                        if ($update->execute()) {
                            $update->close();

                            // update session users email
                            $_SESSION['users']['email'] = $email;

                            // simpan log aktivitas
                            $messages = 'Melakukan update email.';
                            $extra_info = "Kandidat";
                            $level = "INFO";
                            logActivity($conn, $pin, $level, $messages, $extra_info);

                            // Jika semua query berhasil, commit transaksi
                            $conn->commit();

                            $status = "success";
                            $message = translate('Email berhasil disimpan.');
                        } else {
                            throw new Exception(translate('Update email gagal dilakukan.'));
                        }
                    } else {
                        throw new Exception(translate('Update email gagal dilakukan.'));
                    }
                } else {
                    throw new Exception(translate('Update profil gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('Update profil gagal dilakukan.'));
            }
        } else {
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'sendOTPUpdateEmail') {
    if ($_SERVER["REQUEST_METHOD"] === "POST") {
        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // tambahan
            $tgl = date('Y-m-d H:i:s');
            $warna    = '#fbb116';

            // function buat kode verifikasi
            function create_kodever($length)
            {
                $data = '1234567890';
                $string = '';
                for ($i = 0; $i < $length; $i++) {
                    $pos = rand(0, strlen($data) - 1);
                    $string .= $data[$pos];
                }
                return $string;
            }

            $status = "";
            $message = "";

            try {
                // Mulai proses
                $conn->begin_transaction();

                // cek apakah user terdaftar atau tidak
                $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
                $sql->bind_param("s", $pin);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows == 0) {
                    throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
                }

                $row = mysqli_fetch_array($result);
                $email_lama = $row['email'];

                if (!isset($_POST['email']) || !isset($_POST['no_hp_pic'])) {
                    throw new Exception(translate('Kode OTP gagal dikirim.'));
                }

                // get from post data users
                $email = $_POST['email'];
                $no_hp_pic = $_POST['no_hp_pic'];

                $del = $conn->prepare("DELETE FROM kode_verifikasi where email = ?");
                $del->bind_param("s", $email);
                $del->execute();
                $del->close();

                // Cek nomor otp
                $sqlCode = "SELECT code FROM kode_verifikasi";
                $queryCode = $conn->query($sqlCode);

                // Generate nomor otp
                do {
                    $code = create_kodever(5);
                } while ($code == $queryCode);

                // cek email apakah sudah aktif
                $sql = $conn->prepare("SELECT * FROM `users_kandidat` WHERE email = ? AND `status` = 'Active'");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    throw new Exception(translate('Email sudah terdaftar.'));
                }

                // cek email apakah sudah terdaftar
                $sql = $conn->prepare("SELECT * FROM `kode_verifikasi` WHERE email = ? AND `status` = 'Pending'");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows == 0) {
                    $insert = $conn->prepare("INSERT INTO `kode_verifikasi` (`code`,`email`, `email_lama`,`status`,`date`) VALUES (?, ?, ?, 'Pending', ?)");
                    $insert->bind_param("ssss", $code, $email, $email_lama, $tgl);

                    if ($insert->execute()) {
                        $insert->close();

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = translate('Periksa email Anda untuk melihat kode verifikasi akun.');
                    } else {
                        throw new Exception(translate('Kode OTP gagal dikirim.'));
                    }
                } else {
                    $row = mysqli_fetch_array($result);
                    $code = $row['code'];
                }

                $emailParams = [
                    'Destination' => [
                        'ToAddresses' => [$email],
                    ],
                    'Message' => [
                        'Body' => [
                            'Html' => ['Data' => '<!DOCTYPE html
                                            PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                        <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                        <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                        <head>
                                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                            <!--[if !mso]><!-->
                                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                            <!--<![endif]-->
                                            <meta name="viewport" content="width=device-width">
                                            <style type="text/css">
                                                @media only screen and (min-width: 620px) {
                                                    .wrapper {
                                                        min-width: 600px !important
                                                    }

                                                    .wrapper h1 {}

                                                    .wrapper h1 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper h2 {}

                                                    .wrapper h2 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper h3 {}

                                                    .column {}

                                                    .wrapper .size-8 {
                                                        font-size: 8px !important;
                                                        line-height: 14px !important
                                                    }

                                                    .wrapper .size-9 {
                                                        font-size: 9px !important;
                                                        line-height: 16px !important
                                                    }

                                                    .wrapper .size-10 {
                                                        font-size: 10px !important;
                                                        line-height: 18px !important
                                                    }

                                                    .wrapper .size-11 {
                                                        font-size: 11px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-12 {
                                                        font-size: 12px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-13 {
                                                        font-size: 13px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-14 {
                                                        font-size: 14px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-15 {
                                                        font-size: 15px !important;
                                                        line-height: 23px !important
                                                    }

                                                    .wrapper .size-16 {
                                                        font-size: 16px !important;
                                                        line-height: 24px !important
                                                    }

                                                    .wrapper .size-17 {
                                                        font-size: 17px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-18 {
                                                        font-size: 18px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-20 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper .size-22 {
                                                        font-size: 22px !important;
                                                        line-height: 31px !important
                                                    }

                                                    .wrapper .size-24 {
                                                        font-size: 24px !important;
                                                        line-height: 32px !important
                                                    }

                                                    .wrapper .size-26 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper .size-28 {
                                                        font-size: 28px !important;
                                                        line-height: 36px !important
                                                    }

                                                    .wrapper .size-30 {
                                                        font-size: 30px !important;
                                                        line-height: 38px !important
                                                    }

                                                    .wrapper .size-32 {
                                                        font-size: 32px !important;
                                                        line-height: 40px !important
                                                    }

                                                    .wrapper .size-34 {
                                                        font-size: 34px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-36 {
                                                        font-size: 36px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-40 {
                                                        font-size: 40px !important;
                                                        line-height: 47px !important
                                                    }

                                                    .wrapper .size-44 {
                                                        font-size: 44px !important;
                                                        line-height: 50px !important
                                                    }

                                                    .wrapper .size-48 {
                                                        font-size: 48px !important;
                                                        line-height: 54px !important
                                                    }

                                                    .wrapper .size-56 {
                                                        font-size: 56px !important;
                                                        line-height: 60px !important
                                                    }

                                                    .wrapper .size-64 {
                                                        font-size: 64px !important;
                                                        line-height: 63px !important
                                                    }
                                                }
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    margin: 0;
                                                    padding: 0;
                                                }

                                                table {
                                                    border-collapse: collapse;
                                                    table-layout: fixed;
                                                }

                                                * {
                                                    line-height: inherit;
                                                }

                                                [x-apple-data-detectors],
                                                [href^="tel"],
                                                [href^="sms"] {
                                                    color: inherit !important;
                                                    text-decoration: none !important;
                                                }

                                                .wrapper .footer__share-button a:hover,
                                                .wrapper .footer__share-button a:focus {
                                                    color: #ffffff !important;
                                                }

                                                .btn a:hover,
                                                .btn a:focus,
                                                .footer__share-button a:hover,
                                                .footer__share-button a:focus,
                                                .email-footer__links a:hover,
                                                .email-footer__links a:focus {
                                                    opacity: 0.8;
                                                }

                                                .preheader,
                                                .header,
                                                .layout,
                                                .column {
                                                    transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                                }

                                                .preheader td {
                                                    padding-bottom: 8px;
                                                }

                                                .layout,
                                                div.header {
                                                    max-width: 400px !important;
                                                    -fallback-width: 95% !important;
                                                    width: calc(100% - 20px) !important;
                                                }

                                                div.preheader {
                                                    max-width: 360px !important;
                                                    -fallback-width: 90% !important;
                                                    width: calc(100% - 60px) !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    Float: none !important;
                                                }

                                                .column {
                                                    max-width: 400px !important;
                                                    width: 100% !important;
                                                }

                                                .fixed-width.has-border {
                                                    max-width: 402px !important;
                                                }

                                                .fixed-width.has-border .layout__inner {
                                                    box-sizing: border-box;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 50% !important;
                                                }

                                                .ie .btn {
                                                    width: 100%;
                                                }

                                                [owa] .column div,
                                                [owa] .column button {
                                                    display: block !important;
                                                }

                                                .ie .column,
                                                [owa] .column,
                                                .ie .gutter,
                                                [owa] .gutter {
                                                    display: table-cell;
                                                    float: none !important;
                                                    vertical-align: top;
                                                }

                                                .ie div.preheader,
                                                [owa] div.preheader,
                                                .ie .email-footer,
                                                [owa] .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .ie .snippet,
                                                [owa] .snippet,
                                                .ie .webversion,
                                                [owa] .webversion {
                                                    width: 280px !important;
                                                }

                                                .ie div.header,
                                                [owa] div.header,
                                                .ie .layout,
                                                [owa] .layout,
                                                .ie .one-col .column,
                                                [owa] .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .ie .fixed-width.has-border,
                                                [owa] .fixed-width.has-border,
                                                .ie .has-gutter.has-border,
                                                [owa] .has-gutter.has-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .ie .two-col .column,
                                                [owa] .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .ie .three-col .column,
                                                [owa] .three-col .column,
                                                .ie .narrow,
                                                [owa] .narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .ie .wide,
                                                [owa] .wide {
                                                    width: 400px !important;
                                                }

                                                .ie .two-col.has-gutter .column,
                                                [owa] .two-col.x_has-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .ie .three-col.has-gutter .column,
                                                [owa] .three-col.x_has-gutter .column,
                                                .ie .has-gutter .narrow,
                                                [owa] .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .ie .has-gutter .wide,
                                                [owa] .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .ie .two-col.has-gutter.has-border .column,
                                                [owa] .two-col.x_has-gutter.x_has-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .ie .three-col.has-gutter.has-border .column,
                                                [owa] .three-col.x_has-gutter.x_has-border .column,
                                                .ie .has-gutter.has-border .narrow,
                                                [owa] .has-gutter.x_has-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .ie .has-gutter.has-border .wide,
                                                [owa] .has-gutter.x_has-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }

                                                .ie .fixed-width .layout__inner {
                                                    border-left: 0 none white !important;
                                                    border-right: 0 none white !important;
                                                }

                                                .ie .layout__edges {
                                                    display: none;
                                                }

                                                .mso .layout__edges {
                                                    font-size: 0;
                                                }

                                                .layout-fixed-width,
                                                .mso .layout-full-width {
                                                    background-color: #ffffff;
                                                }

                                                @media only screen and (min-width: 620px) {

                                                    .column,
                                                    .gutter {
                                                        display: table-cell;
                                                        Float: none !important;
                                                        vertical-align: top;
                                                    }

                                                    div.preheader,
                                                    .email-footer {
                                                        max-width: 560px !important;
                                                        width: 560px !important;
                                                    }

                                                    .snippet,
                                                    .webversion {
                                                        width: 280px !important;
                                                    }

                                                    div.header,
                                                    .layout,
                                                    .one-col .column {
                                                        max-width: 600px !important;
                                                        width: 600px !important;
                                                    }

                                                    .fixed-width.has-border,
                                                    .fixed-width.ecxhas-border,
                                                    .has-gutter.has-border,
                                                    .has-gutter.ecxhas-border {
                                                        max-width: 602px !important;
                                                        width: 602px !important;
                                                    }

                                                    .two-col .column {
                                                        max-width: 300px !important;
                                                        width: 300px !important;
                                                    }

                                                    .three-col .column,
                                                    .column.narrow {
                                                        max-width: 200px !important;
                                                        width: 200px !important;
                                                    }

                                                    .column.wide {
                                                        width: 400px !important;
                                                    }

                                                    .two-col.has-gutter .column,
                                                    .two-col.ecxhas-gutter .column {
                                                        max-width: 290px !important;
                                                        width: 290px !important;
                                                    }

                                                    .three-col.has-gutter .column,
                                                    .three-col.ecxhas-gutter .column,
                                                    .has-gutter .narrow {
                                                        max-width: 188px !important;
                                                        width: 188px !important;
                                                    }

                                                    .has-gutter .wide {
                                                        max-width: 394px !important;
                                                        width: 394px !important;
                                                    }

                                                    .two-col.has-gutter.has-border .column,
                                                    .two-col.ecxhas-gutter.ecxhas-border .column {
                                                        max-width: 292px !important;
                                                        width: 292px !important;
                                                    }

                                                    .three-col.has-gutter.has-border .column,
                                                    .three-col.ecxhas-gutter.ecxhas-border .column,
                                                    .has-gutter.has-border .narrow,
                                                    .has-gutter.ecxhas-border .narrow {
                                                        max-width: 190px !important;
                                                        width: 190px !important;
                                                    }

                                                    .has-gutter.has-border .wide,
                                                    .has-gutter.ecxhas-border .wide {
                                                        max-width: 396px !important;
                                                        width: 396px !important;
                                                    }
                                                }

                                                @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                                only screen and (min--moz-device-pixel-ratio: 2),
                                                only screen and (-o-min-device-pixel-ratio: 2/1),
                                                only screen and (min-device-pixel-ratio: 2),
                                                only screen and (min-resolution: 192dpi),
                                                only screen and (min-resolution: 2dppx) {
                                                    .fblike {
                                                        background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .tweet {
                                                        background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .linkedinshare {
                                                        background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .forwardtoafriend {
                                                        background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }
                                                }

                                                @media (max-width: 321px) {
                                                    .fixed-width.has-border .layout__inner {
                                                        border-width: 1px 0 !important;
                                                    }

                                                    .layout,
                                                    .column {
                                                        min-width: 320px !important;
                                                        width: 320px !important;
                                                    }

                                                    .border {
                                                        display: none;
                                                    }
                                                }

                                                .mso div {
                                                    border: 0 none white !important;
                                                }

                                                .mso .w560 .divider {
                                                    Margin-left: 260px !important;
                                                    Margin-right: 260px !important;
                                                }

                                                .mso .w360 .divider {
                                                    Margin-left: 160px !important;
                                                    Margin-right: 160px !important;
                                                }

                                                .mso .w260 .divider {
                                                    Margin-left: 110px !important;
                                                    Margin-right: 110px !important;
                                                }

                                                .mso .w160 .divider {
                                                    Margin-left: 60px !important;
                                                    Margin-right: 60px !important;
                                                }

                                                .mso .w354 .divider {
                                                    Margin-left: 157px !important;
                                                    Margin-right: 157px !important;
                                                }

                                                .mso .w250 .divider {
                                                    Margin-left: 105px !important;
                                                    Margin-right: 105px !important;
                                                }

                                                .mso .w148 .divider {
                                                    Margin-left: 54px !important;
                                                    Margin-right: 54px !important;
                                                }

                                                .mso .size-8,
                                                .ie .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important;
                                                }

                                                .mso .size-9,
                                                .ie .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important;
                                                }

                                                .mso .size-10,
                                                .ie .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important;
                                                }

                                                .mso .size-11,
                                                .ie .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-12,
                                                .ie .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-13,
                                                .ie .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-14,
                                                .ie .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-15,
                                                .ie .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important;
                                                }

                                                .mso .size-16,
                                                .ie .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important;
                                                }

                                                .mso .size-17,
                                                .ie .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-18,
                                                .ie .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-20,
                                                .ie .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important;
                                                }

                                                .mso .size-22,
                                                .ie .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important;
                                                }

                                                .mso .size-24,
                                                .ie .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important;
                                                }

                                                .mso .size-26,
                                                .ie .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important;
                                                }

                                                .mso .size-28,
                                                .ie .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important;
                                                }

                                                .mso .size-30,
                                                .ie .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important;
                                                }

                                                .mso .size-32,
                                                .ie .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important;
                                                }

                                                .mso .size-34,
                                                .ie .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-36,
                                                .ie .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-40,
                                                .ie .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important;
                                                }

                                                .mso .size-44,
                                                .ie .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important;
                                                }

                                                .mso .size-48,
                                                .ie .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important;
                                                }

                                                .mso .size-56,
                                                .ie .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important;
                                                }

                                                .mso .size-64,
                                                .ie .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important;
                                                }
                                            </style>

                                            <!--[if !mso]><!-->
                                            <style type="text/css">
                                                @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    background-color: #fff
                                                }

                                                .logo a:hover,
                                                .logo a:focus {
                                                    color: #1e2e3b !important
                                                }

                                                .mso .layout-has-border {
                                                    border-top: 1px solid #ccc;
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .layout-has-bottom-border {
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .border,
                                                .ie .border {
                                                    background-color: #ccc
                                                }

                                                .mso h1,
                                                .ie h1 {}

                                                .mso h1,
                                                .ie h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .mso h2,
                                                .ie h2 {}

                                                .mso h2,
                                                .ie h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .mso h3,
                                                .ie h3 {}

                                                .mso .layout__inner,
                                                .ie .layout__inner {}

                                                .mso .footer__share-button p {}

                                                .mso .footer__share-button p {
                                                    font-family: Cabin, Avenir, sans-serif
                                                }
                                            </style>
                                            <meta name="robots" content="noindex,nofollow">
                                            </meta>
                                            <meta property="og:title" content="Mail v.01">
                                            </meta>
                                        </head>

                                        <body
                                            style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                            <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                                <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                    <tr style="border-collapse:collapse;">
                                                        <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                            <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                        bgcolor="transparent">
                                                                        <div>
                                                                            <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                                cellspacing="0" width="600"
                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                                <tr style="border-collapse:collapse;">
                                                                                    <td align="left"
                                                                                        style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                            <tr style="border-collapse:collapse;">
                                                                                                <td width="560" align="center" valign="top"
                                                                                                    style="padding:0;Margin:0;">
                                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" style="padding:0;Margin:0;">
                                                                                                                <a target="_blank" href="https://digitalcv.id"
                                                                                                                    style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                    <img src="https://digitalcv.id/assets/images/logo/logoDcv2.png"
                                                                                                                        alt
                                                                                                                        style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                        height="131">
                                                                                                                </a>
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" height="42"
                                                                                                                style="padding:0;Margin:0;"></td>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center" bgcolor="transparent"
                                                                        style="padding:0;Margin:0;background-color:transparent;">
                                                                        <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <h1
                                                                                                                style="Margin:0;line-height:36px;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;font-size:30px;font-style:normal;font-weight:bold;color:#fbb116;">
                                                                                                                Kode Verifikasi Registrasi</h1>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center"
                                                                                                            style="padding:0;Margin:0;padding-top:20px;">
                                                                                                            <p
                                                                                                                style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <p style="color:#131313;text-align: justify;">
                                                                                                                Terima kasih telah mendaftarkan email Anda pada
                                                                                                                digitalcv.id. Untuk mengaktifkan akun, silahkan
                                                                                                                masukkan kode verifikasi berikut, yang berlaku
                                                                                                                selama 5 menit.</p>
                                                                                                            <p
                                                                                                                style="font-size:25px;color:#131313;text-align: center;margin-bottom: 20px;">
                                                                                                                <b style="color:#fbb116;text-align: center;">
                                                                                                                    ' . $code . ' </b>
                                                                                                            </p>
                                                                                                            <p style="color:#131313;text-align: justify;">
                                                                                                                Ini adalah kode rahasia.
                                                                                                                Mohon untuk tidak membagikan kode OTP ini
                                                                                                                ke orang lain.
                                                                                                                <br>
                                                                                                                Jika kamu tidak sedang mendaftarkan akun
                                                                                                                digitalcv.id segera hubungi
                                                                                                                0811 779 7779
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                        background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                        <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="left" style="padding:0;Margin:0;">
                                                                                                            <hr>
                                                                                                            <div>digitalcv<br>
                                                                                                                Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                                Barat<br>
                                                                                                                WhatsApp: 0811 779 7779<br>
                                                                                                            </div>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="600" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="40"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </body>
                                        </table>
                                        <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                            src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                        </body>

                                        </html>'],
                        ],
                        'Subject' => ['Data' => 'Kode Verifikasi Registrasi'],
                    ],
                    'Source' => '<EMAIL>',
                ];

                if (canSendEmail($conn, $email, 'OTP Update Email')) {
                    try {
                        $result = $SesClient->sendEmail($emailParams);
                        $message = translate('Periksa email Anda untuk melihat kode verifikasi akun.');
                    } catch (AwsException $e) {
                        echo translate('Kode OTP gagal dikirim.') . " " . $e->getAwsErrorMessage() . "\n";
                    }
                } else {
                    throw new Exception(translate('Kode OTP gagal dikirim.'));
                }
            } catch (Exception $e) {
                // Jika ada error, rollback proses
                $conn->rollback();

                $status = "gagal";
                $message = $e->getMessage();
            }
        } else {
            // Failed reCAPTCHA verification
            $status = "error";
            $message = translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti');
        }
    } else {
        http_response_code(403);
        $status = "error";
        $status = "Invalid request.";
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateKataSandi') {
    $data = array();

    $status = "gagal";
    $message = translate('Update kata sandi gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
            }

            if (!isset($_POST['password_lama']) || !isset($_POST['password']) || !isset($_POST['kon_password']) || !isset($_POST['otp'])) {
                throw new Exception(translate('Update kata sandi gagal dilakukan.'));
            }

            $password_lama = $_POST['password_lama'];
            $password = hashPassword($_POST['password']);
            $kon_password = $_POST['kon_password'];
            $otp = $_POST['otp'];

            // cek password lama
            $row = mysqli_fetch_array($result);
            $storedHash = $row['password'];
            $email = $row['email'];

            // cek otp
            $timezone = "SET time_zone = '+07:00'";
            $conn->query($timezone);

            $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
            $sql->bind_param("ss", $otp, $email);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // Update Status OTP
                $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                $update->bind_param("ss", $otp, $email);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Update kata sandi gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('OTP Salah.'));
            }

            // verifikasi password
            if (verifyPassword($password_lama, $storedHash)) {
                // Validasi panjang password
                if (strlen($_POST['password']) < 8) {
                    throw new Exception(translate('Password minimal 8 karakter.'));
                }

                // Huruf besar password
                if (!preg_match('/[A-Z]/', $_POST['password'])) {
                    throw new Exception(translate('Password harus mengandung huruf besar.'));
                }

                // Huruf kecil password
                if (!preg_match('/[a-z]/', $_POST['password'])) {
                    throw new Exception(translate('Password harus mengandung huruf kecil.'));
                }

                // Angka password
                if (!preg_match('/\d/', $_POST['password'])) {
                    throw new Exception(translate('Password harus mengandung angka.'));
                }

                // Karakter khusus password
                if (!preg_match('/[^A-Za-z0-9]/', $_POST['password'])) {
                    throw new Exception(translate('Password harus mengandung karakter khusus.'));
                }

                // update password
                $update = $conn->prepare("UPDATE `users_kandidat` SET `password` = ?, `update_at` = ? WHERE `pin` = ?");
                $update->bind_param("sss",  $password, $created_at, $pin);
                if ($update->execute()) {
                    $update->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan update kata sandi.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = translate('Kata sandi berhasil disimpan.');
                } else {
                    throw new Exception(translate('Update kata sandi gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('Password lama salah. Silakan sesuaikan kembali.'));
            }
        } else {
            // Failed reCAPTCHA verification
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateVisibilitas') {
    $data = array();

    $status = "gagal";
    $message = translate('Tidak dapat menyimpan data. Silakan untuk login kembali.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek value
            if (!isset($_POST['status'])) {
                throw new Exception(translate('Update data gagal dilakukan.'));
            }

            $visibilitas = $_POST['status'];

            // update password
            $update = $conn->prepare("UPDATE `users_kandidat` SET `visibilitas` = ? WHERE `pin` = ?");
            $update->bind_param("ss",  $visibilitas, $pin);
            if ($update->execute()) {
                $update->close();

                // simpan log aktivitas
                $messages = 'Melakukan update visibilitas.';
                $extra_info = "Kandidat";
                $level = "INFO";
                logActivity($conn, $pin, $level, $messages, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = translate('Data berhasil disimpan.');
            } else {
                throw new Exception(translate('Update data gagal dilakukan.'));
            }
        } else {
            // Failed reCAPTCHA verification
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'terimaPengajuanPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses lamaran gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
        $q = decrypt_url($_POST['q'], $key);
        $arr = explode("|", $q);
        $pin = $arr[0];
        $id_req = $arr[1];

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan untuk login kembali.'));
        }

        $lokasi = "-";
        $pertanyaan_khusus = "";
        $where_lokasi = "";

        // cek apakah lowongan masih ada
        $sql = $conn->prepare("SELECT * FROM list_request WHERE id_req = ?");
        $sql->bind_param("s", $id_req);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan untuk login kembali.'));
        }

        $row = mysqli_fetch_array($result);
        $arr_lokasi = explode(",", $row['lokasi_kerja']);
        if (isset($arr_lokasi[0])) {
            $lokasi = $arr_lokasi[0];
        }

        $lokasi = strtoupper(str_replace("KAB. ", "KABUPATEN ", $lokasi));
        $where_lokasi = "%" . $lokasi . "%";

        // cek apakah lowongan ada
        $get = $conn->prepare("SELECT id_koordinator, perusahaan, posisi, `status` FROM list_request WHERE id_req = ? AND lokasi_kerja LIKE ?");
        $get->bind_param("ss", $id_req, $where_lokasi);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $id_koordinator = "-";
        $perusahaan = "-";
        $posisi = "-";
        if ($result->num_rows > 0) {
            $rowData = mysqli_fetch_array($result);
            if ($rowData['status'] == 'On Proccess') {
                $id_koordinator = $rowData['id_koordinator'];
                $perusahaan = $rowData['perusahaan'];
                $posisi = $rowData['posisi'];
            } else {
                $status = "doesn't exist";
                throw new Exception(translate('Lowongan sudah tidak tersedia.'));
            }
        } else {
            throw new Exception(translate('Proses lamaran gagal dilakukan.'));
        }

        // cek apakah user sudah melakukan proses lamaran pada lowongan
        $get = $conn->prepare("SELECT id_koordinator FROM users_lamar WHERE id_gestalt = ? AND id_req = ? AND lokasi = ?");
        $get->bind_param("sss", $pin, $id_req, $lokasi);
        echo $get->error;
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $status = "exist";
            throw new Exception(translate('Anda sudah melamar lowongan ini.'));
        }

        // Generate id lamar
        $sqlCek = "SELECT id_lamar FROM users_lamar";
        $queryCek = $conn->query($sqlCek);

        do {
            $id_lamar = getIDLamar();
        } while ($id_lamar == $queryCek);

        // insert proses lamar
        $insert = $conn->prepare("INSERT INTO `users_lamar`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`, `lokasi`, `jawaban_k_khusus`) 
        VALUES (?, ?, ?, ?, 'On Proccess Digitalcv', ?, ?, ?)");
        $insert->bind_param("sssssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at, $lokasi, $pertanyaan_khusus);

        if ($insert->execute()) {
            $insert->close();
        } else {
            throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
        }

        // insert riwayat lamar
        $insert = $conn->prepare("INSERT INTO `users_lamar_history`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
        VALUES (?, ?, ?, ?, 'Lamaran Dikirim', ?)");
        $insert->bind_param("sssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at);

        if ($insert->execute()) {
            $insert->close();
        } else {
            throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
        }

        // lakukan proses screening
        $hasil_screening = ProsesScreening($conn, $pin, $id_req);
        if ($hasil_screening) {
            // simpan log aktivitas
            $messages = 'Menerima pengajuan lowongan di ' . $perusahaan . ' dengan posisi ' . $posisi . ' [' . $id_req . '].';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Proses lamaran berhasil dilakukan.');
        } else {
            throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    if ($status == 'success') {
        $konten = '<div class="card-hero" style="padding: 10px 10px !important; border-radius: 20px !important;">
                        <div class=" index-header-content my-2">
                            <h5 class="text-center text-index-header fw-bold my-3">' . translate('Terima Kasih') . '</h5>
                        </div>

                        <div class="row" style="text-align: center;">
                            <p>' . translate('Lamaran Anda sudah kami proses. Untuk lebih lanjutnya bisa login menggunakan akun digitalcv Anda.') . '</p>
                        </div>
                    </div>';
    } elseif ($status == 'exist') {
        $konten = '<div class="alert alert-primary" role="alert">
                        <i class="fas fa-info-circle"></i> ' . translate('Anda sudah melamar untuk posisi ini. Untuk lebih lanjutnya bisa login menggunakan akun digitalcv Anda.') . '
                    </div>';
    } elseif ($status == "doesn't exist") {
        $konten = '<div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> ' . translate('Lamaran untuk posisi ini sudah tidak berlaku.') . '
                    </div>';
    } else {
        $konten = '<div class="alert alert-error" role="alert">
                        <i class="fas fa-exclamation-circle"></i> ' . translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator') . '
                    </div>';
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['konten'] = $konten;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'tutupAkun') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses hapus akun gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
            }

            if (!isset($_POST['password'])) {
                throw new Exception(translate('Proses hapus akun gagal dilakukan.'));
            }

            $password = $_POST['password'];

            // cek password lama
            $row = mysqli_fetch_array($result);
            $storedHash = $row['password'];

            // verifikasi password
            if (verifyPassword($password, $storedHash)) {
                $deleted_at = date("Y-m-d", strtotime($created_at . ' +7 days')) . " 00:00:00";

                // insert ke daftar hapus akun
                $insert = $conn->prepare("INSERT INTO `list_hapus_akun` (`id_akun`,`status`,`deleted_at`,`created_at`) VALUES (?, 'pending', ?, ?)");
                $insert->bind_param("sss", $pin, $deleted_at, $created_at);
                if ($insert->execute()) {
                    $insert->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan proses penghapusan akun.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = translate('Proses hapus akun berhasil dilakukan.');
                } else {
                    throw new Exception(translate('Proses hapus akun gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('Password salah. Silakan sesuaikan kembali.'));
            }
        } else {
            // Failed reCAPTCHA verification
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'batalTutupAkun') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses pembatalan hapus akun gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
            }

            // hapus data pembatalan akun
            $delete = $conn->prepare("DELETE FROM list_hapus_akun WHERE id_akun = ? AND `status` = 'pending'");
            $delete->bind_param("s", $pin);
            if ($delete->execute()) {
                $delete->close();

                // simpan log aktivitas
                $messages = 'Melakukan proses pembatalan penghapusan akun.';
                $extra_info = "Kandidat";
                $level = "INFO";
                logActivity($conn, $pin, $level, $messages, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = translate('Proses pembatalan hapus akun berhasil dilakukan.');
            } else {
                throw new Exception(translate('Proses pembatalan hapus akun gagal dilakukan.'));
            }
        } else {
            // Failed reCAPTCHA verification
            throw new Exception(translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'sendOTPUpdatePassword') {
    if ($_SERVER["REQUEST_METHOD"] === "POST") {
        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // tambahan
            $tgl = date('Y-m-d H:i:s');
            $warna    = '#fbb116';

            // function buat kode verifikasi
            function create_kodever($length)
            {
                $data = '1234567890';
                $string = '';
                for ($i = 0; $i < $length; $i++) {
                    $pos = rand(0, strlen($data) - 1);
                    $string .= $data[$pos];
                }
                return $string;
            }

            $status = "";
            $message = "";

            try {
                // Mulai proses
                $conn->begin_transaction();

                // get email kandidat
                $get = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
                $get->bind_param("s", $pin);
                $get->execute();
                $result = $get->get_result();
                $get->close();

                // get data kandidat
                $row = mysqli_fetch_array($result);
                $email = $row['email'];

                $del = $conn->prepare("DELETE FROM kode_verifikasi where email = ?");
                $del->bind_param("s", $email);
                $del->execute();
                $del->close();

                // Cek nomor otp
                $sqlCode = "SELECT code FROM kode_verifikasi";
                $queryCode = $conn->query($sqlCode);

                // Generate nomor otp
                do {
                    $code = create_kodever(5);
                } while ($code == $queryCode);

                // cek email apakah sudah terdaftar
                $sql = $conn->prepare("SELECT * FROM `kode_verifikasi` WHERE email = ? AND `status` = 'Pending'");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows == 0) {
                    $insert = $conn->prepare("INSERT INTO `kode_verifikasi` (`code`,`email`,`status`,`date`) VALUES (?, ?, 'Pending', ?)");
                    $insert->bind_param("sss", $code, $email, $tgl);

                    if ($insert->execute()) {
                        $insert->close();

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = translate('Periksa email Anda untuk melihat kode ubah password.');
                    } else {
                        throw new Exception(translate('Kode OTP gagal dikirim.'));
                    }
                } else {
                    $row = mysqli_fetch_array($result);
                    $code = $row['code'];
                }

                $emailParams = [
                    'Destination' => [
                        'ToAddresses' => [$email],
                    ],
                    'Message' => [
                        'Body' => [
                            'Html' => ['Data' => '<!DOCTYPE html
                                            PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                        <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                        <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                        <head>
                                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                            <!--[if !mso]><!-->
                                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                            <!--<![endif]-->
                                            <meta name="viewport" content="width=device-width">
                                            <style type="text/css">
                                                @media only screen and (min-width: 620px) {
                                                    .wrapper {
                                                        min-width: 600px !important
                                                    }

                                                    .wrapper h1 {}

                                                    .wrapper h1 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper h2 {}

                                                    .wrapper h2 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper h3 {}

                                                    .column {}

                                                    .wrapper .size-8 {
                                                        font-size: 8px !important;
                                                        line-height: 14px !important
                                                    }

                                                    .wrapper .size-9 {
                                                        font-size: 9px !important;
                                                        line-height: 16px !important
                                                    }

                                                    .wrapper .size-10 {
                                                        font-size: 10px !important;
                                                        line-height: 18px !important
                                                    }

                                                    .wrapper .size-11 {
                                                        font-size: 11px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-12 {
                                                        font-size: 12px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-13 {
                                                        font-size: 13px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-14 {
                                                        font-size: 14px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-15 {
                                                        font-size: 15px !important;
                                                        line-height: 23px !important
                                                    }

                                                    .wrapper .size-16 {
                                                        font-size: 16px !important;
                                                        line-height: 24px !important
                                                    }

                                                    .wrapper .size-17 {
                                                        font-size: 17px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-18 {
                                                        font-size: 18px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-20 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper .size-22 {
                                                        font-size: 22px !important;
                                                        line-height: 31px !important
                                                    }

                                                    .wrapper .size-24 {
                                                        font-size: 24px !important;
                                                        line-height: 32px !important
                                                    }

                                                    .wrapper .size-26 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper .size-28 {
                                                        font-size: 28px !important;
                                                        line-height: 36px !important
                                                    }

                                                    .wrapper .size-30 {
                                                        font-size: 30px !important;
                                                        line-height: 38px !important
                                                    }

                                                    .wrapper .size-32 {
                                                        font-size: 32px !important;
                                                        line-height: 40px !important
                                                    }

                                                    .wrapper .size-34 {
                                                        font-size: 34px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-36 {
                                                        font-size: 36px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-40 {
                                                        font-size: 40px !important;
                                                        line-height: 47px !important
                                                    }

                                                    .wrapper .size-44 {
                                                        font-size: 44px !important;
                                                        line-height: 50px !important
                                                    }

                                                    .wrapper .size-48 {
                                                        font-size: 48px !important;
                                                        line-height: 54px !important
                                                    }

                                                    .wrapper .size-56 {
                                                        font-size: 56px !important;
                                                        line-height: 60px !important
                                                    }

                                                    .wrapper .size-64 {
                                                        font-size: 64px !important;
                                                        line-height: 63px !important
                                                    }
                                                }
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    margin: 0;
                                                    padding: 0;
                                                }

                                                table {
                                                    border-collapse: collapse;
                                                    table-layout: fixed;
                                                }

                                                * {
                                                    line-height: inherit;
                                                }

                                                [x-apple-data-detectors],
                                                [href^="tel"],
                                                [href^="sms"] {
                                                    color: inherit !important;
                                                    text-decoration: none !important;
                                                }

                                                .wrapper .footer__share-button a:hover,
                                                .wrapper .footer__share-button a:focus {
                                                    color: #ffffff !important;
                                                }

                                                .btn a:hover,
                                                .btn a:focus,
                                                .footer__share-button a:hover,
                                                .footer__share-button a:focus,
                                                .email-footer__links a:hover,
                                                .email-footer__links a:focus {
                                                    opacity: 0.8;
                                                }

                                                .preheader,
                                                .header,
                                                .layout,
                                                .column {
                                                    transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                                }

                                                .preheader td {
                                                    padding-bottom: 8px;
                                                }

                                                .layout,
                                                div.header {
                                                    max-width: 400px !important;
                                                    -fallback-width: 95% !important;
                                                    width: calc(100% - 20px) !important;
                                                }

                                                div.preheader {
                                                    max-width: 360px !important;
                                                    -fallback-width: 90% !important;
                                                    width: calc(100% - 60px) !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    Float: none !important;
                                                }

                                                .column {
                                                    max-width: 400px !important;
                                                    width: 100% !important;
                                                }

                                                .fixed-width.has-border {
                                                    max-width: 402px !important;
                                                }

                                                .fixed-width.has-border .layout__inner {
                                                    box-sizing: border-box;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 50% !important;
                                                }

                                                .ie .btn {
                                                    width: 100%;
                                                }

                                                [owa] .column div,
                                                [owa] .column button {
                                                    display: block !important;
                                                }

                                                .ie .column,
                                                [owa] .column,
                                                .ie .gutter,
                                                [owa] .gutter {
                                                    display: table-cell;
                                                    float: none !important;
                                                    vertical-align: top;
                                                }

                                                .ie div.preheader,
                                                [owa] div.preheader,
                                                .ie .email-footer,
                                                [owa] .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .ie .snippet,
                                                [owa] .snippet,
                                                .ie .webversion,
                                                [owa] .webversion {
                                                    width: 280px !important;
                                                }

                                                .ie div.header,
                                                [owa] div.header,
                                                .ie .layout,
                                                [owa] .layout,
                                                .ie .one-col .column,
                                                [owa] .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .ie .fixed-width.has-border,
                                                [owa] .fixed-width.has-border,
                                                .ie .has-gutter.has-border,
                                                [owa] .has-gutter.has-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .ie .two-col .column,
                                                [owa] .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .ie .three-col .column,
                                                [owa] .three-col .column,
                                                .ie .narrow,
                                                [owa] .narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .ie .wide,
                                                [owa] .wide {
                                                    width: 400px !important;
                                                }

                                                .ie .two-col.has-gutter .column,
                                                [owa] .two-col.x_has-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .ie .three-col.has-gutter .column,
                                                [owa] .three-col.x_has-gutter .column,
                                                .ie .has-gutter .narrow,
                                                [owa] .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .ie .has-gutter .wide,
                                                [owa] .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .ie .two-col.has-gutter.has-border .column,
                                                [owa] .two-col.x_has-gutter.x_has-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .ie .three-col.has-gutter.has-border .column,
                                                [owa] .three-col.x_has-gutter.x_has-border .column,
                                                .ie .has-gutter.has-border .narrow,
                                                [owa] .has-gutter.x_has-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .ie .has-gutter.has-border .wide,
                                                [owa] .has-gutter.x_has-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }

                                                .ie .fixed-width .layout__inner {
                                                    border-left: 0 none white !important;
                                                    border-right: 0 none white !important;
                                                }

                                                .ie .layout__edges {
                                                    display: none;
                                                }

                                                .mso .layout__edges {
                                                    font-size: 0;
                                                }

                                                .layout-fixed-width,
                                                .mso .layout-full-width {
                                                    background-color: #ffffff;
                                                }

                                                @media only screen and (min-width: 620px) {

                                                    .column,
                                                    .gutter {
                                                        display: table-cell;
                                                        Float: none !important;
                                                        vertical-align: top;
                                                    }

                                                    div.preheader,
                                                    .email-footer {
                                                        max-width: 560px !important;
                                                        width: 560px !important;
                                                    }

                                                    .snippet,
                                                    .webversion {
                                                        width: 280px !important;
                                                    }

                                                    div.header,
                                                    .layout,
                                                    .one-col .column {
                                                        max-width: 600px !important;
                                                        width: 600px !important;
                                                    }

                                                    .fixed-width.has-border,
                                                    .fixed-width.ecxhas-border,
                                                    .has-gutter.has-border,
                                                    .has-gutter.ecxhas-border {
                                                        max-width: 602px !important;
                                                        width: 602px !important;
                                                    }

                                                    .two-col .column {
                                                        max-width: 300px !important;
                                                        width: 300px !important;
                                                    }

                                                    .three-col .column,
                                                    .column.narrow {
                                                        max-width: 200px !important;
                                                        width: 200px !important;
                                                    }

                                                    .column.wide {
                                                        width: 400px !important;
                                                    }

                                                    .two-col.has-gutter .column,
                                                    .two-col.ecxhas-gutter .column {
                                                        max-width: 290px !important;
                                                        width: 290px !important;
                                                    }

                                                    .three-col.has-gutter .column,
                                                    .three-col.ecxhas-gutter .column,
                                                    .has-gutter .narrow {
                                                        max-width: 188px !important;
                                                        width: 188px !important;
                                                    }

                                                    .has-gutter .wide {
                                                        max-width: 394px !important;
                                                        width: 394px !important;
                                                    }

                                                    .two-col.has-gutter.has-border .column,
                                                    .two-col.ecxhas-gutter.ecxhas-border .column {
                                                        max-width: 292px !important;
                                                        width: 292px !important;
                                                    }

                                                    .three-col.has-gutter.has-border .column,
                                                    .three-col.ecxhas-gutter.ecxhas-border .column,
                                                    .has-gutter.has-border .narrow,
                                                    .has-gutter.ecxhas-border .narrow {
                                                        max-width: 190px !important;
                                                        width: 190px !important;
                                                    }

                                                    .has-gutter.has-border .wide,
                                                    .has-gutter.ecxhas-border .wide {
                                                        max-width: 396px !important;
                                                        width: 396px !important;
                                                    }
                                                }

                                                @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                                only screen and (min--moz-device-pixel-ratio: 2),
                                                only screen and (-o-min-device-pixel-ratio: 2/1),
                                                only screen and (min-device-pixel-ratio: 2),
                                                only screen and (min-resolution: 192dpi),
                                                only screen and (min-resolution: 2dppx) {
                                                    .fblike {
                                                        background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .tweet {
                                                        background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .linkedinshare {
                                                        background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .forwardtoafriend {
                                                        background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }
                                                }

                                                @media (max-width: 321px) {
                                                    .fixed-width.has-border .layout__inner {
                                                        border-width: 1px 0 !important;
                                                    }

                                                    .layout,
                                                    .column {
                                                        min-width: 320px !important;
                                                        width: 320px !important;
                                                    }

                                                    .border {
                                                        display: none;
                                                    }
                                                }

                                                .mso div {
                                                    border: 0 none white !important;
                                                }

                                                .mso .w560 .divider {
                                                    Margin-left: 260px !important;
                                                    Margin-right: 260px !important;
                                                }

                                                .mso .w360 .divider {
                                                    Margin-left: 160px !important;
                                                    Margin-right: 160px !important;
                                                }

                                                .mso .w260 .divider {
                                                    Margin-left: 110px !important;
                                                    Margin-right: 110px !important;
                                                }

                                                .mso .w160 .divider {
                                                    Margin-left: 60px !important;
                                                    Margin-right: 60px !important;
                                                }

                                                .mso .w354 .divider {
                                                    Margin-left: 157px !important;
                                                    Margin-right: 157px !important;
                                                }

                                                .mso .w250 .divider {
                                                    Margin-left: 105px !important;
                                                    Margin-right: 105px !important;
                                                }

                                                .mso .w148 .divider {
                                                    Margin-left: 54px !important;
                                                    Margin-right: 54px !important;
                                                }

                                                .mso .size-8,
                                                .ie .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important;
                                                }

                                                .mso .size-9,
                                                .ie .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important;
                                                }

                                                .mso .size-10,
                                                .ie .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important;
                                                }

                                                .mso .size-11,
                                                .ie .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-12,
                                                .ie .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-13,
                                                .ie .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-14,
                                                .ie .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-15,
                                                .ie .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important;
                                                }

                                                .mso .size-16,
                                                .ie .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important;
                                                }

                                                .mso .size-17,
                                                .ie .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-18,
                                                .ie .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-20,
                                                .ie .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important;
                                                }

                                                .mso .size-22,
                                                .ie .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important;
                                                }

                                                .mso .size-24,
                                                .ie .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important;
                                                }

                                                .mso .size-26,
                                                .ie .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important;
                                                }

                                                .mso .size-28,
                                                .ie .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important;
                                                }

                                                .mso .size-30,
                                                .ie .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important;
                                                }

                                                .mso .size-32,
                                                .ie .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important;
                                                }

                                                .mso .size-34,
                                                .ie .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-36,
                                                .ie .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-40,
                                                .ie .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important;
                                                }

                                                .mso .size-44,
                                                .ie .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important;
                                                }

                                                .mso .size-48,
                                                .ie .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important;
                                                }

                                                .mso .size-56,
                                                .ie .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important;
                                                }

                                                .mso .size-64,
                                                .ie .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important;
                                                }
                                            </style>

                                            <!--[if !mso]><!-->
                                            <style type="text/css">
                                                @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    background-color: #fff
                                                }

                                                .logo a:hover,
                                                .logo a:focus {
                                                    color: #1e2e3b !important
                                                }

                                                .mso .layout-has-border {
                                                    border-top: 1px solid #ccc;
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .layout-has-bottom-border {
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .border,
                                                .ie .border {
                                                    background-color: #ccc
                                                }

                                                .mso h1,
                                                .ie h1 {}

                                                .mso h1,
                                                .ie h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .mso h2,
                                                .ie h2 {}

                                                .mso h2,
                                                .ie h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .mso h3,
                                                .ie h3 {}

                                                .mso .layout__inner,
                                                .ie .layout__inner {}

                                                .mso .footer__share-button p {}

                                                .mso .footer__share-button p {
                                                    font-family: Cabin, Avenir, sans-serif
                                                }
                                            </style>
                                            <meta name="robots" content="noindex,nofollow">
                                            </meta>
                                            <meta property="og:title" content="Mail v.01">
                                            </meta>
                                        </head>

                                        <body
                                            style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                            <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                                <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                    <tr style="border-collapse:collapse;">
                                                        <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                            <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                        bgcolor="transparent">
                                                                        <div>
                                                                            <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                                cellspacing="0" width="600"
                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                                <tr style="border-collapse:collapse;">
                                                                                    <td align="left"
                                                                                        style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                            <tr style="border-collapse:collapse;">
                                                                                                <td width="560" align="center" valign="top"
                                                                                                    style="padding:0;Margin:0;">
                                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" style="padding:0;Margin:0;">
                                                                                                                <a target="_blank" href="https://digitalcv.id"
                                                                                                                    style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                    <img src="https://digitalcv.id/assets/images/logo/logoDcv2.png"
                                                                                                                        alt
                                                                                                                        style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                        height="131">
                                                                                                                </a>
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" height="42"
                                                                                                                style="padding:0;Margin:0;"></td>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center" bgcolor="transparent"
                                                                        style="padding:0;Margin:0;background-color:transparent;">
                                                                        <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <h1
                                                                                                                style="Margin:0;line-height:36px;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;font-size:30px;font-style:normal;font-weight:bold;color:#fbb116;">
                                                                                                                Kode Ubah Kata Sandi</h1>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center"
                                                                                                            style="padding:0;Margin:0;padding-top:20px;">
                                                                                                            <p
                                                                                                                style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <p style="color:#131313;text-align: justify;">
                                                                                                                Silakan masukkan kode berikut untuk mengubah password, yang berlaku selama 5 menit.</p>
                                                                                                            <p
                                                                                                                style="font-size:25px;color:#131313;text-align: center;margin-bottom: 20px;">
                                                                                                                <b style="color:#fbb116;text-align: center;">
                                                                                                                    ' . $code . ' </b>
                                                                                                            </p>
                                                                                                            <p style="color:#131313;text-align: justify;">
                                                                                                                Ini adalah kode rahasia.
                                                                                                                Mohon untuk tidak membagikan kode OTP ini
                                                                                                                ke orang lain.
                                                                                                                <br>
                                                                                                                Jika kamu tidak sedang mengubah kata sandi akun
                                                                                                                digitalcv.id segera hubungi
                                                                                                                0811 779 7779
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                        background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                        <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="left" style="padding:0;Margin:0;">
                                                                                                            <hr>
                                                                                                            <div>digitalcv<br>
                                                                                                                Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                                Barat<br>
                                                                                                                WhatsApp: 0811 779 7779<br>
                                                                                                            </div>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="600" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="40"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </body>
                                        </table>
                                        <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                            src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                        </body>

                                        </html>'],
                        ],
                        'Subject' => ['Data' => 'Kode Ubah Kata Sandi'],
                    ],
                    'Source' => '<EMAIL>',
                ];

                if (canSendEmail($conn, $email, 'OTP Update Password')) {
                    try {
                        $result = $SesClient->sendEmail($emailParams);
                        $message = translate('Periksa email Anda untuk melihat kode ubah kata sandi.');
                    } catch (AwsException $e) {
                        echo translate('Kode OTP gagal dikirim.') . " " . $e->getAwsErrorMessage() . "\n";
                    }
                } else {
                    throw new Exception(translate('Kode OTP gagal dikirim.'));
                }
            } catch (Exception $e) {
                // Jika ada error, rollback proses
                $conn->rollback();

                $status = "gagal";
                $message = $e->getMessage();
            }
        } else {
            // Failed reCAPTCHA verification
            $status = "error";
            $message = translate('Verifikasi reCAPTCHA gagal, silakan coba kembali nanti');
        }
    } else {
        http_response_code(403);
        $status = "error";
        $status = "Invalid request.";
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

$conn->close();
exit;
