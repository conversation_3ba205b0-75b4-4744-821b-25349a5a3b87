# JWT Authentication Implementation for digitalcv.php

## Overview

This document describes the implementation of JWT-based authentication for the `digitalcv.php` endpoint, which displays a candidate's digital CV in a printable format.

## Changes Made

### 1. JWT Authentication Integration

- Added `include '../../jwt_helper.php'` to include JW<PERSON> helper functions
- Added `$userData = requireAuth()` to enforce JWT authentication
- Replaced `$_POST` parameter dependencies with JWT-derived user data

### 2. Parameter Replacement

**Before (Using POST parameters):**

```php
if (isset($_POST['id_koordinator']) && isset($_GET['q'])) {
    // ... validation logic
    $id_koordinator = $_POST['id_koordinator'];
    $divisi = $_POST['divisi'];
    $user_id = $_POST['id'];
}
```

**After (Using JWT data):**

```php
// Check if 'q' parameter exists in GET request (encrypted candidate ID)
if (isset($_GET['q'])) {
    // ... validation logic
    // Get user data from JWT
    $id_koordinator = $userData->id_koordinator;
    $divisi = $userData->divisi;
    $user_id = $userData->id;
}
```

### 3. Security Improvements

- **Authentication**: Now requires valid JWT token in Authorization header
- **Parameter Validation**: Still validates encrypted candidate ID from GET parameter 'q'
- **Access Control**: Maintains existing access control logic based on candidate availability
- **Activity Logging**: Continues to log user activities using JWT-derived user ID

## API Usage

### Request Format

```
GET /api/mobile/company/digitalcv.php?q={encrypted_candidate_id}
Headers:
    Authorization: Bearer {jwt_token}
    Content-Type: application/json
```

### Parameters

- **q** (required): Encrypted candidate ID to display CV for
- **JWT Token** (required): Valid access token containing user authentication data

### JWT Payload Structure

The JWT token must contain:

```json
{
    "id_koordinator": "string",
    "divisi": "string",
    "id": "string",
    "company": "string",
    "iat": timestamp,
    "exp": timestamp
}
```

### Response Format

**Success:**

- HTTP 200 OK
- Returns HTML page with candidate's digital CV formatted for printing

**Authentication Error:**

```json
{
  "success": false,
  "message": "Unauthorized: Invalid or missing token"
}
```

**Access Denied:**

```html
<script>
  alert("Anda tidak dapat mengakses halaman ini.");
  window.close();
</script>
```

## Security Features

### 1. JWT Token Validation

- Validates token signature using secret key
- Checks token expiration
- Verifies token structure and required claims

### 2. Encrypted Candidate ID

- Candidate ID is encrypted using AES-256-CBC
- Decrypted server-side to prevent tampering
- Invalid encrypted IDs result in access denial

### 3. Database Validation

- Validates candidate exists in database before displaying CV
- Cross-references candidate data with encrypted ID

### 4. Activity Logging

- Logs all CV viewing activities
- Records user ID, candidate ID, and timestamp
- Maintains audit trail for compliance

## Migration Benefits

### 1. Enhanced Security

- **Token-based Authentication**: More secure than session-based authentication
- **Stateless Design**: Reduces server-side session management overhead
- **Tamper-proof**: JWT tokens are cryptographically signed

### 2. Improved Scalability

- **Stateless Authentication**: Enables horizontal scaling
- **Reduced Database Load**: No session table lookups required
- **Better Performance**: Faster authentication validation

### 3. Better API Design

- **Consistent Authentication**: Same pattern across all endpoints
- **Clear Authorization**: Explicit token requirements
- **Standardized Responses**: Consistent error handling

## Testing

Use the provided test script `test_jwt_digitalcv.php` to verify the implementation:

```bash
php test_jwt_digitalcv.php
```

The test script will:

1. Authenticate and obtain JWT token
2. Test CV viewing with valid encrypted candidate ID
3. Test error handling for invalid/missing parameters
4. Verify response format and status codes

## Error Handling

### 1. Invalid/Missing JWT Token

- Returns 401 Unauthorized with JSON error message
- Prevents access to sensitive candidate data

### 2. Invalid Encrypted Candidate ID

- Shows access denied alert and closes window
- Logs failed access attempt

### 3. Candidate Not Found

- Shows access denied alert and closes window
- Maintains security by not revealing database structure

## Backward Compatibility

**Breaking Changes:**

- Endpoint now requires JWT authentication
- POST parameters no longer accepted
- Session-based authentication removed

**Migration Required:**

- Client applications must obtain JWT tokens before accessing endpoint
- Update API calls to include Authorization header
- Remove POST parameter dependencies

## Maintenance Notes

### 1. JWT Configuration

- JWT secret key configured in `.env` file
- Token expiration time: 1 hour (configurable)
- Refresh token support available via refresh-token.php

### 2. Encryption Key

- AES-256-CBC encryption key hardcoded in decrypt_url function
- Consider moving to environment configuration for better security

### 3. Database Dependencies

- Requires `koordinator_pic` table for user authentication
- Uses `users_kandidat` and `rh` tables for candidate data
- Activity logging depends on logActivity function in database.php

## Example Implementation

```javascript
// Frontend JavaScript example
async function viewCandidateCV(candidateId) {
  const token = localStorage.getItem("jwt_token");

  try {
    const response = await fetch(
      `/api/mobile/company/digitalcv.php?q=${candidateId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.ok) {
      // Open in new window for printing
      const html = await response.text();
      const newWindow = window.open();
      newWindow.document.write(html);
    } else {
      throw new Error("Failed to load CV");
    }
  } catch (error) {
    console.error("Error viewing CV:", error);
    alert("Failed to load candidate CV");
  }
}
```
