//start function untuk mengambil angka saja
function hanyaAngka(evt) {
  var charCode = evt.which ? evt.which : event.keyCode;

  if (charCode < 48 || charCode > 57) return false;
  return true;
}
//end function untuk mengambil angka saja

//start fungsi untuk kirim OTP
$("#btn-otp").on("click", function () {
  let email = $("#email").val();

  function validateEmail($email) {
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
    return emailReg.test($email);
  }

  if (!validateEmail(email) || email == "") {
    toastr["error"](window.appData.alertEmail);
  } else {
    var button = $("#btn-otp");
    var seconds = 60; // Countdown time
    button
      .prop("disabled", true)
      .text(window.appData.kirimOTP + " (60s)")
      .css("background-color", "#BDBDBD");
    var countdown = setInterval(function () {
      seconds--;
      button.text(window.appData.kirimOTP + " (" + seconds + "s)");

      if (seconds <= 0) {
        clearInterval(countdown);
        button
          .prop("disabled", false)
          .text(window.appData.kirimOTP)
          .css("background-color", "#0d3b72");
      }
    }, 1000);
    $("#btn-otp").html(
      '<div class="spinner-border spinner-border-sm" role="status" style="padding-right: 5px;"><span class="visually-hidden">Loading...</span></div> &nbsp;Proses'
    );
    $("#btn-otp").prop("disabled", true);

    grecaptcha.ready(function () {
      grecaptcha
        .execute("6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF", {
          action: "submit",
        })
        .then(function (token) {
          $.ajax({
            url: "controller/account-deletion/otp-account-deletion.php",
            type: "post",
            data: {
              email: email,
              recaptcha_response: token,
            },
            success: function (result) {
              var obj = JSON.parse(JSON.stringify(result));

              if (obj.status != "success") {
                toastr["error"](obj.message);

                clearInterval(countdown);
                button
                  .prop("disabled", false)
                  .text(window.appData.kirimOTP)
                  .css("background-color", "#0d3b72");
              } else {
                Swal.fire({
                  title: window.appData.berhasil,
                  text: obj.message,
                  icon: "success",
                }).then((result) => {});
              }
            },
          });
        });
    });
  }
});
//end fungsi untuk kirim OTP
