<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';

// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;
$id_pegawai = $userData->id;

// Fungsi keamanan untuk sanitasi output
function sanitizeOutput($data)
{
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeOutput($value);
        }
    } elseif (is_string($data)) {
        // Cek apakah ini adalah URL S3 yang valid
        if (strpos($data, 'amazonaws.com') !== false || strpos($data, 's3.') !== false) {
            // Jangan encode URL S3, tapi pastikan tidak ada script injection
            $data = preg_replace('/[<>"\']/', '', $data);
            return $data;
        }
        return $data;
    }
    return $data;
}

// Fungsi untuk validasi input
function validateInput($input, $type = 'string', $maxLength = null)
{
    if ($input === null || $input === '') {
        return false;
    }

    $input = trim($input);

    switch ($type) {
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
        case 'phone':
            return preg_match('/^[\d\-\+\(\)\s]+$/', $input);
        case 'alphanumeric':
            return preg_match('/^[a-zA-Z0-9]+$/', $input);
        case 'numeric':
            return is_numeric($input);
        case 'filename':
            return preg_match('/^[a-zA-Z0-9\-_\.]+$/', $input);
        default:
            if ($maxLength && strlen($input) > $maxLength) {
                return false;
            }
            return true;
    }
}

// Fungsi untuk execute query yang aman
function executeSecureQuery($conn, $query, $types = "", $params = [])
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Failed to prepare statement");
        }

        if (!empty($params) && !empty($types)) {
            if (!is_array($params)) {
                $params = [$params];
            }
            if (!$stmt->bind_param($types, ...$params)) {
                throw new Exception("Failed to bind parameters");
            }
        }

        if (!$stmt->execute()) {
            throw new Exception("Failed to execute query");
        }

        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Failed to get result");
        }

        $stmt->close();
        return $result;
    } catch (Exception $e) {
        if (isset($stmt)) {
            $stmt->close();
        }
        return false;
    }
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function get_email_from_lamar($id_lamar, $conn)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT uk.email FROM users_lamar ul JOIN users_kandidat uk ON ul.id_gestalt = uk.pin WHERE ul.id_lamar = ?";
    $result = executeSecureQuery($conn, $query, "s", [$id_lamar]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['email'];
    }

    return null;
}

function get_posisi($id_req, $conn)
{
    // Validasi input
    if (!validateInput($id_req, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT posisi FROM list_request WHERE id_req = ?";
    $result = executeSecureQuery($conn, $query, "s", [$id_req]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['posisi'];
    }

    return null;
}

function get_nama_kandidat($id_lamar, $conn)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT uk.nama_lengkap FROM users_lamar ul JOIN users_kandidat uk ON ul.id_gestalt = uk.pin WHERE ul.id_lamar = ?";
    $result = executeSecureQuery($conn, $query, "s", [$id_lamar]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['nama_lengkap'];
    }

    return null;
}

function update_proses_status_lowongan($id_lamar, $conn, $text)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric') || !validateInput($text, 'string')) {
        return 'No';
    }

    $status_update = '';
    if ($text == 'SCREENING') {
        $status_update = "On Proccess Digitalcv";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "On Proccess Interview";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "On Proccess Psikotes";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "On Proccess Validasi";
    } elseif ($text == 'VALIDASI') {
        $status_update = "On Proccess Offering";
    } elseif ($text == 'OFFERING') {
        $status_update = "On Proccess Medical";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Sudah Diterima";
    } else {
        return 'No';
    }

    $query = "UPDATE users_lamar SET status = ? WHERE id_lamar = ?";
    $result = executeSecureQuery($conn, $query, "ss", [$status_update, $id_lamar]);

    return ($result !== false) ? 'Ok' : 'No';
}

function update_close_status_lowongan($id_lamar, $conn, $text)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric') || !validateInput($text, 'string')) {
        return 'No';
    }

    $status_update = '';
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } else {
        return 'No';
    }

    $query = "UPDATE users_lamar SET status = ? WHERE id_lamar = ?";
    $result = executeSecureQuery($conn, $query, "ss", [$status_update, $id_lamar]);

    return ($result !== false) ? 'Ok' : 'No';
}

function update_terima_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    // Validasi input
    if (
        !validateInput($id_lamar, 'alphanumeric') || !validateInput($text, 'string') ||
        !validateInput($id_req, 'alphanumeric') || !validateInput($company, 'alphanumeric')
    ) {
        return 'No';
    }

    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);

    if ($pincode === null) {
        return 'No';
    }

    $status_update = '';
    if ($text == 'SCREENING') {
        $status_update = "Terima Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Terima Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Terima Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Terima Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Terima Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Terima Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Terima Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Terima Rekrutmen";
    } else {
        return 'No';
    }

    // Cek apakah sudah ada data
    $checkQuery = "SELECT id_lamar FROM users_lamar_history WHERE id_lamar = ? AND status LIKE ?";
    $likeText = "%$text%";
    $checkResult = executeSecureQuery($conn, $checkQuery, "ss", [$id_lamar, $likeText]);

    if ($checkResult === false) {
        return 'No';
    }

    if ($checkResult->num_rows > 0) {
        // Update existing record
        $updateQuery = "UPDATE users_lamar_history SET status = ? WHERE id_lamar = ?";
        $result = executeSecureQuery($conn, $updateQuery, "ss", [$status_update, $id_lamar]);
    } else {
        // Insert new record
        $insertQuery = "INSERT INTO users_lamar_history (id_lamar, id_gestalt, id_req, id_koordinator, status, tgl) VALUES (?, ?, ?, ?, ?, ?)";
        $result = executeSecureQuery($conn, $insertQuery, "ssssss", [$id_lamar, $pincode, $id_req, $company, $status_update, $tgl]);
    }

    return ($result !== false) ? 'Ok' : 'No';
}

function update_tolak_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    // Validasi input
    if (
        !validateInput($id_lamar, 'alphanumeric') || !validateInput($text, 'string') ||
        !validateInput($id_req, 'alphanumeric') || !validateInput($company, 'alphanumeric')
    ) {
        return 'No';
    }

    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);

    if ($pincode === null) {
        return 'No';
    }

    $status_update = '';
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Tolak Rekrutmen";
    } else {
        return 'No';
    }

    // Cek apakah sudah ada data
    $checkQuery = "SELECT id_lamar FROM users_lamar_history WHERE id_lamar = ? AND status LIKE ?";
    $likeText = "%$text%";
    $checkResult = executeSecureQuery($conn, $checkQuery, "ss", [$id_lamar, $likeText]);

    if ($checkResult === false) {
        return 'No';
    }

    if ($checkResult->num_rows > 0) {
        // Update existing record
        $updateQuery = "UPDATE users_lamar_history SET status = ? WHERE id_lamar = ?";
        $result = executeSecureQuery($conn, $updateQuery, "ss", [$status_update, $id_lamar]);
    } else {
        // Insert new record
        $insertQuery = "INSERT INTO users_lamar_history (id_lamar, id_gestalt, id_req, id_koordinator, status, tgl) VALUES (?, ?, ?, ?, ?, ?)";
        $result = executeSecureQuery($conn, $insertQuery, "ssssss", [$id_lamar, $pincode, $id_req, $company, $status_update, $tgl]);
    }

    return ($result !== false) ? 'Ok' : 'No';
}

function get_pin($id_lamar, $conn)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT uk.pin FROM users_lamar ul JOIN users_kandidat uk ON ul.id_gestalt = uk.pin WHERE ul.id_lamar = ?";
    $result = executeSecureQuery($conn, $query, "s", [$id_lamar]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['pin'];
    }

    return null;
}

function get_company($company, $conn)
{
    // Validasi input
    if (!validateInput($company, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT label, img, alamat FROM koordinator WHERE id_koordinator = ?";
    $result = executeSecureQuery($conn, $query, "s", [$company]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return [
            'nama_perusahaan' => $row['label'],
            'img' => $row['img'],
            'alamat' => $row['alamat']
        ];
    }

    return null;
}


function cek_feedback_hrd($conn, $id_lamar)
{
    // Validasi input
    if (!validateInput($id_lamar, 'alphanumeric')) {
        return null;
    }

    $query = "SELECT approval FROM feedback WHERE id_lamar = ? AND review = 'HRD'";
    $result = executeSecureQuery($conn, $query, "s", [$id_lamar]);

    if ($result !== false && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['approval'];
    }

    return null;
}

function getInitials($name)
{
    $words = explode(' ', strtoupper($name));
    $initials = '';
    foreach ($words as $w) {
        if ($w !== '') {
            $initials .= $w[0];
        }
    }
    return substr($initials, 0, 2); // maksimal 2 huruf
}

function selisihTahunBulan($tanggal_awal, $tanggal_akhir)
{
    $tanggal_awal = DateTime::createFromFormat('m-Y', $tanggal_awal);
    $tanggal_awal = $tanggal_awal->format('Y-m-d');

    $tanggal_akhir = DateTime::createFromFormat('m-Y', $tanggal_akhir);
    $tanggal_akhir = $tanggal_akhir->format('Y-m-d');

    $awal  = new DateTime($tanggal_awal);
    $akhir = new DateTime($tanggal_akhir);

    if ($awal > $akhir) {
        // Tukar jika tanggal awal lebih besar dari akhir
        $temp = $awal;
        $awal = $akhir;
        $akhir = $temp;
    }

    $selisih = $awal->diff($akhir);

    $tahun = $selisih->y;
    $bulan = $selisih->m;

    $output = "";

    if ($tahun > 0) {
        $output .= $tahun . " Tahun";
    }

    if ($bulan > 0) {
        if ($output !== "") {
            $output .= " ";
        }
        $output .= $bulan . " Bulan";
    }

    return $output === "" ? "0 Bulan" : $output;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

function insert_jadwal_psikotes($id_lamar, $id_gestalt, $nama, $posisi_lamar, $email, $nik, $tgl, $nama_perusahaan, $no_telepon, $foto, $jadwal_tes)
{
    // Validasi input
    if (
        !validateInput($id_lamar, 'alphanumeric') || !validateInput($id_gestalt, 'alphanumeric') ||
        !validateInput($nama, 'string') || !validateInput($email, 'email')
    ) {
        return false;
    }

    $url = 'https://psikotes.digitalcv.id/model/daftar_candidate_dcv_umum.php';
    $cek_data = [
        'id_lamar' => sanitizeOutput($id_lamar),
        'id_gestalt' => sanitizeOutput($id_gestalt),
        'nama' => sanitizeOutput($nama),
        'posisi_lamar' => sanitizeOutput($posisi_lamar),
        'email' => sanitizeOutput($email),
        'nik' => sanitizeOutput($nik),
        'tgl' => sanitizeOutput($tgl),
        'nama_perusahaan' => sanitizeOutput($nama_perusahaan),
        'no_telepon' => sanitizeOutput($no_telepon),
        'foto' => sanitizeOutput($foto),
        'jadwal_tes' => sanitizeOutput($jadwal_tes),
        'func' => 'cekapi'
    ];
    $ch = curl_init($url);

    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($cek_data));

    $headers = [
        'Content-Type: application/json',
        'key: 123456789abcdef'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        error_log('Psikotes API Request Error: ' . curl_error($ch));
        curl_close($ch);
        return false;
    }
    curl_close($ch);

    $responseData = json_decode($response);

    if (!$responseData || !isset($responseData->status) || $responseData->status !== 'success') {
        error_log('Psikotes API Error: ' . ($responseData->message ?? 'Unknown error'));
        return false;
    }

    return true;
}

// Get function from URL parameter
$func = $_GET['func'] ?? '';

if ($func == 'getReqPsikotes') {

    header('Content-Type: application/json');
    $data = [];

    // Validasi input
    $page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? min(50, max(1, (int) $_GET['page_size'])) : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? trim($_GET['q']) : "";

    // Validasi search parameter
    if (!empty($q) && !validateInput($q, 'string', 50)) {
        echo json_encode([
            "status" => false,
            "message" => "Format pencarian tidak valid",
            "data" => []
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    $searchParams = [];
    $searchTypes = "";

    if (!empty($q)) {
        $querySearch .= " AND (rh.nama LIKE ? OR lr.posisi LIKE ?)";
        $searchTerm = "%$q%";
        $searchParams = [$searchTerm, $searchTerm];
        $searchTypes = "ss";
    }

    // Query untuk data
    $sqlData = "SELECT
                    GROUP_CONCAT(ul.id_lamar) as id_lamar,
                    rh.id,
                    rh.nama,
                    GROUP_CONCAT(DISTINCT lr.posisi) as posisi_lamar
                FROM
                    users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN rh ON ul.id_gestalt = rh.id
                WHERE
                    ul.id_koordinator = ?
                    AND ul.status = 'On Proccess Psikotes'
                    $querySearch
                GROUP BY ul.id_gestalt
                ORDER BY rh.nama
                LIMIT ?, ?";

    // Prepare parameters
    $dataParams = array_merge([$id_koordinator], $searchParams, [$offset, $pageSize]);
    $dataTypes = "s" . $searchTypes . "ii";

    $queryData = executeSecureQuery($conn, $sqlData, $dataTypes, $dataParams);

    if ($queryData === false) {
        echo json_encode([
            "status" => false,
            "message" => "Terjadi kesalahan dalam mengambil data",
            "data" => []
        ], JSON_PRETTY_PRINT);
        exit;
    }

    while ($row = $queryData->fetch_assoc()) {
        $data[] = [
            'id' => sanitizeOutput($row['id']),
            'nama' => sanitizeOutput($row['nama']),
            'posisi_lamar' => sanitizeOutput(str_replace(",", ", ", $row['posisi_lamar'])),
            'id_lamar' => sanitizeOutput($row['id_lamar']),
        ];
    }

    // Query untuk total data
    $sqlTotal = "SELECT COUNT(*) AS total FROM (
                    SELECT ul.id_gestalt
                    FROM users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN rh ON ul.id_gestalt = rh.id
                    WHERE
                        ul.id_koordinator = ?
                        AND ul.status = 'On Proccess Psikotes'
                        $querySearch
                    GROUP BY ul.id_gestalt
                ) AS sub";

    // Prepare parameters for total query
    $totalParams = array_merge([$id_koordinator], $searchParams);
    $totalTypes = "s" . $searchTypes;

    $totalResult = executeSecureQuery($conn, $sqlTotal, $totalTypes, $totalParams);

    if ($totalResult === false) {
        echo json_encode([
            "status" => false,
            "message" => "Terjadi kesalahan dalam menghitung total data",
            "data" => []
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $totalRow = $totalResult->fetch_assoc();
    $totalData = (int) $totalRow['total'];
    $totalPage = ceil($totalData / $pageSize);

    // Output response
    $response = [
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => sanitizeOutput($data),
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

if ($func == 'submitReqPsikotes') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT id_pic FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah data ada
        if (!isset($_POST['id_lamar'])) {
            throw new Exception("Harap pilih kandidat terlebih dahulu.");
        }

        $id_lamar = $_POST['id_lamar'];
        $jadwal_tes = $_POST['jadwal_tes'];

        // Ensure $id_lamar is always an array
        if (!is_array($id_lamar)) {
            $id_lamar = [$id_lamar];
        }

        if (count($id_lamar) == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // Cek jumlah quota psikotes
        $getQuota = $conn->prepare("SELECT * FROM quotas_koordinator WHERE kategori = 'Psikotes' AND id_koordinator = ?");
        $getQuota->bind_param("s", $id_koordinator);
        $getQuota->execute();
        $resQuota = $getQuota->get_result();
        $getQuota->close();

        if ($resQuota->num_rows > 0) {
            $rowQuota = mysqli_fetch_array($resQuota);
            $remaining_quota = $rowQuota['remaining_quota'];
            $paid_quota = $rowQuota['paid_quota'];

            if (is_numeric($remaining_quota) && is_numeric($paid_quota)) {
                $total_quota = $remaining_quota + $paid_quota;

                // cek jika jumlah kandidat lebih dari kuota
                if (count($id_lamar) > $total_quota) {
                    throw new Exception(translate('Kuota permintaan psikotes Akun anda tidak mencukupi. Jika ingin melanjutkan, silakan lakukan pembelian kuota terlebih dahulu.'));
                }

                // cek jika jumlah kuota sudah habis
                if ($total_quota > 0) {
                    // kalkulisa sisa kuota
                    if (count($id_lamar) > $remaining_quota) {
                        $remaining_quota_now = 0;
                        $selisih = count($id_lamar) - $remaining_quota;
                        $paid_quota_now = $paid_quota - $selisih;
                    } else {
                        $remaining_quota_now = $remaining_quota - count($id_lamar);
                        $paid_quota_now = $paid_quota;
                    }

                    if ($remaining_quota_now < 0) {
                        $remaining_quota_now = 0;
                    }

                    if ($paid_quota_now < 0) {
                        $paid_quota_now = 0;
                    }

                    // update kuota psikotes
                    $updateQuota = $conn->prepare("UPDATE quotas_koordinator SET remaining_quota = ?, paid_quota = ?, last_updated = ? WHERE kategori = 'Psikotes' AND id_koordinator = ?");
                    $updateQuota->bind_param("ssss", $remaining_quota_now, $paid_quota_now, $created_at, $id_koordinator);
                    if ($updateQuota->execute()) {
                        $updateQuota->close();
                    } else {
                        throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
                    }
                } else {
                    throw new Exception(translate('Kuota permintaan psikotes Akun anda sudah habis. Jika ingin melanjutkan, silakan lakukan pembelian kuota terlebih dahulu.'));
                }
            } else {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
            }
        } else {
            throw new Exception(translate('Akun anda tidak memiliki kuota permintaan psikotes. Silakan lakukan pembelian kuota atau upgrade akun anda.'));
        }

        // buat array id lamar
        $arr = array();
        for ($i = 0; $i < count($id_lamar); $i++) {
            $temp = explode(",", base64_decode($id_lamar[$i]));
            if (count($temp) > 0) {
                for ($j = 0; $j < count($temp); $j++) {
                    $arr[] = addslashes($temp[$j]);
                }
            }
        }

        if (count($arr) == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // Buat string tanda tanya sebanyak jumlah ID
        $placeholders = implode(',', array_fill(0, count($arr), '?'));
        // update status lamaran user
        $update = $conn->prepare("UPDATE users_lamar SET `status` = 'Psikotes Gestalt' WHERE id_koordinator = ? AND id_lamar IN ($placeholders) AND `status` = 'On Proccess Psikotes'");

        // Susun tipe data (contoh semua integer: "iii")
        $types = "s" . str_repeat('s', count($arr));

        // Gabung parameter lain dan array ID jadi satu array
        $params = array_merge([$id_koordinator], $arr);

        // Binding parameter
        $update->bind_param($types, ...$params);

        if ($update->execute()) {
            $update->close();

            // simpan riwayat perubahan status lamaran users
            $insert = $conn->prepare("INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`,`pic`)
            SELECT id_lamar, id_gestalt, id_req, id_koordinator, 'Psikotes Gestalt' as `status`, ? as tgl, ? as pic FROM users_lamar WHERE id_koordinator = ? AND id_lamar IN ($placeholders) AND `status` = 'Psikotes Gestalt'");

            // Susun tipe data (contoh semua integer: "iii")
            $types = 'sss' . str_repeat('s', count($arr));

            // Gabung parameter lain dan array ID jadi satu array
            $params = array_merge([$created_at, $id_pegawai, $id_koordinator], $arr);

            // Binding parameter
            $insert->bind_param($types, ...$params);

            if ($insert->execute()) {
                $insert->close();

                // Buat placeholders untuk query selanjutnya
                $placeholders_api = implode(',', array_fill(0, count($arr), '?'));

                $sql_cek_api = "SELECT
                                    GROUP_CONCAT( ul.id_lamar ) AS id_lamar,
                                    uk.pin as id,
                                    rh.nama,
                                    GROUP_CONCAT( DISTINCT lr.posisi ) AS posisi_lamar,
                                    uk.email,
                                    IF( rh.ktp = '', uk.pin, rh.ktp ) AS nik,
                                    ul.tgl,
                                    k.label as nama_perusahaan,
                                    uk.no_telp as no_telepon,
                                    ul.id_gestalt,
                                    uk.foto
                                FROM
                                    users_lamar ul
                                    JOIN list_request lr ON lr.id_req = ul.id_req
                                    JOIN rh ON ul.id_gestalt = rh.id 
                                    JOIN koordinator k on ul.id_koordinator = k.id_koordinator
                                    JOIN users_kandidat uk on ul.id_gestalt=uk.pin
                                WHERE
                                    ul.id_koordinator = ? AND ul.status = 'Psikotes Gestalt' and ul.id_lamar in ($placeholders_api)
                                GROUP BY
                                    ul.id_gestalt 
                                ORDER BY
                                    rh.nama";

                $stmt = $conn->prepare($sql_cek_api);

                // Susun tipe data untuk binding
                $types_api = 's' . str_repeat('s', count($arr));
                $params_api = array_merge([$id_koordinator], $arr);

                $stmt->bind_param($types_api, ...$params_api);
                $stmt->execute();
                $result_cek_api = $stmt->get_result();
                $stmt->close();

                if ($result_cek_api->num_rows > 0) {
                    // output data of each row
                    while ($row_cek_api = $result_cek_api->fetch_assoc()) {
                        $id_lamar = $row_cek_api['id_lamar'];
                        $id_gestalt = $row_cek_api['id_gestalt'];
                        $nama = $row_cek_api['nama'];
                        $posisi_lamar = $row_cek_api['posisi_lamar'];
                        $email = $row_cek_api['email'];
                        $nik = $row_cek_api['nik'];
                        $tgl = $row_cek_api['tgl'];
                        $nama_perusahaan = $row_cek_api['nama_perusahaan'];
                        $no_telepon = $row_cek_api['no_telepon'];
                        $foto = $row_cek_api['foto'];
                        insert_jadwal_psikotes($id_lamar, $id_gestalt, $nama, $posisi_lamar, $email, $nik, $tgl, $nama_perusahaan, $no_telepon, $foto, $jadwal_tes);
                    }
                } else {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }

                // Activity logging sudah dilakukan di awal function

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = "Kandidat berhasil diproses.";
            } else {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'getHasilPsikotes') {
    header('Content-Type: application/json');
    $data = [];

    // Validate and sanitize input parameters
    $page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? max(1, min(100, (int) $_GET['page_size'])) : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? ($_GET['q'] ? trim($_GET['q']) : "") : "";
    $q = substr($q, 0, 50); // batasi max 50 karakter

    // Prepare search parameters
    $querySearch = "";
    $searchParams = [];
    if (!empty($q)) {
        $querySearch = " AND (
            rh.nama LIKE ? OR 
            lr.posisi LIKE ? OR 
            rh.pendidikan_terakhir LIKE ?
        )";
        $searchTerm = '%' . $q . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Query data dengan pagination
    $sqlData = "SELECT
            ul.id_lamar,
            rh.id,
            rh.nama,
            GROUP_CONCAT(DISTINCT lr.posisi) as posisi_lamar,
            IF(study.id IS NOT NULL, study.jenjang, rh.pendidikan_terakhir) as pendidikan_terakhir,
            IF(study.id IS NOT NULL, study.jurusan, '') as jurusan,
            rh.tgl_lahir
        FROM users_lamar ul
        JOIN list_request lr ON lr.id_req = ul.id_req
        JOIN rh ON ul.id_gestalt = rh.id
        LEFT JOIN riwayat_pendidikan study ON study.id = ul.id_gestalt 
            AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                FROM riwayat_pendidikan WHERE id = ul.id_gestalt
            )
        WHERE ul.id_koordinator = ?
        AND ul.status = 'Selesai Psikotes'
        $querySearch
        GROUP BY ul.id_gestalt
        ORDER BY rh.nama
        LIMIT ?, ?";

    // Prepare parameters for data query
    $dataParams = [$id_koordinator];
    $paramTypes = "s";
    
    if (!empty($searchParams)) {
        $dataParams = array_merge($dataParams, $searchParams);
        $paramTypes .= "sss";
    }

    $dataParams[] = $offset;
    $dataParams[] = $pageSize;
    $paramTypes .= "ii";

    $queryData = executeSecureQuery($conn, $sqlData, $paramTypes, $dataParams);

    while ($row = mysqli_fetch_assoc($queryData)) {
        $tgl_lahir = new DateTime($row['tgl_lahir']);
        $sekarang = new DateTime();
        $umur = $sekarang->diff($tgl_lahir)->y;

        $data[] = [
            'id_lamar' => sanitizeOutput($row['id_lamar']),
            'id' => sanitizeOutput($row['id']),
            'nama' => sanitizeOutput($row['nama']),
            'posisi_lamar' => sanitizeOutput(str_replace(",", ", ", $row['posisi_lamar'])),
            'pendidikan_terakhir' => sanitizeOutput($row['pendidikan_terakhir']),
            'jurusan' => sanitizeOutput($row['jurusan']),
            'umur' => $umur
        ];
    }

    // Hitung total data
    $sqlTotal = "SELECT COUNT(*) AS total FROM (
        SELECT ul.id_gestalt
        FROM users_lamar ul
        JOIN list_request lr ON lr.id_req = ul.id_req
        JOIN rh ON ul.id_gestalt = rh.id
        LEFT JOIN riwayat_pendidikan study ON study.id = ul.id_gestalt 
            AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                FROM riwayat_pendidikan WHERE id = ul.id_gestalt
            )
        WHERE ul.id_koordinator = ?
        AND ul.status = 'Selesai Psikotes'
        $querySearch
        GROUP BY ul.id_gestalt
    ) AS sub";

    // Prepare parameters for total query
    $totalParams = [$id_koordinator];
    $totalParamTypes = "s";

    if (!empty($searchParams)) {
        $totalParams = array_merge($totalParams, $searchParams);
        $totalParamTypes .= "sss";
    }

    $totalResult = executeSecureQuery($conn, $sqlTotal, $totalParamTypes, $totalParams);
    $totalRow = $totalResult->fetch_assoc();
    $totalData = (int) $totalRow['total'];
    $totalPage = ceil($totalData / $pageSize);

    // Output JSON response
    $response = [
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}



$conn->close();
exit;
