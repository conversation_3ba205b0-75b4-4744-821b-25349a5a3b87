<script src="../assets/plugins/jquery/jquery.min.js"></script>
<script src="../assets/plugins/jquery-ui/jquery-ui.min.js"></script>
<script>
    $.widget.bridge('uibutton', $.ui.button)
</script>
<script src="../assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="../assets/plugins/chart.js/Chart.min.js"></script>
<script src="../assets/plugins/select2/js/select2.full.min.js"></script>
<script src="../assets/plugins/sparklines/sparkline.js"></script>
<script src="../assets/plugins/jquery-knob/jquery.knob.min.js"></script>
<script src="../assets/plugins/moment/moment.min.js"></script>
<script src="../assets/plugins/daterangepicker/daterangepicker.js"></script>
<script src="../assets/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
<script src="../assets/plugins/summernote/summernote-bs4.min.js"></script>
<script src="../assets/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>
<script src="../assets/dist/js/adminlte.js"></script>
<script src="../assets/dist/js/pages/dashboard.js"></script>
<script src="../assets/dist/js/demo.js"></script>
<script src="../assets/plugins/sweetalert2/sweetalert2.min.js"></script>
<script src="../assets/plugins/toastr/toastr.min.js"></script>
<script src="../assets/plugins/datatables/jquery.dataTables.js"></script>
<script src="../assets/plugins/datatables-bs4/js/dataTables.bootstrap4.js"></script>
<script src="../assets/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="../assets/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="../assets/plugins/jszip/jszip.min.js"></script>
<script src="../assets/plugins/jsgrid/demos/db.js"></script>
<script src="../assets/plugins/jsgrid/jsgrid.min.js"></script>
<script src="../assets/js/hrd.js"></script>
<script src="../assets/js/jquery-chained.min.js"></script>
<script src="../assets/js/select2.min.js"></script>
<script src="../assets/js/Chart.min.js"></script>
<script src="../assets/js/Chart.bundle.min.js"></script>
<script src="../assets/js/popper.min.js"></script>
<script src="../assets/js/bootstrap4.min.js"></script>
<script src="../assets/js/cropper.min.js"></script>
<script src="../assets/js/bootstrap-datetimepicker.js"></script>
<script src="../assets/js/purify.min.js"></script>
<script src="https://www.google.com/recaptcha/api.js?render=6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF"></script>
<!-- WebSocket -->
<script>
    // WebSocket
    toastr.options = {
        closeButton: true,
        debug: false,
        newestOnTop: true,
        progressBar: true,
        positionClass: "toast-top-right",
        preventDuplicates: true,
        onclick: null,
        showDuration: "300",
        hideDuration: "1000",
        timeOut: "3000",
        extendedTimeOut: "1000",
        showEasing: "swing",
        hideEasing: "linear",
        showMethod: "fadeIn",
        hideMethod: "fadeOut",
    };

    const userId = "<?php echo $id_koordinator; ?>";
    const ws = new WebSocket(
        "ws://localhost:3000?userId=" + userId
    );

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data); // Mengubah JSON menjadi objek

        if (data.to == userId) {
            // Cek tipe
            if (data.tipe == 'notif') {
                // abaikan kalau cuma heartbeat
                if (typeof cekNotif === "function") {
                    // Function ada dan bisa dipanggil
                    cekNotif();
                }

                toastr.info(data.message.replace("+", ""), "Info");
            } else if (data.tipe == 'chat') {
                cekChat(data.message, data.reference_id);
            }
        }
    };

    ws.onclose = () => {
        console.log("Disconnected from WebSocket server");
    };

    function changeLang(params) {
        $.get('<?= $baseURL ?>api/changeLang.php', {
                params: params
            },
            function(response) {
                window.location.reload();
            }
        )
    }

    function safeTrim(text, limit = 25, suffix = "...") {
        // Hapus spasi di awal/akhir
        let trimmed = text.trim();

        // Jika kosong setelah di-trim → bukan emoji
        if (trimmed === "") return "";

        // Regex deteksi emoji umum (seperti versi PHP-mu)
        const emojiRegex = /[\u{1F000}-\u{1FAFF}\u{1F300}-\u{1F9FF}\u{2600}-\u{27BF}\u{FE0F}\u{1F1E6}-\u{1F1FF}]/gu;

        // Hapus emoji untuk cek apakah full emoji
        const noEmoji = trimmed.replace(emojiRegex, "");

        // Jika setelah dihapus emoji string kosong → berarti full emoji
        if (noEmoji.trim() === "") {
            limit = 10;
        }

        // Pisahkan per karakter unicode (termasuk emoji)
        const chars = Array.from(text);
        let output = "";
        let width = 0;

        for (const char of chars) {
            // Cek apakah karakter ini emoji
            if (/[\u{1F000}-\u{1FAFF}\u{2600}-\u{27BF}]/u.test(char)) {
                width += 2; // emoji lebar 2 unit
            } else {
                width += 1; // huruf biasa lebar 1 unit
            }

            if (width > limit) break;
            output += char;
        }

        if (chars.length > output.length) {
            output += suffix;
        }

        return output;
    }

    // Chat function
    function cekChat(message, reference_id) {
        const temp_date = new Date();
        const jam = String(temp_date.getHours()).padStart(2, '0');
        const menit = String(temp_date.getMinutes()).padStart(2, '0');
        waktu = jam + ':' + menit;
        ref_id = btoa(reference_id).replace(/[^a-zA-Z0-9_]/g, '');

        // Auto read
        if ($('#chat-area-' + reference_id).length > 0) {
            // Update chat area
            if (typeof scrollToBottom === "function") {
                chatArea = $('.chat-area').html();
                var message_box = '';

                // Ambil tanggal hari ini dalam format YYYY-MM-DD
                const today = new Date().toISOString().split('T')[0];
                console.log(today);

                // Cek apakah ada elemen dengan data-tanggal = hari ini
                const $todayGroup = $(`.chat-group[data-tanggal="${today}"]`);

                // Buat badge hari ini jika tidak ada
                var chatAwal = false;
                if ($todayGroup.length == 0) {
                    chatAwal = true;
                    message_box = message_box + '<div class="chat-group" data-tanggal="' + today + '">' +
                        '<div class="row">' +
                        '<div class="col-12 chat-badge" style="text-align: center;">' +
                        '<span class="badge bg-light text-dark">Hari Ini</span>' +
                        '</div>' +
                        '</div>';
                }

                let pesanHtml = message.replace(/\n/g, "<br>");
                message_box = message_box + '<div class="msg-row left">' +
                    '<div class="bubble">' +
                    '' + pesanHtml + '' +
                    '<span class="meta">' + waktu + '</span>' +
                    '</div>' +
                    '</div>';

                if (chatAwal) {
                    message_box = message_box + '</div>';
                }

                $('.chat-area').html(chatArea + message_box);
                scrollToBottom();

                // update jam user item
                $('.user-item-' + ref_id + ' .jam').text(waktu);

                // update pesan user item
                $('.user-item-' + ref_id + ' .last-message').text(safeTrim(pesanHtml, 30));
            }

            $.ajax({
                url: "../../controller/chat.php",
                type: "post",
                data: {
                    func: 'autoRead',
                    reference_id: reference_id
                },
                success: function(result) {},
            });
        } else {
            // Update notif chat
            $.ajax({
                url: "../../controller/chat.php",
                type: "post",
                data: {
                    func: 'cekNotifChat',
                    reference_id: reference_id
                },
                success: function(result) {
                    var obj = jQuery.parseJSON(JSON.stringify(result));
                    if (obj.status) {
                        if (obj.data.notifChatAll > 0) {
                            if (obj.data.notifChatAll > 99) {
                                $(".badge-chat-notif-all").html('99+');
                            } else {
                                $(".badge-chat-notif-all").html(obj.data.notifChatAll);
                            }

                            $(".badge-chat-notif-all").show();
                        } else {
                            $(".badge-chat-notif-all").hide();
                        }

                        if (obj.data.notifChatRef > 0) {
                            if (obj.data.notifChatRef > 99) {
                                $("#badge-chat-notif-" + ref_id).html('99+');
                            } else {
                                $("#badge-chat-notif-" + ref_id).html(obj.data.notifChatRef);
                            }

                            $("#badge-chat-notif-" + ref_id).show();

                            // update style jam
                            $('.user-item-' + ref_id + ' .jam').css('color', 'green');
                        } else {
                            $("#badge-chat-notif-" + ref_id).hide();

                            // update style jam
                            $('.user-item-' + ref_id + ' .jam').css('color', '');
                        }
                    }
                },
            });

            // Ubah posisi user list
            const $userList = $('#userList');
            const $target = $userList.find('.user-item-' + ref_id);
            pesanHtml = htmlspecialchars(message).replace(/\n/g, " ");

            // Cek apakah user list tidak ada
            if ($('.user-item-' + ref_id).length > 0) {
                // update jam user item
                $('.user-item-' + ref_id + ' .jam').text(waktu);
                $('.user-item-' + ref_id + ' .jam').css('color', 'green');

                // update pesan user item
                $('.user-item-' + ref_id + ' .last-message').text(safeTrim(pesanHtml, 30));
            } else {
                // Buat user list jika ada pesan dari kandidat baru
                $.ajax({
                    url: "../../controller/chat.php",
                    type: "post",
                    data: {
                        func: 'tamabahUserList',
                        reference_id: reference_id
                    },
                    success: function(result) {
                        var obj = JSON.parse(JSON.stringify(result));
                        responseHtml = obj.html
                        userArea = $('.user-list').html();

                        $('.user-list').html(responseHtml + userArea);
                    },
                });
            }

            // Animasi fade + pindahkan elemen ke atas
            $target.slideUp(200, function() {
                $(this).prependTo($userList).slideDown(300);
            });
        }
    }
    // End chat function
</script>