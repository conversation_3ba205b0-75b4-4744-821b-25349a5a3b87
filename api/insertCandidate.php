<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');
include '../model/database.php';

$create_at = date("Y-m-d H:i:s");

// Cek koneksi
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $conn->connect_error
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] != "POST") {
    // simpan log error
    $pesan = 'Method not allowed. Only POST is permitted.';
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $pesan);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(405); // Method Not Allowed
    echo json_encode([
        "status" => "error",
        "message" => "Method not allowed. Only POST is permitted."
    ]);
    exit;
}

// Set API Key valid
$valid_api_key = "xiAheLul2MLQb4MZKs0MLrG5FcKOtg";

// Ambil API Key dari request header
$headers = apache_request_headers();
$api_key = isset($headers['key']) ? $headers['key'] : null;

// Alternatif kalau apache_request_headers tidak ada
if (!$api_key && isset($_SERVER['HTTP_X_API_KEY'])) {
    $api_key = $_SERVER['HTTP_X_API_KEY'];
}

// Cek API Key
if ($api_key !== $valid_api_key) {
    // simpan log error
    $pesan = 'Unauthorized: Invalid API Key.';
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $pesan);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized: Invalid API Key'
    ]);
    exit;
} else {
    $created_at = date('Y-m-d H:i:s');

    // fungsi untuk melakukan hash password
    function hashPassword($password)
    {
        return password_hash($password, PASSWORD_BCRYPT);
    }

    // function buat pincode
    function create_pincode($length)
    {
        $data = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $string = '';
        for ($i = 0; $i < $length; $i++) {
            $pos = rand(0, strlen($data) - 1);
            $string .= $data[$pos];
        }
        return $string;
    }

    $data = json_decode(file_get_contents("php://input"), true);

    // Jika tidak ada body JSON, fallback ke $_POST
    if (!$data) {
        $data = $_POST;
    }

    // Cek parameter wajib
    $required = ["name", "email", "password", "phone"];
    $missing = [];

    foreach ($required as $param) {
        if (!isset($data[$param]) || $data[$param] === "") {
            $missing[] = $param;
        }
    }

    // Jika ada param yang hilang
    if (!empty($missing)) {
        // simpan log error
        $pesan = 'Missing required parameters.';
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => "Missing required parameters.",
            "missing" => $missing
        ]);
        exit;
    }

    $nama_lengkap = $data['name'];
    $email = $data['email'];
    $password = hashPassword($data['password']);
    $no_telp = $data['phone'];

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        // simpan log error
        $pesan = 'Invalid email. ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => "Invalid email."
        ]);
        exit;
    }

    if (!preg_match('/^[0-9]+$/', $no_telp)) {
        // simpan log error
        $pesan = 'Invalid phone number. ref: ' . $no_telp;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => "Invalid phone number."
        ]);
        exit;
    }

    // Cek data user email
    $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE email = ? GROUP BY pin");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    // Jika ada, pendaftaran gagal
    if ($result->num_rows > 0) {
        // simpan log error
        $pesan = 'Email has been registered. ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(403);
        echo json_encode([
            "status" => "error",
            "message" => "Email has been registered."
        ]);
        exit;
    }

    $status = "error";
    try {
        // Mulai proses
        $conn->begin_transaction();

        // Generate pin code
        $sqlCek = "SELECT pin FROM users_kandidat";
        $queryCek = $conn->query($sqlCek);

        do {
            $pincode = create_pincode(15);
        } while ($pincode == $queryCek);

        // Validasi panjang password
        if (strlen($data['password']) < 8) {
            throw new Exception("Password must be at least 8 characters long.");
        }

        // Huruf besar password
        if (!preg_match('/[A-Z]/', $data['password'])) {
            throw new Exception("Passwords must contain uppercase letters.");
        }

        // Huruf kecil password
        if (!preg_match('/[a-z]/', $data['password'])) {
            throw new Exception("Passwords must contain lowercase letters.");
        }

        // Angka password
        if (!preg_match('/\d/', $data['password'])) {
            throw new Exception("Passwords must contain numbers.");
        }

        // Karakter khusus password
        if (!preg_match('/[^A-Za-z0-9]/', $data['password'])) {
            throw new Exception("Passwords must contain special characters.");
        }

        // Proses penyimpanan data kandidat
        $insert = $conn->prepare("INSERT INTO `users_kandidat`(`pin`, `nama_lengkap`, `email`, `no_telp`, `password`, `status`, `tgl`, `created_at`,`foto`, `from`) 
                                VALUES (?, ?, ?, ?, ?, 'Active', ?, ?, '', 'API Insert Candidate')");
        $insert->bind_param("sssssss", $pincode, $nama_lengkap, $email, $no_telp, $password, $created_at, $created_at);

        if ($insert->execute()) {
            $insert->close();

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Candidate successfully registered.";
        } else {
            throw new Exception("Candidate registration failed.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "error";
        $message = $e->getMessage();
    }

    if ($status == 'success') {
        http_response_code(201);
        echo json_encode([
            "status" => "success",
            "message" => $message
        ]);
        exit;
    } else {
        // simpan log error
        $pesan = $message . ' ref: ' . $email;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'API Insert Candidate', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $pesan);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => $message
        ]);
        exit;
    }
}
