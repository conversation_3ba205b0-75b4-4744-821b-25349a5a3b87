<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../../model/database.php";
include '../../../api/s3.php';

$tempAkses = "all";
if (isset($_GET['q'])) {
    $pin = "-";
    $nama_user = "-";
    $email_user = "-";
    if (isset($_SESSION['users'])) {

        // get data user
        if (isset($_SESSION['users']['pin'])) {
            $pin = addslashes($_SESSION['users']['pin']);
        }

        if (isset($_SESSION['users']['nama'])) {
            $nama_user = $_SESSION['users']['nama'];
        }

        if (isset($_SESSION['users']['email'])) {
            $email_user = $_SESSION['users']['email'];
        }
        $tempAkses = "true";
    } else {
        $tempAkses = "all";
    }

    $q = htmlspecialchars(base64_decode($_GET['q']));

    // cek data lowongan
    $get = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    $nama_perusahaan = "-";
    $alamat = "-";
    $tipe = "-";
    $link_perusahaan = "-";
    $tentang_perusahaan = "-";
    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-3" id="logo-detail-perusahaan" alt="logo-perusahaan">';
    $banner_perusahaan = '<img src="../../../assets/images/content/bg.png" class="card-img-top" alt="banner-perusahaan">';
    if ($result->num_rows > 0) {
        $rowData = mysqli_fetch_array($result);
        $nama_perusahaan = htmlspecialchars($rowData['label']);
        $alamat = htmlspecialchars($rowData['alamat']);
        if ($rowData['tipe'] != "") {
            $tipe = htmlspecialchars($rowData['tipe']);
        }

        if ($rowData['deskripsi'] != "") {
            $tentang_perusahaan = htmlspecialchars($rowData['deskripsi']);
        }

        if ($rowData['link_perusahaan'] != "") {
            $link_perusahaan = htmlspecialchars($rowData['link_perusahaan']);
        }

        // get logo perusahaan
        if ($rowData['img'] != "") {
            if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $rowData['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $rowData['img']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();

                $logo_perusahaan = '<img src="' . $logoURL . '" class="profile-pic-3" id="logo-detail-perusahaan" alt="logo-perusahaan">';
            } else {
                $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-3" id="logo-detail-perusahaan" alt="logo-perusahaan">';
            }
        }

        // get banner perusahaan
        if ($rowData['img_banner'] != "") {
            if ($s3->doesObjectExist($bucket, 'perusahaan/banner/' . $rowData['img_banner'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/banner/' . $rowData['img_banner']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $bannerURL = (string) $request->getUri();

                $banner_perusahaan = '<img src="' . $bannerURL . '" class="card-img-top" alt="banner-perusahaan">';
            }
        }

        // simpan log aktivitas
        $messages = 'Menampilkan halaman detail perusahaan ' . $nama_perusahaan . ' di sistem.';
        $extra_info = "Kandidat";
        $level = "INFO";
        $path = $_SERVER['REQUEST_URI'];
        logActivity($conn, $pin, $level, $messages, $extra_info);
    } else {
        header('Location: index'); // Redirect setelah login sukses
        exit();
    }
} else {
    header('Location: index'); // Redirect setelah login sukses
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><?= $nama_perusahaan ?> - digitalcv</title>
    <meta
        content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
        name="viewport" />
    <?php include_once '../style.php'; ?>
    <style>
        .card-img-top {
            object-fit: cover;
            width: 100%;
            max-height: 400px;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <?php include '../sidebar.php' ?>
        <div class="main-panel">
            <?php include '../header.php' ?>
            <div class="container">
                <div class="page-inner">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <?= $banner_perusahaan ?>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <?= $logo_perusahaan ?>
                                        </div>
                                        <div class="col-md-9">
                                            <h5 class="card-title"><?= $nama_perusahaan ?></h5>
                                            <p class="card-text">
                                                <b><?= translate('Alamat Perusahaan') ?></b><br>
                                                <?= $alamat ?>
                                            </p>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p>
                                                        <b><?= translate('Industri') ?></b><br>
                                                        <?= $tipe ?>
                                                    </p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p>
                                                        <b><?= translate('Link Perusahaan') ?></b><br>
                                                        <a href="http://<?= $link_perusahaan ?>"><?= $link_perusahaan ?></a>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?= translate('Tentang Perusahaan') ?></h5>
                                    <p>
                                        <?= $tentang_perusahaan ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header" style="background-color: #0D3B72; color: white; border-top-left-radius: 5px; border-top-right-radius: 5px;">
                                    <?= translate('Lowongan yang tersedia di') ?> <?= $nama_perusahaan ?>
                                </div>
                                <ul class="list-group list-group-flush">
                                    <div class="row mt-3 mb-3" id="loading-pagination" style="display: none;">
                                        <div class="col-md-12 d-flex justify-content-center">
                                            <div class="loader"></div>
                                        </div>
                                    </div>
                                    <div id="job-list"></div>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../footer.php' ?>

    <script>
        function loadData(page) {
            $.ajax({
                url: "../../controller/controller.php?func=paginationListJobCompany",
                type: "POST",
                data: {
                    page: page,
                    q: '<?= $q ?>'
                },
                success: function(response) {
                    var obj = JSON.parse(JSON.stringify(response));

                    $("#job-list").html(obj.html);
                    $("#loading-pagination").hide();
                    $("#job-list").show();
                }
            });
        }

        function simpanFavLowongan(id) {
            grecaptcha.ready(function() {
                grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                    action: 'submit'
                }).then(function(token) {
                    if ($('[name="check-fav-' + id + '[]"').is(':checked')) {
                        $('[name="check-fav-' + id + '[]"').prop("checked", false);
                        $(".btn-fav-" + id).removeClass('bi bi-heart-fill').addClass('bi bi-heart');
                        var check = 'false';
                    } else {
                        $('[name="check-fav-' + id + '[]"').prop("checked", true);
                        $(".btn-fav-" + id).removeClass('bi bi-heart').addClass('bi bi-heart-fill');
                        var check = 'true';
                    }

                    var q = $('[name="check-fav-' + id + '[]"').val();

                    $.ajax({
                        type: "post",
                        url: "../../controller/controller.php?func=simpanFavLowongan",
                        data: {
                            q: q,
                            check: check,
                            recaptcha_response: token
                        },
                        success: function(result) {},
                    });
                });
            });
        }

        $(document).ready(function() {
            $("#job-list").hide();
            $("#loading-pagination").show();
            loadData(1); // Load halaman pertama saat pertama kali dibuka

            $(document).on("click", ".page-link", function() {
                $("#job-list").hide();
                $("#loading-pagination").show();

                let page = $(this).data("page");
                loadData(page);
            });
        });
    </script>
</body>

</html>