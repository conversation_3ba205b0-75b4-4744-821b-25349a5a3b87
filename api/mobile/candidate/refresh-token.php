<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../jwt_helper.php';

$response = [
    'success' => false,
    'message' => 'Refresh token gagal',
    'data' => []
];

try {
    // Ambil refresh token dari request body
    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['refresh_token'])) {
        throw new Exception('Refresh token diperlukan');
    }

    $refreshToken = $input['refresh_token'];

    // Verifikasi refresh token
    $verification = verifyRefreshToken($refreshToken);

    if (!$verification['valid']) {
        throw new Exception($verification['message']);
    }

    $userData = $verification['data'];

    // Cek apakah refresh token masih valid di database
    $sql = $conn->prepare("SELECT pin, nama_lengkap, email, foto, refresh_token_expiry FROM users_kandidat WHERE pin = ? AND refresh_token = ? AND refresh_token_expiry > NOW()");
    $sql->bind_param("ss", $userData->pin, $refreshToken);
    $sql->execute();
    $result = $sql->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Refresh token tidak valid atau sudah expired');
    }

    $row = $result->fetch_assoc();
    $sql->close();

    // Generate access token baru
    $newUserData = [
        'pin' => $row['pin'],
        'nama' => $row['nama_lengkap'],
        'email' => $row['email'],
        'tipe' => 'kandidat'
    ];

    $newAccessToken = generateNewAccessToken($newUserData);

    $response = [
        'success' => true,
        'message' => 'Access token berhasil diperbarui',
        'data' => [
            'access_token' => $newAccessToken,
            'token_type' => 'Bearer',
            'expires_in' => 86400 // 24 jam dalam detik
        ]
    ];
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
