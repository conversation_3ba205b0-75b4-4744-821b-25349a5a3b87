<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}
function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}

// Secure query execution function
function executeSecureQuery($conn, $sql, $params = [], $types = "")
{
    try {
        if (!empty($params)) {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types)) {
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        } else {
            return $conn->query($sql);
        }
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Input validation function
function validateInput($input, $type = 'string', $maxLength = 255)
{
    if (empty($input) && $input !== '0') return '';

    switch ($type) {
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT) !== false ? (int)$input : 0;
        case 'id_lamar':
            // Validate id_lamar format - should be alphanumeric with possible underscores/hyphens
            return preg_match('/^[a-zA-Z0-9_-]+$/', $input) ? substr(trim($input), 0, $maxLength) : '';
        default:
            return preg_match('/^[a-zA-Z0-9\s\.\-_,]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
    }
}

// Output sanitization function
function sanitizeOutput($data)
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $key => $value) {
            $sanitized[$key] = sanitizeOutput($value);
        }
        return $sanitized;
    }

    if (is_string($data)) {
        return $data;
    }

    return $data;
}

// Validate and sanitize input parameters
$limit = validateInput($_POST['page_size'] ?? 6, 'int');
$limit = ($limit > 0 && $limit <= 100) ? $limit : 6; // Max 100 items per page

$page = validateInput($_POST['page'] ?? 1, 'int');
$page = ($page > 0 && $page <= 10000) ? $page : 1; // Max 10000 pages to prevent DOS

$start = ($page - 1) * $limit;

// Validate id_lamar parameter
$idLamar = validateInput($_POST['id_lamar'] ?? '', 'id_lamar', 50);

if (empty($idLamar)) {
    $response = [
        "success" => false,
        "message" => "Id Lamar tidak valid atau tidak ada",
        "data" => []
    ];
    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}


$response['data'] = [];

// Execute secure query to get timeline history
$sql = "SELECT 
    ulh.status, 
    ulh.tgl,
    ulh.id_lamar
FROM users_lamar_history ulh 
WHERE ulh.id_lamar = ? 
ORDER BY ulh.tgl ASC";

$result = executeSecureQuery($conn, $sql, [$idLamar], "s");

if ($result === false) {
    $response = [
        "success" => false,
        "message" => "Terjadi kesalahan dalam mengambil data timeline",
        "data" => []
    ];
    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
    $conn->close();
    exit;
}

// Inisialisasi array untuk menyimpan tahapan
$timeline = [];
$statusMapping = [
    'Lamaran Dikirim' => ['tahap' => 1, 'nama' => 'Lamaran Dikirim'],
    'On Proccess Psikotes' => ['tahap' => 2, 'nama' => 'Proses Screening CV'],
    'Tolak Digitalcv' => ['tahap' => 2, 'nama' => 'Ditolak Digital CV'],
    'Psikotes Gestalt' => ['tahap' => 3, 'nama' => 'Tes Psikotes'],
    'Selesai Psikotes' => ['tahap' => 4, 'nama' => 'Selesai Psikotes'],
    'Terima Rekrutmen' => ['tahap' => 4, 'nama' => 'Kandidat Diterima'],
    'Tolak Rekrutmen' => ['tahap' => 4, 'nama' => 'Kandidat Tidak Diterima']
];

$tahapData = [
    1 => ['status' => '', 'tanggal' => ''],
    2 => ['status' => '', 'tanggal' => ''],
    3 => ['status' => '', 'tanggal' => ''],
    4 => ['status' => '', 'tanggal' => ''],
];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $status = $row['status'];
        $tanggal = $row['tgl'];

        if (isset($statusMapping[$status])) {
            $tahap = $statusMapping[$status]['tahap'];
            $nama = $statusMapping[$status]['nama'];

            // Update data tahap
            $tahapData[$tahap]['status'] = $nama;
            $tahapData[$tahap]['tanggal'] = $tanggal ? formatTanggal($tanggal) : '';
        }
    }
}

// Format output sebagai array of objects with sanitization
$outputData = [];
foreach ($tahapData as $tahap) {
    $outputData[] = [
        'tahap' => sanitizeOutput($tahap['status']),
        'tgl_tahap' => sanitizeOutput($tahap['tanggal'])
    ];
}

$response['data'] = $outputData;

// Format JSON response
$response = [
    "success" => true,
    "message" => "fetch data berhasil",
    "data" => $response['data']
];

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

$conn->close();
exit;
