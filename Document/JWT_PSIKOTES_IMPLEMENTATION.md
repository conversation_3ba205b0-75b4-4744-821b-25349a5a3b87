# JWT Implementation for Psikotes API

## Overview

This document details the JWT authentication implementation for the `psikotes.php` endpoint in the company recruitment system. This endpoint manages psychological test (psikotes) scheduling and results for job candidates.

## Endpoint Information

- **File**: `api/mobile/company/psikotes.php`
- **Purpose**: Manages psychological test scheduling and results viewing
- **Authentication**: JWT Bearer <PERSON>ken required

## Migration Changes

### 1. Authentication Updates

- **Added**: JWT helper inclusion and authentication requirement
- **Removed**: Session-based authentication and POST parameter validation
- **Added**: `requireAuth()` function call to validate JWT tokens

### 2. User Data Extraction

**Before (Parameter-based)**:

```php
if (isset($_POST['id_koordinator'])) {
    $id_koordinator = $_POST['id_koordinator'];
} else {
    $id_koordinator = "";
}

if (isset($_POST['divisi'])) {
    $divisi = $_POST['divisi'];
} else {
    $divisi = "";
}

if (isset($_POST['id'])) {
    $id_pegawai = $_POST['id'];
} else {
    $id_pegawai = "";
}
```

**After (JWT-based)**:

```php
// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->company;
$id_pegawai = $id_koordinator;
```

### 3. SQL Query Updates

- All queries now use data extracted from JWT tokens
- Replaced `$company` from GET parameters with `$id_koordinator` from JWT
- Updated prepared statements to use JWT-derived values
- Enhanced query security with proper parameter binding

### 4. Activity Logging

- Added comprehensive activity logging for all operations
- Logs user access to psychological test data and scheduling actions
- Includes IP address, user agent, and timestamp

## API Functions

### 1. getReqPsikotes

**Purpose**: Retrieve list of candidates ready for psychological testing with pagination and search

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/psikotes.php?func=getReqPsikotes&page=1&page_size=5&q=searchterm
Authorization: Bearer <jwt_token>
```

**Response**:

```json
{
  "status": true,
  "message": "Data berhasil diambil!",
  "data": [
    {
      "id": "candidate_id",
      "nama": "Candidate Name",
      "posisi_lamar": "Software Engineer, Data Analyst",
      "id_lamar": "encoded_lamar_ids"
    }
  ],
  "page": 1,
  "page_size": 5,
  "total_page": 10,
  "total_data": 50
}
```

### 2. submitReqPsikotes

**Purpose**: Schedule selected candidates for psychological testing

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/psikotes.php?func=submitReqPsikotes
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- id_lamar[]: array of encoded candidate application IDs
- jadwal_tes: test schedule date and time
```

**Response**:

```json
{
  "status": true,
  "message": "Kandidat berhasil diproses.",
  "data": []
}
```

### 3. getHasilPsikotes

**Purpose**: Retrieve psychological test results for candidates with pagination and search

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/psikotes.php?func=getHasilPsikotes&page=1&page_size=5&q=searchterm
Authorization: Bearer <jwt_token>
```

**Response**:

```json
{
  "status": true,
  "message": "Data berhasil diambil!",
  "data": [
    {
      "id_lamar": "application_id",
      "id": "candidate_id",
      "nama": "Candidate Name",
      "posisi_lamar": "Applied Position",
      "pendidikan_terakhir": "S1",
      "jurusan": "Computer Science",
      "umur": 25
    }
  ],
  "page": 1,
  "page_size": 5,
  "total_page": 5,
  "total_data": 25
}
```

## Security Improvements

### 1. Token-Based Authentication

- JWT tokens contain encrypted user data
- Tokens have expiration times
- Invalid or expired tokens are automatically rejected

### 2. Eliminated Parameter Injection

- No longer accepts sensitive data via POST/GET parameters for user identification
- All user identification comes from verified JWT payload
- Prevents unauthorized access to other companies' data

### 3. Enhanced Query Security

- All database queries use prepared statements with JWT-derived values
- Eliminated direct variable interpolation in SQL
- Prevents SQL injection attacks

### 4. Activity Logging

- All access attempts are logged with detailed information
- Failed authentication attempts are tracked
- Activity logs include user context and timestamps

## Key Features Maintained

### 1. Psychological Test Integration

- External API integration with psikotes.digitalcv.id preserved
- Candidate data synchronization maintained
- Test scheduling workflow unchanged

### 2. Status Management

- Candidate status progression maintained
- History tracking for status changes preserved
- Multi-step approval process intact

### 3. Search and Filtering

- Name and position search functionality preserved
- Pagination support maintained
- Educational background filtering available

### 4. Database Transactions

- Atomic operations for status updates
- Rollback capability on errors
- Data integrity maintained

## Error Handling

### Authentication Errors

```json
{
  "error": "Authentication required",
  "message": "No token provided or invalid token"
}
```

### Authorization Errors

```json
{
  "error": "Access denied",
  "message": "Insufficient permissions for this resource"
}
```

### Business Logic Errors

```json
{
  "status": false,
  "message": "Tidak dapat menyimpan data. Silakan hubungi administrator.",
  "data": []
}
```

## Implementation Notes

### 1. External API Integration

- Psychological test API integration preserved
- Data synchronization with external system maintained
- Error handling for external API failures included

### 2. Candidate Status Workflow

- Status progression from "On Process Psikotes" to "Psikotes Gestalt" to "Selesai Psikotes"
- History tracking for all status changes
- Multi-company data isolation ensured

### 3. Performance Optimization

- Prepared statements used for all database queries
- Efficient pagination implementation
- Search optimization with proper indexing considerations

## Testing

### Test Authentication

```bash
# Get JWT token first from login endpoint
curl -X POST "http://localhost/digitalcv/api/mobile/company/login.php" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use token for psikotes access
curl -X GET "http://localhost/digitalcv/api/mobile/company/psikotes.php?func=getReqPsikotes" \
  -H "Authorization: Bearer <jwt_token>"
```

### Test Psikotes Scheduling

```bash
curl -X POST "http://localhost/digitalcv/api/mobile/company/psikotes.php?func=submitReqPsikotes" \
  -H "Authorization: Bearer <jwt_token>" \
  -d "id_lamar[0]=encoded_id&jadwal_tes=2024-12-15 10:00:00"
```

### Test Results Viewing

```bash
curl -X GET "http://localhost/digitalcv/api/mobile/company/psikotes.php?func=getHasilPsikotes&page=1&page_size=10" \
  -H "Authorization: Bearer <jwt_token>"
```

## Migration Checklist

- [x] JWT helper integration
- [x] Authentication requirement added
- [x] Parameter-based authentication removed
- [x] User data extraction from JWT
- [x] SQL queries updated with prepared statements
- [x] Activity logging implemented
- [x] Error handling improved
- [x] External API integration preserved
- [x] Database transactions maintained
- [x] Search and pagination functionality preserved

## External Dependencies

- **Psychological Test API**: https://psikotes.digitalcv.id/
- **Database Tables**: users_lamar, users_lamar_history, koordinator_pic, list_request, rh, users_kandidat
- **S3 Storage**: For candidate photos and documents
- **Email Service**: For test notifications

## Next Steps

1. Update client applications to use JWT authentication
2. Test external API integration with new authentication
3. Monitor logs for authentication issues
4. Update API documentation for consumers
5. Verify psychological test workflow end-to-end
