<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama_lengkap ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Validasi dan sanitasi input
$search = isset($_GET['q']) ? trim($_GET['q']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$perPage = isset($_GET['page_size']) ? min(100, max(1, (int)$_GET['page_size'])) : 5;

// Validasi search parameter - hanya boleh huruf, angka, spasi, dan beberapa karakter khusus
if (!empty($search) && !preg_match('/^[a-zA-Z0-9\s\-_\.]+$/', $search)) {
    header('Content-Type: application/json');
    echo json_encode([
        "success" => false,
        "message" => "Format pencarian tidak valid",
        "data" => [],
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => 0
    ], JSON_PRETTY_PRINT);
    exit;
}

// Validasi page dan perPage untuk mencegah overflow
if ($page > 10000) {
    header('Content-Type: application/json');
    echo json_encode([
        "success" => false,
        "message" => "Halaman tidak valid",
        "data" => [],
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => 0
    ], JSON_PRETTY_PRINT);
    exit;
}

$offset = ($page - 1) * $perPage;

// Jika search diisi tetapi kurang dari 3 karakter, kembalikan error
// if (!empty($search) && strlen($search) < 3) {
//     header('Content-Type: application/json');
//     echo json_encode([
//         "success" => false,
//         "message" => "Harus lebih dari 3 karakter",
//         "data" => [],
//         "page" => $page,
//         "page_size" => $perPage,
//         "total_data" => 0
//     ], JSON_PRETTY_PRINT);
//     exit;
// }

$sql = "SELECT `bahasa` FROM bahasa";
$countQuery = "SELECT COUNT(*) as total FROM bahasa";

// Tambahkan kondisi WHERE jika ada pencarian minimal 3 karakter
if (!empty($search)) {
    $sql .= " WHERE `bahasa` LIKE ?";
    $countQuery .= " WHERE `bahasa` LIKE ?";
}

// Tambahkan limit dan offset untuk pagination
$sql .= " LIMIT ?, ?";

// Persiapkan statement untuk query utama dengan validasi ketat
try {
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Failed to prepare statement");
    }

    if (!empty($search)) {
        $searchTerm = "%$search%";
        if (!$stmt->bind_param("sii", $searchTerm, $offset, $perPage)) {
            throw new Exception("Failed to bind parameters");
        }
    } else {
        if (!$stmt->bind_param("ii", $offset, $perPage)) {
            throw new Exception("Failed to bind parameters");
        }
    }

    if (!$stmt->execute()) {
        throw new Exception("Failed to execute query");
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Failed to get result");
    }
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        "success" => false,
        "message" => "Terjadi kesalahan dalam mengambil data",
        "data" => [],
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => 0
    ], JSON_PRETTY_PRINT);
    exit;
}

$bahasa = [];
while ($row = $result->fetch_assoc()) {
    // Sanitasi output data
    $bahasaValue = htmlspecialchars($row['bahasa'], ENT_QUOTES, 'UTF-8');
    $bahasa[] = ['id' => $bahasaValue, 'text' => $bahasaValue];
}

// Persiapkan statement untuk count query dengan validasi
try {
    $stmt = $conn->prepare($countQuery);
    if (!$stmt) {
        throw new Exception("Failed to prepare count statement");
    }

    if (!empty($search)) {
        if (!$stmt->bind_param("s", $searchTerm)) {
            throw new Exception("Failed to bind count parameters");
        }
    }

    if (!$stmt->execute()) {
        throw new Exception("Failed to execute count query");
    }

    $countResult = $stmt->get_result();
    if (!$countResult) {
        throw new Exception("Failed to get count result");
    }

    $countData = $countResult->fetch_assoc();
    $totalRows = isset($countData['total']) ? (int)$countData['total'] : 0;
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        "success" => false,
        "message" => "Terjadi kesalahan dalam menghitung data",
        "data" => [],
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => 0
    ], JSON_PRETTY_PRINT);
    exit;
}

// Format response JSON dengan validasi
$response = [
    "success" => true,
    "message" => "fetch data berhasil",
    "data" => $bahasa,
    "page" => $page,
    "page_size" => $perPage,
    "total_data" => $totalRows
];

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

if ($stmt) {
    $stmt->close();
}
$conn->close();
exit;
