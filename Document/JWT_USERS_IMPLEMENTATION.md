# JWT Implementation for Users Management API

## Overview

This document details the JWT authentication implementation for the `users.php` endpoint in the company recruitment system. This endpoint manages user accounts, permissions, and access control within the company system.

## Endpoint Information

- **File**: `api/mobile/company/users.php`
- **Purpose**: Manages company user accounts, permissions, and access control
- **Authentication**: JWT Bearer Token required

## Migration Changes

### 1. Authentication Updates

- **Added**: JWT helper inclusion and authentication requirement
- **Removed**: Session-based authentication and POST parameter validation
- **Added**: `requireAuth()` function call to validate JWT tokens

### 2. User Data Extraction

**Before (Parameter-based)**:

```php
if (isset($_POST['id_koordinator'])) {
    $id_koordinator = $_POST['id_koordinator'];
} else {
    $id_koordinator = "";
}

if (isset($_POST['divisi'])) {
    $divisi = $_POST['divisi'];
} else {
    $divisi = "";
}

if (isset($_POST['id'])) {
    $id_pegawai = $_POST['id'];
} else {
    $id_pegawai = "";
}
```

**After (JWT-based)**:

```php
// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->company;
$id_pegawai = $id_koordinator;
```

### 3. SQL Query Updates

- All queries now use data extracted from JWT tokens
- Replaced `$_GET['company']` parameters with `$id_koordinator` from JWT
- Updated prepared statements to use JWT-derived values
- Enhanced query security with proper parameter binding

### 4. Activity Logging

- Added comprehensive activity logging for all user management operations
- Logs user access, creation, updates, and deletions
- Includes IP address, user agent, and timestamp

## API Functions

### 1. getDataUsers

**Purpose**: Retrieve list of company users with pagination and search

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/users.php?func=getDataUsers&page=1&page_size=5&q=searchterm
Authorization: Bearer <jwt_token>
```

**Response**:

```json
{
  "status": true,
  "message": "Data berhasil diambil!",
  "data": [
    {
      "no": 1,
      "id_pic": "PIC-12345678",
      "id_koordinator": "COORD-123",
      "nama": "John Doe",
      "email": "<EMAIL>",
      "no_hp": "081234567890",
      "posisi": "HR Manager",
      "img": "profile.jpg",
      "role": "normal",
      "fitur": "feature_id_1|feature_id_2"
    }
  ],
  "page": 1,
  "page_size": 5,
  "total_page": 10,
  "total_data": 50
}
```

### 2. penambahanUser

**Purpose**: Add new user to the company system

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=penambahanUser
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- nama: string (user full name)
- email: string (user email)
- no_hp: string (phone number)
- password: string (user password)
- kon_password: string (password confirmation)
```

**Response**:

```json
{
  "status": true,
  "message": "Penambahan user berhasil.",
  "data": []
}
```

### 3. getDataPengaturanUsers

**Purpose**: Retrieve users for settings management with pagination

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/users.php?func=getDataPengaturanUsers&page=1&page_size=5&q=searchterm
Authorization: Bearer <jwt_token>
```

### 4. updateDataUser

**Purpose**: Update user information

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=updateDataUser
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- id_pic: string (base64 encoded user ID)
- nama: string (updated name)
- email: string (updated email)
- no_hp: string (updated phone)
```

### 5. hapusUser

**Purpose**: Delete user from the system

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=hapusUser
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- id_pic: string (base64 encoded user ID to delete)
```

### 6. ubahDataAkun

**Purpose**: Update current user's account information

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=ubahDataAkun
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- nama: string (updated name)
- email: string (updated email)
- no_hp: string (updated phone)
```

### 7. ubahKataSandiAkun

**Purpose**: Change current user's password

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=ubahKataSandiAkun
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- password_lama: string (current password)
- password: string (new password)
- kon_password: string (new password confirmation)
```

### 8. getDataPengaturanAkses

**Purpose**: Retrieve users for access management

**Authentication**: JWT token required in Authorization header

### 9. getDataAkses

**Purpose**: Retrieve user access permissions/features

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/users.php?func=getDataAkses&id=base64_encoded_user_id
Authorization: Bearer <jwt_token>
```

### 10. updateAksesUser

**Purpose**: Update user access permissions

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/users.php?func=updateAksesUser
Authorization: Bearer <jwt_token>
Content-Type: application/x-www-form-urlencoded

Parameters:
- id_pic: string (base64 encoded user ID)
- akses: string (comma-separated feature IDs)
```

## Security Improvements

### 1. Token-Based Authentication

- JWT tokens contain encrypted user data
- Tokens have expiration times
- Invalid or expired tokens are automatically rejected

### 2. Eliminated Parameter Injection

- No longer accepts sensitive data via GET parameters for company identification
- All user identification comes from verified JWT payload
- Prevents unauthorized access to other companies' user data

### 3. Enhanced Query Security

- All database queries use prepared statements with JWT-derived values
- Eliminated direct variable interpolation in SQL
- Prevents SQL injection attacks

### 4. Password Security

- Passwords are hashed using PHP's password_hash() function
- Password verification uses password_verify() function
- Secure password storage and validation

### 5. Activity Logging

- All user management operations are logged
- Failed authentication attempts are tracked
- Activity logs include user context and timestamps

## Key Features Maintained

### 1. User Role Management

- Role-based access control preserved
- Master vs normal user distinctions maintained
- Permission system intact

### 2. Feature Access Control

- Granular feature permissions preserved
- Dynamic feature assignment capability
- Access level management maintained

### 3. User Limits and Package Control

- Company package-based user limits enforced
- User quota management preserved
- Subscription-based access control maintained

### 4. Data Validation

- Email uniqueness validation
- Password strength requirements
- Input sanitization and validation

### 5. Transaction Management

- Atomic operations for user management
- Rollback capability on errors
- Data integrity maintained

## Error Handling

### Authentication Errors

```json
{
  "error": "Authentication required",
  "message": "No token provided or invalid token"
}
```

### Authorization Errors

```json
{
  "error": "Access denied",
  "message": "Insufficient permissions for this resource"
}
```

### Business Logic Errors

```json
{
  "status": false,
  "message": "Email sudah terdaftar!",
  "data": []
}
```

### Package Limit Errors

```json
{
  "status": false,
  "message": "Tidak dapat melakukan penambahan user, karena limit sudah terpenuhi.",
  "data": []
}
```

## Implementation Notes

### 1. User Package Limitations

- Different packages have different user limits
- Package 3: Maximum 3 users
- Package 4: Maximum 10 users
- Limits enforced during user creation

### 2. Password Management

- Passwords are hashed with PHP's default algorithm
- Old password verification required for changes
- Password confirmation validation implemented

### 3. Feature Access System

- Features stored as pipe-separated values in database
- Dynamic feature assignment based on available features
- Granular permission control per user

### 4. User History Tracking

- Deleted users moved to history table
- Audit trail for user management actions
- Complete activity logging for compliance

## Testing

### Test Authentication

```bash
# Get JWT token first from login endpoint
curl -X POST "http://localhost/digitalcv/api/mobile/company/login.php" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use token for users access
curl -X GET "http://localhost/digitalcv/api/mobile/company/users.php?func=getDataUsers" \
  -H "Authorization: Bearer <jwt_token>"
```

### Test User Creation

```bash
curl -X POST "http://localhost/digitalcv/api/mobile/company/users.php?func=penambahanUser" \
  -H "Authorization: Bearer <jwt_token>" \
  -d "nama=John Doe&email=<EMAIL>&no_hp=081234567890&password=password123&kon_password=password123"
```

### Test User Access Management

```bash
curl -X POST "http://localhost/digitalcv/api/mobile/company/users.php?func=updateAksesUser" \
  -H "Authorization: Bearer <jwt_token>" \
  -d "id_pic=encoded_user_id&akses=feature1,feature2,feature3"
```

## Migration Checklist

- [x] JWT helper integration
- [x] Authentication requirement added
- [x] Parameter-based authentication removed
- [x] User data extraction from JWT
- [x] SQL queries updated with prepared statements
- [x] Activity logging implemented
- [x] Password security maintained
- [x] Role-based access control preserved
- [x] Package limitations maintained
- [x] Feature access system preserved

## Database Dependencies

- **Main Tables**: koordinator_pic, koordinator, list_fitur
- **History Tables**: hist_delete_pic
- **Activity Tracking**: activity_logs
- **Package Management**: koordinator.paket field

## Next Steps

1. Update client applications to use JWT authentication
2. Test user management workflows end-to-end
3. Monitor logs for authentication issues
4. Update API documentation for consumers
5. Verify package limitations and user quotas work correctly
