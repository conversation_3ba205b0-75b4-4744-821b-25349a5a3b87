<?php
header('Content-Type: application/json');
include '../model/database.php';
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

$data = array();

$status = "gagal";
$message = "Proses ganti bahasa gagal.";

if (isset($_GET['params'])) {
    $lang = $_GET['params'];
    $_SESSION['lang_params'] = $_GET['params'];

    try {
        // Mulai proses
        $conn->begin_transaction();

        // get data
        $get = $conn->prepare("SELECT * FROM translate WHERE lang = ?");
        $get->bind_param("s", $lang);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $array = array();
        if ($result->num_rows > 0) {
            while ($row = mysqli_fetch_array($result)) {
                $text = $row['text'];
                $array[$text] = $row['translate'];
            }

            $_SESSION['lang'] = $array;

            $status = "success";
            $message = "Proses ganti bahasa berhasil.";
        } else {
            $_SESSION['lang'] = $array;
            throw new Exception("Data tidak ditemukan.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }
}

$data['status'] = $status;
$data['message'] = $message;

$output = array("data" => $data);
$output = json_encode($output, JSON_PRETTY_PRINT);
echo $output;
