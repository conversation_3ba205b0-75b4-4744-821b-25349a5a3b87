<?php

/**
 * Cleanup script untuk membersihkan token blacklist yang expired
 * Script ini sebaik<PERSON> dijalankan via cron job setiap hari atau setiap jam
 */

require_once __DIR__ . '/../jwt_helper.php';

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Log start
$logFile = __DIR__ . '/../cache/cleanup_log.txt';
$startTime = date('Y-m-d H:i:s');

echo "Starting blacklisted tokens cleanup...\n";
file_put_contents($logFile, "[$startTime] Starting blacklisted tokens cleanup...\n", FILE_APPEND);

try {
    $deletedCount = cleanupExpiredBlacklistedTokens();

    $message = "Cleanup completed. Deleted {$deletedCount} expired tokens.";
    echo $message . "\n";
    echo "Cleanup time: " . date('Y-m-d H:i:s') . "\n";

    // Log success
    file_put_contents($logFile, "[$startTime] $message\n", FILE_APPEND);
    file_put_contents($logFile, "[$startTime] Cleanup completed successfully\n", FILE_APPEND);
} catch (Exception $e) {
    $errorMessage = "Error during cleanup: " . $e->getMessage();
    echo $errorMessage . "\n";

    // Log error
    file_put_contents($logFile, "[$startTime] ERROR: $errorMessage\n", FILE_APPEND);
}

// Bersihkan log lama (hanya simpan 30 hari terakhir)
$logLines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
if (count($logLines) > 1000) { // Jika log terlalu banyak
    $recentLines = array_slice($logLines, -500); // Ambil 500 baris terakhir
    file_put_contents($logFile, implode("\n", $recentLines) . "\n");
}
