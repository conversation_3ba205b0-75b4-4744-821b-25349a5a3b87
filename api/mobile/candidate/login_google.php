<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';

require_once(__DIR__ . '/../../../vendor/autoload.php');

use Firebase\JWT\JWT;
use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/../../..');
$dotenv->load();

function generateAccessToken($userData)
{
    $secretKey = $_ENV['ACCESS_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24); // 24 jam

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'nama' => $userData['nama'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}

function generateRefreshToken($userData)
{
    $secretKey = $_ENV['REFRESH_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24 * 30); // 30 hari

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}


$email = $_POST['email'];

$status = false;
$message = "Login gagal.";

$response['data'] = [];

function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}

$koordinat = "";
if (isset($_POST['koordinat'])) {
    $koordinat = $_POST['koordinat'];
}
$device = "";
if (isset($_POST['device'])) {
    $device = $_POST['device'];
}
$ip = "";
if (isset($_POST['ip'])) {
    $ip = $_POST['ip'];
}

try {
    // Mulai proses
    $conn->begin_transaction();

    // cek email user
    $sql = $conn->prepare("SELECT u.*, r.tempat_lahir, 
    r.tgl_lahir, r.jenis_kelamin, 
    CONCAT(r.alamat, ' RT ', r.rt, ' RW ', r.rw, ' ', 
    r.kecamatan, ' ', r.kota, ' ', r.provinsi, '. ', r.kode_pos) as alamat
    FROM users_kandidat u LEFT JOIN rh r ON u.pin = r.id WHERE u.email = ?");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $storedHash  = $row['password'];
        $pin = $row['pin'];
        $nama = $row['nama_lengkap'];
        $img = $row['foto'];
        $no_telp = $row['no_telp'];
        $tempat_lahir = $row['tempat_lahir'];
        $tgl_lahir = formatTanggal($row['tgl_lahir']);
        $jk = $row['jenis_kelamin'];
        $alamat = $row['alamat'];
        $fcmToken = $row['fcm_token'];

        if ($img != "") {
            if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $img)) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'kandidat/foto-profil/' . $img
                ]);

                $request = $s3->createPresignedRequest($cmd, '+24 hours');
                $img = (string) $request->getUri();
            }
        }

        // Set session data untuk kompatibilitas
        $dataUser = array('pin' => $pin, 'nama' => $nama, 'email' => $email, 'img' => $img, 'tipe' => 'kandidat');
        $_SESSION['users'] = $dataUser;
        unset($_SESSION['user_google']);

        // Generate JWT tokens
        $accessToken = generateAccessToken($dataUser);
        $refreshToken = generateRefreshToken($dataUser);

        // Simpan refresh token ke database
        $refresh_token_expiry = date('Y-m-d H:i:s', time() + (60 * 60 * 24 * 30)); // 30 hari
        $update_token_sql = $conn->prepare("UPDATE users_kandidat SET refresh_token = ?, refresh_token_expiry = ? WHERE pin = ?");
        $update_token_sql->bind_param("sss", $refreshToken, $refresh_token_expiry, $pin);
        $update_token_sql->execute();
        $update_token_sql->close();

        // simpan login history
        $id_log_hist = date("YmdHis");
        $created_at = date("Y-m-d H:i:s");

        $stmt = $conn->prepare("INSERT INTO login_history (`id`, `id_user`, `koordinat`, `device`, `ip`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $id_log_hist, $pin, $koordinat, $device, $ip, $created_at);
        $stmt->execute();

        // Log aktivitas berhasil login
        logActivity($conn, $pin, "INFO", "User atas nama $nama telah login via Google ke sistem.", "Web Digitalcv");

        // Jika semua query berhasil, commit transaksi
        $conn->commit();


        $status = true;
        $message = "Login berhasil.";
        $response['data'][] = array(
            'pin' => $pin,
            'nama' => $nama,
            'email' => $email,
            'img' => $img,
            'no_telp' => $no_telp,
            'tempat_lahir' => $tempat_lahir,
            'tgl_lahir' => $tgl_lahir,
            'jenis_kelamin' => $jk,
            'alamat' => $alamat,
            'fcm_token' => $fcmToken,
            'access_token' => isset($accessToken) ? $accessToken : null,
            'refresh_token' => isset($refreshToken) ? $refreshToken : null
        );
    } else {
        $status = false;
        $message = "Email belum terdaftar.";
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = false;
    $message = $e->getMessage();
}

$responses = [
    "success" => $status,
    "message" => $message,
    "data" => $response['data'],
];

$output = json_encode($responses, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit();
