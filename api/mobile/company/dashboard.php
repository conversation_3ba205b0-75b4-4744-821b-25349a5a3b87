<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';

// Import AWS Exception
use Aws\Exception\AwsException;

// Verify JWT token and get user data
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi ?? "";
$id_pegawai = $userData->id ?? "";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

// Note: id_koordinator, divisi, dan id_pegawai sudah diambil dari JWT token di bagian atas
// Data user yang terverifikasi melalui JWT:
// - id_koordinator: sudah terverifikasi dari token
// - divisi: sudah terverifikasi dari token
// - id_pegawai: sudah terverifikasi dari token

// Override parameter jika ada di POST (untuk kompatibilitas tertentu)
if (isset($_POST['id'])) {
    $id_pegawai = $_POST['id'];
}

if (isset($_POST['akses'])) {
    $akses = $_POST['akses'];
} else {
    $akses = "";
}
$func = $_GET['func'];


if ($func == 'cekNotif') {
    header('Content-Type: application/json');
    $data = [];

    // Menggunakan data dari JWT token, bukan dari $_GET
    // $id_koordinator sudah diambil dari JWT token
    // $divisi sudah diambil dari JWT token  
    $text = $_GET['text'] ?? '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $page_size = isset($_GET['page_size']) ? max(1, (int)$_GET['page_size']) : 10;
    $offset = ($page - 1) * $page_size;

    // ===== DATA 1: Dari list_request =====
    $sqlReq = $conn->prepare("SELECT * FROM `list_request`
               WHERE `status` != 'Deleted' AND `status` != '' AND `status` = 'Pending' AND id_koordinator = ?
               ORDER BY `create_at` DESC");
    $sqlReq->bind_param("s", $id_koordinator);
    $sqlReq->execute();
    $queryReq = $sqlReq->get_result();
    $sqlReq->close();

    if ($queryReq->num_rows > 0) {
        while ($row = mysqli_fetch_assoc($queryReq)) {
            $isi = strip_tags($row['id_req'] . ' : Permintaan recruitment dari department ' . $row['department'] . ' untuk posisi ' . $row['posisi'] . '.');
            if (strlen($isi) > 50) {
                $isi = substr($isi, 0, strrpos(substr($isi, 0, 50), ' ')) . '... Read More';
            }

            $jam = waktuLalu($row['create_at']);
            $data[] = [
                'tipe' => 'request',
                'id' => $row['id'],
                'isi' => $isi,
                'department' => $row['department'],
                'posisi' => $row['posisi'],
                'status' => $row['status'],
                'waktu' => $jam,
            ];
        }
    }

    // ===== DATA 2: Dari notif_summary =====
    $filterStatus = $text === 'Belum Dibaca' ? "AND n.status = 'belum dibaca'" : "";

    // Total data notif_summary
    $sqlCountNotif = $conn->prepare("SELECT COUNT(*) as total FROM notif_summary n
                      WHERE n.penerima = ? AND n.id_koordinator = ? $filterStatus");
    $sqlCountNotif->bind_param("ss", $divisi, $id_koordinator);
    $sqlCountNotif->execute();
    $resCountNotif = $sqlCountNotif->get_result();
    $sqlCountNotif->close();
    $row = mysqli_fetch_array($resCountNotif);

    $totalNotif = ($resCountNotif->num_rows > 0) ? (int)$row['total'] : 0;
    $totalPage = ceil($totalNotif / $page_size);

    // Ambil data notif_summary
    $sqlNotif = $conn->prepare("SELECT * FROM notif_summary n
                 WHERE n.penerima = ? AND n.id_koordinator = ? $filterStatus
                 ORDER BY n.create_at DESC
                 LIMIT ?, ?");
    $sqlNotif->bind_param("ssii", $divisi, $id_koordinator, $offset, $page_size);
    $sqlNotif->execute();
    $queryNotif = $sqlNotif->get_result();
    $sqlNotif->close();

    if ($queryNotif->num_rows > 0) {
        while ($row = mysqli_fetch_assoc($queryNotif)) {
            $jam = waktuLalu($row['create_at']);
            $data[] = [
                'tipe' => 'notif',
                'id' => $row['id'],
                'isi' => $row['isi'],
                'penerima' => $row['penerima'],
                'pengirim' => $row['pengirim'],
                'status' => $row['status'],
                'waktu' => $jam,
                'link' => $row['link'],
            ];
        }
    }

    // Output JSON
    echo json_encode([
        'status' => true,
        'message' => 'Berhasil mengambil notifikasi',
        'data' => $data,
        'page' => $page,
        'page_size' => $page_size,
        'total_data' => $totalNotif,
        'total_page' => $totalPage
    ], JSON_PRETTY_PRINT);
}

if ($func == 'summaryDivisi') {
    header('Content-Type: application/json');

    $response = [
        "status" => false,
        "message" => "Gagal mengambil data.",
        "data" => []
    ];

    // Menggunakan id_koordinator dari JWT token, bukan dari $_GET
    $company = $id_koordinator;
    // Hitung jumlah permintaan
    $sqlData = "SELECT id_req, department, posisi, status FROM list_request WHERE id_koordinator = ? AND id_req != '' AND `status` = 'On Proccess' GROUP BY id_req";
    $stmtData = $conn->prepare($sqlData);
    $stmtData->bind_param("s", $company);
    $stmtData->execute();
    $resultData = $stmtData->get_result();
    $countPermintaan = $resultData->num_rows;
    $stmtData->close();

    //get data pelamar
    $sqlPelamar = "SELECT
                        ul.id_lamar,
                        ul.id_req 
                    FROM
                        users_lamar ul
                        JOIN list_request lr ON ul.id_req = lr.id_req
                    WHERE
                        ul.id_koordinator = ? 
                        AND lr.`status` = 'On Proccess'
                    GROUP BY
                        ul.id_gestalt, ul.id_lamar";
    $stmtData = $conn->prepare($sqlPelamar);
    $stmtData->bind_param("s", $company);
    $stmtData->execute();
    $resultData = $stmtData->get_result();
    $stmtData->close();

    if ($resultData->num_rows > 0) {
        $countPelamar = $resultData->num_rows;
    } else {
        $countPelamar = 0;
    }

    // Log aktifitas
    $messages = 'Menampilkan data di dashboard.';
    $extra_info = "HRD";
    $level = "INFO";
    logActivity($conn, $id_pegawai, $level, $messages, $extra_info);

    // Set response
    $response['status'] = true;
    $response['message'] = "Berhasil mengambil data";
    $response['data'] = [[
        "total_permintaan" => $countPermintaan,
        "total_pelamar" => $countPelamar
    ]];

    echo json_encode($response, JSON_PRETTY_PRINT);
}

// if ($func == 'getChartJobPosting') {
//     $labels = [];
//     $data = [];
//     $link = [];

//     // get data job posting
//     $get = $conn->prepare("SELECT
//                                     MAX(id_req) as id_req,
//                                     posisi,
//                                     SUM(jumlah) as jumlah
//                                 FROM
//                                     (
//                                         SELECT
//                                             lr.id_req,
//                                             lr.posisi,
//                                             COUNT(DISTINCT ul.id_gestalt) as jumlah
//                                         FROM
//                                             list_request lr
//                                             LEFT JOIN users_lamar ul ON lr.id_req = ul.id_req AND lr.id_koordinator = ul.id_koordinator
//                                         WHERE
//                                             lr.id_koordinator = ?
//                                         GROUP BY
//                                             lr.id_req
//                                     ) h
//                                 GROUP BY
//                                     h.posisi");
//     $get->bind_param("s", $id_koordinator);
//     $get->execute();
//     $result = $get->get_result();
//     $get->close();

//     $temp = 0;
//     while ($row = mysqli_fetch_array($result)) {
//         $temp_label = $row['posisi'];
//         $id_req = $row['id_req'];

//         $labels[] = $row['posisi'];
//         $data[] = $row['jumlah'];
//         $link[$temp_label] = $id_req;

//         $temp += $row['jumlah'];
//     }

//     echo json_encode([
//         'labels' => $labels,
//         'data' => $data,
//         'jumlah' => $temp,
//         'id_req' => $link
//     ], JSON_PRETTY_PRINT);
// }

if ($func == 'getChartJobPosting') {
    $data = [];

    // get data job posting
    $get = $conn->prepare("SELECT
                                MAX(id_req) as id_req,
                                posisi,
                                SUM(jumlah) as jumlah
                            FROM
                                (
                                    SELECT
                                        lr.id_req,
                                        lr.posisi,
                                        COUNT(DISTINCT ul.id_gestalt) as jumlah
                                    FROM
                                        list_request lr
                                        LEFT JOIN users_lamar ul ON lr.id_req = ul.id_req AND lr.id_koordinator = ul.id_koordinator
                                    WHERE
                                        lr.id_koordinator = ?
                                        AND lr.`status` = 'On Proccess'
                                    GROUP BY
                                        lr.id_req
                                ) h
                            GROUP BY
                                h.posisi");
    $get->bind_param("s", $id_koordinator);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    while ($row = mysqli_fetch_array($result)) {
        $data[] = [
            "posisi" => $row['posisi'],
            "jumlah" => (int)$row['jumlah'],
            "id_req" => $row['id_req']
        ];
    }

    $status = !empty($data);
    $message = $status ? "Data ditemukan." : "Data tidak ditemukan.";

    echo json_encode([
        "status" => $status,
        "message" => $message,
        "data" => $data
    ], JSON_PRETTY_PRINT);
}




$conn->close();
exit();
