<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

$userData = requireAuth();


function getHarga($paket, $conn) {
    $sqlData = "SELECT price FROM pricing where paket = ?";
    $get = $conn->prepare($sqlData);
    $get->bind_param("s", $paket);

    $get->execute();
    $result = $get->get_result();
    $get->close();
    if ($result->num_rows > 0) {
        $harga = (int) json_encode(mysqli_fetch_assoc($result)['price']);
        return $harga;
    }

    return 0;
}

function formatRupiah($angka) {
    return 'Rp ' . number_format($angka, 0, ',', '.');
}

$response = [
    "status" => "success",
    "message" => "Berhasil Mengambil Data",
    "data" => [
        [
            "paket" => "C1",
            "judul" => getHarga('C1',$conn) == 0 ? "Gratis" : formatRupiah(getHarga('C1',$conn)),
            "harga" => getHarga('C1',$conn),
            "items" => [
                "Gratis akses 1 user",
                "Posting 3 lowongan/bulan",
                "Link Website",
                "Terima CV di digitalcv akses"
            ]
        ],
        [
            "paket" => "C2",
            "judul" => getHarga('C2',$conn) == 0 ? "Gratis" : formatRupiah(getHarga('C2',$conn)),
            "harga" => getHarga('C2',$conn),
            "items" => [
                "Gratis akses 1 user", 
                "Posting 10 lowongan/bulan", 
                "Link Website",
                "Online screening by system", 
                "Terima CV di digitalcv akses"
            ]
        ],
        [
            "paket" => "C3",
            "judul" => getHarga('C3',$conn) == 0 ? "Gratis" : formatRupiah(getHarga('C3',$conn)),
            "harga" => getHarga('C3',$conn),
            "items" => [
                "Gratis akses 3 users", 
                "Posting 20 lowongan/bulan", 
                "Link Website",
                "Online screening by system", 
                "Terima CV di digitalcv akses",
                "Cek digitalcv kandidat di Gestalt Universe max 10 kandidat/bulan"
            ]
        ],
        [
            "paket" => "C4",
            "judul" => getHarga('C4',$conn) == 0 ? "Gratis" : formatRupiah(getHarga('C4',$conn)),
            "harga" => getHarga('C4',$conn),
            "items" => [
                "Gratis akses 10 users", 
                "Unlimited posting lowongan", 
                "Link Website",
                "Online screening by system", 
                "Terima CV di digitalcv akses",
                "Cek digitalcv kandidat di Gestalt Universe (unlimited)",
                "Online Tes"
            ]
        ],
        [
            "paket" => "Custom",
            "judul" => "Konsultasi Dengan Kami",
            "harga" => 0,
            "items" => [
                "Multi user (unlimited)", 
                "Unlimited posting lowongan", 
                "Link Website",
                "Online screening by system", 
                "Terima CV di digitalcv akses",
                "Cek digitalcv kandidat di Gestalt Universe (unlimited)",
                "Online Tes",
                "Customise program Human Capital Recruitment (Client Tale Services)"
            ]
        ],
    ]
];


echo json_encode($response, JSON_PRETTY_PRINT);
exit;
