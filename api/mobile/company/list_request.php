<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';

// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;

// Legacy variables for backward compatibility
$id_pegawai = $userData->id;


$data = [];
$no = 0;

// PAGINATION PARAMS
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page_size = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
$offset = ($page - 1) * $page_size;
$q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
$q = substr($q, 0, 50); // batasi max 50 karakter
$querySearch = "";
if (!empty($q)) {
    $querySearch .= " AND (
        department LIKE '%$q%' OR 
        posisi LIKE '%$q%' OR 
        status LIKE '%$q%'
    )";
}

// Hitung total data (hanya yang belum expired)
$sqlTotalQuery = "SELECT COUNT(*) as total FROM list_request 
                WHERE status != 'Deleted' AND status != 'Selesai' AND status != '' AND id_req != '' AND id_koordinator = ? $querySearch";
$sqlTotal = $conn->prepare($sqlTotalQuery);
$sqlTotal->bind_param("s", $id_koordinator);
$sqlTotal->execute();
$resultTotal = $sqlTotal->get_result();
$sqlTotal->close();

$totalData = ($row = $resultTotal->fetch_assoc()) ? (int)$row['total'] : 0;

// Hitung total halaman
$totalPage = ceil($totalData / $page_size);

// Ambil data dengan limit dan offset (hanya yang belum expired)
$sqlData = $conn->prepare("SELECT
                id_req,
                department,
                posisi,
                create_at,
                jmlh_kandidat,
                status AS status_permintaan,
                expired_date
            FROM
                list_request 
            WHERE
                status != 'Deleted' AND status != 'Selesai' AND status != '' AND id_req != '' AND id_koordinator = ?
                $querySearch
            ORDER BY
                status, create_at DESC
            LIMIT ?, ?");
$sqlData->bind_param("sii", $id_koordinator, $offset, $page_size);
$sqlData->execute();
$queryData = $sqlData->get_result();
$sqlData->close();

if ($queryData->num_rows > 0) {
    while ($row = $queryData->fetch_assoc()) {
        $data[] = [
            'id_req' => $row['id_req'],
            'department' => $row['department'],
            'posisi' => $row['posisi'],
            'tanggal_permintaan' => date("d M Y", strtotime($row['create_at'])),
            'expired_date' => date("d M Y", strtotime($row['expired_date'])),
            'jumlah_kandidat' => (int)$row['jmlh_kandidat'],
            'status' => $row['status_permintaan'],
        ];
    }
}

$response = [
    'status' => true,
    'message' => 'Berhasil mengambil data',
    'data' => $data,
    'page' => $page,
    'page_size' => $page_size,
    'total_page' => $totalPage,
    'total_data' => $totalData
];

echo json_encode($response, JSON_PRETTY_PRINT);