<meta name="viewport" content="width=device-width, initial-scale=1">
<meta property="og:title" content="Cari <PERSON> Kerja Indonesia" />
<meta property="og:description" content="A short description of your content." />
<meta property="og:image" content="<?= $baseURL ?>assets/images/logo/logoDcv2.png" />
<meta property="og:url" content="<?= $baseURL ?>" />
<link rel="icon" href="../../../assets/images/logo/logoDcv2.png" />
<link rel="stylesheet" href="../assets/css/bootstrap-icons.css" />
<script src="../assets/js/plugin/webfont/webfont.min.js"></script>
<script>
    WebFont.load({
        google: {
            families: ["Public Sans:300,400,500,600,700"]
        },
        custom: {
            families: [
                "Font Awesome 5 Solid",
                "Font Awesome 5 Regular",
                "Font Awesome 5 Brands",
                "simple-line-icons",
            ],
            urls: ["../assets/css/fonts.min.css"],
        },
        active: function() {
            sessionStorage.fonts = true;
        },
    });
</script>

<link rel="stylesheet" href="../assets/css/bootstrap.min.css" />
<link rel="stylesheet" href="../assets/css/plugins.min.css" />
<link rel="stylesheet" href="../assets/css/kaiadmin.min.css" />
<link rel="stylesheet" href="../assets/css/style.css">
<link rel="stylesheet" href="../assets/css/bootstrap-datepicker.min.css">
<link rel="stylesheet" href="../assets/css/toastr/toastr.min.css" />
<script src="../assets/js/core/jquery-3.7.1.min.js"></script>
<script src="../assets/js/core/popper.min.js"></script>
<script src="../assets/js/core/bootstrap.min.js"></script>
<script src="../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js"></script>
<script src="../assets/js/plugin/chart.js/chart.min.js"></script>
<script src="../assets/js/plugin/jquery.sparkline/jquery.sparkline.min.js"></script>
<script src="../assets/js/plugin/chart-circle/circles.min.js"></script>
<script src="../assets/js/plugin/datatables/datatables.min.js"></script>
<script src="../assets/js/plugin/bootstrap-notify/bootstrap-notify.min.js"></script>
<script src="../assets/js/plugin/jsvectormap/jsvectormap.min.js"></script>
<script src="../assets/js/plugin/jsvectormap/world.js"></script>
<script src="../assets/js/plugin/sweetalert/sweetalert.min.js"></script>
<script src="../assets/js/plugin/select2/select2.full.min.js"></script>
<script src="../assets/js/kaiadmin.min.js"></script>
<script src="../assets/js/plugin/toastr/toastr.min.js"></script>
<script src="../assets/js/plugin/datepicker/bootstrap-datepicker.min.js"></script>
<script src="../assets/js/purify.min.js"></script>
<script src="https://www.google.com/recaptcha/api.js?render=6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF"></script>
<style>
    body {
        margin: 0;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 200px);
    }

    .wrapper {
        flex: 1;
        overflow-y: auto;
        scrollbar-width: none;
        /* Firefox */
    }

    .wrapper::-webkit-scrollbar {
        display: none;
        /* Chrome, Safari */
    }

    .whatsapp-wrapper {
        position: fixed;
        bottom: 70px;
        right: 20px;
        z-index: 1004;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: fadeIn 0.5s ease;
    }

    .whatsapp-float {
        width: 60px;
        height: 60px;
        background-color: #25D366;
        color: white;
        border-radius: 50%;
        text-align: center;
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        text-decoration: none;
    }

    .whatsapp-float:hover {
        transform: scale(1.1);
    }

    .whatsapp-close {
        background: #fff;
        border: none;
        color: #333;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
        padding: 0;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
        transition: background 0.2s;
    }

    .whatsapp-close:hover {
        background: #eee;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .profile-pic-2 {
        object-fit: cover;
        display: block;
        width: 100px;
        height: 100px;
    }

    .profile-pic-3 {
        object-fit: cover;
        max-height: 180px;
        display: block;
        width: 170px;
    }

    .profile-pic-4 {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        border: 3px solid #ddd;
        display: block;
    }
</style>
<script>
    $(document).ready(function() {
        cekNotif();
    });

    function closeWhatsAppWidget() {
        document.getElementById("whatsapp-widget").style.display = "none";
    }

    // Notif function
    function cekNotif() {
        $.get('../../controller/controller.php', {
                func: 'cekNotif'
            },
            function(response) {
                var obj = JSON.parse(JSON.stringify(response));

                if (obj.status == 'true') {
                    if (obj.konten.notification > 0) {
                        $(".notification").html(obj.konten.notification);
                        $(".notification").show();
                        $("#title-notif").html(obj.konten.titleNotif);
                        $(".notif-center").html(obj.konten.contentNotif);
                        $("#btn-read-all-notif").show();
                    } else {
                        $(".notification").html(0);
                        $(".notification").hide();
                        $("#title-notif").html('<?= translate('Anda tidak memiliki notfikasi') ?>');
                        $(".notif-center").html('');
                        $("#btn-read-all-notif").hide();
                    }
                } else {
                    $(".notification").html(0);
                    $(".notification").hide();
                    $("#title-notif").html('<?= translate('Anda tidak memiliki notfikasi') ?>');
                    $(".notif-center").html('');
                    $("#btn-read-all-notif").hide();
                }
            }
        );
    }

    function updateNotif(penerima, pengirim, tgl) {
        $.ajax({
            url: "../../controller/controller.php?func=updateNotif",
            type: "post",
            data: {
                penerima: penerima,
                pengirim: pengirim,
                tgl: tgl,
            },
            success: function(result) {
                var obj = jQuery.parseJSON(JSON.stringify(result));
                if (obj.status == "true") {
                    cekNotif();
                }
            },
        });
    }

    function readAllNotif() {
        $.ajax({
            url: "../../controller/controller.php",
            type: "post",
            data: {
                func: 'updateAllNotif'
            },
            success: function(result) {
                var obj = jQuery.parseJSON(JSON.stringify(result));
                if (obj.status == "true") {
                    cekNotif();
                }
            },
        });
    }

    function changeLang(params) {
        $.get('<?= $baseURL ?>api/changeLang.php', {
                params: params
            },
            function(response) {
                window.location.reload();
            }
        )
    }
    // End notif function

    // Chat function
    function cekChat(message, reference_id) {
        // Auto read
        if ($('#chat-area-' + reference_id).length > 0 && $("#modal-chat").hasClass('show')) {
            // Update chat area
            if (typeof scrollToBottom === "function") {
                chatArea = $('.chat-area').html();
                const temp_date = new Date();
                const jam = String(temp_date.getHours()).padStart(2, '0');
                const menit = String(temp_date.getMinutes()).padStart(2, '0');
                waktu = jam + ':' + menit;
                var message_box = '';

                // Ambil tanggal hari ini dalam format YYYY-MM-DD
                const today = new Date().toISOString().split('T')[0];
                console.log(today);

                // Cek apakah ada elemen dengan data-tanggal = hari ini
                const $todayGroup = $(`.chat-group[data-tanggal="${today}"]`);

                // Buat badge hari ini jika tidak ada
                var chatAwal = false;
                if ($todayGroup.length == 0) {
                    chatAwal = true;
                    message_box = message_box + '<div class="chat-group" data-tanggal="' + today + '">' +
                        '<div class="row">' +
                        '<div class="col-12 chat-badge" style="text-align: center;">' +
                        '<span class="badge bg-light text-dark">Hari Ini</span>' +
                        '</div>' +
                        '</div>';
                }

                let pesanHtml = message.replace(/\n/g, "<br>");
                message_box = message_box + '<div class="msg-row left">' +
                    '<div class="bubble">' +
                    '' + pesanHtml + '' +
                    '<span class="meta">' + waktu + '</span>' +
                    '</div>' +
                    '</div>';

                if (chatAwal) {
                    message_box = message_box + '</div>';
                }

                $('.chat-area').html(chatArea + message_box);
                scrollToBottom();
            }

            $.ajax({
                url: "../../controller/chat.php",
                type: "post",
                data: {
                    func: 'autoRead',
                    reference_id: reference_id
                },
                success: function(result) {},
            });
        } else {
            // Update notif chat
            $.ajax({
                url: "../../controller/chat.php",
                type: "post",
                data: {
                    func: 'cekNotifChat',
                    reference_id: reference_id
                },
                success: function(result) {
                    var obj = jQuery.parseJSON(JSON.stringify(result));
                    if (obj.status) {
                        if (obj.data.notifChatAll > 0) {
                            if (obj.data.notifChatAll > 99) {
                                $(".badge-chat-notif-all").html('99+');
                            } else {
                                $(".badge-chat-notif-all").html(obj.data.notifChatAll);
                            }

                            $(".badge-chat-notif-all").show();
                        } else {
                            $(".badge-chat-notif-all").hide();
                        }

                        ref_id = btoa(reference_id).replace(/[^a-zA-Z0-9_]/g, '');
                        if (obj.data.notifChatRef > 0) {
                            if (obj.data.notifChatRef > 99) {
                                $("#badge-chat-notif-" + ref_id).html('99+');
                            } else {
                                $("#badge-chat-notif-" + ref_id).html(obj.data.notifChatRef);
                            }

                            $("#badge-chat-notif-" + ref_id).show();
                        } else {
                            $("#badge-chat-notif-" + ref_id).hide();
                        }
                    }
                },
            });
        }
    }
    // End chat function
</script>
<!-- WebSocket -->
<script>
    const userId = "<?php echo $pin; ?>";
    const ws = new WebSocket(
        "ws://localhost:3000?userId=" + userId
    );

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data); // Mengubah JSON menjadi objek

        if (data.to == userId) {
            // Cek tipe
            if (data.tipe == 'notif') {
                // abaikan kalau cuma heartbeat
                if (typeof cekNotif === "function") {
                    // Function ada dan bisa dipanggil
                    cekNotif();
                }
            } else if (data.tipe == 'chat') {
                cekChat(data.message, data.reference_id);
            }
        }
    };

    ws.onclose = () => {
        console.log("Disconnected from WebSocket server");
    };
</script>
<div id="whatsapp-widget" class="whatsapp-wrapper">
    <a href="https://wa.me/628117797779"
        target="_blank"
        title="Chat with us on WhatsApp"
        class="whatsapp-float"><i class="fab fa-whatsapp" style="font-size:35px"></i>
    </a>
    <button class="whatsapp-close" onclick="closeWhatsAppWidget()">×</button>
</div>