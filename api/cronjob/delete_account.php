<?php
header('Content-Type: application/json');
include '../../model/database.php';
require '../../vendor/autoload.php';
include '../../api/ses.php';
include '../../api/s3.php';

// Cek koneksi
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $conn->connect_error
    ]);
    exit;
}

$data = array();

$status = "gagal";
$message = "Proses hapus akun gagal.";

$tgl = date("Y-m-d") . " 00:00:00";
$created_at = date("Y-m-d H:i:s");

try {
    // <PERSON><PERSON> proses
    $conn->begin_transaction();

    // kirim email informasi penutupan loker ke kandidat yang masih dalam proses
    $get = $conn->prepare("SELECT
                            *
                        FROM
                            list_hapus_akun
                        WHERE
                            deleted_at = ?
                            AND `status` = 'pending'
                        GROUP BY id_akun");
    $get->bind_param("s", $tgl);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    $errors = [];

    if ($result->num_rows > 0) {
        // hapus file img akun
        $stmt = $conn->prepare("SELECT
                                    l.id_akun,
                                    IF(k.label IS NULL, uk.nama_lengkap,k.label) as nama_akun,
                                    IF(k.label IS NULL, 'Kandidat', 'Perusahaan') as ket,
                                    IF(k.img IS NULL, uk.foto, k.img) as img_akun,
                                    IF(k.img_banner IS NULL, '', k.img_banner) as img_banner_akun
                                FROM
                                    list_hapus_akun l
                                    LEFT JOIN koordinator k ON l.id_akun = k.id_koordinator
                                    LEFT JOIN users_kandidat uk ON l.id_akun = uk.pin
                                WHERE
                                    l.deleted_at = ?
                                    AND l.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();

            if ($result->num_rows > 0) {
                // Daftar file yang ingin dihapus
                $filesToDelete = [];
                while ($row = mysqli_fetch_array($result)) {
                    if ($row['ket'] == 'Kandidat' && $row['img_akun'] != '') {
                        $temp = ['Key' => 'kandidat/foto-profil/' . $row['img_akun']];
                        $filesToDelete[] = $temp;
                    } else {
                        if ($row['img_akun'] != '') {
                            $temp = ['Key' => 'perusahaan/logo/' . $row['img_akun']];
                            $filesToDelete[] = $temp;
                        }

                        if ($row['img_banner_akun'] != '') {
                            $temp = ['Key' => 'perusahaan/banner/' . $row['img_banner_akun']];
                            $filesToDelete[] = $temp;
                        }
                    }
                }

                try {
                    $result = $s3->deleteObjects([
                        'Bucket' => $bucket,
                        'Delete' => [
                            'Objects' => $filesToDelete,
                            'Quiet'   => false
                        ],
                    ]);
                } catch (Aws\Exception\AwsException $e) {
                    $errors[] = "Penghapusan img akun gagal: " . $e->getMessage();
                }
            }
        } else {
            $errors[] = "Penghapusan img akun gagal: " . $conn->error;
        }
        // end hapus file img akun

        // simpan histori akun yang akan dihapus
        $stmt = $conn->prepare("INSERT INTO hist_hapus_akun (`id_akun`,`nama_akun`,`ket`,`created_at`)
                                SELECT
                                    l.id_akun,
                                    IF(k.label IS NULL, uk.nama_lengkap,k.label) as nama_akun,
                                    IF(k.label IS NULL, 'Kandidat', 'Perusahaan') as ket,
                                    ? as created_at
                                FROM
                                    list_hapus_akun l
                                    LEFT JOIN koordinator k ON l.id_akun = k.id_koordinator
                                    LEFT JOIN users_kandidat uk ON l.id_akun = uk.pin
                                WHERE
                                    l.deleted_at = ?
                                    AND l.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("ss", $created_at, $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penyimpanan histori akun gagal: " . $conn->error;
        }
        // end simpan histori akun yang akan dihapus

        // hapus login_history
        $stmt = $conn->prepare("DELETE a 
                                FROM
                                    login_history a
                                    LEFT JOIN users_kandidat uk ON a.id_user = uk.pin
                                    LEFT JOIN koordinator_pic kp ON a.id_user = kp.id_pic
                                    JOIN list_hapus_akun b ON uk.pin = b.id_akun 
                                    OR kp.id_koordinator = b.id_akun 
                                WHERE
                                    b.deleted_at = ? 
                                    AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table login_history gagal: " . $conn->error;
        }
        // end hapus login_history

        // hapus feedback
        $stmt = $conn->prepare("DELETE a FROM feedback a JOIN list_hapus_akun b ON a.pincode COLLATE utf8mb4_0900_ai_ci = b.id_akun OR a.id_koordinator COLLATE utf8mb4_0900_ai_ci = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table feedback gagal: " . $conn->error;
        }
        // end hapus feedback

        // hapus hasil_screening
        $stmt = $conn->prepare("DELETE a FROM hasil_screening a JOIN list_hapus_akun b ON a.id_gestalt = b.id_akun OR a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table hasil_screening gagal: " . $conn->error;
        }
        // end hapus hasil_screening

        // hapus hist_delete_pic
        $stmt = $conn->prepare("DELETE a FROM hist_delete_pic a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table hist_delete_pic gagal: " . $conn->error;
        }
        // end hapus hist_delete_pic

        // hapus kandidat_pilihan
        $stmt = $conn->prepare("DELETE a FROM kandidat_pilihan a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table kandidat_pilihan gagal: " . $conn->error;
        }
        // end hapus kandidat_pilihan

        // hapus koordinator
        $stmt = $conn->prepare("DELETE a FROM koordinator a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table koordinator gagal: " . $conn->error;
        }
        // end hapus koordinator

        // hapus koordinator_pic
        $stmt = $conn->prepare("DELETE a FROM koordinator_pic a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table koordinator_pic gagal: " . $conn->error;
        }
        // end hapus koordinator_pic

        // hapus limit_cv_universe
        $stmt = $conn->prepare("DELETE a FROM limit_cv_universe a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table limit_cv_universe gagal: " . $conn->error;
        }
        // end hapus limit_cv_universe

        // hapus list_favorite
        $stmt = $conn->prepare("DELETE a FROM list_favorite a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table list_favorite gagal: " . $conn->error;
        }
        // end hapus list_favorite

        // hapus list_iklan
        $stmt = $conn->prepare("DELETE a FROM list_iklan a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table list_iklan gagal: " . $conn->error;
        }
        // end hapus list_iklan

        // hapus list_kriteria
        $stmt = $conn->prepare("DELETE a FROM list_kriteria a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table list_kriteria gagal: " . $conn->error;
        }
        // end hapus list_kriteria

        // hapus list_request
        $stmt = $conn->prepare("DELETE a FROM list_request a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table list_request gagal: " . $conn->error;
        }
        // end hapus list_request

        // hapus notif_summary
        $stmt = $conn->prepare("DELETE a FROM notif_summary a JOIN list_hapus_akun b ON a.pengirim = b.id_akun OR a.penerima = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table notif_summary gagal: " . $conn->error;
        }
        // end hapus notif_summary  

        // hapus penguasaan_bahasa
        $stmt = $conn->prepare("DELETE a FROM penguasaan_bahasa a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table penguasaan_bahasa gagal: " . $conn->error;
        }
        // end hapus penguasaan_bahasa

        // hapus rh
        $stmt = $conn->prepare("DELETE a FROM rh a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table rh gagal: " . $conn->error;
        }
        // end hapus rh

        // hapus riwayat_kursus
        $stmt = $conn->prepare("DELETE a FROM riwayat_kursus a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table riwayat_kursus gagal: " . $conn->error;
        }
        // end hapus riwayat_kursus

        // hapus riwayat_organisasi
        $stmt = $conn->prepare("DELETE a FROM riwayat_organisasi a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table riwayat_organisasi gagal: " . $conn->error;
        }
        // end hapus riwayat_organisasi

        // hapus riwayat_pekerjaan
        $stmt = $conn->prepare("DELETE a FROM riwayat_pekerjaan a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table riwayat_pekerjaan gagal: " . $conn->error;
        }
        // end hapus riwayat_pekerjaan

        // hapus riwayat_pendidikan
        $stmt = $conn->prepare("DELETE a FROM riwayat_pendidikan a JOIN list_hapus_akun b ON a.id = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table riwayat_pendidikan gagal: " . $conn->error;
        }
        // end hapus riwayat_pendidikan

        // hapus riwayat_screening
        $stmt = $conn->prepare("DELETE a FROM riwayat_screening a JOIN list_hapus_akun b ON a.id_gestalt = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table riwayat_screening gagal: " . $conn->error;
        }
        // end hapus riwayat_screening

        // hapus screening_recruitment
        $stmt = $conn->prepare("DELETE a FROM screening_recruitment a JOIN list_hapus_akun b ON a.id_akun = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table screening_recruitment gagal: " . $conn->error;
        }
        // end hapus screening_recruitment

        // hapus template_lowongan
        $stmt = $conn->prepare("DELETE a FROM template_lowongan a JOIN list_hapus_akun b ON a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table template_lowongan gagal: " . $conn->error;
        }
        // end hapus template_lowongan

        // hapus users_kandidat
        $stmt = $conn->prepare("DELETE a FROM users_kandidat a JOIN list_hapus_akun b ON a.pin = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table users_kandidat gagal: " . $conn->error;
        }
        // end hapus users_kandidat

        // hapus users_lamar
        $stmt = $conn->prepare("DELETE a FROM users_lamar a JOIN list_hapus_akun b ON a.id_gestalt = b.id_akun OR a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table users_lamar gagal: " . $conn->error;
        }
        // end hapus users_lamar

        // hapus users_lamar_history
        $stmt = $conn->prepare("DELETE a FROM users_lamar_history a JOIN list_hapus_akun b ON a.id_gestalt = b.id_akun OR a.id_koordinator = b.id_akun WHERE b.deleted_at = ? AND b.`status` = 'pending'");
        if ($stmt) {
            $stmt->bind_param("s", $tgl);
            $stmt->execute();
            $stmt->close();
        } else {
            $errors[] = "Penghapusan akun pada table users_lamar_history gagal: " . $conn->error;
        }
        // end hapus users_lamar_history

        // jika error
        if (count($errors) > 0) {
            throw new Exception("Transaksi dibatalkan karena error:\n" . implode("\n", $errors));
        }

        // update status
        $update = $conn->prepare("UPDATE list_hapus_akun SET `status` = 'selesai' WHERE deleted_at = ? AND `status` = 'pending'");
        $update->bind_param("s", $tgl);
        if ($update->execute()) {
            $update->close();

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Proses hapus akun berhasil.";
        } else {
            throw new Exception("Proses hapus akun gagal.");
        }
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = "gagal";
    $message = $e->getMessage();
}

$data['status'] = $status;
$data['message'] = $message;

$output = array("data" => $data);
$output = json_encode($output, JSON_PRETTY_PRINT);
echo $output;
