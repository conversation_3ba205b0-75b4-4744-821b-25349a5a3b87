<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}
$data = array();

$status = false;
$message = "Proses gagal dilakukan.";
$created_at = date("Y-m-d H:i:s");

$insertedData = null;
$deletedData = null;
$favoriteList = [];
try {
    // Mulai proses
    $conn->begin_transaction();

    // cek apakah user terdaftar atau tidak
    $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows == 0) {
        throw new Exception("Proses gagal dilakukan. Silakan untuk login kembali.");
    }

    $q = "-";
    $check = "-";
    $id_req = "-";
    $lokasi = "-";

    // cek parameter
    if (isset($_POST['q']) && isset($_POST['check'])) {
        $q = base64_decode($_POST['q']);
        $arr = explode("|", $q);

        if (isset($arr[0])) {
            $id_req = $arr[0];
        }

        if (isset($arr[1])) {
            $lokasi = $arr[1];
        }

        $check = $_POST['check'];
    } else {
        throw new Exception("Proses gagal dilakukan. Parameter tidak ditemukan.");
    }

    if ($check == "-" || $id_req == "-" || $lokasi == "-") {
        throw new Exception("Proses gagal dilakukan. Parameter tidak ditemukan.");
    }


    // Update lowongan favorite
    if ($check == "true") {
        // Simpan lowongan favorite
        $insert = $conn->prepare("INSERT INTO `list_favorite`(`id`, `id_req`, `lokasi`, `created_at`) 
        VALUES (?, ?, ?, ?)");
        $insert->bind_param("ssss", $pin, $id_req, $lokasi, $created_at);

        if ($insert->execute()) {
            $insert->close();

            $messages = 'Menyimpan lowongan favorite dengan id lowongan ' . $id_req . ' lokasi ' . $lokasi . '.';

            // Ambil kembali data yang baru saja diinsert
            $select = $conn->prepare("SELECT * FROM `list_favorite` WHERE id = ? AND id_req = ? AND lokasi = ?");
            $select->bind_param("sss", $pin, $id_req, $lokasi);
            $select->execute();
            $result = $select->get_result();

            if ($result->num_rows > 0) {
                $insertedData = $result->fetch_assoc();
            }
            $select->close();
            $status = true;
            $message = "Menyimpan lowongan favorite berhasil dilakukan.";
        } else {
            throw new Exception("Proses gagal dilakukan. Silakan hubungi administrator");
        }
    } else {
        // Ambil data sebelum dihapus
        $select = $conn->prepare("SELECT * FROM `list_favorite` WHERE id = ? AND id_req = ? AND lokasi = ?");
        $select->bind_param("sss", $pin, $id_req, $lokasi);
        $select->execute();
        $result = $select->get_result();

        if ($result->num_rows > 0) {
            $deletedData = $result->fetch_assoc();
        }
        $select->close();

        // Hapus lowongan favorite
        $delete = $conn->prepare("DELETE FROM `list_favorite` WHERE id = ? AND id_req = ? AND lokasi = ?");
        $delete->bind_param("sss", $pin, $id_req, $lokasi);

        if ($delete->execute()) {
            $delete->close();

            $messages = 'Membatalkan lowongan favorite dengan id lowongan ' . $id_req . ' lokasi ' . $lokasi . '.';
            $status = true;
            $message = "Membatalkan lowongan favorite berhasil dilakukan.";
        } else {
            throw new Exception("Proses gagal dilakukan. Silakan hubungi administrator");
        }
    }

    // Simpan log aktivitas
    $extra_info = "Kandidat";
    $level = "INFO";
    logActivity($conn, $pin, $level, $messages, $extra_info);

    // Jika semua query berhasil, commit transaksi
    $conn->commit();
} catch (Exception $e) {
    $conn->rollback();
    $status = false;
    $message = $e->getMessage();
}

$data['success'] = $status;
$data['message'] = $message;
$data['data'] = $insertedData != null ? $insertedData : $deletedData;

header('Content-Type: application/json');
echo json_encode($data, JSON_PRETTY_PRINT);


$conn->close();
exit;
