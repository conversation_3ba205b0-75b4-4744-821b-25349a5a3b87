//start function untuk mengambil angka saja
function hanyaAngka(evt) {
  var charCode = evt.which ? evt.which : event.keyCode;

  if (charCode < 48 || charCode > 57) return false;
  return true;
}
//end function untuk mengambil angka saja

//start funciton untuk menampilkan password saat melakukan input
function showPass(id, btn) {
  var input = document.getElementById(id);
  btn = document.getElementById(btn);

  if (input.type === "password") {
    input.type = "text";
    btn.innerHTML = '<i class="bi bi-eye"></i>';
  } else {
    input.type = "password";
    btn.innerHTML = '<i class="bi bi-eye-slash"></i>';
  }
}
//end funciton untuk menampilkan password saat melakukan input

//start fungsi submit daftar company
$("#form-daftar").on("submit", function (event) {
  event.preventDefault(); // Mencegah reload form
  event.stopPropagation(); // Menghentikan event bubbling

  if (this.checkValidity() === false) {
    $(this).addClass("was-validated");
    return;
  }

  //informasi perekrut
  var nama_pic = $("[name=nama_pic]").val();
  let phoneNumber = $("[name=no_hp_pic]").val().replace(/^0+/, ''); // Hilangkan angka 0 di awal
  let countryCode = $('#country-code').val();
  let no_hp_pic = countryCode + phoneNumber;
  var email_pic = $("[name=email_pic]").val();
  var password = $("[name=password]").val();
  var kon_password = $("[name=kon_password]").val();

  //informasi perusahaan
  var nama_perusahaan = $("[name=nama_perusahaan]").val();
  var no_tlp_perusahaan = $("[name=no_tlp_perusahaan]").val();
  var email_perusahaan = $("[name=email_perusahaan]").val();
  var alamat_perusahaan = $("[name=alamat_perusahaan]").val();
  var website_perusahaan = $("[name=website_perusahaan]").val();
  var industri_perusahaan = $("[name=industri_perusahaan]").val();
  var jenis_lainnya = $("[name=jenis_lainnya]").val();
  if (industri_perusahaan == 'Lainnya') {
    var industri_perusahaan = jenis_lainnya;
  }
  var deskripsi_perusahaan = $("[name=deskripsi_perusahaan]").val();

  // cek password dan konfirmasi password apakah sudah sama
  if (password != kon_password) {
    toastr["warning"](window.appData.alert3);
  } else {
    $("#btn-daftar").html(window.appData.harapTunggu + "....");
    $("#btn-daftar").attr("disabled", true);
    $.ajax({
      type: "post",
      url: "../../controller/sign-up/registerCompany",
      data: {
        nama_pic: nama_pic,
        no_hp_pic: no_hp_pic,
        email_pic: email_pic,
        password: password,
        nama_perusahaan: nama_perusahaan,
        no_tlp_perusahaan: no_tlp_perusahaan,
        email_perusahaan: email_perusahaan,
        alamat_perusahaan: alamat_perusahaan,
        website_perusahaan: website_perusahaan,
        industri_perusahaan: industri_perusahaan,
        deskripsi_perusahaan: deskripsi_perusahaan,
      },
      success: function (result) {
        var obj = JSON.parse(JSON.stringify(result));

        if (obj.status == "success") {
          Swal.fire({
            title: window.appData.berhasil,
            text: window.appData.textberhasil,
            icon: "success",
          }).then((result) => {
            window.location.href = "../login";
          });
        } else {
          toastr["error"](obj.message);
          $("#btn-daftar").html(window.appData.daftar);
          $("#btn-daftar").prop("disabled", false);
        }
      },
    });
  }
});
//end fungsi submit daftar company


$("#industri_perusahaan").on("change", function (event) {
  if ($(this).val() == 'Lainnya') {
    $('#konten_jenis_lainnya').show();
    $("#jenis_lainnya").prop("required", true)
  } else {
    $('#konten_jenis_lainnya').hide();
    $("#jenis_lainnya").prop("required", false)
  }

})