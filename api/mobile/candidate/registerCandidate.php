<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

// get from post data users
$nama = $_POST['nama'];
$email = $_POST['email'];
$otp = $_POST['otp'];
$foto = '';
if (isset($_POST['foto'])) {
    $foto = $_POST['foto'];
}
$no_tlp = $_POST['no_tlp'];
$password = hashPassword($_POST['password']);
$fcm_token = "";
if (isset($_POST['fcm_token'])) {
    $fcm_token = $_POST['fcm_token'];
}

// tambahan
$tgl = date('Y-m-d H:i:s');

// function buat pincode
function create_pincode($length)
{
    $data = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
    $string = '';
    for ($i = 0; $i < $length; $i++) {
        $pos = rand(0, strlen($data) - 1);
        $string .= $data[$pos];
    }
    return $string;
}

$status = "gagal";
$message = "Pendaftaran gagal.";

try {
    // Mulai proses
    $conn->begin_transaction();

    // cek jika pada rh email atau nomor telepon sudah terdaftar
    $sql = $conn->prepare("SELECT id, nama, ktp FROM rh WHERE email = ? ORDER BY `no` DESC LIMIT 1");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $rowRH = mysqli_fetch_array($result);
        $pincode = $rowRH['id'];
    } else {
        // Generate pin code
        $sqlCek = "SELECT id FROM rh";
        $queryCek = $conn->query($sqlCek);

        do {
            $pincode = create_pincode(15);
        } while ($pincode == $queryCek);
    }

    if (!isset($_SESSION['user_google'])) {
        $timezone = "SET time_zone = '+07:00'";
        $conn->query($timezone);

        //cek otp
        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
        $sql->bind_param("ss", $otp, $email);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // Update Status OTP
            $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
            $update->bind_param("ss", $otp, $email);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Pendaftaran Gagal.");
            }
        } else {
            throw new Exception("OTP Salah.");
        }
    }

    // cek data user email dan no telepon
    $sql = $conn->prepare("SELECT
                            GROUP_CONCAT(DISTINCT flag) as flag
                        FROM
                        (
                            SELECT pin, 'email' as flag FROM users_kandidat WHERE email = ? GROUP BY pin
                        ) h");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);

        if ($row['flag'] == 'email,no_telp') {
            throw new Exception("E-mail dan No. Handphone sudah terdaftar.");
        } elseif ($row['flag'] == 'email') {
            throw new Exception("E-mail sudah terdaftar.");
        } elseif ($row['flag'] == 'no_telp') {
            throw new Exception("No. Handphone sudah terdaftar.");
        }
    }

    // Proses penyimpanan data kandidat
    $insert = $conn->prepare("INSERT INTO `users_kandidat`(`pin`, `nama_lengkap`, `email`, `no_telp`, `password`, `status`, `tgl`, `created_at`,`foto`, `from`, `fcm_token`) 
                              VALUES (?, ?, ?, ?, ?, 'Active', ?, ?, ?, 'Digitalcv CV Mobile', ?)");
    $insert->bind_param("sssssssss", $pincode, $nama, $email, $no_tlp, $password, $tgl, $tgl, $foto, $fcm_token);

    if ($insert->execute()) {
        $insert->close();

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Pendaftaran berhasil.";
    } else {
        throw new Exception("Pendaftaran Gagal.");
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = "gagal";
    $message = $e->getMessage();
}

$data['status'] = $status;
$data['message'] = $message;

$output = json_encode($data, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit();
