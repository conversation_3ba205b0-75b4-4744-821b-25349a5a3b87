<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../jwt_helper.php';

// Middleware untuk autentikasi JWT
$userData = requireAuth();

$response = [
    'success' => false,
    'message' => 'Update profil gagal',
    'data' => []
];

try {
    // Ambil input dari request body
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Data input tidak valid');
    }

    // Mulai transaksi
    $conn->begin_transaction();

    // Update data user berdasarkan input yang diterima
    $updateFields = [];
    $params = [];
    $types = "";

    if (isset($input['nama_lengkap'])) {
        $updateFields[] = "nama_lengkap = ?";
        $params[] = $input['nama_lengkap'];
        $types .= "s";
    }

    if (isset($input['no_telp'])) {
        $updateFields[] = "no_telp = ?";
        $params[] = $input['no_telp'];
        $types .= "s";
    }

    if (isset($input['fcm_token'])) {
        $updateFields[] = "fcm_token = ?";
        $params[] = $input['fcm_token'];
        $types .= "s";
    }

    if (empty($updateFields)) {
        throw new Exception('Tidak ada data yang akan diupdate');
    }

    // Tambahkan parameter pin untuk WHERE clause
    $params[] = $userData->pin;
    $types .= "s";

    $sql = "UPDATE users_kandidat SET " . implode(', ', $updateFields) . " WHERE pin = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();

    if ($stmt->affected_rows > 0) {
        // Log aktivitas update profil
        $updateInfo = implode(', ', array_keys($input));
        logActivity($conn, $userData->pin, "INFO", "User atas nama {$userData->nama} telah mengupdate profil: {$updateInfo}", "Mobile App");

        // Commit transaksi
        $conn->commit();

        $response = [
            'success' => true,
            'message' => 'Profil berhasil diupdate',
            'data' => []
        ];
    } else {
        throw new Exception('Tidak ada data yang berubah atau user tidak ditemukan');
    }

    $stmt->close();
} catch (Exception $e) {
    // Rollback jika ada error
    $conn->rollback();

    $response['message'] = $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
