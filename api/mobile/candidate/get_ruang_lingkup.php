<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

// Secure query execution function
function executeSecureQuery($conn, $sql, $params = [], $types = "")
{
    try {
        if (!empty($params)) {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types)) {
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        } else {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        }
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Output sanitization function
function sanitizeOutput($data)
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $key => $value) {
            $sanitized[$key] = sanitizeOutput($value);
        }
        return $sanitized;
    }

    if (is_string($data)) {
        return $data;
    }

    return $data;
}


$response['data'] = [];

// Execute secure query to fetch ruang lingkup data
$sql = "SELECT * FROM ruang_lingkup_pekerjaan ORDER BY id ASC";
$result = executeSecureQuery($conn, $sql);

if ($result === false) {
    // Handle database error
    $response = [
        "success" => false,
        "message" => "Terjadi kesalahan dalam mengambil data",
        "data" => []
    ];
} else {
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Sanitize output data to prevent XSS
            $response['data'][] = sanitizeOutput($row);
        }
    }

    // Format successful response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];
}

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

$conn->close();
exit;
