<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

// $userData = requireAuth();
// $pin = $userData->pin ?? "-";
// $nama_user = $userData->nama ?? "-";
// $email_user = $userData->email ?? "-";

function sanitizeOutput($data, $key = '')
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $k => $value) {
            $sanitized[$k] = sanitizeOutput($value, $k);
        }
        return $sanitized;
    }

    // Don't sanitize specific fields that contain URLs or encoded data
    $protected_fields = ['logoURL', 'link', 'link_perusahaan', 'temp_kode', 'img'];
    if (in_array($key, $protected_fields)) {
        return $data;
    }

    // Don't sanitize URLs (especially S3 presigned URLs)
    if (is_string($data)) {
        if (filter_var($data, FILTER_VALIDATE_URL) !== false) {
            return $data;
        }
        return $data;
    }

    return $data;
}

function executeSecureQuery($conn, $query, $params = [], $types = "")
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Failed to prepare statement");
        }

        if (!empty($params) && !empty($types)) {
            if (!$stmt->bind_param($types, ...$params)) {
                throw new Exception("Failed to bind parameters");
            }
        }

        if (!$stmt->execute()) {
            throw new Exception("Failed to execute query");
        }

        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Failed to get result");
        }

        $stmt->close();
        return $result;
    } catch (Exception $e) {
        if (isset($stmt)) {
            $stmt->close();
        }
        return false;
    }
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Validasi dan sanitasi input parameters
$limit = isset($_POST['page_size']) ? min(100, max(1, (int) $_POST['page_size'])) : 3;
$page = isset($_POST['page']) ? max(1, (int) $_POST['page']) : 1;

// Validasi halaman maksimal untuk mencegah overflow
if ($page > 10000) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Halaman tidak valid'
    ], JSON_PRETTY_PRINT);
    exit;
}

$start = ($page - 1) * $limit;

// Validasi parameter q (id_koordinator)
if (!isset($_POST['q']) || empty(trim($_POST['q']))) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Parameter q (ID koordinator) wajib diisi'
    ], JSON_PRETTY_PRINT);
    exit;
}

$q = trim($_POST['q']);

// Validasi format id_koordinator - hanya alphanumerik dan underscore
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $q)) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Format ID koordinator tidak valid'
    ], JSON_PRETTY_PRINT);
    exit;
}

// Query utama dengan security yang ditingkatkan
$query = "SELECT
            lr.*,
            k.img,
            k.alamat,
            k.tipe,
            k.link,
            k.link_perusahaan,
            k.deskripsi,
            lk.kk,
            lk.kb,
            lk.km,
            lk.kbdu,
            lk.rlp
        FROM
            list_request lr
            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
            JOIN list_kriteria lk ON lr.id_req = lk.id_req
        WHERE
            lr.`status` = 'On Proccess' 
            AND lr.id_koordinator = ?
        ORDER BY lr.create_at DESC
        LIMIT ?, ?";

$result = executeSecureQuery($conn, $query, [$q, $start, $limit], "sii");

if ($result === false) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan dalam mengambil data'
    ], JSON_PRETTY_PRINT);
    exit;
}

// Query untuk total data dengan security yang ditingkatkan
$total_query = "SELECT lr.lokasi_kerja FROM list_request lr
    JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
    JOIN list_kriteria lk ON lr.id_req = lk.id_req
    WHERE lr.status = 'On Proccess'
    AND lr.id_koordinator = ?";

$total_result = executeSecureQuery($conn, $total_query, [$q], "s");

if ($total_result === false) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan dalam menghitung total data'
    ], JSON_PRETTY_PRINT);
    exit;
}

$total_data = 0;

while ($row = $total_result->fetch_assoc()) {
    $arr_lokasi = explode(",", $row['lokasi_kerja']);

    if (is_array($arr_lokasi)) {
        $total_data += count($arr_lokasi); // hitung per kota
    } else {
        $total_data += 1; // fallback jika bukan array
    }
}

$total_pages = ceil($total_data / $limit);
$response['data'] = [];
if ($result->num_rows > 0) {

    while ($row = $result->fetch_assoc()) {
        $id_req = $row['id_req'];
        $waktu_lalu = waktuLalu($row['create_at']);
        $tgl_posting = formatTanggalIndonesia($row['create_at']);
        $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
        $logoURL = null; // Default jika tidak ada gambar
        $is_favorit = false; // Default jika bukan favorit
        $is_lamar = false;

        // Cek apakah ada gambar logo di S3
        if (!empty($row['img'])) {
            if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $row['img']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();
            }
        }

        // Loop melalui setiap lokasi_kerja dan buat entry baru untuk masing-masing lokasi
        foreach ($arr_lokasi_kerja as $lokasi) {
            // Sanitasi lokasi
            $lokasi = trim($lokasi);
            $temp_kode = base64_encode($id_req . "|" . $lokasi);

            if (isset($lokasi) && !empty($lokasi)) {
                $lokasi_kerja = str_replace("KOTA ", "", str_replace("KABUPATEN ", "KAB. ", $lokasi));
            } else {
                $lokasi_kerja = "-";
            }

            if ($pin != '-') {
                // Query favorit dengan keamanan yang ditingkatkan
                $favQuery = "SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?";
                $resultFav = executeSecureQuery($conn, $favQuery, [$pin, $id_req, $lokasi], "sss");

                if ($resultFav !== false && $resultFav->num_rows > 0) {
                    $is_favorit = true;
                }

                // Query lamar dengan keamanan yang ditingkatkan - PERBAIKAN SQL INJECTION
                $lamarQuery = "SELECT ul.id_lamar
                FROM users_lamar ul
                JOIN list_request lr ON lr.id_req = ul.id_req
                WHERE ul.id_gestalt = ? AND ul.id_req = ?
                LIMIT ?, ?";

                $resultLamar = executeSecureQuery($conn, $lamarQuery, [$pin, $id_req, $start, $limit], "ssii");

                if ($resultLamar !== false && $resultLamar->num_rows > 0) {
                    $is_lamar = true;
                }
            }


            // Tambahkan semua data asli + tambahan ke dalam array sebagai entri terpisah
            $row_copy = $row; // Buat salinan dari row asli
            $row_copy['lokasi_kerja2'] = $lokasi_kerja;
            $row_copy['waktu_lalu'] = $waktu_lalu;
            $row_copy['tgl_posting'] = $tgl_posting;
            $row_copy['logoURL'] = $logoURL;
            $row_copy['is_favorit'] = $is_favorit;
            $row_copy['is_lamar'] = $is_lamar;
            $row_copy['temp_kode'] = $temp_kode;
            $row_copy['arr_lokasi'] = [$lokasi]; // Ubah menjadi array dengan satu lokasi

            // Sanitasi output sebelum menambahkan ke response
            $response['data'][] = sanitizeOutput($row_copy);
        }
    }
} else {
    // Jika tidak ada data, set array kosong
    $response['data'] = [];
}

// Format JSON response dengan sanitasi
$response = [
    "success" => true,
    "message" => "fetch data berhasil",
    "data" => $response['data'],
    "page" => $page,
    "total_page" => $total_pages,
    "page_size" => $limit,
    "total_data" => $total_data
];

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

$conn->close();
exit;
