<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$perjalanan_dinas = "";
$minat_lokasi_kerja = "";
$temp_gaji = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $perjalanan_dinas = htmlspecialchars($row['perjalanan_dinas']);
    $minat_lokasi_kerja = htmlspecialchars($row['minat_lokasi_kerja']);
    $temp_gaji = htmlspecialchars($row['minat_gaji']);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}
?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Informasi Pekerjaan') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-6 mt-2">
            <label class="form-label"><?= translate('Apakah anda bersedia melakukan perjalanan dinas') ?>? <a style="color: red;">(*)</a></label>
            <table style="width: 100%;">
                <tr>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="dinas_y" name="perjalanan_dinas" value="Ya" required class="form-check-input" required>
                            <label class="form-check-label" for="dinas_y">
                                <?= translate('Ya') ?>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="dinas_t" name="perjalanan_dinas" value="Tidak" required class="form-check-input" required>
                            <label class="form-check-label" for="dinas_t">
                                <?= translate('Tidak') ?>
                            </label>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-4 mt-2">
            <label for="minat_lokasi_kerja" class="form-label"><?= translate('Lokasi Kerja yang Anda minati') ?> <span style="color: red;">(*)</span></label>
            <select class="form-check-input" id="minat_lokasi_kerja" name="minat_lokasi_kerja" style="width: 100%" required>
            </select>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mt-2">
            <label for="temp_gaji" class="form-label"><?= translate('Sebutkan gaji yang Anda inginkan') ?> <a style="color: red;">(*)</a></label>
            <div class="input-group mb-3">
                <span class="input-group-text">Rp.</span>
                <input type="text" class="form-control" id="temp_gaji" name="temp_gaji" value="<?= $temp_gaji ?>" placeholder="<?= translate('Contoh') ?>: 5.000.000" required>
            </div>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-riwayat-pekerjaan.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('#minat_lokasi_kerja').select2({
            placeholder: "<?= translate('Silakan masukan nama kota') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getLokasi',
                        ketForm: 'formInformasiPekerjaan',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });
        $('#temp_gaji').mask('000.000.000', {
            reverse: true
        }); // Format mata uang

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data riwayat pekerjaan
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var perjalanan_dinas = $("input[name='perjalanan_dinas']:checked").val();
            minat_lokasi_kerja = $("#minat_lokasi_kerja").val();
            temp_gaji = $("#temp_gaji").val();

            if (temp_gaji.charAt(0) == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Gaji yang di-inginkan tidak boleh nol') ?>.",
                    buttons: false,
                    timer: 1600
                });
            } else {
                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanInformasiPekerjaan",
                            data: {
                                perjalanan_dinas: perjalanan_dinas,
                                minat_lokasi_kerja: minat_lokasi_kerja,
                                temp_gaji: temp_gaji,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-minat-konsep.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }
        });
        // End Fungsi simpan data riwayat pekerjaan

        // Auto input
        var perjalanan_dinas = '<?= $perjalanan_dinas ?>';
        $("input[name='perjalanan_dinas'][value='" + perjalanan_dinas + "']").prop("checked", true);

        var minat_lokasi_kerja = '<?= $minat_lokasi_kerja ?>';
        if (minat_lokasi_kerja != "") {
            $("#minat_lokasi_kerja").select2('destroy');
            $('#minat_lokasi_kerja').append('');
            $('#minat_lokasi_kerja').append('<option value="' + minat_lokasi_kerja + '">' + minat_lokasi_kerja + '</option>');

            $('#minat_lokasi_kerja').select2({
                placeholder: "<?= translate('Silakan masukan nama kota') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getLokasi',
                            ketForm: 'formInformasiPekerjaan',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#minat_lokasi_kerja").val(minat_lokasi_kerja).trigger('change');
        }
        // End Auto input
    });
</script>