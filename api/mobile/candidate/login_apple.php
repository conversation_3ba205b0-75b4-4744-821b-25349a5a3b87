<?php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';

require_once(__DIR__ . '/../../../vendor/autoload.php');

use Firebase\JWT\JWT;
use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/../../..');
$dotenv->load();

// Pastikan hanya method POST yang diizinkan
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $responses = [
        "success" => false,
        "message" => "Method tidak diizinkan. Gunakan POST.",
        "data" => [],
    ];
    echo json_encode($responses, JSON_PRETTY_PRINT);
    exit();
}

function generateAccessToken($userData)
{
    $secretKey = $_ENV['ACCESS_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24); // 24 jam

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'nama' => $userData['nama'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}

function generateRefreshToken($userData)
{
    $secretKey = $_ENV['REFRESH_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24 * 30); // 30 hari

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}


// Ambil data dari POST request
$email = trim($_POST['email'] ?? '');
$nama_apple = trim($_POST['nama'] ?? '');

// Sanitasi input
$email = filter_var($email, FILTER_SANITIZE_EMAIL);
$nama_apple = htmlspecialchars($nama_apple, ENT_QUOTES, 'UTF-8');

$status = false;
$message = "Login gagal.";

$response['data'] = [];

// Validasi input
if (empty($email)) {
    $responses = [
        "success" => false,
        "message" => "Email tidak boleh kosong.",
        "data" => [],
    ];
    echo json_encode($responses, JSON_PRETTY_PRINT);
    exit();
}

// Validasi format email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $responses = [
        "success" => false,
        "message" => "Format email tidak valid.",
        "data" => [],
    ];
    echo json_encode($responses, JSON_PRETTY_PRINT);
    exit();
}

function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}

$koordinat = "";
if (isset($_POST['koordinat'])) {
    $koordinat = $_POST['koordinat'];
}
$device = "";
if (isset($_POST['device'])) {
    $device = $_POST['device'];
}
$ip = "";
if (isset($_POST['ip'])) {
    $ip = $_POST['ip'];
}

function create_pincode($length)
{
    $data = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
    $string = '';
    for ($i = 0; $i < $length; $i++) {
        $pos = rand(0, strlen($data) - 1);
        $string .= $data[$pos];
    }
    return $string;
}

function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

try {
    // Mulai proses
    $conn->begin_transaction();

    // cek email user
    $sql = $conn->prepare("SELECT u.*, r.tempat_lahir, 
    r.tgl_lahir, r.jenis_kelamin, 
    CONCAT(r.alamat, ' RT ', r.rt, ' RW ', r.rw, ' ', 
    r.kecamatan, ' ', r.kota, ' ', r.provinsi, '. ', r.kode_pos) as alamat
    FROM users_kandidat u LEFT JOIN rh r ON u.pin = r.id WHERE u.email = ?");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $storedHash  = $row['password'];
        $pin = $row['pin'];
        $nama = $row['nama_lengkap'];
        $img = $row['foto'];
        $no_telp = $row['no_telp'];
        $tempat_lahir = $row['tempat_lahir'];
        $tgl_lahir = formatTanggal($row['tgl_lahir']);
        $jk = $row['jenis_kelamin'];
        $alamat = $row['alamat'];
        $fcmToken = $row['fcm_token'];

        if ($img != "") {
            if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $img)) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'kandidat/foto-profil/' . $img
                ]);

                $request = $s3->createPresignedRequest($cmd, '+24 hours');
                $img = (string) $request->getUri();
            }
        }

        // Set session data untuk kompatibilitas
        $dataUser = array('pin' => $pin, 'nama' => $nama, 'email' => $email, 'img' => $img, 'tipe' => 'kandidat');
        $_SESSION['users'] = $dataUser;
        unset($_SESSION['user_google']);

        // Generate JWT tokens
        $accessToken = generateAccessToken($dataUser);
        $refreshToken = generateRefreshToken($dataUser);

        // Simpan refresh token ke database
        $refresh_token_expiry = date('Y-m-d H:i:s', time() + (60 * 60 * 24 * 30)); // 30 hari
        $update_token_sql = $conn->prepare("UPDATE users_kandidat SET refresh_token = ?, refresh_token_expiry = ? WHERE pin = ?");
        $update_token_sql->bind_param("sss", $refreshToken, $refresh_token_expiry, $pin);
        $update_token_sql->execute();
        $update_token_sql->close();

        // simpan login history
        $id_log_hist = date("YmdHis");
        $created_at = date("Y-m-d H:i:s");

        $stmt = $conn->prepare("INSERT INTO login_history (`id`, `id_user`, `koordinat`, `device`, `ip`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $id_log_hist, $pin, $koordinat, $device, $ip, $created_at);
        $stmt->execute();

        // Log aktivitas berhasil login
        logActivity($conn, $pin, "INFO", "User atas nama $nama telah login via Apple ke sistem.", "Mobile App");

        // Jika semua query berhasil, commit transaksi
        $conn->commit();


        $status = true;
        $message = "Login berhasil.";
        $response['data'][] = array(
            'pin' => $pin,
            'nama' => $nama,
            'email' => $email,
            'img' => $img,
            'no_telp' => $no_telp,
            'tempat_lahir' => $tempat_lahir,
            'tgl_lahir' => $tgl_lahir,
            'jenis_kelamin' => $jk,
            'alamat' => $alamat,
            'fcm_token' => $fcmToken,
            'access_token' => isset($accessToken) ? $accessToken : null,
            'refresh_token' => isset($refreshToken) ? $refreshToken : null
        );
    } else {
        // cek jika pada rh email atau nomor telepon sudah terdaftar
        $sql = $conn->prepare("SELECT id, nama, ktp FROM rh WHERE email = ? ORDER BY `no` DESC LIMIT 1");
        $sql->bind_param("s", $email);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();
        $pincode = "";
        if ($result->num_rows > 0) {
            $rowRH = mysqli_fetch_array($result);
            $pincode = $rowRH['id'];
        } else {
            // Generate pin code
            $sqlCek = "SELECT id FROM rh";
            $queryCek = $conn->query($sqlCek);

            do {
                $pincode = create_pincode(15);
            } while ($pincode == $queryCek);
        }

        // Initialize required variables
        $no_telp = "";
        $foto = "";
        $fcm_token = "";

        // Insert user baru
        $insert_sql = $conn->prepare("INSERT INTO `users_kandidat`(`pin`, `nama_lengkap`, `email`, `no_telp`, `password`, `status`, `tgl`, `created_at`,`foto`, `from`, `fcm_token`) VALUES (?, ?, ?, ?, ?, 'Active', ?, ?, ?, 'Digitalcv CV Mobile', ?)");
        $default_password = hashPassword('password');
        $current_date = date('Y-m-d H:i:s');
        $insert_sql->bind_param("sssssssss", $pincode, $nama_apple, $email, $no_telp, $default_password, $current_date, $current_date, $foto, $fcm_token);

        if ($insert_sql->execute()) {
            $insert_sql->close();

            // Set data untuk JWT dan response
            $pin = $pincode;
            $nama = $nama_apple;
            $img = "";
            $no_telp = "";
            $tempat_lahir = "";
            $tgl_lahir = "";
            $jk = "";
            $alamat = "";
            $fcmToken = "";

            // Set session data untuk kompatibilitas
            $dataUser = array('pin' => $pin, 'nama' => $nama, 'email' => $email, 'img' => $img, 'tipe' => 'kandidat');
            $_SESSION['users'] = $dataUser;
            unset($_SESSION['user_google']);

            // Generate JWT tokens
            $accessToken = generateAccessToken($dataUser);
            $refreshToken = generateRefreshToken($dataUser);

            // Simpan refresh token ke database
            $refresh_token_expiry = date('Y-m-d H:i:s', time() + (60 * 60 * 24 * 30)); // 30 hari
            $update_token_sql = $conn->prepare("UPDATE users_kandidat SET refresh_token = ?, refresh_token_expiry = ? WHERE pin = ?");
            $update_token_sql->bind_param("sss", $refreshToken, $refresh_token_expiry, $pin);
            $update_token_sql->execute();
            $update_token_sql->close();

            // simpan login history
            $id_log_hist = date("YmdHis");
            $created_at = date("Y-m-d H:i:s");

            $stmt = $conn->prepare("INSERT INTO login_history (`id`, `id_user`, `koordinat`, `device`, `ip`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssss", $id_log_hist, $pin, $koordinat, $device, $ip, $created_at);
            $stmt->execute();

            // Log aktivitas registrasi dan login via Apple
            logActivity($conn, $pin, "INFO", "User baru atas nama $nama berhasil registrasi dan login via Apple ke sistem.", "Mobile App");

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = true;
            $message = "Registrasi dan login berhasil.";
            $response['data'][] = array(
                'pin' => $pin,
                'nama' => $nama,
                'email' => $email,
                'img' => $img,
                'no_telp' => $no_telp,
                'tempat_lahir' => $tempat_lahir,
                'tgl_lahir' => $tgl_lahir,
                'jenis_kelamin' => $jk,
                'alamat' => $alamat,
                'fcm_token' => $fcmToken,
                'access_token' => isset($accessToken) ? $accessToken : null,
                'refresh_token' => isset($refreshToken) ? $refreshToken : null
            );
        } else {
            $insert_sql->close();
            $status = false;
            $message = "Gagal melakukan registrasi.";
        }
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = false;
    $message = $e->getMessage();
}

$responses = [
    "success" => $status,
    "message" => $message,
    "data" => $response['data'],
];

$output = json_encode($responses, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit();
