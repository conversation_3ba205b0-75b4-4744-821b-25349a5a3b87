# Automatic Token Blacklist Cleanup

## Overview

Sistem ini secara otomatis membersihkan token JWT yang sudah expired dari blacklist untuk menghemat storage dan menjaga performa aplikasi.

## Komponen

### 1. Cleanup Function (`api/jwt_helper.php`)

- Fungsi `cleanupExpiredBlacklistedTokens()` sudah tersedia
- Membaca file `cache/blacklisted_tokens.json`
- Menghapus token yang `expires_at` < waktu sekarang
- Mengembalikan jumlah token yang dihapus

### 2. Cleanup Script (`api/cronjob/cleanup_blacklisted_tokens.php`)

- Script PHP untuk menjalankan cleanup
- Logging ke `cache/cleanup_log.txt`
- Membersihkan log lama secara otomatis
- Error handling yang robust

### 3. Batch Scripts

- `cleanup_tokens.bat` - Script batch sederhana untuk Windows
- `cleanup_tokens.ps1` - <PERSON>ript PowerShell yang lebih lengkap

## Setup Automatic Cleanup di Windows

### Option 1: Task Scheduler (Recommended)

1. **Buka Task Scheduler**

   - Tekan `Win + R`, ketik `taskschd.msc`

2. **Create Basic Task**

   - Klik "Create Basic Task..." di panel kanan
   - Name: "DigitalCV Token Cleanup"
   - Description: "Cleanup expired JWT tokens from blacklist"

3. **Trigger Setup**

   - Pilih "Daily" untuk harian, atau "Weekly" untuk mingguan
   - Atau pilih "When the computer starts" jika ingin berjalan saat startup
   - Set waktu yang sesuai (misal: 02:00 AM setiap hari)

4. **Action Setup**

   - Pilih "Start a program"
   - Program/script: `C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe`
   - Arguments: `-ExecutionPolicy Bypass -File "d:\xampp\htdocs\digitalcv\cleanup_tokens.ps1"`
   - Start in: `d:\xampp\htdocs\digitalcv`

5. **Advanced Settings** (Opsional)
   - Centang "Run whether user is logged on or not"
   - Centang "Run with highest privileges" jika diperlukan

### Option 2: Manual dengan Batch File

Jalankan `cleanup_tokens.bat` secara manual kapan saja diperlukan.

### Option 3: XAMPP Control Panel

Jika menggunakan XAMPP, bisa tambahkan script ke startup Apache/MySQL.

## Monitoring

### Log File

- Location: `cache/cleanup_log.txt`
- Format: `[YYYY-MM-DD HH:MM:SS] Message`
- Auto-cleanup: Hanya simpan 500 baris terakhir

### Manual Check

```bash
# Via command line
php "d:\xampp\htdocs\digitalcv\api\cronjob\cleanup_blacklisted_tokens.php"

# Via PowerShell
powershell -ExecutionPolicy Bypass -File "d:\xampp\htdocs\digitalcv\cleanup_tokens.ps1"
```

## Troubleshooting

### Common Issues

1. **PHP Path Not Found**

   - Update path di script sesuai instalasi XAMPP
   - Default: `C:\xampp\php\php.exe`

2. **Permission Denied**

   - Jalankan sebagai Administrator
   - Atau ubah permission folder `cache/`

3. **Script Not Running**
   - Cek log di `cache/cleanup_log.txt`
   - Cek Event Viewer Windows untuk Task Scheduler errors

### Testing

```php
// Test manual di browser atau CLI
require_once 'api/jwt_helper.php';
$deleted = cleanupExpiredBlacklistedTokens();
echo "Deleted: $deleted tokens";
```

## Rekomendasi

1. **Frekuensi**: Jalankan setiap hari pada jam sepi (misal 2-3 AM)
2. **Monitoring**: Cek log secara berkala
3. **Backup**: Backup `blacklisted_tokens.json` sebelum cleanup besar
4. **Testing**: Test script di development environment dulu

## Security Notes

- Script hanya membaca/tulis file JSON, tidak akses database
- Logging tidak menyimpan data sensitif
- Cleanup berdasarkan timestamp, tidak ada akses token asli
