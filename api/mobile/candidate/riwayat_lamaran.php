<?php
header("Content-Type: application/json");
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

// Security functions
function executeSecureQuery($conn, $query, $types, $params)
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            error_log("Prepare failed: " . $conn->error);
            return false;
        }

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        if (!$stmt->execute()) {
            error_log("Execute failed: " . $stmt->error);
            $stmt->close();
            return false;
        }

        $result = $stmt->get_result();
        $stmt->close();
        return $result;
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

function validateInput($input, $type, $options = [])
{
    switch ($type) {
        case 'pin':
            return preg_match('/^[a-zA-Z0-9_-]{1,50}$/', $input) ? $input : false;
        case 'id':
            return preg_match('/^[a-zA-Z0-9_-]{1,100}$/', $input) ? $input : false;
        case 'search':
            $maxLength = $options['max_length'] ?? 100;
            $cleaned = trim($input);
            return (strlen($cleaned) <= $maxLength) ? $cleaned : false;
        case 'page':
            $int_val = filter_var($input, FILTER_VALIDATE_INT, ['options' => ['min_range' => 1, 'max_range' => 10000]]);
            return $int_val !== false ? $int_val : 1;
        case 'page_size':
            $int_val = filter_var($input, FILTER_VALIDATE_INT, ['options' => ['min_range' => 1, 'max_range' => 100]]);
            return $int_val !== false ? $int_val : 6;
        case 'filename':
            return preg_match('/^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|gif|svg)$/i', $input) ? $input : false;
        default:
            return false;
    }
}

function sanitizeOutput($data)
{
    if (is_array($data)) {
        $protected_fields = ['logoURL', 'img', 'foto', 'avatar', 'link', 'access_token', 'refresh_token'];
        $result = [];
        foreach ($data as $key => $value) {
            if (in_array($key, $protected_fields)) {
                // Protect S3 URLs and other URLs from HTML encoding
                if (is_string($value) && (strpos($value, 'amazonaws.com') !== false ||
                    strpos($value, 'http') === 0 || filter_var($value, FILTER_VALIDATE_URL))) {
                    $result[$key] = $value; // Keep URLs intact
                } else {
                    $result[$key] = $value;
                }
            } else {
                $result[$key] = sanitizeOutput($value);
            }
        }
        return $result;
    } elseif (is_string($data)) {
        return $data;
    }
    return $data;
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Validate and sanitize input parameters
$clean_pin = validateInput($pin, 'pin');
if (!$clean_pin) {
    $error_response = [
        "success" => false,
        "message" => "Invalid user PIN",
        "data" => [],
        "page" => 1,
        "page_size" => 6,
        "total_page" => 0,
        "total_data" => 0
    ];
    echo json_encode($error_response, JSON_PRETTY_PRINT);
    exit;
}

$limit = validateInput($_POST['page_size'] ?? 6, 'page_size');
$page = validateInput($_POST['page'] ?? 1, 'page');
$start = ($page - 1) * $limit;

// Validate and sanitize search parameters
$clean_pencarian = "";
if (isset($_POST['pencarian']) && !empty($_POST['pencarian'])) {
    $pencarian_input = validateInput($_POST['pencarian'], 'search', ['max_length' => 100]);
    if ($pencarian_input !== false) {
        $clean_pencarian = $pencarian_input;
    }
}

$clean_id_lamar = "";
if (isset($_POST['id_lamar']) && !empty($_POST['id_lamar'])) {
    $id_lamar_input = validateInput($_POST['id_lamar'], 'id');
    if ($id_lamar_input !== false) {
        $clean_id_lamar = $id_lamar_input;
    }
}

$clean_id_req = "";
if (isset($_POST['id_req']) && !empty($_POST['id_req'])) {
    $id_req_input = validateInput($_POST['id_req'], 'id');
    if ($id_req_input !== false) {
        $clean_id_req = $id_req_input;
    }
}

// Build secure query with parameters
$base_query = "SELECT
            ul.id_lamar,
            ul.id_req,
            lr.perusahaan,
            lr.posisi,
            ul.lokasi,
            ulh.tgl,
            ul.status,
            lr.id_koordinator,
            k.img
        FROM
            users_lamar ul
            JOIN list_request lr ON lr.id_req = ul.id_req
            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
            JOIN users_lamar_history ulh ON ul.id_lamar = ulh.id_lamar
        WHERE
            ul.id_gestalt = ?
            AND ulh.status = 'Lamaran Dikirim'
            AND ulh.tgl >= DATE_SUB(NOW(), INTERVAL 90 DAY)";

$params = [$clean_pin];
$types = "s";

// Add optional filters with secure parameters
if (!empty($clean_pencarian)) {
    $base_query .= " AND (lr.perusahaan LIKE ? OR lr.posisi LIKE ?)";
    $search_param = "%{$clean_pencarian}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ss";
}

if (!empty($clean_id_lamar)) {
    $base_query .= " AND ul.id_lamar = ?";
    $params[] = $clean_id_lamar;
    $types .= "s";
}

if (!empty($clean_id_req)) {
    $base_query .= " AND ul.id_req = ?";
    $params[] = $clean_id_req;
    $types .= "s";
}

$base_query .= " GROUP BY ul.id_req, ul.lokasi ORDER BY ulh.tgl DESC LIMIT ?, ?";
$params[] = $start;
$params[] = $limit;
$types .= "ii";

// Execute main query with secure parameters
$result = executeSecureQuery($conn, $base_query, $types, $params);

// Build secure count query
$count_query = "SELECT COUNT(*) AS total FROM (
                    SELECT ul.id_lamar
                    FROM users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                    JOIN users_lamar_history ulh ON ul.id_lamar = ulh.id_lamar
                    WHERE
                        ul.id_gestalt = ?
                        AND ulh.status = 'Lamaran Dikirim'
                        AND ulh.tgl >= DATE_SUB(NOW(), INTERVAL 90 DAY)";

$count_params = [$clean_pin];
$count_types = "s";

// Add the same optional filters for count query
if (!empty($clean_pencarian)) {
    $count_query .= " AND (lr.perusahaan LIKE ? OR lr.posisi LIKE ?)";
    $count_params[] = $search_param;
    $count_params[] = $search_param;
    $count_types .= "ss";
}

if (!empty($clean_id_lamar)) {
    $count_query .= " AND ul.id_lamar = ?";
    $count_params[] = $clean_id_lamar;
    $count_types .= "s";
}

if (!empty($clean_id_req)) {
    $count_query .= " AND ul.id_req = ?";
    $count_params[] = $clean_id_req;
    $count_types .= "s";
}

$count_query .= " GROUP BY ul.id_req, ul.lokasi
                ) AS subquery";

// Execute count query with secure parameters
$total_result = executeSecureQuery($conn, $count_query, $count_types, $count_params);

if ($total_result && $total_result->num_rows > 0) {
    $total_row = $total_result->fetch_assoc();
    $total_data = $total_row['total'];
    $total_pages = ceil($total_data / $limit);
} else {
    $total_data = 0;
    $total_pages = 0;
}

$response['data'] = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $id_lamar = $row['id_lamar'];
        $id_req = $row['id_req'];
        $nama_perusahaan = mb_strimwidth($row['perusahaan'], 0, 30, "...");
        $posisi = $row['posisi'];
        $lokasi = $row['lokasi'];
        $waktu_lalu = waktuLalu($row['tgl']);
        $id_koordinator = $row['id_koordinator'];
        $logoURL = "";

        // Secure S3 logo handling
        if (!empty($row['img'])) {
            // Sanitize filename to prevent path traversal
            $filename = basename($row['img']);
            if (validateInput($filename, 'filename')) {
                $s3Key = 'perusahaan/logo/' . $filename;

                try {
                    if ($s3->doesObjectExist($bucket, $s3Key)) {
                        $cmd = $s3->getCommand('GetObject', [
                            'Bucket' => $bucket,
                            'Key'    => $s3Key
                        ]);
                        $request = $s3->createPresignedRequest($cmd, '+24 hours');
                        $logoURL = (string) $request->getUri();
                    }
                } catch (Exception $e) {
                    error_log("S3 error for logo: " . $e->getMessage());
                    $logoURL = "";
                }
            }
        }

        // Get last history status with secure query
        $history_result = executeSecureQuery(
            $conn,
            "SELECT status FROM users_lamar_history WHERE id_lamar = ? ORDER BY tgl DESC LIMIT 1",
            "s",
            [$id_lamar]
        );

        $status = "";
        if ($history_result && $history_result->num_rows > 0) {
            $history_row = $history_result->fetch_assoc();
            $status = $history_row['status'];
        }

        // Prepare secure row data
        $row_data = [
            'id_lamar' => $id_lamar,
            'id_req' => $id_req,
            'perusahaan' => $nama_perusahaan,
            'posisi' => $posisi,
            'lokasi' => $lokasi,
            'tgl' => $row['tgl'],
            'status' => $row['status'],
            'id_koordinator' => $id_koordinator,
            'img' => $row['img'],
            'last_status' => $status,
            'logoURL' => $logoURL,
            'waktu_lalu' => $waktu_lalu
        ];

        $response['data'][] = $row_data;
    }
}

// Format secure JSON response
$response = [
    "success" => true,
    "message" => "fetch data berhasil",
    "data" => $response['data'],
    "page" => $page,
    "page_size" => $limit,
    "total_page" => $total_pages,
    "total_data" => $total_data
];

// Sanitize output untuk melindungi dari XSS dan corrupted URLs
$sanitized_response = sanitizeOutput($response);

header('Content-Type: application/json');
echo json_encode($sanitized_response, JSON_PRETTY_PRINT);

$conn->close();
exit;
