<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../jwt_helper.php';

// Secure query execution function
function executeSecureQuery($conn, $sql, $params = [], $types = "")
{
    try {
        if (!empty($params)) {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types)) {
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        } else {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        }
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Input validation function
function validateInput($input, $type = 'string', $maxLength = 255)
{
    if (empty($input) && $input !== '0') return '';

    switch ($type) {
        case 'pin':
            // Validate PIN format - should be alphanumeric
            return preg_match('/^[a-zA-Z0-9_-]+$/', $input) ? substr(trim($input), 0, $maxLength) : '';
        case 'filename':
            // Validate filename - prevent path traversal
            $input = basename($input); // Remove path components
            return preg_match('/^[a-zA-Z0-9._-]+$/', $input) ? substr(trim($input), 0, $maxLength) : '';
        default:
            return preg_match('/^[a-zA-Z0-9\s\.\-_,@]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
    }
}

// Output sanitization function with enhanced URL protection
function sanitizeOutput($data, $key = '')
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $k => $v) {
            $sanitized[$k] = sanitizeOutput($v, $k);
        }
        return $sanitized;
    }

    // Don't sanitize specific fields that contain URLs or encoded data
    $protected_fields = ['img', 'foto', 'avatar', 'logoURL', 'link', 'link_perusahaan'];
    if (in_array($key, $protected_fields)) {
        return $data; // Always return as-is for protected fields
    }

    // Don't sanitize URLs (especially S3 presigned URLs)
    if (is_string($data)) {
        // Check if it's a URL (starts with http/https) or contains S3-like structure
        if (
            filter_var($data, FILTER_VALIDATE_URL) !== false ||
            (strpos($data, 'http') === 0 && (strpos($data, 'amazonaws.com') !== false || strpos($data, '?') !== false))
        ) {
            return $data;
        }
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }

    return $data;
}

// Middleware untuk autentikasi JWT
$userData = requireAuth();

// Validate JWT data
if (!isset($userData->pin) || empty($userData->pin)) {
    $response = [
        'success' => false,
        'message' => 'Token tidak valid atau tidak mengandung data PIN',
        'data' => []
    ];
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

// Validate and sanitize PIN from JWT
$userPin = validateInput($userData->pin, 'pin', 50);
if (empty($userPin)) {
    $response = [
        'success' => false,
        'message' => 'Format PIN tidak valid',
        'data' => []
    ];
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

$response = [
    'success' => true,
    'message' => 'Data profil berhasil diambil',
    'data' => []
];

try {
    // Execute secure query to get profile data
    $sql = "SELECT u.*, r.tempat_lahir, r.tgl_lahir, r.jenis_kelamin, 
            CONCAT(r.alamat, ' RT ', r.rt, ' RW ', r.rw, ' ', 
            r.kecamatan, ' ', r.kota, ' ', r.provinsi, '. ', r.kode_pos) as alamat
            FROM users_kandidat u 
            LEFT JOIN rh r ON u.pin = r.id 
            WHERE u.pin = ?";

    $result = executeSecureQuery($conn, $sql, [$userPin], "s");

    if ($result === false) {
        throw new Exception('Terjadi kesalahan dalam mengambil data profil');
    }

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        // Format tanggal lahir
        $tgl_lahir = null;
        if ($row['tgl_lahir']) {
            $bulanIndo = [
                "Januari",
                "Februari",
                "Maret",
                "April",
                "Mei",
                "Juni",
                "Juli",
                "Agustus",
                "September",
                "Oktober",
                "November",
                "Desember"
            ];
            $tanggalObj = date_create($row['tgl_lahir']);
            if ($tanggalObj) {
                $tgl_lahir = date_format($tanggalObj, "j") . " " .
                    $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
                    date_format($tanggalObj, "Y");
            }
        }

        // Handle foto profil dari S3 jika ada dengan validasi keamanan
        $img = $row['foto'];
        if (!empty($img)) {
            // Validate and sanitize filename to prevent path traversal
            $safeFilename = validateInput($img, 'filename', 100);
            if (!empty($safeFilename)) {
                // Include S3 configuration
                include '../../../api/s3.php';

                // Secure S3 path construction
                $s3Key = 'kandidat/foto-profil/' . $safeFilename;

                if ($s3->doesObjectExist($bucket, $s3Key)) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => $s3Key
                    ]);
                    $request = $s3->createPresignedRequest($cmd, '+24 hours');
                    $img = (string) $request->getUri();
                } else {
                    $img = null; // File tidak ditemukan di S3
                }
            } else {
                $img = null; // Filename tidak valid
            }
        }

        // Sanitize all output data
        $profileData = [
            'pin' => $row['pin'],
            'nama' => $row['nama_lengkap'],
            'email' => $row['email'],
            'no_telp' => $row['no_telp'],
            'img' => $img,
            'tempat_lahir' => $row['tempat_lahir'],
            'tgl_lahir' => $tgl_lahir,
            'jenis_kelamin' => $row['jenis_kelamin'],
            'alamat' => $row['alamat'],
            'fcm_token' => $row['fcm_token']
        ];

        $response['data'] = sanitizeOutput($profileData);
    } else {
        $response = [
            'success' => false,
            'message' => 'Data profil tidak ditemukan',
            'data' => []
        ];
    }
} catch (Exception $e) {
    // Log error for debugging but don't expose details to client
    error_log("Profile error for PIN {$userPin}: " . $e->getMessage());

    $response = [
        'success' => false,
        'message' => 'Terjadi kesalahan dalam mengambil data profil',
        'data' => []
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
