:root {
  --header-height: 2rem;
  --nav-width: 68px;
  --orange: #ff9800;
  --green: #26b448;
  --red: #f90000;
  --primary: #0d3b72;
  --first-color-light: #afa5d9;
  --white-color: #ffffff;
  --body-font: "Poppins", sans-serif;
  --normal-font-size: 1rem;
  --z-fixed: 100;
  --black: #000;
  --placeholder: #8d8d8d;
}

*,
::before,
::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

* {
  font-family: var(--body-font);
}

#body-pd {
  margin: var(--header-height) 0 0 0;
  font-size: var(--normal-font-size);
  transition: 0.5s;
}

a {
  text-decoration: none;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

/*========================Header & Navbar=================================*/

header .dropdown-toggle:focus {
  box-shadow: none !important;
}

.header_toggle {
  font-size: 1.5rem;
  cursor: pointer;
}

.dropdown-toggle:focus {
  border: none;
}

.dropdown-toggle i {
  font-size: 20px;
  color: #1a73e8;
}

.header {
  width: 100%;
  height: 4.5rem;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  background-color: var(--white-color);
  z-index: var(--z-fixed);
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  transition: 0.5s;
}

.l-navbar {
  position: fixed;
  top: 0;
  left: -30%;
  width: var(--nav-width);
  height: 100vh;
  background-color: #ffffff;
  padding: 0.5rem 1rem 0 0;
  transition: 0.5s;
  border-radius: 0 15px 15px 0;
  box-shadow: 0 0 4px black;
  z-index: var(--z-fixed);
}

.nav {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.nav_logo,
.nav_link {
  display: grid;
  grid-template-columns: max-content max-content;
  align-items: center;
  column-gap: 1rem;
  padding: 0.5rem 0 0.5rem 1.5rem;
}

.nav_logo {
  margin-bottom: 2rem;
}

.nav_logo-icon {
  font-size: 1.25rem;
  color: var(--black);
}

.nav_logo-name {
  color: var(--black);
  font-weight: 700;
}

.nav_link {
  position: relative;
  color: var(--black);
  margin-bottom: 1.5rem;
  transition: 0.3s;
}

.nav_link:hover {
  color: var(--orange);
}

.nav_icon {
  font-size: 1.25rem;
}

.show-sidebar {
  left: 0;
}

.body-pd {
  padding-left: calc(var(--nav-width) + 0.5rem);
}

.aktif {
  color: var(--orange);
  width: 100%;
}

.aktif::before {
  content: "";
  position: absolute;
  left: 0;
  width: 2px;
  height: 32px;
  background-color: var(--orange);
}

.header-text {
  color: var(--black);
  fonst-size: 16px;
  font-weight: 600;
}

/* search */
.main {
  width: 50%;
  margin: 50px auto;
}

.header_img {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  border-radius: 50%;
}

.header_img img {
  width: 40px;
}

/*==========================Header & Navbar End=================================*/

/*========================My-container & Multiple Select=================================*/
.my-container {
  transition: 0.4s;
  margin-top: 70px;
}

.my-container-rh {
  margin-top: 30px;
}

.header-rh {
  transition: 0.4s;
  margin-top: 70px;
}

.bg-checkGroup {
  background-color: var(--primary);
}
/*========================My-container & Multiple Select End=================================*/

/*============================Home Page Kandidat=================================*/
.card-page {
  width: 250px;
  height: 1380px;
  overflow: auto;
}

.homepage-empolye-logo {
  /* box-shadow: 0 0 10px black; */
  padding: 6px;
  margin-left: 10px;
  border-radius: 5px;
}

.home-page-heading li {
  font-size: 14px;
}

.home-page {
  margin-top: 30px;
  padding: 0 10px;
}

/*========================Home Page Kandidat End=================================*/

/*========================Detail Job Page=================================*/
.detailjob-page-hero a {
  text-decoration: none;
}

.detailjob-page-hero a:hover {
  text-decoration: underline;
}

.detail-jobpage-hero {
  box-shadow: 0 0 2px black;
  border-radius: 5px;
}

.detail-jobpage-loker {
  background-color: var(--primary);
  box-shadow: 0 0 2px black;
  border-radius: 5px;
}

.detail-jobpage-card-saran {
  border-radius: 5px;
}

.detail-jobpage-card-saran a {
  text-decoration: none;
}

.detail-jobpage-card-saran button {
  border: none;
  box-shadow: none;
}

.detail-jobpage-card-saran .btn-detail {
  background-color: var(--primary);
  color: #fff;
  font-weight: 600;
  border-radius: 24px;
}

.detail-jobpage-card-saran .btn-detail:hover {
  background-color: var(--orange);
  color: #fff;
}

.detail-jobpage-text-employe {
  color: var(--orange);
  font-weight: bold;
  text-decoration: none;
}

.detail-jobpage-text-loker {
  font-size: 13px;
  color: var(--black);
}
/*========================Detail Job End=================================*/

/*========================Save Job Page=================================*/
.save-jobpage-container {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
}

.save-jobpage-wrap h2,
button {
  color: var(--black);
}

.save-job-hero h6 {
  color: var(--orange);
}
/*========================Save Job Page End=================================*/

/*===========================Footer Section=================================*/
.text-footer p a {
  color: #000;
}

.text-cpy p,
.text-cpy a {
  color: var(--black);
  font-size: 14px;
  text-decoration: none;
}

.text-footer p a {
  color: #000;
}
/*========================Footer Section End=================================*/

/*===========================Button & Icon Component=================================*/
/* .btn {
  border: none;
} */
.btn-notif {
  border: none;
}

button.btn-lamar {
  background-color: var(--orange);
  color: #fff;
  border-radius: 10px;
}
button.btn-lamar:hover {
  background-color: var(--primary);
  color: #fff;
}
.btn-hapus:hover {
  color: var(--orange);
}

.btn-hapus:focus {
  box-shadow: none !important;
}

.btn-save {
  border: 2px solid var(--orange);
}

.btn-save:hover {
  background-color: var(--orange);
  color: #fff;
}

.btn-orange {
  color: var(--orange);
}

.bi-favorit {
  font-size: 22px;
  cursor: pointer;
}

.bi-heart-fill:hover {
  color: var(--orange);
}

.bi-favorit {
  color: var(--primary);
}

.bi-favorit.pressed {
  color: var(--orange);
}

/*========================Button Component End=================================*/

/*===========================Card Component =================================*/

.card {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  color: var(--black);
}
.card-text {
  font-size: 14px;
  color: var(--orange);
}

.bg-img {
  padding: 10px;
  margin-top: 50px;
  border-radius: 5px;
  filter: drop-shadow(0px 4px 4px rgba(78, 78, 78, 0.2));
}

.bg-img-logo {
  margin-left: 30px;
  width: 180px;
  height: 180px;
  border-radius: 5px;
  /* box-shadow: 0 0 3px black; */
}

.img-bg {
  filter: drop-shadow(0px 4px 4px rgba(78, 78, 78, 0.2));
}

.text-loc {
  font-size: 12px;
  color: var(--black);
}

.actived {
  width: 100%;
}

.actived::before {
  content: "";
  position: absolute;
  left: 0;
  width: 5px;
  height: 220px;
  background-color: var(--primary);
}

/*========================Card Component End=================================*/

/*==============================Page History=================================*/
.history-header {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
}

.history-text-employe {
  color: var(--black);
  font-weight: 600;
  font-size: 16px;
}

.history-header h2 {
  color: #4a5568;
}
.history-text-lamaran {
  color: var(--green);
  font-weight: 500;
  font-size: 16px;
}

.text-gagal {
  color: var(--red);
  font-weight: 500;
  font-size: 16px;
}

.text-screening {
  color: var(--orange);
  font-weight: 500;
  font-size: 16px;
}

.history-select {
  box-shadow: none !important;
}

.history-page-text {
  font-size: 12px;
  color: var(--black);
}

.btn.history-btn-timeline {
  background-color: var(--primary);
  color: #fff;
  font-weight: 500;
  padding: 5px 20px;
  border-radius: 10px;

  border-radius: 24px;
}
.btn.history-btn-timeline:hover {
  background-color: var(--orange);
  color: #fff;
}
/*========================Page History End=================================*/

/*========================Page History Profile=================================*/
.card-profile {
  background: #ffffff;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
}

/*========================Page Profile End=================================*/

/*========================Page Pengaturan Profile End=================================*/
.pengaturan-profile {
  margin-bottom: 50px;
  margin-top: 50px;
}

/*========================Page History End=================================*/

/*========================Timeline Page=================================*/
.timeline-text-employe {
  color: var(--orange);
}

.timeline-text {
  font-size: 14px;
}

.timeline-card .timeline-button-detail {
  background-color: var(--primary);
  font-weight: 500;
  color: #fff;
  padding: 5px 20px;
  border-radius: 24px;
}

.timeline-card .timeline-button-detail:hover {
  background-color: var(--orange);
  color: #fff;
}

.timeline-card {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
}

.timeline {
  position: relative;
  padding: 10px;
  margin: 0 auto;
  overflow: hidden;
  color: #ffffff;
}

.timeline:after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -1px;
  border-right: 2px dashed #c4d2e2;
  height: 100%;
  display: block;
}

.timeline-row {
  padding-left: 50%;
  position: relative;
  margin-bottom: 30px;
}

.timeline-row .timeline-time {
  position: absolute;
  right: 50%;
  top: 31px;
  text-align: right;
  margin-right: 20px;
  color: #000000;
  font-size: 1.5rem;
}

.timeline-row .timeline-time small {
  display: block;
  font-size: 0.8rem;
  color: #8796af;
}

.timeline-row .timeline-content {
  position: relative;
  padding: 20px 30px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
}

.timeline-row .timeline-content:after {
  content: "";
  position: absolute;
  top: 20px;
  height: 3px;
  width: 40px;
}

.timeline-row .timeline-content:before {
  content: "";
  position: absolute;
  top: 20px;
  right: -50px;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
  z-index: 100;
  background: #ffffff;
  border: 2px dashed #c4d2e2;
}

.timeline-row .timeline-content h4 {
  margin: 0 0 20px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 150%;
}

.timeline-row .timeline-content p {
  margin-bottom: 30px;
  line-height: 150%;
}

.timeline-row .timeline-content i {
  font-size: 2rem;
  color: #ffffff;
  line-height: 100%;
  padding: 10px;
  border: 2px solid #ffffff;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
  margin-bottom: 10px;
  display: inline-block;
}

.timeline-row .timeline-content .thumbs {
  margin-bottom: 20px;
}

.timeline-row .timeline-content .thumbs img {
  margin-bottom: 10px;
}

.timeline-row:nth-child(even) .timeline-content {
  background-color: #ff9800;
  margin-left: 40px;
  text-align: left;
}

.timeline-row:nth-child(even) .timeline-content:after {
  left: -39px;
  border-right: 18px solid #ff5000;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
}

.timeline-row:nth-child(even) .timeline-content:before {
  left: -50px;
  right: initial;
}

.timeline-row:nth-child(odd) {
  padding-left: 0;
  padding-right: 50%;
}

.timeline-row:nth-child(odd) .timeline-time {
  right: auto;
  left: 50%;
  text-align: left;
  margin-right: 0;
  margin-left: 20px;
}

.timeline-row:nth-child(odd) .timeline-content {
  background-color: #0d3b72;

  margin-right: 40px;
  margin-left: 0;
  text-align: right;
}

.timeline-row:nth-child(odd) .timeline-content:after {
  right: -39px;
  border-left: 18px solid #1379bb;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
}

@media (max-width: 767px) {
  .timeline {
    padding: 15px 10px;
  }

  .timeline:after {
    left: 28px;
  }

  .timeline .timeline-row {
    padding-left: 0;
    margin-bottom: 16px;
  }

  .timeline .timeline-row .timeline-time {
    position: relative;
    right: auto;
    top: 0;
    text-align: left;
    margin: 0 0 6px 56px;
  }

  .timeline .timeline-row .timeline-time strong {
    display: inline-block;
    margin-right: 10px;
  }

  .timeline .timeline-row .timeline-icon {
    top: 52px;
    left: -2px;
    margin-left: 0;
  }

  .timeline .timeline-row .timeline-content {
    padding: 15px;
    margin-left: 56px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .timeline .timeline-row .timeline-content:after {
    right: auto;
    left: -39px;
    top: 32px;
  }

  .timeline .timeline-row:nth-child(odd) {
    padding-right: 0;
  }

  .timeline .timeline-row:nth-child(odd) .timeline-time {
    position: relative;
    right: auto;
    left: auto;
    top: 0;
    text-align: left;
    margin: 0 0 6px 56px;
  }

  .timeline .timeline-row:nth-child(odd) .timeline-content {
    margin-right: 0;
    margin-left: 55px;
  }

  .timeline .timeline-row:nth-child(odd) .timeline-content:after {
    right: auto;
    left: -39px;
    top: 32px;
    border-right: 18px solid #5a99ee;
    border-left: inherit;
  }

  .timeline.animated .timeline-row:nth-child(odd) .timeline-content {
    left: 20px;
  }

  .timeline.animated .timeline-row.active:nth-child(odd) .timeline-content {
    left: 0;
  }
}
/*========================Timeline Page=================================*/

/*========================Media Query=================================*/

@media screen and (min-width: 768px) {
  body {
    margin: calc(var(--header-height) + 1rem) 0 0 0;
    padding-left: calc(var(--nav-width) + 0.1rem);
  }

  .header {
    height: calc(var(--header-height) + 2.4rem);
    padding: 0 1rem 0 calc(var(--nav-width) + 1rem);
  }

  .header_img {
    width: 40px;
    height: 40px;
  }

  .header_img img {
    width: 45px;
  }

  .l-navbar {
    left: 0;
    padding: 1rem 1rem 0 0;
  }

  .l-navbar a {
    text-decoration: none;
  }

  .show-sidebar {
    width: calc(var(--nav-width) + 156px);
  }

  .body-pd {
    padding-left: calc(var(--nav-width) + 158px);
  }

  .text-cpy {
    font-size: 1.2rem;
  }
}

/*========================Media Query End=================================*/

/*==============================================  */
.sec-1 {
  background-color: #f5f5f5;
  border-radus: 10px;
}

.posisi-pelamar {
  color: var(--primary);
}
::-webkit-scrollbar {
  width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
/*Outter Card*/
.card0 {
  background-color: #f5f5f5;
  border-radius: 0px;
  z-index: 0;
}

/*Inner Card*/
.card00 {
  z-index: 0;
}

/*Left side card with progressbar*/
.card1 {
  margin-left: 100px;
  z-index: 0;
  border-right: 1px solid #f5f5f5;
}

/*right side cards*/
.card2 {
  display: none;
}

.card2.show {
  display: block;
}

.social {
  border-radius: 50%;
  background-color: #ffcdd2;
  color: var(--primary);
  height: 47px;
  width: 47px;
  padding-top: 16px;
  cursor: pointer;
}

input,
select {
  padding: 2px;
  border-radius: 0px;
  box-sizing: border-box;
  color: #9e9e9e;
  border: 1px solid #bdbdbd;
  font-size: 16px;
  letter-spacing: 1px;
}

select {
  width: 100%;
  margin-bottom: 85px;
}

input:focus,
select:focus {
  -moz-box-shadow: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  border: 1px solid var(--primary) !important;
  outline-width: 0 !important;
}

/*Red colored checkbox*/
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  background-color: var(--primary);
}

.form-group {
  position: relative;
  margin-bottom: 1.5rem;
  width: 77%;
}

.form-control-placeholder {
  position: absolute;
  top: 0px;
  padding: 12px 2px 0 2px;
  transition: all 300ms;
  opacity: 0.5;
}

.form-control:focus + .form-control-placeholder,
.form-control:valid + .form-control-placeholder {
  font-size: 95%;
  top: 10px;
  transform: translate3d(0, -100%, 0);
  opacity: 1;
  background-color: #fff;
}

.next-button {
  width: 18%;
  font-family: "Poppins", sans-serif;
  background-color: var(--primary);
  color: #fff;
  border-radius: 6px;
  padding: 10px 0;
  cursor: pointer;
}

.next-button:hover {
  background-color: var(--orange);
  color: #fff;
}

/*Icon progressbar*/
#progressbar {
  position: absolute;
  left: 15px;
  overflow: hidden;
  color: var(--primary);
}

#progressbar li {
  list-style-type: none;
  font-size: 8px;
  font-weight: 400;
  margin-bottom: 36px;
}

/* #progressbar li:nth-child(10) {
  margin-bottom: 150px;
} */

#progressbar .step0:before {
  content: "";
  color: #fff;
}

#progressbar li:before {
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: block;
  font-size: 20px;
  background: #fff;
  border: 2px solid var(--primary);
  border-radius: 50%;
  margin: 0 0 0 13px;
}

/*ProgressBar connectors*/
#progressbar li:after {
  content: "";
  width: 3px;
  height: 66px;
  background: #bdbdbd;
  position: absolute;
  left: 58px;
  /* top: 10px; */
  z-index: -1;
}

#progressbar li:last-child:after {
  top: 147px;
  height: 132px;
}

#progressbar li:nth-child(10):after {
  top: 81px;
}

#progressbar li:nth-child(2):after {
  top: 0px;
}

#progressbar li:first-child:after {
  position: absolute;
  top: -81px;
}

/*Color of the connector before*/
#progressbar li.active:after {
  background: var(--primary);
}

/*Color of the step before*/
#progressbar li.active:before {
  background: var(--primary);
  font-family: FontAwesome;
  content: "\f00c";
}

.tick {
  width: 100px;
  height: 100px;
}

.prev {
  outline: 2px solid var(--primary);
  cursor: pointer;

  width: 18%;
  color: #000;
  border-radius: 6px;
  padding: 10px 0;
  cursor: pointer;
}

.prev:hover {
  color: #fff !important;
  background-color: var(--primary) !important;
}

@media screen and (max-width: 912px) {
  .card00 {
    padding-top: 30px;
  }

  .card1 {
    border: none;
    margin-left: 50px;
  }

  .card2 {
    border-bottom: 1px solid #f5f5f5;
    margin-bottom: 25px;
  }

  .social {
    height: 30px;
    width: 30px;
    font-size: 15px;
    padding-top: 8px;
    margin-top: 7px;
  }

  .get-bonus {
    margin-top: 40px !important;
    margin-left: 75px;
  }

  #progressbar {
    left: -25px;
  }
}

/*  */
.input-group-append {
  cursor: pointer;
}

/* ====================== */
.container-progress {
  margin-top: 15px;
  text-align: center;
}

.progress-container::before {
  content: "";
  background: #e0e0e0;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  height: 4px;
  width: 100%;
  z-index: -1;
}

.progress-container {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
  max-width: 100%;
}

.progress {
  background: #3498db;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  height: 4px;
  width: 0%;
  z-index: -1;
  transition: 0.4s ease;
}

.circle {
  background: #fff;
  color: #999;
  border-radius: 50%;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #e0e0e0;
  transition: 0.4s ease;
}

.circle.active {
  border-color: var(--primary);
  background-color: var(--primary);
  color: #fff;
}

.bi-1-form {
  color: var(--primary);
}

/* Fle Upload */
[type="file"] {
  position: absolute;
  filter: alpha(opacity=0);
  opacity: 0;
  cursor: pointer;
}
.file-upload,
[type="file"] + label {
  border-radius: 5px;
  width: 170px;
  text-align: center;
  cursor: pointer;
  padding: 10px;
}
[type="file"] + label {
  text-align: center;
  left: 7.35em;
  top: 0.5em;
  /* Decorative */
  background: var(--primary);
  color: #fff;
  border: none;
  cursor: pointer;
}

::placeholder {
  color: #ababab;
  opacity: 1;
}

.bi-number {
  color: var(--primary);
}

.select i {
  color: #0d3b72;
}

/* pengalaman */
.file-sertifikat,
.sosial-media,
.select-pendidikan,
.pengalaman,
.show-form-pendidikan,
.keterangan,
.pelatihan-wrap,
.sertifikat,
.rawat-inap-wrap,
.riwayat-alergi-wrap,
.riwayat-operasi-wrap,
.dokter-pribadi-wrap,
.kondisi-wrap,
.konsumsi-wrap,
.obat-wrap,
.umum-wrap,
.kondisi-fisik-wrap,
.tambahan-wrap,
.minat-konsep,
.aktivitas-sosial,
.lain-lain-wrap {
  display: none;
}

p,
label {
  font-size: 14px;
}

.hide {
  display: none;
}
/* ================================FORM SCREENING=============================== */
.containerrh {
  display: flex;
  justify-content: center;
  align-items: center;
}

.containerrh .cardrh {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  width: 100%;
  height: 100%;
}

.containerrh .cardrh .form {
  width: 100%;
  height: 100%;

  display: flex;
}

.containerrh .cardrh .left-side {
  width: 35%;
  background-color: #f5f5f5;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  padding: 20px 30px;
  box-sizing: border-box;
}

/*left-side-start*/
.left-heading {
  color: #000;
  border-bottom: 1px solid #000;
}

.steps-content {
  margin-top: 30px;
  color: #000;
}

.steps-content p {
  font-size: 14px;
  margin-top: 15px;
}

.progress-barrh {
  list-style: none;
  /*color:#fff;*/
  margin-top: 30px;
  font-size: 13px;
  font-weight: 700;
  counter-reset: containerrh 0;
}

.progress-barrh li {
  position: relative;
  margin-left: 40px;
  margin-top: 50px;
  counter-increment: containerrh 1;
  color: #4f6581;
}

.progress-barrh li::before {
  content: counter(containerrh);
  line-height: 30px;
  text-align: center;
  position: absolute;
  height: 30px;
  width: 30px;
  border: 1px solid #4f6581;
  border-radius: 50%;
  left: -39px;
  top: -5px;
  z-index: 10;
  background-color: #dfdfdf;
}

.progress-barrh li::after {
  content: "";
  position: absolute;
  height: 90px;
  width: 2px;
  background-color: #4f6581;
  z-index: 1;
  left: -25px;
  top: -70px;
}

.progress-barrh li.activerh::after {
  background-color: #0d3b72;
}

.progress-barrh li:first-child:after {
  display: none;
}

/*.progress-barrh li:last-child:after{*/
/* display:none; */
/*}*/
.progress-barrh li.activerh::before {
  color: #fff;
  background-color: #0d3b72;
  border: 1px solid #000;
}

.progress-barrh li.activerh {
  color: #000;
  font-size: 14px;
}

.showing {
  display: none;
}

/*left-side-end*/
.containerrh .cardrh .right-side {
  width: 65%;
  background-color: #fff;
  height: 100%;
  border-radius: 20px;
}

/*right-side-start*/
.mainrh {
  display: none;
}

.activerh {
  display: block;
}

.mainrh {
  padding: 10px 30px;
}

.text {
  margin-top: 20px;
}

.congrats {
  text-align: center;
}

.text p {
  margin-top: 10px;
  font-size: 13px;
  font-weight: 700;
  color: #cbced4;
}

.input-text {
  margin: 15px 0;
  display: flex;
  gap: 20px;
}

.input-text .input-div {
  width: 100%;
  position: relative;
}

input[type="tel"],
input[type="email"],
input[type="text"] {
  width: 100%;
  height: 40px;
  border: none;
  outline: 0;
  border-radius: 5px;
  color: #000;
  border: 1px solid #cbced4;
  gap: 20px;
  box-sizing: border-box;
  padding: 0px 10px;
}
.form-select {
  font-size: 14px;
}

.fs-14::placeholder {
  font-size: 14px;
}

.singgle-select {
  font-size: 14px;
}

select {
  width: 100%;
  height: 40px;
  border: none;
  outline: 0;
  border-radius: 5px;
  color: #000;
  font-size: 14px;
  border: 1px solid #cbced4;
  gap: 20px;
  box-sizing: border-box;
  padding: 0px 10px;
}

.input-text .input-div span {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 14px;
  transition: all 0.5s;
}

.input-div input:focus ~ span,
.input-div input:valid ~ span {
  top: -15px;
  left: 6px;
  font-size: 10px;
  font-weight: 600;
}

.input-div span {
  top: -15px;
  left: 6px;
  font-size: 10px;
}

.buttons button {
  height: 40px;
  width: 100px;
  border: none;
  border-radius: 5px;
  background-color: #0d3b72;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
}

.buttons button:hover {
  background-color: #ff9800;
}

.button_space {
  display: flex;
  gap: 20px;
}

.button_space button:nth-child(1) {
  background-color: #fff;
  color: #0d3b72;
  border: 1px solid#0D3B72;
}

.button_space button:nth-child(1):hover {
  background-color: #0d3b72;
  color: #fff;
  border: 1px solid#fff;
}

.user_cardrh {
  margin-top: 20px;
  margin-bottom: 40px;
  height: 200px;
  width: 100%;
  border: 1px solid #c7d3d9;
  border-radius: 10px;
  display: flex;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

.user_cardrh span {
  height: 80px;
  width: 100%;
  background-color: #dfeeff;
}

.circle {
  position: absolute;
  top: 40px;
  left: 60px;
}

.circle span {
  height: 70px;
  width: 70px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #fff;
  border-radius: 50%;
}

.circle span img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.social {
  display: flex;
  position: absolute;
  top: 100px;
  right: 10px;
}

.social span {
  height: 30px;
  width: 30px;
  border-radius: 7px;
  background-color: #fff;
  border: 1px solid #cbd6dc;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  color: #cbd6dc;
}

.social span i {
  cursor: pointer;
}

.heart {
  color: red !important;
}

.share {
  color: red !important;
}

.user_name {
  position: absolute;
  top: 110px;
  margin: 10px;
  padding: 0 30px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.user_name h3 {
  color: #4c5b68;
}

.detail {
  /*margin-top:10px;*/
  display: flex;
  justify-content: space-between;
  margin-right: 50px;
}

.detail p {
  font-size: 12px;
  font-weight: 700;
}

.detail p a {
  text-decoration: none;
  color: blue;
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #7ac142;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #fff;
  stroke-miterlimit: 10;
  margin: 10% auto;
  box-shadow: inset 0px 0px 0px #7ac142;
  animation: fill 0.4s ease-in-out 0.4s forwards,
    scale 0.3s ease-in-out 0.9s both;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }

  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 30px #7ac142;
  }
}

.warning {
  border: 1px solid red !important;
}

/*right-side-end*/
@media (max-width: 750px) {
  .containerrh {
    height: scroll;
  }

  .containerrh .cardrh {
    max-width: 350px;
    height: auto !important;
    margin: 30px 0;
  }

  .containerrh .cardrh .right-side {
    width: 100%;
  }

  .input-text {
    display: block;
  }

  .input-text .input-div {
    margin-top: 20px;
  }

  .containerrh .cardrh .left-side {
    display: none;
  }
}

/*Profile Pic Start*/
.picture-container {
  position: relative;
  cursor: pointer;
  text-align: center;
}

.picture {
  width: 80px;
  height: 80px;
  background-color: #999999;
  border: 4px solid #cccccc;
  color: #ffffff;
  border-radius: 50%;
  margin: 0px auto;
  overflow: hidden;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}

.picture:hover {
  border-color: #2ca8ff;
}

.content.ct-wizard-green .picture:hover {
  border-color: #05ae0e;
}

.content.ct-wizard-blue .picture:hover {
  border-color: #3472f7;
}

.content.ct-wizard-orange .picture:hover {
  border-color: #ff9500;
}

.content.ct-wizard-red .picture:hover {
  border-color: #ff3b30;
}

.picture input[type="file"] {
  cursor: pointer;
  display: block;
  height: 100%;
  left: 0;
  opacity: 0 !important;
  position: absolute;
  top: 0;
  width: 100%;
}

.picture-src {
  width: 100%;
}

select option,
label {
  color: #ababab;
}

.form-check-label,
.sosial-media label {
  color: #000;
}

/* Notifikasi */

ul.dropdown-menu-notif {
  margin-top: 2px;
  width: 300px;
  /* height: 540px; */
  overflow: scroll;
}

.dropdown-notify {
  display: block;
  padding: 10px 15px;
}

.dropdown-notify:hover {
  background: #eee;
}

.dropdown-notify-btn {
  border: none;
  border-radius: 5px;
  background: white;
  text-transform: uppercase;
  font-weight: 500;
  color: #0d3b72;
}

.dropdown-notify-header {
  color: white;
  margin-top: -8px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background: #0d3b72;
  font-weight: 700;
}

.dropdown-notify-header:hover {
  background: #347ab8;
  color: white;
  text-decoration: none;
}

.dropdown-menu-notif a {
  color: #333;
}

.dropdown-menu-notif a:hover {
  color: #333;
  text-decoration: none;
}

.badge {
  background: #f93943;
}

.fa-envelope-o {
  font-size: 18px;
  position: relative;
  top: 1px;
  left: -3px;
  margin-right: 2px;
  color: #0d3b72;
}

.notify-title {
  font-weight: 700;
}

.notify-message {
  margin-bottom: 5px;
}

.notify-date {
  margin-bottom: 0px;
  font-size: 12px;
  letter-spacing: 1px;
}
