<?php
// db_helper.php
// Untuk PHP 5 (tanpa fitur modern PHP 7+ seperti type hinting)

class CustomMysqli extends mysqli
{
    var $log_table = 'log_aktifitas';

    function prepare($query)
    {
        $stmt = parent::prepare($query);
        if (!$stmt) {
            $this->logError("prepare", $query);
            throw new Exception(translate('Proses gagal.'));
        }
        $stmt = $this->wrapStatement($stmt, $query);
        return $stmt;
    }

    function query($query, $resultmode = null)
    {
        $result = parent::query($query, $resultmode);
        if ($result === false) {
            $this->logError("query", $query);
            throw new Exception(translate('Proses gagal.'));
        }
        return $result;
    }

    function wrapStatement($stmt, $query)
    {
        $conn = $this;

        $stmt_wrapper = new class($stmt, $conn, $query) {
            var $stmt, $conn, $query;
            function __construct($stmt, $conn, $query)
            {
                $this->stmt = $stmt;
                $this->conn = $conn;
                $this->query = $query;
            }

            function __call($name, $args)
            {
                if ($name === 'bind_param') {
                    // Pastikan semua parameter adalah referensi
                    $refs = [];
                    foreach ($args as $key => $value) {
                        $refs[$key] = &$args[$key];  // pakai referensi
                    }

                    $result = @call_user_func_array([$this->stmt, 'bind_param'], $refs);
                    if ($result === false) {
                        $this->conn->logError("bind_param", $this->query);
                        throw new Exception(translate('Proses gagal.'));
                    }
                    return $result;
                } elseif ($name === 'execute') {
                    $result = @call_user_func_array([$this->stmt, 'execute'], $args);
                    if ($result === false) {
                        $this->conn->logError("execute", $this->query);
                        throw new Exception(translate('Proses gagal.'));
                    }
                    return $result;
                }

                return call_user_func_array([$this->stmt, $name], $args);
            }

            function __get($name)
            {
                return $this->stmt->$name;
            }

            function __set($name, $value)
            {
                $this->stmt->$name = $value;
            }
        };

        return $stmt_wrapper;
    }

    function logError($step, $query = '')
    {
        $err_no = $this->errno;
        $err_msg = $this->real_escape_string($this->error);
        $query_escaped = $this->real_escape_string($query);
        $created_at = date("Y-m-d H:i:s");

        // Dapatkan info tambahan
        $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
        $agent = isset($_SERVER['HTTP_USER_AGENT']) ? $this->real_escape_string($_SERVER['HTTP_USER_AGENT']) : '';

        $err_msg = 'error_no : ' . $err_no . ', error_msg : ' . $err_msg . ', query : ' . $query_escaped;

        $log_sql = "INSERT INTO `{$this->log_table}` (`id`, `timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
        VALUES ('', ?, 'ERROR', ?, ?, 'SISTEM', ?, ?, '')";

        $log_conn = new mysqli(DB_SERVER, DB_USER, DB_PASSWORD, DB_DATABASE);

        $log_stmt = $log_conn->prepare($log_sql);
        if ($log_stmt) {
            $bind = $log_stmt->bind_param("sssss", $created_at, $err_msg, $step, $ip, $agent);
            $log_stmt->execute();
            $log_stmt->close();
        } else {
            // Optional: Tulis ke file jika logging ke DB juga gagal
            error_log("Gagal menyimpan log ke database: [$step] $err_no - $err_msg");
        }
    }
}
