<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
$id_pegawai = $userData->id;

$func = $_GET['func'];
$data = [];
$status = false;
$message = 'gagal mengambil data';

if ($func == 'getKuota') {
    // cek apakah user terdaftar atau tidak
    $sql = $conn->prepare("SELECT id_pic FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
    $sql->bind_param("ss", $id_pegawai, $id_koordinator);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $quota = 0;
    if ($result->num_rows > 0) {
        if (isset($_GET['kategori'])) {
            $status = true;
            $message = "Berhasil mengambil data";
            
            $kategori = $_GET['kategori'];
            // get kuota cari kandidat
            $getQuota = $conn->prepare("SELECT remaining_quota + paid_quota as total_quota FROM quotas_koordinator WHERE id_koordinator = ? AND kategori = ?");
            $getQuota->bind_param("ss", $id_koordinator, $kategori);
            $getQuota->execute();
            $resQuota = $getQuota->get_result();
            $getQuota->close();

            if ($resQuota->num_rows > 0) {
                $rowQuota = mysqli_fetch_array($resQuota);
                if ($rowQuota['total_quota'] > 0 && is_numeric($rowQuota['total_quota'])) {
                    $quota = $rowQuota['total_quota'];
                }
            }
        }
    }

    $data = [
        "kuota" => $quota
    ];
}

// Format response JSON
$response = [
    "status" => $status,
    "message" => $message,
    "data" => $data,
];

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);
exit;