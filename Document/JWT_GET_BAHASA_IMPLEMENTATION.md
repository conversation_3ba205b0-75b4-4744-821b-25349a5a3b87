# JWT Authentication Implementation for get_bahasa.php

## Overview

This document describes the implementation of JWT-based authentication for the `get_bahasa.php` endpoint, which provides language data with search and pagination functionality.

## Changes Made

### 1. JWT Authentication Integration

- Added `include '../../jwt_helper.php'` to include JWT helper functions
- Added `$userData = requireAuth()` to enforce JWT authentication
- Added activity logging using JWT-derived user data

### 2. Security Implementation

**Before (No authentication):**

```php
<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
// No authentication - endpoint was publicly accessible
```

**After (JWT authentication required):**

```php
<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();
```

### 3. Activity Logging

Added comprehensive activity logging to track usage:

```php
// Log activity
$searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
$messages = "Mengakses data bahasa $searchLog (halaman $page dari $totalPage)";
$extra_info = "API";
$level = "INFO";
logActivity($conn, $userData->id, $level, $messages, $extra_info);
```

## API Usage

### Request Format

```
GET /api/mobile/company/get_bahasa.php?q={search}&page={page}&page_size={size}
Headers:
    Authorization: Bearer {jwt_token}
    Content-Type: application/json
```

### Parameters

- **q** (optional): Search term for filtering languages
- **page** (optional): Page number for pagination (default: 1)
- **page_size** (optional): Number of items per page (default: 10)
- **JWT Token** (required): Valid access token containing user authentication data

### JWT Payload Structure

The JWT token must contain:

```json
{
    "id_koordinator": "string",
    "divisi": "string",
    "id": "string",
    "company": "string",
    "iat": timestamp,
    "exp": timestamp
}
```

### Response Format

**Success (HTTP 200):**

```json
{
  "status": true,
  "message": "fetch data berhasil",
  "data": [
    {
      "id": "English",
      "text": "English"
    },
    {
      "id": "Mandarin",
      "text": "Mandarin"
    }
  ],
  "page": 1,
  "page_size": 10,
  "total_page": 5,
  "total_data": 47
}
```

**Authentication Error (HTTP 401):**

```json
{
  "success": false,
  "message": "Unauthorized: Invalid or missing token"
}
```

**Server Error (HTTP 500):**

```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Security Features

### 1. JWT Token Validation

- Validates token signature using secret key
- Checks token expiration
- Verifies token structure and required claims
- Ensures user has valid authentication

### 2. Access Control

- Restricts endpoint access to authenticated users only
- Prevents unauthorized data access
- Maintains user context throughout request

### 3. Activity Logging

- Logs all data access requests
- Records search terms and pagination details
- Tracks user ID from JWT token
- Maintains audit trail for compliance

### 4. Input Validation

- Sanitizes search input to prevent SQL injection
- Validates pagination parameters
- Implements proper parameter binding

## Functionality Preserved

### 1. Search Capability

- Language search using LIKE queries
- Case-insensitive search functionality
- Maintains existing search logic

### 2. Pagination

- Configurable page size
- Proper offset calculation
- Total page count calculation
- Maintains pagination metadata

### 3. Data Format

- Preserves Select2-compatible response format
- Maintains id/text structure for frontend components
- Consistent JSON response format

## Migration Benefits

### 1. Enhanced Security

- **Authentication Required**: Prevents unauthorized data access
- **Token-based Security**: More secure than session-based authentication
- **User Context**: Maintains user identity throughout request lifecycle

### 2. Audit Trail

- **Activity Logging**: Comprehensive tracking of data access
- **Search Tracking**: Records what users are searching for
- **Usage Analytics**: Enables analysis of feature usage patterns

### 3. Consistency

- **Standardized Authentication**: Same pattern as other endpoints
- **Unified Error Handling**: Consistent response format
- **Maintainable Code**: Centralized authentication logic

## Error Handling

### 1. Authentication Errors

- **Invalid Token**: Returns 401 with descriptive message
- **Expired Token**: Prompts for token refresh
- **Missing Token**: Clear unauthorized response

### 2. Database Errors

- **Connection Issues**: Graceful error handling
- **Query Failures**: Proper error responses
- **Data Validation**: Input sanitization

### 3. Parameter Validation

- **Invalid Page Numbers**: Defaults to page 1
- **Invalid Page Size**: Uses default page size
- **Malformed Search**: Handles empty/invalid search terms

## Testing

### Manual Testing

```bash
# Test with valid token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/digitalcv/api/mobile/company/get_bahasa.php?q=english&page=1&page_size=5"

# Test without token (should fail)
curl "http://localhost/digitalcv/api/mobile/company/get_bahasa.php"

# Test with search
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost/digitalcv/api/mobile/company/get_bahasa.php?q=indo"
```

### Integration Testing

The endpoint can be tested as part of the complete authentication flow:

1. Login to get JWT token
2. Use token to access language data
3. Verify search and pagination work correctly
4. Check activity logs are created

## Performance Considerations

### 1. Database Queries

- Uses prepared statements for security
- Implements proper pagination to limit result sets
- Efficient LIKE queries for search functionality

### 2. Memory Usage

- Processes results in chunks via pagination
- Avoids loading large datasets into memory
- Proper resource cleanup

### 3. Response Times

- Lightweight JWT validation
- Minimal processing overhead
- Fast JSON encoding

## Backward Compatibility

**Breaking Changes:**

- Endpoint now requires JWT authentication
- Previously public endpoint is now secured
- Unauthenticated requests will be rejected

**Migration Required:**

- Client applications must obtain JWT tokens before accessing endpoint
- Update API calls to include Authorization header
- Handle 401 Unauthorized responses appropriately

## Maintenance Notes

### 1. Database Dependencies

- Requires `bahasa` table for language data
- Uses `koordinator_pic` table for user authentication (via JWT)
- Activity logging depends on logActivity function

### 2. Configuration

- JWT secret key in `.env` file
- Configurable pagination limits
- Search functionality can be tuned

### 3. Monitoring

- Monitor search patterns for optimization opportunities
- Track authentication failure rates
- Analyze pagination usage for performance tuning

## Usage Examples

### Frontend JavaScript

```javascript
async function getLanguages(searchTerm = "", page = 1, pageSize = 10) {
  const token = localStorage.getItem("jwt_token");

  try {
    const params = new URLSearchParams({
      q: searchTerm,
      page: page,
      page_size: pageSize,
    });

    const response = await fetch(
      `/api/mobile/company/get_bahasa.php?${params}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      throw new Error("Failed to fetch languages");
    }
  } catch (error) {
    console.error("Error fetching languages:", error);
    throw error;
  }
}

// Usage in Select2
$("#language-select").select2({
  ajax: {
    url: "/api/mobile/company/get_bahasa.php",
    dataType: "json",
    delay: 250,
    beforeSend: function (xhr) {
      const token = localStorage.getItem("jwt_token");
      xhr.setRequestHeader("Authorization", `Bearer ${token}`);
    },
    data: function (params) {
      return {
        q: params.term,
        page: params.page || 1,
        page_size: 10,
      };
    },
    processResults: function (data, params) {
      params.page = params.page || 1;

      return {
        results: data.data,
        pagination: {
          more: params.page < data.total_page,
        },
      };
    },
  },
});
```

### PHP Client

```php
function getLanguages($token, $search = '', $page = 1, $pageSize = 10) {
    $url = "http://localhost/digitalcv/api/mobile/company/get_bahasa.php";
    $params = http_build_query([
        'q' => $search,
        'page' => $page,
        'page_size' => $pageSize
    ]);

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json'
            ]
        ]
    ]);

    $response = file_get_contents($url . '?' . $params, false, $context);
    return json_decode($response, true);
}
```

## Conclusion

The JWT authentication implementation for `get_bahasa.php` successfully secures the language data endpoint while maintaining all existing functionality. The endpoint now requires proper authentication, provides comprehensive activity logging, and follows the established security patterns used throughout the application.
