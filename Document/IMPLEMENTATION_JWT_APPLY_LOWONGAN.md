# Implementasi JWT Authentication untuk Candidate Mobile API

## File yang Diubah

### 1. File: `api/mobile/candidate/apply_lowongan.php`

### 2. File: `api/mobile/candidate/form_cv.php`

## Perubahan yang Dilakukan

### Header dan Imports

**Untuk kedua file (`apply_lowongan.php` dan `form_cv.php`):**

```php
// SEBELUM
$pin = "-";
$nama_user = "-";
$email_user = "-";

// get data user
if (isset($_POST['pin'])) {
    $pin = $_POST['pin'];
}

if (isset($_POST['nama'])) {
    $nama_user = $_POST['nama'];
}

if (isset($_POST['email'])) {
    $email_user = $_POST['email'];
}

// SESUDAH
include '../../jwt_helper.php'; // tambahkan import JWT helper

// Verifikasi access token dan dapatkan data user
$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama_lengkap ?? "-";
$email_user = $userData->email ?? "-";
```

#### Perubahan yang Dilakukan:

1. **Menambahkan import `jwt_helper.php`** untuk menggunakan fungsi JWT
2. **Mengganti pengambilan data dari POST parameter** dengan **JWT token verification**
3. **Menggunakan `requireAuth()`** yang akan:
   - Mengambil Bearer token dari header Authorization
   - Memverifikasi validitas token
   - Mengembalikan data user yang sudah tervalidasi
   - Mengembalikan error 401 jika token tidak valid/tidak ada

## Fungsi-fungsi yang Terpengaruh

### File `apply_lowongan.php`:

- Proses lamaran kerja dengan screening otomatis

### File `form_cv.php`:

- `getProgresPengisian`: Mendapatkan progress pengisian CV
- `simpanIdentitasDiri`: Menyimpan identitas diri kandidat
- `simpanRiwayatPendidikan`: Menyimpan riwayat pendidikan
- `updateRiwayatPendidikan`: Update riwayat pendidikan
- `deleteRiwayatPendidikan`: Hapus riwayat pendidikan
- `simpanRiwayatKursus`: Menyimpan riwayat kursus
- `updateRiwayatKursus`: Update riwayat kursus
- `deleteRiwayatKursus`: Hapus riwayat kursus
- `simpanRiwayatPekerjaan`: Menyimpan riwayat pekerjaan
- `updateRiwayatPekerjaan`: Update riwayat pekerjaan
- `deleteRiwayatPekerjaan`: Hapus riwayat pekerjaan
- `simpanInformasiPekerjaan`: Menyimpan informasi pekerjaan
- `simpanMinatKonsep`: Menyimpan minat dan konsep kerja
- `simpanRiwayatOrganisasi`: Menyimpan riwayat organisasi
- `updateRiwayatOrganisasi`: Update riwayat organisasi
- `deleteRiwayatOrganisasi`: Hapus riwayat organisasi

## Fungsi JWT Helper yang Digunakan

### `requireAuth()`

```php
function requireAuth()
{
    $token = getBearerToken();

    if (!$token) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Access token diperlukan'
        ]);
        exit();
    }

    $verification = verifyAccessToken($token);

    if (!$verification['valid']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => $verification['message']
        ]);
        exit();
    }

    return $verification['data'];
}
```

## Cara Penggunaan dari Client/Frontend

### Sebelum (menggunakan PIN di body)

```javascript
// POST request body
{
    "pin": "USER123",
    "nama": "John Doe",
    "email": "<EMAIL>",
    "id_req": "REQ123",
    "lokasi": "JAKARTA",
    "pertanyaan_khusus": ["jawaban1", "jawaban2"]
}
```

### Sesudah (menggunakan JWT token di header)

**Untuk semua endpoint di kedua file:**

```javascript
// Headers
{
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "Content-Type": "application/json"
}

// Contoh POST request body untuk apply_lowongan.php
{
    "id_req": "REQ123",
    "lokasi": "JAKARTA",
    "pertanyaan_khusus": ["jawaban1", "jawaban2"]
}

// Contoh POST request body untuk form_cv.php (simpanIdentitasDiri)
{
    "nama": "John Doe",
    "tempat_lahir": "Jakarta",
    "tgl_lahir": "1990-01-01",
    "jk": "Laki-Laki",
    "status_pernikahan": "Belum Menikah",
    "ktp": "1234567890123456",
    "no_telepon": "081234567890",
    "email": "<EMAIL>"
    // ... parameter lainnya (PIN tidak diperlukan lagi dari body)
}
```

## Keuntungan Implementasi JWT

1. **Security**: Token memiliki expiration time dan signed secara cryptographic
2. **Stateless**: Server tidak perlu menyimpan session
3. **Standardization**: Menggunakan standar JWT yang universal
4. **Data Integrity**: Data user di-embed dalam token dan terverifikasi
5. **Scalability**: Lebih mudah untuk scale horizontal

## Response Error Handling

### Token tidak ada

```json
{
  "success": false,
  "message": "Access token diperlukan"
}
```

### Token tidak valid/expired

```json
{
  "success": false,
  "message": "Token sudah expired" // atau "Token tidak valid"
}
```

## Testing

Untuk testing endpoint ini, pastikan:

1. User sudah login dan mendapat access token
2. Sertakan token di header `Authorization: Bearer <token>`
3. **Untuk apply_lowongan.php**: Body request berisi `id_req`, `lokasi`, dan `pertanyaan_khusus` (opsional)
4. **Untuk form_cv.php**: Body request berisi parameter sesuai fungsi yang dipanggil (tanpa perlu mengirim PIN, nama, email di body)

### Contoh Testing untuk form_cv.php:

```bash
# Test getProgresPengisian
GET /api/mobile/candidate/form_cv.php?func=getProgresPengisian
Headers: Authorization: Bearer <token>

# Test simpanIdentitasDiri
POST /api/mobile/candidate/form_cv.php?func=simpanIdentitasDiri
Headers: Authorization: Bearer <token>
Body: {
  "nama": "John Doe",
  "tempat_lahir": "Jakarta",
  "tgl_lahir": "1990-01-01",
  // ... parameter lainnya
}
```

## Backward Compatibility

Implementasi ini **tidak backward compatible** dengan sistem lama yang menggunakan PIN di POST body. Semua client harus diupdate untuk menggunakan JWT authentication.
