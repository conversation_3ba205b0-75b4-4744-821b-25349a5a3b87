<?php
// callback.php
date_default_timezone_set('Asia/Jakarta');

// Database connection details
include '../../model/database.php';

// iPaymu Credentials (Replace with your actual keys)
$merchant_code = '0000008117797779';
$private_key = 'SANDBOXEA517E38-52EA-42E5-88F4-1DDD3F7A6366'; // For this example, let's assume this is your private key

// Use an existing connection class or a simple mysqli
$conn = new mysqli(DB_SERVER, DB_USER, DB_PASSWORD, DB_DATABASE);

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $conn->connect_error]);
    exit;
}
$conn->set_charset("utf8mb4");

$create_at = date("Y-m-d H:i:s");

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // simpan log error
    $message = "Method not allowed";
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'Callback Pembayaran', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $message);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get raw body and headers
$rawData = file_get_contents("php://input");
$headers = getallheaders();

// --- SECURITY: SIGNATURE VERIFICATION ---
// Check for required headers
$required_headers = ['x-signature', 'x-external-id', 'x-timestamp'];
foreach ($required_headers as $field) {
    if (!isset($headers[$field])) {
        // simpan log error
        $message = "Missing required header: " . $field;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'Callback Pembayaran', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $message);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode(['error' => "Missing required header: " . $field]);
        exit;
    }
}

// // Extract headers for verification
// $ipaymu_signature = $headers['x-signature'];
// $external_id = $headers['x-external-id'];
// $timestamp = $headers['x-timestamp'];

// // Recreate the signature based on iPaymu's formula
// $stringToSign = strtoupper('POST') . ':' . $merchant_code . ':' . strtolower(hash('sha256', $rawData)) . ':' . $private_key;
// $my_signature = hash_hmac('sha256', $stringToSign, $private_key);

// if ($my_signature !== $ipaymu_signature) {
//     // Log this attempt and exit immediately
//     http_response_code(403);
//     echo json_encode(['error' => 'Invalid signature. Request may be fraudulent.']);
//     exit;
// }
// // --- END OF SIGNATURE VERIFICATION ---

// Parse the request body
parse_str($rawData, $parsed);

if (count($parsed) === 1 && is_string(current($parsed))) {
    $data = json_decode(current($parsed), true);
} else {
    $data = $parsed;
}

if (!is_array($data)) {
    // simpan log error
    $message = "Invalid data format";
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'Callback Pembayaran', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $message);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(400);
    echo json_encode(['error' => 'Invalid data format']);
    exit;
}

$required_fields = ['trx_id', 'sid', 'reference_id', 'status', 'amount', 'fee', 'paid_off', 'created_at', 'paid_at', 'settlement_status', 'via', 'buyer_name'];
foreach ($required_fields as $field) {
    if (!isset($data[$field])) {
        // simpan log error
        $message = "Missing required field: " . $field;
        $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'Callback Pembayaran', 'SISTEM', '', '', '')");
        $sql->bind_param("ss", $create_at, $message);

        if ($sql->execute()) {
            $sql->close();
        }

        http_response_code(400);
        echo json_encode(['error' => "Missing required field: " . $field]);
        exit;
    }
}

$trx_id = $data['trx_id'];
$sid = $data['sid'];
$reference_id = $data['reference_id'];
$status = $data['status'];
$amount = $data['amount'];
$fee = $data['fee'];
$paid_off = $data['paid_off'];
$created_at = $data['created_at'];
$paid_at = $data['paid_at'];
$settlement_status = $data['settlement_status'];
$via = $data['via'];
$buyer_name = $data['buyer_name'];

try {
    $conn->begin_transaction();

    // Log the callback data
    $log_stmt = $conn->prepare("INSERT INTO `callback_log` (`session_id`, `reference_id`, `log`) VALUES (?, ?, ?)");
    if (!$log_stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }
    $log_stmt->bind_param("sss", $sid, $reference_id, $rawData);
    if (!$log_stmt->execute()) {
        throw new Exception('Failed to log callback data.');
    }
    $log_stmt->close();

    // Fetch the current payment status before updating
    $current_payment_stmt = $conn->prepare("SELECT `status` FROM `payment` WHERE SessionID = ? and referenceId = ?");
    $current_payment_stmt->bind_param("ss", $sid, $reference_id);
    $current_payment_stmt->execute();
    $current_payment_result = $current_payment_stmt->get_result();
    $current_payment_row = $current_payment_result->fetch_assoc();

    // Check if the transaction has already been successfully processed
    if ($current_payment_row && $current_payment_row['status'] === 'berhasil' && $status === 'berhasil') {
        http_response_code(200);
        echo json_encode(['success' => true, 'message' => 'Callback received and transaction already processed.']);
        exit; // Exit to prevent double processing
    }

    // Update the payment table
    $payment_stmt = $conn->prepare("UPDATE payment SET fee = ?, paid_off = ?, `status` = ?, trx_id = ?, paid_at = ?, via = ?, settlement_status = ? WHERE SessionID = ? and referenceId = ?");
    if (!$payment_stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }
    $payment_stmt->bind_param("sssssssss", $fee, $paid_off, $status, $trx_id, $paid_at, $via, $settlement_status, $sid, $reference_id);
    if (!$payment_stmt->execute()) {
        throw new Exception('Failed to update payment.');
    }
    $payment_stmt->close();

    // Only update the coordinator if the payment is successful and settled
    if ($status === 'berhasil') {
        // Get payment details to update coordinator
        $get_stmt = $conn->prepare("SELECT product, paket, qty FROM payment WHERE SessionID = ? and referenceId = ?");
        if (!$get_stmt) {
            throw new Exception('Prepare statement failed: ' . $conn->error);
        }
        $get_stmt->bind_param("ss", $sid, $reference_id);
        $get_stmt->execute();
        $result = $get_stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $product = $row['product'];
            $paket = $row['paket'];
            $qty = $row['qty'];
            $id_koordinator = $reference_id;

            if ($product == 'Pembelian Psikotes' && $paket == 'Psikotes') {
                $get_koordinator_stmt = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
                if (!$get_koordinator_stmt) {
                    throw new Exception('Prepare statement failed: ' . $conn->error);
                }
                $get_koordinator_stmt->bind_param("s", $id_koordinator);
                $get_koordinator_stmt->execute();
                $koordinator_result = $get_koordinator_stmt->get_result();

                if ($koordinator_result->num_rows > 0) {
                    $kategori = 'Psikotes';
                    $row_koordinator = mysqli_fetch_array($koordinator_result);
                    $paket_koordinator = $row_koordinator['paket'];

                    // Quota Update Logic
                    $get_koordinator_quota_stmt = $conn->prepare("SELECT * FROM `quotas_koordinator` WHERE `id_koordinator` = ? AND `kategori` = ?");
                    if (!$get_koordinator_quota_stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }
                    $get_koordinator_quota_stmt->bind_param("ss", $id_koordinator, $kategori);
                    $get_koordinator_quota_stmt->execute();
                    $koordinator_quota_result = $get_koordinator_quota_stmt->get_result();

                    if ($koordinator_quota_result->num_rows > 0) {
                        $quota_stmt = $conn->prepare("UPDATE `quotas_koordinator` SET `paid_quota` = paid_quota + ?, `last_updated` = NOW() WHERE `id_koordinator` = ? AND `kategori` = ?");
                        if (!$quota_stmt) {
                            throw new Exception('Prepare statement failed: ' . $conn->error);
                        }
                        $quota_stmt->bind_param("sss", $qty, $id_koordinator, $kategori);
                        if (!$quota_stmt->execute()) {
                            throw new Exception('Failed to update quotas: ' . $quota_stmt->error);
                        }
                        $quota_stmt->close();
                    } else {
                        $get_quotas_stmt = $conn->prepare("SELECT `default_quota`, `kategori` FROM `quotas` WHERE `paket` = ?");
                        if (!$get_quotas_stmt) {
                            throw new Exception('Prepare statement failed: ' . $conn->error);
                        }
                        $get_quotas_stmt->bind_param("s", $paket_koordinator);
                        $get_quotas_stmt->execute();
                        $quotas_result = $get_quotas_stmt->get_result();

                        if ($quotas_result->num_rows > 0) {
                            $quota_row = $quotas_result->fetch_assoc();
                            $default_quota = $quota_row['default_quota'];

                            $quota_stmt = $conn->prepare("INSERT INTO `quotas_koordinator` (`id_koordinator`, `remaining_quota`, `paid_quota`, `kategori`, `last_updated`) VALUES (?, ?, ?, ?, NOW())");
                            if (!$quota_stmt) {
                                throw new Exception('Prepare statement failed: ' . $conn->error);
                            }
                            $quota_stmt->bind_param("ssss", $id_koordinator, $default_quota, $qty, $kategori);
                            if (!$quota_stmt->execute()) {
                                throw new Exception('Failed to insert new quotas: ' . $quota_stmt->error);
                            }
                            $quota_stmt->close();
                        } else {
                            throw new Exception('Default quota not found.');
                        }
                    }
                } else {
                    throw new Exception('Company data not found for the given reference ID.');
                }
            } elseif ($product == 'Pembelian Cari Kandidat' && $paket == 'Cari Kandidat') {
                $get_koordinator_stmt = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
                if (!$get_koordinator_stmt) {
                    throw new Exception('Prepare statement failed: ' . $conn->error);
                }
                $get_koordinator_stmt->bind_param("s", $id_koordinator);
                $get_koordinator_stmt->execute();
                $koordinator_result = $get_koordinator_stmt->get_result();

                if ($koordinator_result->num_rows > 0) {
                    $kategori = 'Cari Kandidat';
                    $row_koordinator = mysqli_fetch_array($koordinator_result);
                    $paket_koordinator = $row_koordinator['paket'];

                    // Quota Update Logic
                    $get_koordinator_quota_stmt = $conn->prepare("SELECT * FROM `quotas_koordinator` WHERE `id_koordinator` = ? AND `kategori` = ?");
                    if (!$get_koordinator_quota_stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }
                    $get_koordinator_quota_stmt->bind_param("ss", $id_koordinator, $kategori);
                    $get_koordinator_quota_stmt->execute();
                    $koordinator_quota_result = $get_koordinator_quota_stmt->get_result();

                    if ($koordinator_quota_result->num_rows > 0) {
                        $quota_stmt = $conn->prepare("UPDATE `quotas_koordinator` SET `paid_quota` = paid_quota + ?, `last_updated` = NOW() WHERE `id_koordinator` = ? AND `kategori` = ?");
                        if (!$quota_stmt) {
                            throw new Exception('Prepare statement failed: ' . $conn->error);
                        }
                        $quota_stmt->bind_param("sss", $qty, $id_koordinator, $kategori);
                        if (!$quota_stmt->execute()) {
                            throw new Exception('Failed to update quotas: ' . $quota_stmt->error);
                        }
                        $quota_stmt->close();
                    } else {
                        $get_quotas_stmt = $conn->prepare("SELECT `default_quota`, `kategori` FROM `quotas` WHERE `paket` = ?");
                        if (!$get_quotas_stmt) {
                            throw new Exception('Prepare statement failed: ' . $conn->error);
                        }
                        $get_quotas_stmt->bind_param("s", $paket_koordinator);
                        $get_quotas_stmt->execute();
                        $quotas_result = $get_quotas_stmt->get_result();

                        if ($quotas_result->num_rows > 0) {
                            $quota_row = $quotas_result->fetch_assoc();
                            $default_quota = $quota_row['default_quota'];

                            $quota_stmt = $conn->prepare("INSERT INTO `quotas_koordinator` (`id_koordinator`, `remaining_quota`, `paid_quota`, `kategori`, `last_updated`) VALUES (?, ?, ?, ?, NOW())");
                            if (!$quota_stmt) {
                                throw new Exception('Prepare statement failed: ' . $conn->error);
                            }
                            $quota_stmt->bind_param("ssss", $id_koordinator, $default_quota, $qty, $kategori);
                            if (!$quota_stmt->execute()) {
                                throw new Exception('Failed to insert new quotas: ' . $quota_stmt->error);
                            }
                            $quota_stmt->close();
                        } else {
                            throw new Exception('Default quota not found.');
                        }
                    }
                } else {
                    throw new Exception('Company data not found for the given reference ID.');
                }
            } elseif ($paket == 'C2' || $paket == 'C3' || $paket == 'C4') {
                $get_koordinator_stmt = $conn->prepare("SELECT expired FROM koordinator WHERE id_koordinator = ?");
                if (!$get_koordinator_stmt) {
                    throw new Exception('Prepare statement failed: ' . $conn->error);
                }
                $get_koordinator_stmt->bind_param("s", $id_koordinator);
                $get_koordinator_stmt->execute();
                $koordinator_result = $get_koordinator_stmt->get_result();

                if ($koordinator_result->num_rows > 0) {
                    $koordinator_row = $koordinator_result->fetch_assoc();
                    $expired_date = $koordinator_row['expired'];
                    $current_date = new DateTime('now');

                    // If expired date is in the past, use the current date as a base
                    if ($expired_date && new DateTime($expired_date) > $current_date) {
                        $date = new DateTime($expired_date);
                    } else {
                        $date = $current_date;
                    }

                    $date->modify('+1 month');
                    $new_expired_date = $date->format('Y-m-d');

                    $koordinator_stmt = $conn->prepare("UPDATE koordinator SET paket = ?, expired = ? WHERE id_koordinator = ?");
                    if (!$koordinator_stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }
                    $koordinator_stmt->bind_param("sss", $paket, $new_expired_date, $id_koordinator);
                    if (!$koordinator_stmt->execute()) {
                        throw new Exception('Failed to update coordinator: ' . $koordinator_stmt->error);
                    }
                    $koordinator_stmt->close();

                    // Quota Update Logic
                    $get_quotas_stmt = $conn->prepare("SELECT `default_quota`, `kategori` FROM `quotas` WHERE `paket` = ?");
                    if (!$get_quotas_stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }
                    $get_quotas_stmt->bind_param("s", $paket);
                    $get_quotas_stmt->execute();
                    $quotas_result = $get_quotas_stmt->get_result();

                    if ($quotas_result->num_rows > 0) {
                        while ($quota_row = $quotas_result->fetch_assoc()) {
                            $default_quota = $quota_row['default_quota'];
                            $kategori = $quota_row['kategori'];

                            $get_koordinator_quota_stmt = $conn->prepare("SELECT * FROM `quotas_koordinator` WHERE `id_koordinator` = ? AND `kategori` = ?");
                            if (!$get_koordinator_quota_stmt) {
                                throw new Exception('Prepare statement failed: ' . $conn->error);
                            }
                            $get_koordinator_quota_stmt->bind_param("ss", $id_koordinator, $kategori);
                            $get_koordinator_quota_stmt->execute();
                            $koordinator_quota_result = $get_koordinator_quota_stmt->get_result();

                            if ($koordinator_quota_result->num_rows > 0) {
                                $quota_stmt = $conn->prepare("UPDATE `quotas_koordinator` SET `remaining_quota` = ?, `last_updated` = NOW() WHERE `id_koordinator` = ? AND `kategori` = ?");
                                if (!$quota_stmt) {
                                    throw new Exception('Prepare statement failed: ' . $conn->error);
                                }
                                $quota_stmt->bind_param("sss", $default_quota, $id_koordinator, $kategori);
                                if (!$quota_stmt->execute()) {
                                    throw new Exception('Failed to update quotas: ' . $quota_stmt->error);
                                }
                                $quota_stmt->close();
                            } else {
                                $quota_stmt = $conn->prepare("INSERT INTO `quotas_koordinator` (`id_koordinator`, `remaining_quota`, `kategori`, `last_updated`) VALUES (?, ?, ?, NOW())");
                                if (!$quota_stmt) {
                                    throw new Exception('Prepare statement failed: ' . $conn->error);
                                }
                                $quota_stmt->bind_param("sss", $id_koordinator, $default_quota, $kategori);
                                if (!$quota_stmt->execute()) {
                                    throw new Exception('Failed to insert new quotas: ' . $quota_stmt->error);
                                }
                                $quota_stmt->close();
                            }
                        }
                    }
                } else {
                    throw new Exception('Company data not found for the given reference ID.');
                }
            } else {
                throw new Exception('The payment was not found.');
            }
        } else {
            // Transaction is successful but payment record not found. This is a severe issue.
            throw new Exception('Payment data not found for the given SessionID.');
        }
    }

    // Commit the transaction
    $conn->commit();

    // Send success response
    http_response_code(200);
    echo json_encode(['success' => true, 'message' => 'Callback received and processed.']);
} catch (Exception $e) {
    // Rollback and send an error response
    $conn->rollback();

    // simpan log error
    $message = $e->getMessage() . " From transaction " . $sid . ' - Ref: ' . $reference_id;
    $sql = $conn->prepare("INSERT INTO `log_aktifitas` (`timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES (?, 'ERROR', ?, 'Callback Pembayaran', 'SISTEM', '', '', '')");
    $sql->bind_param("ss", $create_at, $message);

    if ($sql->execute()) {
        $sql->close();
    }

    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    $conn->close();
}
