# Apple Login Implementation - Candidate

## Overview

Implementasi login Apple untuk kandidat yang menerima data email dan nama dari frontend. API ini mendukung:

- Login untuk user yang sudah terdaftar
- Registrasi otomatis untuk user baru dari Apple
- <PERSON>rasi JWT access token dan refresh token
- Logging aktivitas user

## Endpoint

```
POST /api/mobile/candidate/login_apple.php
```

## Headers

```
Content-Type: application/json
```

## Request Body

```json
{
  "email": "<EMAIL>",
  "nama": "John Doe",
  "koordinat": "optional_coordinates",
  "device": "optional_device_info",
  "ip": "optional_ip_address"
}
```

### Required Parameters

- `email` (string): Email address dari Apple ID
- `nama` (string): <PERSON>a lengkap user dari Apple ID

### Optional Parameters

- `koordinat` (string): Koordinat lokasi user
- `device` (string): Informasi device user
- `ip` (string): IP address user

## Response

### Success Response (User Existing)

```json
{
  "success": true,
  "message": "Login berhasil.",
  "data": [
    {
      "pin": "20250806123456789",
      "nama": "John Doe",
      "email": "<EMAIL>",
      "img": "https://presigned-url-to-profile-image",
      "no_telp": "081234567890",
      "tempat_lahir": "Jakarta",
      "tgl_lahir": "1 Januari 1990",
      "jenis_kelamin": "L",
      "alamat": "Jl. Contoh No. 123 RT 01 RW 02 Kelurahan Kecamatan Jakarta Pusat DKI Jakarta. 10110",
      "fcm_token": "fcm_token_here",
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
  ]
}
```

### Success Response (New User Registration)

```json
{
  "success": true,
  "message": "Registrasi dan login berhasil.",
  "data": [
    {
      "pin": "20250806123456999",
      "nama": "John Doe",
      "email": "<EMAIL>",
      "img": "",
      "no_telp": "",
      "tempat_lahir": "",
      "tgl_lahir": "",
      "jenis_kelamin": "",
      "alamat": "",
      "fcm_token": "",
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
  ]
}
```

### Error Responses

#### Missing Email

```json
{
  "success": false,
  "message": "Email tidak boleh kosong.",
  "data": []
}
```

#### Invalid Email Format

```json
{
  "success": false,
  "message": "Format email tidak valid.",
  "data": []
}
```

#### Missing Name for New Registration

```json
{
  "success": false,
  "message": "Nama tidak boleh kosong untuk registrasi baru.",
  "data": []
}
```

#### Method Not Allowed

```json
{
  "success": false,
  "message": "Method tidak diizinkan. Gunakan POST.",
  "data": []
}
```

#### Database Error

```json
{
  "success": false,
  "message": "Error message details",
  "data": []
}
```

## Token Information

### Access Token

- **Expiry**: 24 hours
- **Audience**: candidate
- **Contains**: pin, nama, email, tipe

### Refresh Token

- **Expiry**: 30 days
- **Audience**: candidate
- **Contains**: pin, email, tipe
- **Storage**: Saved in database `users_kandidat.refresh_token`

## Security Features

1. **Input Validation**:

   - Email format validation
   - Input sanitization dengan `htmlspecialchars`
   - Required field validation

2. **CORS Headers**:

   - Access-Control-Allow-Origin: \*
   - Access-Control-Allow-Methods: POST
   - Access-Control-Allow-Headers: Content-Type

3. **Database Security**:

   - Prepared statements untuk mencegah SQL injection
   - Transaction rollback pada error
   - Password hashing untuk user baru

4. **Logging**:
   - Login history tracking
   - Activity logging untuk audit trail

## Frontend Integration Example

### JavaScript/Fetch

```javascript
const loginWithApple = async (email, nama) => {
  try {
    const response = await fetch("/api/mobile/candidate/login_apple.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        email: email,
        nama: nama,
        device: "mobile_app",
        ip: "***********", // optional
      }),
    });

    const result = await response.json();

    if (result.success) {
      // Simpan tokens
      localStorage.setItem("access_token", result.data[0].access_token);
      localStorage.setItem("refresh_token", result.data[0].refresh_token);

      // Redirect atau update UI
      console.log("Login successful:", result.data[0]);
    } else {
      console.error("Login failed:", result.message);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};
```

### Flutter/Dart

```dart
Future<Map<String, dynamic>> loginWithApple(String email, String nama) async {
  final response = await http.post(
    Uri.parse('https://your-domain.com/api/mobile/candidate/login_apple.php'),
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: {
      'email': email,
      'nama': nama,
      'device': 'flutter_app',
    },
  );

  return json.decode(response.body);
}
```

## Database Changes

### Tables Affected

1. **users_kandidat**: User data dan refresh token storage
2. **login_history**: Login tracking
3. **activity_logs**: Activity logging (via logActivity function)

### New User Registration Fields

- `pin`: Auto-generated timestamp + random number
- `nama_lengkap`: From Apple nama parameter
- `email`: From Apple email parameter
- `password`: Hashed default password
- `created_at`: Current timestamp
- `email_verified_at`: Current timestamp (auto-verified for Apple)

## Notes

1. **Auto Registration**: User baru dari Apple akan otomatis didaftarkan dengan password default
2. **Profile Completion**: User baru mungkin perlu melengkapi profil (no_telp, alamat, dll)
3. **Token Management**: Refresh token disimpan di database untuk session management
4. **S3 Integration**: Profile image menggunakan S3 presigned URL
5. **Session Compatibility**: Tetap set $\_SESSION untuk backward compatibility

## Error Handling

API menggunakan database transaction untuk memastikan data consistency. Jika ada error dalam proses:

- Transaction akan di-rollback
- Error message akan dikembalikan dalam response
- Tidak ada partial data yang tersimpan

## Testing

Untuk testing API ini, gunakan tool seperti Postman atau curl:

```bash
curl -X POST https://your-domain.com/api/mobile/candidate/login_apple.php \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&nama=Test User"
```
