ada paket C1-C5
limitasi aksesnya ada di PDF
tabelnya ada di limit_cv_universe
ada field paket di tabel koordinator dan list fitur, itu dijoin langsung aja biar dapet pas login

buat filter hasil_screening, harus bikin manual di flutternya (tapi coba dulu hasil dari API nya)

buat akses menu sesuai dengan list fitur (fitur akses dikirim berbarengan dengan login)

captcha htmlnya harus ditambahin di domain yang udah kedaftar di goole

hanya paket C4 yang bisa menjadwalkan Psikotest (Daftarkan Tes Online), selain C4, hanya bisa tolak dan terima
tiap proses tolak, terima, daftarkan tes, dll menggunakan recaptcha
Ajukan pekerjaan unutk C4 tak terbatas

yang company, cuman bisa 1 device saja, pake token

tambahkan Login History di Login

./gradlew signingReport
keytool -genkey -v -keystore my-release-key.keystore -keyalg RSA -keysize 2048 -validity 10000 -alias my-key-alias


getHasilPsikotes
  <a class="dropdown-item" href="javascript:void(0);" onclick="terimaKandidat(\'' . base64_encode($id_lamar) . '\')">Terima Kandidat</a>
                    <a class="dropdown-item" href="javascript:void(0);" onclick="tolakKandidat(\'' . base64_encode($id_lamar) . '\',\'psikotes\')">Tolak Kandidat</a>

Hasil Screening
if ($paket == 'C4') {
                    $btn_terpilih = '<div class="dropdown">
                                        <button type="button" class="btn btn-light btn-sm btn-false" style="background-color: #215899 !important; color: white;" onclick="submitHasilSCreening(\'' . base64_encode($row['id_lamar'] . '|false') . '\')"><i class="fas fa-thumbs-down"></i> Tidak Cocok</button>
                                        <button class="btn bg-light dropdown-toggle btn-sm btn-true" type="button" data-toggle="dropdown" aria-expanded="false" style="background-color: #215899 !important; color: white !important;">
                                            <i class="fas fa-thumbs-up"></i> Cocok
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:void(0);" onclick="terimaKandidat(\'' . base64_encode($row['id_lamar']) . '\')">Terima Kandidat</a>
                                            <a class="dropdown-item" href="javascript:void(0);" onclick="submitHasilSCreening(\'' . base64_encode($row['id_lamar'] . '|true') . '\')">Daftarkan Online Tes</a>
                                        </div>
                                    </div>';
                } else {
                    $btn_terpilih = '<button type="button" class="btn btn-light btn-sm btn-false" style="background-color: #215899 !important; color: white;" onclick="submitHasilSCreening(\'' . base64_encode($row['id_lamar'] . '|false') . '\')"><i class="fas fa-thumbs-down"></i> Tidak Cocok</button>
                    <button type="button" class="btn btn-light btn-sm btn-true" style="background-color: #215899 !important; color: white;" onclick="submitHasilSCreening(\'' . base64_encode($row['id_lamar'] . '|true') . '\')"><i class="fas fa-thumbs-up"></i> Terpilih</button>';
                }

CV Kandidat
$btn .= '<div class="dropdown">
                                <button class="btn bg-blue-dark dropdown-toggle btn-xs" type="button" data-toggle="dropdown" aria-expanded="false">
                                    Konfirmasi
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="javascript:void(0);" onclick="terimaKandidat(\'' . base64_encode($id_lamar) . '\')">Terima Kandidat</a>
                                    <a class="dropdown-item" href="javascript:void(0);" onclick="tolakKandidat(\'' . base64_encode($id_lamar) . '\',\'screening\')">Tolak Kandidat</a>';
                    if ($paket == 'C4') {
                        $btn .= '<a class="dropdown-item" href="javascript:void(0);" onclick="prosesPsikotes(\'' . base64_encode($id_lamar) . '\')">Datfarkan Online Tes</a>';
                    }

Terima Kandidat = terimaKandidat
Tolak Kandidat = tolakKandidat
Daftarkan Online Test = submitHasilScreening true
Tidak Cocok = submitHasilScreening false
Cocok = submitHasilScreening true