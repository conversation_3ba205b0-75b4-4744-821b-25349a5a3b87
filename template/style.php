<link
    rel="stylesheet"
    href="/digitalcv/node_modules/bootstrap/dist/css/bootstrap.min.css" />
<link
    rel="stylesheet"
    href="/digitalcv/assets/css/bootstrap-icons.css" />
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
    href="/digitalcv/assets/fonts/css2.css?family=Poppins:wght@400;500;700&display=swap"
    rel="stylesheet" />
<link href="/digitalcv/assets/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/digitalcv/assets/css/beforeLogin.css" />
<link rel="stylesheet" href="/digitalcv/node_modules/sweetalert2/dist/sweetalert2.min.css">
<link rel="stylesheet" href="/digitalcv/assets/css/font-awesome5.css" />
<link rel="stylesheet" href="/digitalcv/assets/css/owl.carousel.min.css" />
<link rel="stylesheet" href="/digitalcv/assets/css/toastr.css" />
<link rel="icon" href="/digitalcv/assets/images/logo/logoDcv2.png" />
<script src="/digitalcv/assets/js/jquery.min.js"></script>
<script src="/digitalcv/assets/js/toastr.min.js"></script>
<script src="/digitalcv/node_modules/sweetalert2/dist/sweetalert2.all.min.js"></script>
<script src="/digitalcv/assets/js/owl.carousel.min.js"></script>
<script src="/digitalcv/node_modules/bootstrap/dist/js/bootstrap.bundle.js"></script>
<script src="https://www.google.com/recaptcha/api.js?render=6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF"></script>
<script src="/digitalcv/assets/js/select2.min.js"></script>
<style>
    .whatsapp-wrapper {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: fadeIn 0.5s ease;
    }

    .whatsapp-float {
        width: 60px;
        height: 60px;
        background-color: #25D366;
        color: white;
        border-radius: 50%;
        text-align: center;
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        text-decoration: none;
    }

    .whatsapp-float:hover {
        transform: scale(1.1);
    }

    .whatsapp-close {
        background: #fff;
        border: none;
        color: #333;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
        padding: 0;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
        transition: background 0.2s;
    }

    .whatsapp-close:hover {
        background: #eee;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .profile-pic-3 {
        object-fit: cover;
        max-height: 100px;
        display: block;
    }
</style>
<script>
    function closeWhatsAppWidget() {
        document.getElementById("whatsapp-widget").style.display = "none";
    }

    function changeLang(params) {
        $.get('<?= $baseURL ?>api/changeLang.php', {
                params: params
            },
            function(response) {
                window.location.reload();
            }
        )
    }
</script>
<div id="whatsapp-widget" class="whatsapp-wrapper">
    <a href="https://wa.me/628117797779"
        target="_blank"
        title="Chat with us on WhatsApp"
        class="whatsapp-float"><i class="fab fa-whatsapp" style="font-size:35px"></i>
    </a>
    <button class="whatsapp-close" onclick="closeWhatsAppWidget()">×</button>
</div>