//start funciton untuk menampilkan password saat melakukan input
function showPass(id, btn) {
  var input = document.getElementById(id);
  btn = document.getElementById(btn);

  if (input.type === "password") {
    input.type = "text";
    btn.innerHTML = '<i class="bi bi-eye"></i>';
  } else {
    input.type = "password";
    btn.innerHTML = '<i class="bi bi-eye-slash"></i>';
  }
}
//end funciton untuk menampilkan password saat melakukan input

//start fungsi login kandidat
$("#form-login").on("submit", function (event) {
  event.preventDefault(); // Mencegah reload form
  event.stopPropagation(); // Menghentikan event bubbling

  if (this.checkValidity() === false) {
    $(this).addClass("was-validated");
    return;
  }

  var email = $("[name=email]").val();
  password = $("[name=password]").val();
  latitude = $("#latitude").val();
  longitude = $("#longitude").val();
  remember = "false";

  if ($("#remember").is(":checked")) {
    remember = "true";
  }

  $("#btn-masuk").html(window.appData.harapTunggu + "....");
  $("#btn-masuk").attr("disabled", true);

  grecaptcha.ready(function () {
    grecaptcha
      .execute("6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF", {
        action: "submit",
      })
      .then(function (token) {
        $.ajax({
          type: "post",
          url: "../controller/login/loginCompany",
          data: {
            email: email,
            password: password,
            latitude: latitude,
            longitude: longitude,
            remember: remember,
            recaptcha_response: token,
          },
          success: function (result) {
            var obj = JSON.parse(JSON.stringify(result));

            if (obj.status == "success") {
              Swal.fire({
                title: window.appData.berhasil,
                text: window.appData.textberhasil,
                icon: "success",
              }).then((result) => {
                window.location.href = "dashboard/" + obj.link;
              });
            } else {
              toastr["warning"](obj.message);
              $("#btn-masuk").html(window.appData.masuk);
              $("#btn-masuk").prop("disabled", false);
            }
          },
        });
      });
  });
});
//end fungsi submit login kandidat

//start fungsi lupa password kandidat
$("#form-forgot").on("submit", function (event) {
  event.preventDefault(); // Mencegah reload form
  event.stopPropagation(); // Menghentikan event bubbling

  if (this.checkValidity() === false) {
    $(this).addClass("was-validated");
    return;
  }

  grecaptcha.ready(function () {
    grecaptcha
      .execute("6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF", {
        action: "submit",
      })
      .then(function (token) {
        var email = $("[name=email]").val();

        $("#btn-kirim").html(window.appData.harapTunggu + "....");
        $("#btn-kirim").attr("disabled", true);
        $.ajax({
          type: "post",
          url: "../controller/login/forgotPasswordCompany",
          data: {
            email: email,
            recaptcha_response: token,
          },
          success: function (result) {
            var obj = JSON.parse(JSON.stringify(result));

            if (obj.status == "success") {
              Swal.fire({
                title: window.appData.berhasil,
                text: obj.message,
                icon: "success",
              }).then((result) => {
                window.location.href = "login";
              });
            } else {
              toastr["warning"](obj.message);
              $("#btn-kirim").html(window.appData.kirim);
              $("#btn-kirim").prop("disabled", false);
            }
          },
        });
      });
  });
});
//end fungsi lupa password kandidat
