<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

require_once '../../fcm_helper.php';
include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;

// Legacy variables for backward compatibility
$id_pegawai =  $userData->id;
$pin = "-";
$nama_user = "-";
$email_user = "-";


function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Get function from URL parameter
$func = $_GET['func'] ?? '';


if ($func == "cekNotif") {

    try {
        $data = array();

        // Ambil parameter dari query string
        $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
        $page_size = isset($_GET['page_size']) ? (int) $_GET['page_size'] : 5;
        $offset = ($page - 1) * $page_size;

        $status = "false";
        $message = "Notifikasi gagal";
        $konten = array(
            'notification' => 0,
            'titleNotif' => 'Anda tidak memiliki notifikasi',
            'items' => []
        );

        // Hitung total data
        $countQuery = $conn->prepare("SELECT COUNT(*) as total FROM notif_summary WHERE penerima = ?");
        $countQuery->bind_param("s", $pin);
        $countQuery->execute();
        $countResult = $countQuery->get_result();
        $total_data = $countResult->fetch_assoc()['total'];
        $countQuery->close();

        $total_page = ceil($total_data / $page_size);

        $get = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
        $get->bind_param("s", $id_koordinator);
        $get->execute();
        $res = $get->get_result();
        $get->close();

        $paket = 1;
        if ($res->num_rows > 0) {
            $row = mysqli_fetch_array($res);
            $paket = preg_replace("/[^0-9]/", "", $row['paket']);
        }


        // Ambil data dengan pagination
        $get = $conn->prepare("SELECT
                            *
                        FROM
                            notif_summary
                        WHERE
                            (penerima = ? OR penerima = ?)
                        ORDER BY
                            create_at DESC
                        LIMIT ?, ?");
        $get->bind_param("ssii", $id_koordinator, $id_pegawai, $offset, $page_size);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $notifList = [];
            $jml_notif = 0;

            while ($row = mysqli_fetch_array($result)) {


                // Waktu relatif
                $waktuawal  = date_create($row['create_at']);
                $waktuakhir = date_create();
                $diff  = date_diff($waktuawal, $waktuakhir);

                if ($diff->d != '0') {
                    $waktu = $diff->d . ' hari yang lalu';
                } elseif ($diff->h != '0') {
                    $waktu = $diff->h . ' jam yang lalu';
                } elseif ($diff->i != '0') {
                    $waktu = $diff->i . ' menit yang lalu';
                } else {
                    $waktu = $diff->s . ' detik yang lalu';
                }

                // Link jika judul tertentu
                $link = "";
                if ($row['judul'] == 'Melamar Pekerjaan') {
                    if ($paket > 1) {
                        $link = "http://" . $_SERVER['HTTP_HOST'] . "/company/dashboard/daftar_pelamar/detail_request.php?id_req=" . $row['id_referensi'];
                    } else {
                        $link = "http://" . $_SERVER['HTTP_HOST'] . "/company/dashboard/daftar_pelamar/cv_kandidat";
                    }
                }

                // Tambahkan ke list
                $notifList[] = array(
                    'isi' => $row['isi'],
                    'waktu' => $waktu,
                    'link' => $link,
                    'penerima' => $row['penerima'],
                    'pengirim' => $row['pengirim'],
                    'tgl_kirim' => $row['create_at'],
                    'status' => $row['status'],
                    'id_req' => base64_decode($row['id_referensi']),
                );

                $jml_notif++;
            }


            $status = "true";
            $message = $result;
            $konten = array(
                'notification' => $total_data,
                'titleNotif' => 'Anda memiliki ' . $total_data . ' notifikasi baru',
                'items' => $notifList
            );
        } else {
            $status = "true";
            $message = "Tidak ada notifikasi";
            $konten = array(
                'notification' => 0,
                'titleNotif' => 'Anda tidak memiliki notifikasi',
                'items' => []
            );
        }

        // $dataPayload = [
        //     'isi' => 'Dandy melamar pekerjaan...',
        //     'waktu' => '11 hari yang lalu',
        //     'link' => 'http://192.168.100.46/...'
        // ];

        // try {
        //     $result = sendNotifikasiFromCompany("dWvLxPNIRDeYr5xiawuGYp:APA91bFtwwlYhTz2JzMq-U1NL3vi_T0w6qOEVqZrVTjBjA77IT3AJ7TNfTquNpMKz5LPxWnKowKBy54rcNHsDbZE3obHIz4J56RkBccwGJfxhwFOvt_WGlc", "Test", "Test Message", $dataPayload);
        // } catch (Exception $e) {
        //     $status = "false";
        //     $message = "Gagal mengirim notifikasi: " . $e->getMessage();
        // }
    } catch (\Throwable $th) {
        $status = "false";
        $message = "Get notifikasi gagal";
        $konten = array(
            'notification' => 0,
            'titleNotif' => 'Anda tidak memiliki notifikasi',
            'items' => []
        );
    }

    // Susun data akhir
    $data = array(
        'status' => $status === "true",
        'message' => $message,
        'notification' => $konten['notification'],
        'titleNotif' => $konten['titleNotif'],
        'data' => $konten['items'],
        'page' => $page,
        'page_size' => $page_size,
        'total_page' => $total_page,
        'total_data' => $total_data
    );

    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == "countNewNotif") {

    $data = array();

    $status = "false";
    $message = "Gagal mengambil jumlah notifikasi";

    // Query untuk mengambil total notifikasi yang belum dibaca
    $get = $conn->prepare("SELECT
                            COUNT(*) as total 
                        FROM
                            notif_summary
                        WHERE
                            (penerima = ? OR penerima = ?) 
                            AND status <> 'Dibaca'");
    $get->bind_param("ss", $id_koordinator, $id_pegawai);
    $get->execute();
    $result = $get->get_result();
    $row = $result->fetch_assoc();
    $get->close();

    $total_notif = (int)(isset($row['total']) ? $row['total'] : 0);

    $status = "true";
    $message = "Berhasil mengambil jumlah notifikasi";

    // Susun data akhir
    $data = array(
        'status' => $status === "true",
        'message' => $message,
        'data' => [],
        'total_notif' => $total_notif,
    );

    echo json_encode($data, JSON_PRETTY_PRINT);
}


if ($func == 'updateNotif') {

    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    if (isset($_POST['penerima']) && isset($_POST['pengirim']) && isset($_POST['tgl'])) {
        $penerima = $_POST['penerima'];
        $pengirim = $_POST['pengirim'];
        $tgl = $_POST['tgl'];

        $update = $conn->prepare("UPDATE notif_summary SET `status` = 'Dibaca' WHERE penerima = ? AND pengirim = ? AND create_at = ?");
        $update->bind_param("sss", $penerima, $pengirim, $tgl);
        if ($update->execute()) {
            $update->close();

            $status = 'true';
            $message = 'Update notifikasi berhasil';
        }
    }

    $data['status'] = $status === "true";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateAllNotif') {
    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    $update = $conn->prepare("UPDATE notif_summary SET `status` = 'Dibaca' WHERE penerima = ? AND `status` = 'Dikirim'");
    $update->bind_param("s", $id_koordinator);
    if ($update->execute()) {
        $update->close();

        $status = 'true';
        $message = 'Update notifikasi berhasil';
    }

    $data['status'] = $status === "true";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}


$conn->close();
exit;
