<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$organisasi = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $organisasi = htmlspecialchars($row['organisasi']);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}
?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Pengalaman Organisasi') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-12 mt-2">
            <div class="mb-3" id="cekdiplomas">
                <label for="exampleInputEmail1" class="form-label"><?= translate('Apakah Anda pernah mengikuti suatu organisasi') ?>? <a style="color: red;">(*)</a></label>
                <br>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="organisasi" id="organisasi-y" value="Ya" required>
                    <label class="form-check-label" for="organisasi-y">
                        <?= translate('Ya') ?>
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="organisasi" id="organisasi-t" value="Tidak">
                    <label class="form-check-label" for="organisasi-t">
                        <?= translate('Tidak') ?>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div id="konten-input-organisasi" style="display: none;">
        <div class="row mt-2">
            <div class="col-md-4 mt-2">
                <label for="temp_nama" class="form-label"><?= translate('Nama Organisasi') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control" minlength="3" id="temp_nama" name="temp_nama" placeholder="<?= translate('Silakan Tuliskan Nama Organisasi') ?>">
            </div>
            <div class="col-md-4 mt-2">
                <label for="temp_jabatan" class="form-label"><?= translate('Jabatan Organisasi') ?> <a style="color: red;">(*)</a></label>
                <input type="text" class="form-control" minlength="3" id="temp_jabatan" name="temp_jabatan" placeholder="<?= translate('Silakan Tuliskan Jabatan Anda Diorganisasi') ?>">
            </div>
            <div class="col-md-4 mt-2">
                <label for="temp_tahun" class="form-label"><?= translate('Tahun') ?> <a style="color: red;">(*)</a></label>
                <select class="form-select" id="temp_tahun" name="temp_tahun" style="width: 100%;">
                    <option value="" disabled selected><?= translate('Silakan pilih tahun') ?></option>
                    <?php
                    $tahunSekarang = date("Y"); // Ambil tahun saat ini
                    $tahunMulai = 1900; // Tahun paling awal
                    for ($i = $tahunSekarang; $i >= $tahunMulai; $i--) {
                        echo "<option value='$i'>$i</option>";
                    }
                    ?>
                </select>
            </div>
            <div class="col-md-4 mt-2">
                <label for="temp_tempat" class="form-label"><?= translate('Tempat Organisasi') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_tempat" name="temp_tempat" style="width: 100%">
                </select>
            </div>
            <div class="col-md-2 mt-2">
                <label class="form-label" style="visibility: hidden">#</label><br>
                <button type="button" class="btn btn-primary btn-sm" onclick="tambahOrganisasi()"><i class="fas fa-plus-square"></i> <?= translate('Simpan') ?></button>
            </div>
        </div>

        <div class="row mt-3">
            <span><?= translate('Detail Riwayat Organisasi') ?></span>
            <?php
            // get riwayat organisasi
            $sql = $conn->prepare("SELECT * FROM riwayat_organisasi WHERE id = ?");
            $sql->bind_param("s", $temp_pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                echo '<div class="col-md-12" id="konten-info-detail-organisasi" style="display: none;>
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Pengalaman Organisasi belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-organisasi">';
                $no = 0;
                while ($row = mysqli_fetch_array($result)) {
                    echo '<div class="col-md-4" id="detailOrganisasi-' . $no . '">
                        <div class="card">
                        <div class="card-body">
                        <span class="card-title" style="font-size: 14px;">' . htmlspecialchars($row['nama']) . '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailOrganisasi(' . $no . ')"></i></span>
                        <p class="card-text mt-2" style="font-size: 12px;">
                        ' . translate('Jabatan') . ' : ' . htmlspecialchars($row['jabatan']) . '<br>
                        ' . translate('Tempat') . ' : ' . htmlspecialchars($row['tempat']) . '<br>
                        ' . translate('Tahun') . ' : ' . htmlspecialchars($row['tahun']) . '
                        </p>
                        <input type="hidden" name="nama[]" value="' . htmlspecialchars($row['nama']) . '">
                        <input type="hidden" name="jabatan[]" value="' . htmlspecialchars($row['jabatan']) . '">
                        <input type="hidden" name="tempat[]" value="' . htmlspecialchars($row['tempat']) . '">
                        <input type="hidden" name="tahun[]" value="' . htmlspecialchars($row['tahun']) . '">
                        </div>
                        </div>
                        </div>';
                }
                echo '</div>';
            } else {
                echo '<div class="col-md-12" id="konten-info-detail-organisasi">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Pengalaman Organisasi belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-organisasi">
                    </div>';
            }
            ?>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-minat-konsep.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Simpan') ?></button>
        </div>
    </div>
</form>
<script>
    $(document).ready(function() {
        $("#temp_tahun").select2();
        $('#temp_tempat').select2({
            placeholder: "<?= translate('Silakan masukan nama kota') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getLokasi',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        // Fungsi onchange organisasi
        $('input[name="organisasi"]').on('change', function() {
            var value = $(this).val(); // Ambil value dari radio yang dipilih

            if (value == 'Ya') {
                $("#konten-input-organisasi").show();
            } else {
                $("#konten-input-organisasi").hide();
            }
        });

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data riwayat organisasi
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var organisasi = $('input[name="organisasi"]:checked').val();
            temp_nama = [];
            temp_jabatan = [];
            temp_tahun = [];
            temp_tempat = [];

            if (organisasi == 'Ya' && $("input[name='nama[]']").length == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan riwayat organisasi terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600
                });
            } else {
                if ($("input[name='nama[]']").length > 0) {
                    temp_nama = $("input[name='nama[]']").map(function() {
                        return this.value;
                    }).get();
                    temp_jabatan = $("input[name='jabatan[]']").map(function() {
                        return this.value;
                    }).get();
                    temp_tahun = $("input[name='tahun[]']").map(function() {
                        return this.value;
                    }).get();
                    temp_tempat = $("input[name='tempat[]']").map(function() {
                        return this.value;
                    }).get();
                }

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanRiwayatOrganisasi",
                            data: {
                                organisasi: organisasi,
                                nama: temp_nama,
                                jabatan: temp_jabatan,
                                tahun: temp_tahun,
                                tempat: temp_tempat,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selesai') ?>",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        window.location.href = '../beranda/index.php';
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Simpan') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }
        });
        // End fungsi simpan data riwayat organisasi

        // Auto input
        var organisasi = '<?= $organisasi ?>';
        $("input[name='organisasi'][value='" + organisasi + "']").prop("checked", true);

        if (organisasi == "Ya") {
            $("#konten-input-organisasi").show();
        }
        // End Auto input
    });

    function tambahOrganisasi() {
        var temp_nama = htmlspecialchars($("#temp_nama").val());
        temp_jabatan = htmlspecialchars($("#temp_jabatan").val());
        temp_tahun = htmlspecialchars($("#temp_tahun").val());
        temp_tempat = htmlspecialchars($("#temp_tempat").val());

        if (temp_nama == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan nama organisasi terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_jabatan == "") {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan jabatan organisasi terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tahun == "" || temp_tahun === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih tahun organisasi terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_tempat == "" || temp_tempat === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan tempat organisasi terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else {
            $("#konten-info-detail-organisasi").hide();

            var id_input = Math.floor(Math.random() * 900) + 100;
            var content = $("#konten-detail-organisasi").html();

            newContent = content + '<div class="col-md-4" id="detailOrganisasi-' + id_input + '">' +
                '<div class="card">' +
                '<div class="card-body">' +
                '<span class="card-title" style="font-size: 14px;">' + temp_nama + '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailOrganisasi(' + id_input + ')"></i></span>' +
                '<p class="card-text mt-2" style="font-size: 12px;">' +
                '<?= translate('Jabatan') ?> : ' + temp_jabatan + '<br>' +
                '<?= translate('Tempat') ?> : ' + temp_tempat + '<br>' +
                '<?= translate('Tahun') ?> : ' + temp_tahun + '' +
                '</p>' +
                '<input type="hidden" name="nama[]" value="' + temp_nama + '">' +
                '<input type="hidden" name="jabatan[]" value="' + temp_jabatan + '">' +
                '<input type="hidden" name="tempat[]" value="' + temp_tempat + '">' +
                '<input type="hidden" name="tahun[]" value="' + temp_tahun + '">' +
                '</div>' +
                '</div>' +
                '</div>';

            $("#konten-detail-organisasi").html(newContent);

            $("#temp_nama").val('');
            $("#temp_jabatan").val('');
            $("#temp_tahun").val('').trigger('change');
            $("#temp_tempat").val(null).trigger('change');
        }
    }

    function hapusDetailOrganisasi(id) {
        $("#detailOrganisasi-" + id).remove();

        // cek apakah html kosong atau tidak
        if ($("#konten-detail-organisasi").text().trim().length === 0) {
            $("#konten-info-detail-organisasi").show();
        }
    }
</script>