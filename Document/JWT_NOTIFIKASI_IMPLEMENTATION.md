# JWT Notifikasi Implementation Documentation

## Overview

This document describes the implementation of JWT authentication for the notification endpoints in the recruitment system. The migration replaces parameter-based authentication with secure JWT token-based user validation.

## File Modified

- `api/mobile/company/notifikasi.php`

## JWT Implementation Details

### Authentication Changes

1. **JWT Helper Integration**

   - Added `include '../../jwt_helper.php'`
   - Added `requireAuth()` call to validate JWT tokens
   - Removed session-based authentication

2. **User Data Extraction**
   - Replaced `$_POST['id_koordinator']` with `$userData->id_koordinator`
   - Replaced `$_POST['divisi']` with `$userData->divisi`
   - Replaced `$_POST['id']` with JWT-derived values
   - Added `$company = $userData->company` for compatibility

### Endpoint Functions

#### 1. cekNotif

- **Purpose**: Get paginated list of notifications for the user
- **JWT Data Used**:
  - `id_koordinator`: Filter notifications by company/coordinator
  - `id_pegawai`: Filter notifications by employee ID
- **Changes**:
  - Added activity logging
  - Uses JWT user data for notification filtering
  - Maintains pagination functionality

#### 2. countNewNotif

- **Purpose**: Get count of unread notifications
- **JWT Data Used**:
  - `id_koordinator`: Filter notifications by company/coordinator
  - `id_pegawai`: Filter notifications by employee ID
- **Changes**:
  - Added activity logging
  - Uses JWT data for unread notification counting

#### 3. updateNotif

- **Purpose**: Mark specific notification as read
- **JWT Data Used**:
  - `id_koordinator`: User identification for logging
- **Changes**:
  - Added activity logging
  - Maintains existing POST parameter handling for notification details

#### 4. updateAllNotif

- **Purpose**: Mark all notifications as read for the user
- **JWT Data Used**:
  - `id_koordinator`: Filter and update notifications by company/coordinator
- **Changes**:
  - Added activity logging
  - Uses JWT data for bulk notification updates

### Security Improvements

#### Before JWT

```php
// Insecure parameter-based authentication
if (isset($_POST['id_koordinator'])) {
    $id_koordinator = $_POST['id_koordinator'];
}
```

#### After JWT

```php
// Secure JWT-based authentication
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
```

### Activity Logging

All notification operations now log user activities:

- Accessing notification list
- Checking unread notification count
- Marking notifications as read
- Bulk marking notifications as read

### Database Queries Updated

All SQL queries now use JWT-derived user data:

```sql
-- Example: Notification filtering by user
SELECT * FROM notif_summary WHERE (penerima = ? OR penerima = ?)

-- Example: Unread notification count
SELECT COUNT(*) as total FROM notif_summary
WHERE (penerima = ? OR penerima = ?) AND status <> 'Dibaca'

-- Example: Bulk notification update
UPDATE notif_summary SET status = 'Dibaca'
WHERE penerima = ? AND status = 'Dikirim'
```

## Client-Side Usage

### Authentication Required

All endpoints now require a valid JWT token in the Authorization header:

```javascript
const headers = {
  Authorization: `Bearer ${jwtToken}`,
  "Content-Type": "application/json",
};
```

### Example API Calls

#### Get Notifications

```javascript
fetch("/api/mobile/company/notifikasi.php?func=cekNotif&page=1&page_size=10", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
  },
});
```

#### Count Unread Notifications

```javascript
fetch("/api/mobile/company/notifikasi.php?func=countNewNotif", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
  },
});
```

#### Mark Notification as Read

```javascript
fetch("/api/mobile/company/notifikasi.php?func=updateNotif", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  body: new URLSearchParams({
    penerima: "recipient_id",
    pengirim: "sender_id",
    tgl: "2024-01-01 12:00:00",
    // Note: id_koordinator, divisi, id are now extracted from JWT
  }),
});
```

#### Mark All Notifications as Read

```javascript
fetch("/api/mobile/company/notifikasi.php?func=updateAllNotif", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  // Note: id_koordinator is now extracted from JWT
});
```

## Response Formats

### cekNotif Response

```json
{
  "status": true,
  "message": "Success message",
  "notification": 5,
  "titleNotif": "Anda memiliki 5 notifikasi baru",
  "data": [
    {
      "isi": "Notification content",
      "waktu": "2 jam yang lalu",
      "link": "http://example.com/link",
      "penerima": "recipient_id",
      "pengirim": "sender_id",
      "tgl_kirim": "2024-01-01 12:00:00",
      "status": "Dikirim",
      "id_req": "decoded_reference_id"
    }
  ],
  "page": 1,
  "page_size": 10,
  "total_page": 1,
  "total_data": 5
}
```

### countNewNotif Response

```json
{
  "status": true,
  "message": "Berhasil mengambil jumlah notifikasi",
  "data": [],
  "total_notif": 3
}
```

### updateNotif/updateAllNotif Response

```json
{
  "status": true,
  "message": "Update notifikasi berhasil",
  "data": []
}
```

## Error Handling

### JWT Validation Errors

- **401 Unauthorized**: Invalid or expired JWT token
- **403 Forbidden**: Valid JWT but insufficient permissions

### Business Logic Errors

- **400 Bad Request**: Invalid input data
- **500 Internal Server Error**: Database or processing errors

## Migration Impact

### Backward Compatibility

- **Breaking Change**: All endpoints now require JWT authentication
- **Parameters Removed**: id_koordinator, divisi, id from POST data
- **Client Updates Required**: Must include Authorization header

### Data Integrity

- All user identification now comes from validated JWT tokens
- Improved security through token-based authentication
- Enhanced activity logging for audit trails

### Notification Features Preserved

- Pagination support maintained
- Notification status updates preserved
- FCM integration compatibility maintained
- Link generation for specific notification types

## Security Benefits

1. **Stateless Authentication**

   - No server-side session storage required
   - Tokens are self-contained and verifiable

2. **User Data Security**

   - User identification extracted from signed JWT tokens
   - Prevents tampering with user identification parameters

3. **Activity Tracking**

   - Comprehensive logging of notification access patterns
   - Audit trail for security monitoring

4. **Access Control**
   - JWT validation ensures only authenticated users can access notifications
   - User-specific notification filtering based on JWT data

## Testing

Use the test script `test_jwt_notifikasi.php` to verify all endpoint functionality with JWT authentication.

## Notes

- All endpoints maintain the same response format
- Notification filtering uses both `id_koordinator` and `id_pegawai` for comprehensive coverage
- FCM helper integration preserved for push notification functionality
- Link generation for job application notifications maintained
- Activity logging provides comprehensive audit trails
