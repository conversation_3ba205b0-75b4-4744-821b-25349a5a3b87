# API Mobile Candidate - Get CV Documentation

## Overview

File `get_cv.php` telah dimodifikasi untuk menggunakan JWT Authentication menggantikan sistem PIN yang lama.

## Perubahan Utama

### 1. Authentication Method

- **Sebelum**: Menggunakan parameter `pin` yang dikirim via POST
- **Sesudah**: Menggunakan JWT Access Token yang dikirim via Authorization Header

### 2. Request Headers

```
Authorization: Bearer <jwt_access_token>
```

### 3. Data User

Data user (pin, nama, email) sekarang diambil langsung dari JWT token yang terverifikasi, bukan dari parameter POST.

## Endpoints Yang Tersedia

### GET /api/mobile/candidate/get_cv.php?func=getRH

Mendapatkan data riwayat hidup lengkap kandidat

**Headers:**

```
Authorization: Bearer <jwt_access_token>
Content-Type: application/json
```

**Response:**

```json
{
    "success": true,
    "message": "fetch data berhasil",
    "data": [
        {
            "id": "kandidat_pin",
            "nama_lengkap": "<PERSON> Doe",
            "alamat_lengkap": "Jl. Contoh RT 01 RW 02 Kecamatan Kota Provinsi. 12345",
            "lingkup_pekerjaan_nama": "IT, Software Development",
            ...
        }
    ]
}
```

### GET /api/mobile/candidate/get_cv.php?func=getRiwayatPendidikan

Mendapatkan riwayat pendidikan kandidat

### GET /api/mobile/candidate/get_cv.php?func=getRiwayatKursus

Mendapatkan riwayat kursus kandidat

### GET /api/mobile/candidate/get_cv.php?func=getRiwayatPekerjaan

Mendapatkan riwayat pekerjaan kandidat

### GET /api/mobile/candidate/get_cv.php?func=getRiwayatOrganisasi

Mendapatkan riwayat organisasi kandidat

### GET /api/mobile/candidate/get_cv.php?func=getPenguasaanBahasa

Mendapatkan data penguasaan bahasa kandidat

## Error Responses

### 401 Unauthorized - Token Tidak Ada

```json
{
  "success": false,
  "message": "Access token diperlukan"
}
```

### 401 Unauthorized - Token Tidak Valid

```json
{
  "success": false,
  "message": "Token tidak valid: <error_detail>"
}
```

### 401 Unauthorized - Token Expired

```json
{
  "success": false,
  "message": "Token sudah expired"
}
```

## Contoh Penggunaan (JavaScript)

```javascript
// Contoh fetch dengan JWT
const accessToken = "your_jwt_access_token_here";

fetch("/api/mobile/candidate/get_cv.php?func=getRH", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${accessToken}`,
    "Content-Type": "application/json",
  },
})
  .then((response) => response.json())
  .then((data) => {
    if (data.success) {
      console.log("Data CV:", data.data);
    } else {
      console.error("Error:", data.message);
    }
  })
  .catch((error) => {
    console.error("Network Error:", error);
  });
```

## Contoh Penggunaan (cURL)

```bash
# Get riwayat hidup
curl -X GET "http://localhost/api/mobile/candidate/get_cv.php?func=getRH" \
     -H "Authorization: Bearer your_jwt_access_token_here" \
     -H "Content-Type: application/json"

# Get riwayat pendidikan
curl -X GET "http://localhost/api/mobile/candidate/get_cv.php?func=getRiwayatPendidikan" \
     -H "Authorization: Bearer your_jwt_access_token_here" \
     -H "Content-Type: application/json"
```

## Migration Notes

### Untuk Client Applications:

1. Hapus pengiriman parameter `pin`, `nama`, dan `email` via POST
2. Pastikan JWT access token tersedia dan valid
3. Kirim token via Authorization header dengan format "Bearer <token>"
4. Handle error responses untuk token yang tidak valid atau expired

### Security Benefits:

1. **Stateless Authentication**: Tidak bergantung pada session server
2. **Token Expiration**: Token memiliki waktu kedaluwarsa (24 jam)
3. **Cryptographic Security**: Token ditandatangani secara kriptografi
4. **No Sensitive Data Exposure**: Pin/password tidak dikirim dalam request
