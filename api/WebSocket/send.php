<?php

require __DIR__ . '/../../vendor/autoload.php';

use WebSocket\Client;

// ==== Konfigurasi ====
$API_KEY_VALID = '57pOr213C&^XM701%(*U4'; // Ganti dengan API key rahasia
$websocket_host = getHostByName(getHostName());
$websocket_port = 3000;
$websocket_url = "ws://127.0.0.1:{$websocket_port}";

// ==== Validasi Header API Key ====
$headers = getallheaders();
if (!isset($headers['X-Api-Key']) || $headers['X-Api-Key'] !== $API_KEY_VALID) {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized']);
    exit;
}

// ==== Validasi Metode dan Parameter ====
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Only POST allowed']);
    exit;
}

$to = $_POST['to'] ?? '';
$tipe = $_POST['tipe'] ?? '';
$reference_id = $_POST['reference_id'] ?? '';
$message = $_POST['message'] ?? '';

if (empty($to) || empty($tipe) || empty($message)) {
    echo json_encode(['status' => 'error', 'message' => 'Missing to or message']);
    exit;
}

if ($tipe == 'chat' && empty($reference_id)) {
    echo json_encode(['status' => 'error', 'message' => 'Missing reference_id']);
    exit;
}

// ==== Kirim ke WebSocket Server ====
$data = [
    'to' => $to,
    'tipe' => $tipe,
    'reference_id' => $reference_id,
    'message' => $message,
    'timestamp' => time()
];

try {
    $ws = new Client($websocket_url); // Tetap gunakan WebSocket\Client
    $ws->send(json_encode($data));
    $ws->close();
} catch (Exception $e) {
    echo $e->getMessage();
}
