<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../../model/database.php";
include '../../../api/s3.php';

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        if ($diff->y > 1) {
            return $diff->y . translate(' tahun yang lalu');
        } else {
            return $diff->y . " " . translate('tahun yang lalu');
        }
    } elseif ($diff->m > 0) {
        if ($diff->m > 1) {
            return $diff->m . translate(' bulan yang lalu');
        } else {
            return $diff->m . " " . translate('bulan yang lalu');
        }
    } elseif ($diff->d > 0) {
        if ($diff->d > 1) {
            return $diff->d . translate(' hari yang lalu');
        } else {
            return $diff->d . " " . translate('hari yang lalu');
        }
    } elseif ($diff->h > 0) {
        if ($diff->h > 1) {
            return $diff->h . translate(' jam yang lalu');
        } else {
            return $diff->h . " " . translate('jam yang lalu');
        }
    } elseif ($diff->i > 0) {
        if ($diff->i > 1) {
            return $diff->i . translate(' menit yang lalu');
        } else {
            return $diff->i . " " . translate('menit yang lalu');
        }
    } else {
        return translate('Baru saja');
    }
}

if (isset($_GET['q'])) {
    $pin = "-";
    $nama_user = "-";
    $email_user = "-";
    if (isset($_SESSION['users'])) {

        // get data user
        if (isset($_SESSION['users']['pin'])) {
            $pin = addslashes($_SESSION['users']['pin']);
        }

        if (isset($_SESSION['users']['nama'])) {
            $nama_user = $_SESSION['users']['nama'];
        }

        if (isset($_SESSION['users']['email'])) {
            $email_user = $_SESSION['users']['email'];
        }
        $tempAkses = "true";
    } else {
        $tempAkses = "all";
    }
    $q = base64_decode($_GET['q']);
    $arr = explode("|", $q);

    $id_req = "-";
    if (isset($arr[0])) {
        $id_req = htmlspecialchars($arr[0]);
    }

    $lokasi = "-";
    $where_lokasi = "%-%";
    if (isset($arr[1])) {
        $lokasi = $arr[1];
        $where_lokasi = "%" . htmlspecialchars($arr[1]) . "%";
    }

    // cek data lowongan
    $get = $conn->prepare("SELECT
                            lr.*,
                            k.img,
                            k.tipe
                        FROM
                            list_request lr
                            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                        WHERE
                            lr.id_req = ? 
                            AND lr.lokasi_kerja LIKE ?");
    $get->bind_param("ss", $id_req, $where_lokasi);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    $nama_perusahaan = "-";
    $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
    if ($result->num_rows > 0) {
        $rowData = mysqli_fetch_array($result);
        $nama_perusahaan = htmlspecialchars($rowData['perusahaan']);
        $departemen      = htmlspecialchars($rowData['tipe']);
        $posisi          = htmlspecialchars($rowData['posisi']);
        $pendidikan      = $rowData['k_pendidikan'];
        $jurusan = str_replace("Semua Jurusan", translate("Semua Jurusan"), $rowData['k_jurusan']);
        $jurusan = str_replace("|", ", ", htmlspecialchars($jurusan));
        $usia            = str_replace("-", " - ", $rowData['k_usia']);
        $pengalaman      = $rowData['k_pengalaman'];
        $pengalaman_sama = $rowData['k_pengalaman_sama'];
        $jenis_kelamin   = $rowData['k_jk'];
        $status          = $rowData['k_status'];
        $lokasi_pilihan  = ucwords($lokasi);
        $lokasi_kerja    = str_replace(",", ", ", $rowData['lokasi_kerja']);
        $lokasi          = explode(", ", ucwords(strtolower(str_replace(",", ", ", $rowData['lokasi_kerja']))));
        $sim             = str_replace(",", ", ", $rowData['k_sim']);
        $sistem_kerja    = $rowData['sistem_kerja'];
        if ($rowData['note'] == '') {
            $note       = '-';
        } else {
            $note       = $rowData['note'];
        }

        $tglPost           = date("Y-m-d", strtotime($rowData['create_at']));
        $check_lowongan    = explode("|", $rowData['check_lowongan']);
        $posting           = waktuLalu($rowData['create_at']);
        $pertanyaan_khusus = array();
        if ($rowData['k_khusus'] != '') {
            $pertanyaan_khusus = explode("|", $rowData['k_khusus']);
        }

        $shareLink = $baseURL . 'candidate/dashboard/beranda/detailJob?q=' . $_GET['q'];
        $shareTwitter = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($rowData['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;
        $shareWA = translate('Lowongan pekerjaan posisi') . ' ' . htmlspecialchars($rowData['posisi']) . '. ' . translate('Kunjungi') . ' ' . $shareLink;

        $id_salin_link = rand(100, 999);
        $btnShare = '<div class="dropdown mt-2">
                        <i class="bi bi-share" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 20px; float: right; cursor: pointer; padding-left: 10px;"></i>
                        <div class="dropdown-menu dropdown-for-share">
                            <table style="width: 100%">
                                <tr>
                                    <td style="background-color: white;padding: 5px;">
                                        <a href="https://x.com/intent/tweet?text=' . urlencode($shareTwitter) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/twitter-x.svg" alt="twitter-x" width="16" height="16"></a>
                                    </td>
                                    <td style="background-color: white;padding: 5px;">
                                        <a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($shareLink) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/facebook.svg" alt="facebook" width="16" height="16"></a>
                                    </td>
                                    <td style="background-color: white;padding: 5px;">
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $shareLink . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/linkedin.svg" alt="linkedin" width="16" height="16"></a>
                                    </td>
                                    <td style="background-color: white;padding: 5px;">
                                        <a href="https://wa.me/?text=' . urlencode($shareWA) . '" target="_blank"><img src="../../../assets/images/bootstrap-icons-1.13.1/whatsapp.svg" alt="whatsapp" width="16" height="16"></a>
                                    </td>
                                    <td style="background-color: white;padding: 5px;">
                                        <a href="javascript:void(0)" id="btn-salin-' . $id_salin_link . '" onclick="salinLink(event, \'' . $shareLink . '\',\'' . $id_salin_link . '\')" tabindex="0" data-toggle="tooltip" data-bs-original-title="' . translate('Salin Link') . '"><i class="fas fa-link" style="font-size: 16px; color: black; vertical-align: middle;"></i></a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>';

        // get logo perusahaan
        if ($rowData['img'] != "") {
            if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $rowData['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $rowData['img']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();

                $logo_perusahaan = '<img src="' . $logoURL . '" class="profile-pic-2" id="logo-detail-job" width="80" alt="logo-perusahaan">';
            } else {
                $logo_perusahaan = '<img src="../../../assets/images/logo/logoDcv2.png" class="profile-pic-2" width="80" alt="logo-perusahaan">';
            }
        }

        if ($status == 'Tidak') {
            $ket_status = 'Tidak Perlu / Tidak Wajib';
        } else {
            $ket_status = $status;
        }

        $dataLokasi = '';
        for ($i = 0; $i < count($lokasi); $i++) {
            $arrLokasi = $lokasi[$i];
            $dataLokasi .= '<li>' . $arrLokasi . '</li>';
        }

        //data kriteria khusus
        $sqlKhusus = "SELECT * FROM list_kriteria WHERE id_req = ? GROUP BY id_req";
        $get = $conn->prepare($sqlKhusus);
        $get->bind_param("s", $id_req);
        $get->execute();
        $queryKhusus = $get->get_result();
        $get->close();

        $rowKhusus = mysqli_fetch_assoc($queryKhusus);
        $kk = explode(",", $rowKhusus['kk']);
        $kb = str_replace(",", ", ", $rowKhusus['kb']);
        $km = $rowKhusus['km'];
        $kbdu = $rowKhusus['kbdu'];
        $rlp = explode(",", $rowKhusus['rlp']);

        $htmlSK = "";
        $htmlPendidikan = "";
        $htmlJurusan = "";
        $htmlUsia = "";
        $htmlSim = "";
        $htmlJk = "";
        $htmlTotalPengalaman = "";
        $htmlStatus = "";
        $htmlkk = "";
        $htmlKm = "";
        $htmlkb = "";
        $htmlKbdu = "";
        $htmlRlp = "";

        if (in_array("sistem_kerja", $check_lowongan)) {
            $htmlSK = '<li>' . translate('Kebutuhan traveling') . ' : ' . htmlspecialchars($sistem_kerja) . '</li>';
        }

        if (in_array("k_pendidikan", $check_lowongan)) {
            $htmlPendidikan = '<li>' . translate('Minimal Pendidikan') . ' : ' . htmlspecialchars($pendidikan) . '</li>';
        }

        if (in_array("k_jurusan", $check_lowongan)) {
            $htmlJurusan = '<li>' . translate('Jurusan') . ' : ' . htmlspecialchars($jurusan) . '</li>';
        }

        if (in_array("k_usia", $check_lowongan)) {
            $htmlUsia = '<li>' . translate('Rentang Usia') . ' : ' . htmlspecialchars($usia) . ' ' . translate('Tahun') . '</li>';
        }

        if (in_array("k_sim", $check_lowongan)) {
            if ($sim != 'Tidak Ada') {
                $htmlSim = '<li>' . translate('SIM') . ' : ' . htmlspecialchars($sim) . '</li>';
            }
        }

        if (in_array("k_jk", $check_lowongan)) {
            $htmlJk = '<li>' . translate('Jenis Kelamin') . ' : ' . htmlspecialchars($jenis_kelamin) . '</li>';
        }

        if (in_array("k_pengalaman", $check_lowongan)) {
            if ($pengalaman_sama == 0) {
                if ($pengalaman == 0) {
                    $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Fresh Graduate</li>';
                } else {
                    $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Minimal ' . htmlspecialchars($pengalaman) . ' ' . translate('Tahun') . '</li>';
                }
            } else {
                $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Minimal ' . htmlspecialchars($pengalaman_sama) . ' ' . translate('Tahun sebagai') . ' ' . $posisi . '</li>';
            }
        }

        if (in_array("k_pengalaman_sama", $check_lowongan)) {
            if ($pengalaman_sama == 0) {
                if ($pengalaman == 0) {
                    $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Fresh Graduate</li>';
                } else {
                    $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Minimal ' . htmlspecialchars($pengalaman) . ' ' . translate('Tahun') . '</li>';
                }
            } else {
                $htmlTotalPengalaman = '<li>' . translate('Pengalaman Kerja') . ' : Minimal ' . htmlspecialchars($pengalaman_sama) . ' ' . translate('Tahun sebagai') . ' ' . $posisi . '</li>';
            }
        }

        if (in_array("k_status", $check_lowongan)) {
            $htmlStatus = '<li>' . translate('Status diutamakan') . ' : ' . htmlspecialchars($ket_status) . '</li>';
        }

        if (in_array("kk", $check_lowongan)) {
            $dataKk = '<li>' . translate('Penguasaan Komputer') . ' : 
                    <ol>';
            for ($i = 0; $i < count($kk); $i++) {
                $arrkk = $kk[$i];

                if ($arrkk == '1') {
                    $dataKk .= '<li>' . translate('Mengetahui dasar dan pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)') . '</li>';
                } else if ($arrkk == '2') {
                    $dataKk .= '<li>' . translate('Mengerti dan Menguasai pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)') . '</li>';
                } else if ($arrkk == '3') {
                    $dataKk .= '<li>' . translate('Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya') . '</li>';
                } else if ($arrkk == '4') {
                    $dataKk .= '<li>' . translate('Menguasai Pemograman dan Analist Pemograman') . '</li>';
                } else if ($arrkk == '5') {
                    $dataKk .= '<li>' . translate('Spesialis Komputer Sains') . '</li>';
                } else if ($arrkk == '6') {
                    $dataKk .= '<li>' . translate('Tidak diperlukan') . '</li>';
                }
            }
            $dataKk .= '</ol>
                </li>';

            $htmlkk = $dataKk;
        }

        if (in_array("kb", $check_lowongan)) {
            $htmlkb = '<li>' . translate('Bahasa yang dikuasai') . ' : ' . htmlspecialchars($kb) . '</li>';
        }

        if (in_array("km", $check_lowongan)) {
            if ($km == '1') {
                $ket_km = translate('Tidak diperlukan');
            } else if ($km == '2') {
                $ket_km = translate('1 - 3 orang');
            } else if ($km == '3') {
                $ket_km = translate('4 - 10 orang');
            } else if ($km == '4') {
                $ket_km = translate('11 - 50 orang');
            } else if ($km == '5') {
                $ket_km = translate('Lebih dari 50 orang');
            }

            $htmlKm = '<li>' . translate('Kemampuan memimpin') . ' : ' . htmlspecialchars($ket_km) . '</li>';
        }

        if (in_array("kbdu", $check_lowongan)) {
            if ($kbdu == '1') {
                $ket_kbdu = translate('Tidak diperlukan');
            } else if ($kbdu == '2') {
                $ket_kbdu = translate('Diperlukan');
            } else if ($kbdu == '3') {
                $ket_kbdu = translate('Sangat diperlukan (Ahli)');
            }

            $htmlKbdu = '<li>' . translate('Menguasai kemampuan berbicara didepan umum') . ' : ' . htmlspecialchars($ket_kbdu) . '</li>';
        }

        if (in_array("rlp", $check_lowongan)) {
            $dataRlp = '<ol>';
            for ($i = 0; $i < count($rlp); $i++) {
                $rlpArr = $rlp[$i];

                $sqlRlp = "SELECT name FROM ruang_lingkup_pekerjaan WHERE id = '$rlpArr' GROUP BY id";
                $queryRlp = $conn->query($sqlRlp);
                if (mysqli_num_rows($queryRlp) > 0) {
                    $rowRlp = mysqli_fetch_assoc($queryRlp);
                    $teks = $rowRlp['name'];
                    $dataRlp .= '<li>' . translate(htmlspecialchars($teks)) . '.</li>';
                }
            }
            $dataRlp .= '</ol>';
            $htmlRlp = '<li>' . translate('Ruang Lingkup Pekerjaan') . ' : ' . $dataRlp . '</li>';
        }

        // simpan log aktivitas
        $messages = 'Menampilkan halaman detail lowongan dari id ' . $id_req . ' di sistem.';
        $extra_info = "Kandidat";
        $level = "INFO";
        $path = $_SERVER['REQUEST_URI'];
        logActivity($conn, $pin, $level, $messages, $extra_info);
    } else {
        header('Location: index'); // Redirect setelah login sukses
        exit();
    }
} else {
    header('Location: index'); // Redirect setelah login sukses
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title> <?= $posisi ?> - <?= $nama_perusahaan ?> - digitalcv</title>
    <meta
        content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
        name="viewport" />
    <?php include_once '../style.php'; ?>
    <style>
        .swal-text-center .swal-text {
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <?php include '../sidebar.php' ?>
        <div class="main-panel">
            <?php include '../header.php' ?>
            <div class="container">
                <div class="page-inner">
                    <div class="row mb-2">
                        <div class="col-md-12" style="font-size: 25px;text-align: center;">
                            <div class="card">
                                <div class="card-body" style="background-color: #FFE31D;">
                                    <i class="fas fa-info-circle" style="color: #C37070;"></i> <b><?= strtoupper(translate('Perhatian')) ?></b>
                                    <p><?= $nama_perusahaan ?> <?= translate('tidak memungut biaya apapun selama berproses pendaftaran dan seleksi karir berlangsung') ?>.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <table style="width: 100%;">
                                                <tr>
                                                    <td style="width: 30%;">
                                                        <?= $logo_perusahaan ?>
                                                    </td>
                                                    <td style="text-align: center; width: 40%;"><b style="font-size: 10px;" id="perusahaan-detail-job"><?= strtoupper($nama_perusahaan) ?></b></td>
                                                    <td style="text-align: right; width: 30%;">
                                                        <button type="button" class="btn btn-secondary btn-sm" style="padding: 3px 13px !important;" onclick="window.history.back()"><i class="fas fa-long-arrow-alt-left"></i> <?= translate('Kembali') ?></button>
                                                        <?= $btnShare ?>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-lg-8" style="border-right: 1px solid rgb(236, 236, 236)">
                                            <div class="deskripsi">
                                                <h4 class="fw-bold"><?= translate('Deskripsi Pekerjaan') ?></h4>
                                                <p id="deskripsi"></p>
                                            </div>
                                            <hr />
                                            <div class="kriteria-umum">
                                                <h4 class="fw-bold"><?= translate('Kriteria Umum') ?></h4>
                                                <ul>
                                                    <?php
                                                    echo $htmlSK;
                                                    echo $htmlPendidikan;
                                                    echo $htmlJurusan;
                                                    echo $htmlUsia;
                                                    echo $htmlSim;
                                                    echo $htmlJk;
                                                    echo $htmlTotalPengalaman;
                                                    echo $htmlStatus;

                                                    ?>
                                                </ul>
                                            </div>
                                            <hr />
                                            <?php
                                            if ($htmlkb != '' || $htmlkk != '' || $htmlKm != '' || $htmlKbdu != '' || $htmlRlp != '') {

                                                echo '<div class="kriteria-khusus">
                                                <h4 class="fw-bold">' . translate('Kriteria Khusus') . '</h4>
                                                <ul>';
                                                echo $htmlkb;
                                                echo $htmlkk;
                                                echo $htmlKm;
                                                echo $htmlKbdu;
                                                echo $htmlRlp;
                                                echo '</ul>
                                                </div>
                                                <hr />';
                                            }
                                            ?>
                                        </div>
                                        <div class="col-lg-4">
                                            <div class="row g-3">
                                                <div class="col-lg-12">
                                                    <div class="right-side">
                                                        <div>
                                                            <h3 class="fw-bold"><?= $posisi ?></h3>
                                                        </div>
                                                        <div class="py-2">
                                                            <p class="text__right-side">
                                                                <i class="bi bi-briefcase-fill"></i>
                                                                <?= $departemen ?>
                                                            </p>
                                                            <p class="text__right-side">
                                                                <i class="bi bi-geo-alt-fill"></i>
                                                                <?= $lokasi_pilihan ?>
                                                            </p>
                                                        </div>
                                                        <div class="d-grid py-1">
                                                            <?php
                                                            if (isset($_SESSION['users'])) {
                                                                echo '<button class="btn btn-primary px-5 py-2" data-bs-toggle="modal" data-bs-target="#modal-persetujuan-lamar">' . translate('Lamar') . '</button>';
                                                            } else {
                                                                echo '<a class="btn btn-primary px-5 py-2" href="../../login">' . translate('Login untuk melamar') . '</a>';
                                                            }
                                                            ?>

                                                        </div>
                                                    </div>
                                                </div>
                                                <hr class="my-3" />
                                                <div class="col-lg-12">
                                                    <h4 class="fw-bold"><?= translate('Diposting') ?></h4>
                                                    <p><i class="bi bi-clock-fill"></i> <?= $posting; ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../footer.php' ?>

    <!-- Modal Persetujuan Lamar -->
    <div class="modal fade" id="modal-persetujuan-lamar" aria-labelledby="modal-persetujuan-lamar" aria-hidden="true">
        <div class="modal-dialog" style="max-width: 1350px!important;">
            <div class="modal-content">
                <div class="modal-header" style="display: block !important;">
                    <h1 class="modal-title fs-5 text-center" id="exampleModalLabel">
                        <?= translate('Lanjutkan proses pendaftaran Anda untuk lowongan posisi') ?><br>
                        <strong><?= $posisi; ?></strong><br>
                        <?= translate('di') ?> <?= $nama_perusahaan ?>
                    </h1>
                </div>
                <form id="myForm" class="needs-validation">
                    <div class="modal-body" style="max-height: 470px; overflow-y: auto; overflow-x: hidden;">
                        <div class="row" style="border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);">
                            <div class="col-md-12">
                                <p class="text-center" style="margin-top: 10px;">
                                    <b><?= translate('Pendaftaran rekrutmen') ?> <?= $nama_perusahaan ?> <?= translate('menggunakan platform digitalcv') ?>.</b>
                                </p>
                            </div>
                        </div>
                        <?php
                        if (count($pertanyaan_khusus) > 0) {
                        ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <p class="mt-3"><?= translate('Pertanyaan Khusus Lowongan') ?> :</p>
                                    <ol>
                                        <?php
                                        for ($i = 0; $i < count($pertanyaan_khusus); $i++) {
                                            echo '<li>
                                                <div class="mb-3">
                                                    <label for="pk_' . $i . '" class="form-label">' . htmlspecialchars($pertanyaan_khusus[$i]) . ' <span style="color: red;">(*)</span></label>
                                                    <input type="text" class="form-control" id="pk_' . $i . '" name="pertanyaan_khusus[]" required>
                                                </div>
                                            </li>';
                                        }
                                        ?>
                                    </ol>
                                </div>
                            </div>
                        <?php
                        }
                        ?>

                        <div class="row">
                            <div class="col-md-12">
                                <p><?= translate('Pernyataan Kandidat') ?> :</p>
                                <div class="form-check" style="display: flex;">
                                    <input class="form-check-input" type="checkbox" value="Ya" id="flexCheckDefault" required>
                                    <label class="form-check-label" for="flexCheckDefault" style="white-space: normal !important;">
                                        <?= translate('Saya memahami sepenuhnya dan setuju') ?>:
                                        <ol>
                                            <li>
                                                <?= translate('Data yang akan diisi, foto dan atau dokumen lampiran lainnya yang diberikan adalah benar, akurat dan sesuai / asli sepenuhnya menjadi tanggung jawab saya') ?>.
                                            </li>
                                            <li>
                                                <?= translate('Pengisian data yang akan saya lakukan adalah untuk') ?>
                                                <?= $nama_perusahaan ?> <?= translate('sehingga') ?> <?= $nama_perusahaan ?>
                                                <?= translate('berhak mengakses dan menggunakannya untuk proses rekrutmen') ?>.
                                            </li>
                                        </ol>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" style="border-color: #00000038;" data-bs-dismiss="modal"><?= translate('Tutup') ?></button>
                        <button type="submit" class="btn btn-secondary" id="btn-lanjutkan"><?= translate('Lanjutkan') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            var deskripsi = <?= json_encode($note) ?>;
            $("#deskripsi").html(DOMPurify.sanitize(deskripsi));

            setTimeout(() => {
                // untuk posisi footer
                updateBodyHeight();
            }, 500); // 1000 ms = 1 detik

            // Fungsi lamar
            $("#myForm").submit(function(event) {
                event.preventDefault(); // Mencegah reload form
                event.stopPropagation(); // Menghentikan event bubbling

                if (this.checkValidity() === false) {
                    $(this).addClass("was-validated");
                    return;
                }

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-lanjutkan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-lanjutkan").prop("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=getProgresPengisian",
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    if (obj.value == 100) {
                                        // get value jika petanyaan khusus ada
                                        if ($('[name="pertanyaan_khusus[]"]').length > 0) {
                                            var pertanyaan_khusus = $("input[name='pertanyaan_khusus[]']").map(function() {
                                                return this.value;
                                            }).get();
                                        } else {
                                            var pertanyaan_khusus = [];
                                        }

                                        $.ajax({
                                            type: "post",
                                            url: "../../controller/controller.php?func=prosesLamar",
                                            data: {
                                                id_req: '<?php echo $id_req ?>',
                                                lokasi: '<?php echo $lokasi_pilihan ?>',
                                                pertanyaan_khusus: pertanyaan_khusus,
                                                recaptcha_response: token
                                            },
                                            success: function(result) {
                                                var obj = JSON.parse(JSON.stringify(result));

                                                if (obj.status == "success") {
                                                    swal({
                                                        icon: "success",
                                                        text: "<?= translate('Lamaran berhasil dilakukan') ?>.",
                                                        buttons: false,
                                                        buttons: {
                                                            confirm: {
                                                                text: "Ok",
                                                                className: "btn btn-success",
                                                            }
                                                        },
                                                    }).then((confirm) => {
                                                        window.location.href = 'index.php';
                                                    });
                                                } else if (obj.status == "exist") {
                                                    swal({
                                                        icon: "warning",
                                                        text: "<?= translate('Anda sudah melamar lowongan ini. Silakan cek pada riwayat anda.') ?>",
                                                        className: "swal-text-center", // Tambahkan class kustom
                                                        buttons: false,
                                                        timer: 2000
                                                    });
                                                    $("#btn-lanjutkan").html("<?= translate('Lanjutkan') ?>");
                                                    $("#btn-lanjutkan").prop("disabled", false);
                                                } else {
                                                    swal({
                                                        icon: "error",
                                                        text: obj.message,
                                                        buttons: false,
                                                        timer: 2000
                                                    });
                                                    $("#btn-lanjutkan").html("<?= translate('Lanjutkan') ?>");
                                                    $("#btn-lanjutkan").prop("disabled", false);
                                                }
                                            },
                                        });
                                    } else {
                                        swal({
                                            icon: "warning",
                                            text: "<?= translate('Data diri belum lengkap. Silakan lengkapi terlebih dahulu data diri anda.') ?>",
                                            className: "swal-text-center", // Tambahkan class kustom
                                            buttons: false,
                                            buttons: {
                                                confirm: {
                                                    text: "<?= translate('Lengkapi') ?>",
                                                    className: "btn btn-primary",
                                                }
                                            },
                                        }).then((confirm) => {
                                            window.location.href = '../data_diri/index.php';
                                        });
                                    }
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-lanjutkan").html("<?= translate('Lanjutkan') ?>");
                                    $("#btn-lanjutkan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            });
        });
    </script>
</body>

</html>