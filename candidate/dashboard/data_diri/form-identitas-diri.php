<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter

}
function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

// get data user
$sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$nama = "";
$email = "";
$no_telepon = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $nama = htmlspecialchars($row['nama_lengkap']);
    $email = htmlspecialchars($row['email']);
    $no_telepon = htmlspecialchars($row['no_telp']);
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$tempat_lahir = "";
$tgl_lahir = "";
$jenis_kelamin = "";
$status_pernikahan = "";
$ktp = "";
$sim = array();
$provinsi = "";
$kota = "";
$kecamatan = "";
$rt = "";
$rw = "";
$alamat = "";
$kode_pos = "";
$id_propinsi = "";
$nama_provinsi = "";
$id_kota = "";
$nama_kota = "";
$id_kec = "";
$nama_kecamatan = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $nama = htmlspecialchars($row['nama']);
    $tempat_lahir = htmlspecialchars($row['tempat_lahir']);
    $tgl_lahir = htmlspecialchars($row['tgl_lahir']);
    $jenis_kelamin = htmlspecialchars($row['jenis_kelamin']);
    $status_pernikahan = htmlspecialchars($row['status_pernikahan']);
    $ktp = htmlspecialchars($row['ktp']);
    $no_telepon = htmlspecialchars($row['no_telepon']);
    $email = htmlspecialchars($row['email']);
    $sim = explode(",", htmlspecialchars($row['sim']));
    $nama_provinsi = htmlspecialchars($row['provinsi']);
    $nama_kota = htmlspecialchars($row['kota']);
    $nama_kecamatan = htmlspecialchars($row['kecamatan']);
    $rt = htmlspecialchars($row['rt']);
    $rw = htmlspecialchars($row['rw']);
    $alamat = htmlspecialchars($row['alamat']);
    $kode_pos = htmlspecialchars($row['kode_pos']);

    // get kode provinsi, kota dan kecamatan
    $get = $conn->prepare("SELECT `id`, 'provinsi' as ket FROM `provinces` WHERE `name` = ?
                        UNION
                        SELECT `id`, 'kota' as ket FROM `regencies` WHERE `name` = ?
                        UNION
                        SELECT `id`, 'kecamatan' as ket FROM `districts` WHERE `name` = ?");
    $get->bind_param("sss", $nama_provinsi, $nama_kota, $nama_kecamatan);
    $get->execute();
    $res = $get->get_result();
    $get->close();

    $id_propinsi = "";
    $id_kota = "";
    $id_kec = "";

    while ($row = mysqli_fetch_array($res)) {
        if ($row['ket'] == 'provinsi') {
            $id_propinsi = htmlspecialchars($row['id']);
        } elseif ($row['ket'] == 'kota') {
            $id_kota = htmlspecialchars($row['id']);
        } elseif ($row['ket'] == 'kecamatan') {
            $id_kec = htmlspecialchars($row['id']);
        }
    }
}


$sim = json_encode($sim);

?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Identitas Diri') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-md-6">
            <label for="nama" class="form-label"><?= translate('Nama Lengkap') ?> <span style="color: red;">(*)</span></label>
            <input type="text" class="form-control" id="nama" name="nama" value="<?= $nama ?>" placeholder="<?= translate('Silahkan masukan nama lengkap anda') ?>" required>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mt-2">
            <label for="tempat_lahir" class="form-label"><?= translate('Tempat Lahir') ?> <span style="color: red;">(*)</span></label>
            <select class="form-check-input" id="tempat_lahir" name="tempat_lahir" style="width: 100%" required>
            </select>
        </div>
        <div class="col-md-4 mt-2">
            <label for="tgl_lahir" class="form-label"><?= translate('Tanggal Lahir') ?> <span style="color: red;">(*)</span></label>
            <input type="date" class="form-control" id="tgl_lahir" name="tgl_lahir" min="1900-01-01" max="<?= date('Y-m-d', strtotime('-16 years')) ?>" value="<?= $tgl_lahir ?>" required>
        </div>
        <div class="col-md-4 mt-2">
            <label class="form-label"><?= translate('Jenis Kelamin') ?> <a style="color: red;">(*)</a></label>
            <table style="width: 100%;">
                <tr>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="l" name="jk" value="Laki-Laki" required class="form-check-input" required>
                            <label class="form-check-label" for="l">
                                <?= translate('Laki-Laki') ?>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="p" name="jk" value="Perempuan" required class="form-check-input" required>
                            <label class="form-check-label" for="p">
                                <?= translate('Perempuan') ?>
                            </label>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row">
        <!-- <div class="col-md-4 mt-2" style="display: hidden;">
            <label for="ktp" class="form-label"><?= translate('No. KTP') ?> </label>
            <input type="text" class="form-control" id="ktp" name="ktp" value="<?= $ktp ?>" placeholder="<?= translate('Contoh') ?> : 317503421245xxx" maxlength="16" minlength="16" onkeypress="return hanyaAngka(event)">
        </div> -->
        <div class="col-md-4 mt-2">
            <label for="status_pernikahan" class="form-label"><?= translate('Status Pernikahaan') ?> <span style="color: red;">(*)</span></label>
            <select class="form-select" id="status_pernikahan" name="status_pernikahan" style="width: 100%" required>
                <option value="" selected><?= translate('Pilih status pernikahan') ?></option>
                <option value="Belum Menikah"><?= translate('Belum Menikah') ?></option>
                <option value="Menikah"><?= translate('Menikah') ?></option>
                <option value="Janda"><?= translate('Janda') ?></option>
                <option value="Duda"><?= translate('Duda') ?></option>
            </select>
        </div>
        <div class="col-md-4 mt-2">
            <label for="no_telepon" class="form-label"><?= translate('No. Handphone') ?> <span style="color: red;">(*)</span></label>
            <input type="text" class="form-control" id="no_telepon" name="no_telepon" value="<?= $no_telepon ?>" placeholder="<?= translate('Contoh') ?> : 081 2345 6789" maxlength="14" minlength="9" onkeypress="return hanyaAngka(event)" required>
        </div>
        <div class="col-md-4 mt-2">
            <label for="email" class="form-label"><?= translate('Alamat Email') ?> <span style="color: red;">(*)</span></label>
            <input type="email" class="form-control" id="email" name="email" value="<?= $email ?>" placeholder="<?= translate('Contoh') ?> : <EMAIL>" required readonly>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mt-2">
            <div class="mb-3 required">
                <label for="exampleInputEmail1" class="form-label"><?= translate('SIM apa saja yang Anda miliki') ?>? <a style="color: red;">(*)</a></label>
                <br>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" name="sim[]" id="simA" value="A">
                    <label class="form-check-label" for="simA">A</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" id="simB1" name="sim[]" value="B1">
                    <label class="form-check-label" for="simB1">B1</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" id="simB2" name="sim[]" value="B2">
                    <label class="form-check-label" for="simB2">B2</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" id="simC" name="sim[]" value="C">
                    <label class="form-check-label" for="simC">C</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" id="simD" name="sim[]" value="D">
                    <label class="form-check-label" for="simD">D</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simYes" type="checkbox" id="simSIO" name="sim[]" value="SIO">
                    <label class="form-check-label" for="simSIO">SIO (Forklift)</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input simNo" type="checkbox" id="simTidak" name="sim[]" value="Tidak Ada">
                    <label class="form-check-label" for="simTidak"><?= translate('Tidak Ada') ?></label>
                </div>
                <div id="checkboxError" class="text-danger"></div>
            </div>
        </div>
    </div>

    <h5 class="mt-4"><?= translate('Informasi Alamat Tinggal') ?></h5>
    <hr>
    <div class="row">
        <div class="col-md-4 mt-2">
            <label for="propinsi" class="form-label"><?= translate('Provinsi') ?> <span style="color: red;">(*)</span></label>
            <select class="form-check-input" id="propinsi" name="propinsi" style="width: 100%" onchange="getChainAlamat('provinsi')" required>
            </select>
        </div>
        <div class="col-md-4 mt-2">
            <label for="kota_tinggal" class="form-label"><?= translate('Kota/Kabupaten') ?> <span style="color: red;">(*)</span></label>
            <select class="form-check-input" id="kota_tinggal" name="kota_tinggal" style="width: 100%" onchange="getChainAlamat('kota')" required>
            </select>
        </div>
        <div class="col-md-4 mt-2">
            <label for="kec_tinggal" class="form-label"><?= translate('Kecamatan') ?> <span style="color: red;">(*)</span></label>
            <select class="form-check-input" id="kec_tinggal" name="kec_tinggal" style="width: 100%" required>
            </select>
        </div>
    </div>
    <small class="mt-2">*<?= translate('Note : Jika nama kota dan kecamatan tidak ada, coba pilih nama provinsi terlebih dahulu') ?>.</small>
    <div class="row">
        <div class="col-md-5 mt-2">
            <label for="alamat_tinggal" class="form-label"><?= translate('Alamat Tinggal') ?> <span style="color: red;">(*)</span></label>
            <textarea class="form-control" id="alamat_tinggal" name="alamat_tinggal" minlength="10" rows="3" required><?= $alamat ?></textarea>
        </div>
        <div class="col-md-2 mt-2">
            <label for="rt_tinggal" class="form-label"><?= translate('RT') ?> <span style="color: red;">(*)</span></label>
            <input type="text" class="form-control" id="rt_tinggal" name="rt_tinggal" value="<?= $rt ?>" placeholder="<?= translate('Contoh') ?>: 01" maxlength="2" minlength="2" onkeypress="return hanyaAngka(event)" required>
        </div>
        <div class="col-md-2 mt-2">
            <label for="rw_tinggal" class="form-label"><?= translate('RW') ?> <span style="color: red;">(*)</span></label>
            <input type="text" class="form-control" id="rw_tinggal" name="rw_tinggal" value="<?= $rw ?>" placeholder="<?= translate('Contoh') ?>: 01" maxlength="2" minlength="2" onkeypress="return hanyaAngka(event)" required>
        </div>
        <div class="col-md-3 mt-2">
            <label for="pos_tinggal" class="form-label"><?= translate('Kode POS') ?> <span style="color: red;">(*)</span></label>
            <input type="text" class="form-control" id="pos_tinggal" name="pos_tinggal" value="<?= $kode_pos ?>" placeholder="<?= translate('Contoh') ?>: 40184" minlength="5" onkeypress="return hanyaAngka(event)" required>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-2" style="text-align: right;">
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('#tempat_lahir').select2({
            placeholder: "<?= translate('Silakan masukan nama kota') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getLokasi',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        $('#propinsi').select2({
            placeholder: "<?= translate('Silakan masukan nama provinsi') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getProvinsi',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        $('#kota_tinggal').select2({
            placeholder: "<?= translate('Silakan masukan nama kota') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getKota',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        $('#kec_tinggal').select2({
            placeholder: "<?= translate('Silakan masukan nama kecamatan') ?>",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getKecamatan',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        //start fungsi validasi SIM
        $(".simNo").change(function() {
            if (this.checked) {
                $('.simYes').attr('disabled', 'true').prop('checked', false);
            } else {
                $('.simYes').removeAttr('disabled');
            }
        });
        //end fungsi validasi SIM

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data identitas
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var nama = $("[name=nama]").val();
            tempat_lahir = $("[name=tempat_lahir]").val();
            tgl_lahir = $("[name=tgl_lahir]").val();
            jk = $("input[name=jk]:checked").val();
            status_pernikahan = $("[name=status_pernikahan]").val();
            // ktp = $("[name=ktp]").val();
            ktp = "";
            no_telepon = $("[name=no_telepon]").val();
            email = $("[name=email]").val();
            sim = $("input[name='sim[]']:checked").map(function() {
                return this.value;
            }).get();
            propinsi = $("[name=propinsi]").val();
            kota_tinggal = $("[name=kota_tinggal]").val();
            kec_tinggal = $("[name=kec_tinggal]").val();
            rt_tinggal = $("[name=rt_tinggal]").val();
            rw_tinggal = $("[name=rw_tinggal]").val();
            alamat_tinggal = $("[name=alamat_tinggal]").val();
            pos_tinggal = $("[name=pos_tinggal]").val();

            if (sim.length == 0) {
                $("input[name='sim[]']").addClass("was-validated");
                $("input[name='sim[]']").focus();
            } else {
                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanIdentitasDiri",
                            data: {
                                nama: nama,
                                tempat_lahir: tempat_lahir,
                                tgl_lahir: tgl_lahir,
                                jk: jk,
                                status_pernikahan: status_pernikahan,
                                ktp: ktp,
                                no_telepon: no_telepon,
                                email: email,
                                sim: sim,
                                propinsi: propinsi,
                                kota_tinggal: kota_tinggal,
                                kec_tinggal: kec_tinggal,
                                rt_tinggal: rt_tinggal,
                                rw_tinggal: rw_tinggal,
                                alamat_tinggal: alamat_tinggal,
                                pos_tinggal: pos_tinggal,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-riwayat-pendidikan.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }
        });

        // Hapus error saat ada checkbox yang dicentang
        $("input[name='sim[]']").change(function() {
            if ($("input[name='sim[]']:checked").length > 0) {
                $("input[name='sim[]']").removeClass("is-invalid");
            }
        });

        // auto input
        var tempat_lahir = '<?= $tempat_lahir ?>';
        if (tempat_lahir != "") {
            $("#tempat_lahir").select2('destroy');
            $('#tempat_lahir').append('');
            $('#tempat_lahir').append('<option value="' + tempat_lahir + '">' + tempat_lahir + '</option>');

            $('#tempat_lahir').select2({
                placeholder: "<?= translate('Silakan masukan nama kota') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getLokasi',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#tempat_lahir").val(tempat_lahir).trigger('change');
        }

        var jenis_kelamin = '<?= $jenis_kelamin ?>';
        $("input[name='jk'][value='" + jenis_kelamin + "']").prop("checked", true);

        var status_pernikahan = '<?= $status_pernikahan ?>';
        $("#status_pernikahan").val(status_pernikahan).trigger('change');

        var sim = JSON.parse('<?= $sim ?>');
        sim.forEach(function(value) {
            if (value == 'Tidak Ada') {
                $('.simYes').attr('disabled', 'true').prop('checked', false);
            }
            $("input[name='sim[]'][value='" + value + "']").prop("checked", true);
        });

        var id_propinsi = '<?= $id_propinsi ?>';
        var nama_provinsi = '<?= $nama_provinsi ?>';
        if (id_propinsi != "") {
            $("#propinsi").select2('destroy');
            $('#propinsi').append('');
            $('#propinsi').append('<option value="' + id_propinsi + '">' + nama_provinsi + '</option>');

            $('#propinsi').select2({
                placeholder: "<?= translate('Silakan masukan nama provinsi') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getProvinsi',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#propinsi").val(id_propinsi).trigger('change');
        }

        var id_kota = '<?= $id_kota ?>';
        var nama_kota = '<?= $nama_kota ?>';
        if (id_kota != "") {
            $("#kota_tinggal").select2('destroy');
            $('#kota_tinggal').append('');
            $('#kota_tinggal').append('<option value="' + id_kota + '">' + nama_kota + '</option>');

            $('#kota_tinggal').select2({
                placeholder: "<?= translate('Silakan masukan nama kota') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getKota',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#kota_tinggal").val(id_kota).trigger('change');
        }

        var id_kec = '<?= $id_kec ?>';
        var nama_kecamatan = '<?= $nama_kecamatan ?>';
        if (id_kec != "") {
            $("#kec_tinggal").select2('destroy');
            $('#kec_tinggal').append('');
            $('#kec_tinggal').append('<option value="' + id_kec + '">' + nama_kecamatan + '</option>');

            $('#kec_tinggal').select2({
                placeholder: "<?= translate('Silakan masukan nama kecamatan') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getKecamatan',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#kec_tinggal").val(id_kec).trigger('change');
        }
        // end auto input
    });

    function getChainAlamat(tipe) {
        if (tipe == 'provinsi') {
            var province_id = $('#propinsi').val();

            $('#kota_tinggal').select2('destroy');
            $('#kota_tinggal').select2({
                placeholder: "<?= translate('Silakan masukan nama kota') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getKota',
                            province_id: province_id,
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $('#kota_tinggal').val(null).trigger('change');
            $('#kec_tinggal').val(null).trigger('change');
        } else {
            var regency_id = $('#kota_tinggal').val();

            $('#kec_tinggal').select2('destroy');
            $('#kec_tinggal').select2({
                placeholder: "<?= translate('Silakan masukan nama kecamatan') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getKecamatan',
                            regency_id: regency_id,
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $('#kec_tinggal').val(null).trigger('change');
        }
    }
</script>