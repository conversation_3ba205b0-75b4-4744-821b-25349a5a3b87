<?php
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 86400,
        'path' => '/',
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
    session_start();
}
if (strpos($_SERVER['REQUEST_URI'], '/company') !== false) {
    $company = 'fw-bold';
    $kandidat = '';
    $sign = '/digitalcv/company/login';
} else {
    $company = '';
    $kandidat = 'fw-bold';
    $sign = '/digitalcv/candidate/login';
    $avatar = !empty($_SESSION['users']['img']) ? $_SESSION['users']['img'] : '/digitalcv/assets/images/content/img-avatar-default.png';
}

$nav_kandidat = '<li class="nav-item">
                    <a href="/digitalcv/" class="nav-link text-btn ' . $kandidat . '">' . translate('Kandidat') . '</a>
                </li>
                <li class="nav-item">
                    <a href="/digitalcv/candidate/dashboard/beranda" class="btn btn-signIn fw-bold text-white mt-1 nav-btn">' . translate('Cari Lowongan') . '</a>
                </li>';

$nav_company = '<li class="nav-item">
                    <a href="/digitalcv/company/" class="nav-link text-btn ' . $company . '">' . translate('Perusahaan') . '</a>
                </li>
                <li class="nav-item">
                    <a href="/digitalcv/company/dashboard/beranda" class="btn btn-signIn fw-bold text-white mt-1 nav-btn">' . translate('Mulai Merekrut') . '</a>
                </li>';

$nav_guest = '<li class="nav-item">
                <a href="/digitalcv/" class="nav-link text-btn ' . $kandidat . '">' . translate('Kandidat') . '</a>
            </li>
            <li class="nav-item">
                <a href="/digitalcv/company/" class="nav-link text-btn ' . $company . '">' . translate('Perusahaan') . '</a>
            </li>';

$check_en = "";
$check_id = "";

if (translate("Kandidat") == "Candidate") {
    $check_en = '<i class="fas fa-check" style="padding-right: 5px; color: green;"></i>';
} else {
    $check_id = '<i class="fas fa-check" style="padding-right: 5px; color: green;"></i>';
}
?>
<style>
    @media screen and (max-width: 400px) {
        .index-container-hero {
            padding-top: 60px !important;
        }
    }

    @media screen and (max-width: 600px) {
        footer img {
            margin-left: 10px;
            margin-right: 15px;
            float: left;
        }

        .lg-dcv-footer {
            width: 150px;
        }

        .text-welcome-sign-up {
            margin-top: 60px !important;
        }

        .index-container-hero {
            padding-top: 100px !important;
        }
    }

    @media screen and (min-width: 601px) and (max-width: 1024px) {
        footer img {
            margin-left: 10px;
            margin-right: 15px;
            float: left;
        }

        .lg-dcv-footer {
            width: 200px;
            height: auto;
        }

        .img-candidat-content {
            margin-top: 100px;
            width: 360px;
        }

        .nav-btn {
            margin-left: 20px;
        }
    }

    @media screen and (min-width: 1025px) {
        .lg-dcv-footer {
            width: 200px;
            height: auto;
        }

        .nav-btn {
            margin-left: 20px;
        }
    }
</style>



<?php
if (strpos($_SERVER['REQUEST_URI'], '/company') !== false) {
    $company = 'fw-bold';
    $kandidat = '';
    $sign = '/digitalcv/company/login';
    $signup = '/digitalcv/company/sign-up';
} else {
    $company = '';
    $kandidat = 'fw-bold';
    $sign = '/digitalcv/candidate/login';
    $signup = '/digitalcv/candidate/sign-up';
}
?>

<header class="header">
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a href="/digitalcv/" class="navbar-brand"><img src="/digitalcv/assets/images/logo/logoDcv2.png" width="90" alt="" /></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div id="navbarSupportedContent" class="collapse navbar-collapse" style="margin-left: 200px;">
                <ul class="navbar-nav ms-auto">
                    <?php
                    if (isset($_SESSION['users'])) {
                        echo $nav_kandidat;
                    } elseif (isset($_SESSION['users-pic'])) {
                        echo $nav_company;
                    } else {
                        echo $nav_guest;
                    }
                    ?>
                </ul>
            </div>


            <div id="navbarSupportedContent" class="collapse navbar-collapse">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item" style="text-align: right;">
                        <div class="dropdown mx-1 p-2">
                            <a href="/digitalcv/faq"><?= translate('FAQ') ?></a>
                            <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-translate"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="changeLang('en')"><?= $check_en ?> English</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="changeLang('id')"><?= $check_id ?> Bahasa</a></li>
                            </ul>


                            <?php
                            if (isset($_SESSION['users']) && $kandidat != '') {
                            ?>
                                <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <img src="<?= $avatar ?>" alt="User Avatar" class="rounded-circle me-2" width="40" height="40" referrerpolicy="no-referrer" style="border-radius: 50%!important;max-height: 50px;object-fit: cover;width: 40px;">
                                    <span id="userName"><?= $_SESSION['users']['nama'] ?></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/digitalcv/candidate/dashboard/profile"><?= translate('Profil') ?></a></li>
                                    <li><a class="dropdown-item" href="/digitalcv/template/logout"><?= translate('Keluar') ?></a></li>
                                </ul>
                            <?php
                            } elseif (isset($_SESSION['users-pic']) && $company != '') {
                            ?>
                                <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <img src="/digitalcv/assets/images/content/img-avatar-default.png" alt="User Avatar" class="rounded-circle me-2" width="40" height="40" referrerpolicy="no-referrer" style="border-radius: 50%!important;max-height: 50px;object-fit: cover;width: 40px;">
                                    <span id="userName"><?= $_SESSION['users-pic']['label'] ?></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="">Profile</a></li>
                                    <li><a class="dropdown-item" href="/digitalcv/template/logout"><?= translate('Keluar') ?></a></li>
                                </ul>
                            <?php
                            } else {
                            ?>

                                <a href="<?= $signup ?>" class="btn btn-signUp fw-bold m-2"><?= translate('Daftar') ?></a>
                                <a href="<?= $sign ?>" class="btn btn-signIn fw-bold m-2 text-white"><?= translate('Masuk') ?></a>
                            <?php
                            }
                            ?>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</header>