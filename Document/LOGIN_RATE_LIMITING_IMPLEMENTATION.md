# Implementasi Mekanisme Pembatasan Login

## Deskripsi

Implementasi sistem keamanan untuk membatasi percobaan login yang mencurigakan. Sistem akan memblokir akses login selama 1 menit setelah 5 kali percobaan login yang gagal berturut-turut.

## Fitur yang Diimplementasikan

### 1. Pelacakan Percobaan Login

- Setiap percobaan login yang gagal dicatat dalam tabel `login_attempts`
- Mencatat email, jumlah per<PERSON>an, dan waktu percobaan terakhir

### 2. Pemblokiran Otomatis

- Setelah 5 kali percobaan gagal, akun diblokir selama 1 menit
- W<PERSON>tu pemblokiran disimpan dalam kolom `blocked_until`

### 3. Reset Otomatis

- Percobaan login berhasil akan mereset counter
- Pemblokiran akan otomatis berakhir setelah 1 menit

## Struktur Database

### Tabel login_attempts

```sql
CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `attempts` int(11) NOT NULL DEFAULT 0,
  `blocked_until` datetime DEFAULT NULL,
  `last_attempt` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_blocked_until` (`blocked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Fungsi yang Ditambahkan

### 1. checkLoginAttempts($conn, $email)

- Mengecek status percobaan login untuk email tertentu
- Mengembalikan informasi apakah akun diblokir dan berapa sisa waktu pemblokiran

### 2. recordFailedLogin($conn, $email)

- Mencatat percobaan login yang gagal
- Menambah counter percobaan
- Memblokir akun jika mencapai 5 percobaan

### 3. resetLoginAttempts($conn, $email)

- Menghapus record percobaan login setelah berhasil login
- Mereset counter ke 0

## Alur Kerja

1. **Pengecekan Awal**: Sistem mengecek apakah email sedang dalam status pemblokiran
2. **Validasi Login**: Jika tidak diblokir, proses login normal dilanjutkan
3. **Pencatatan Kegagalan**: Jika password salah, sistem mencatat percobaan gagal
4. **Pemblokiran**: Setelah 5 kali gagal, akun diblokir selama 1 menit
5. **Reset**: Login berhasil akan mereset semua counter

## Pesan Error yang Ditampilkan

- **Password salah**: "Password salah! Sisa percobaan: X kali."
- **Email tidak terdaftar**: "Email belum terdaftar. Sisa percobaan: X kali."
- **Akun diblokir**: "Akun Anda diblokir karena terlalu banyak percobaan login yang gagal. Silakan coba lagi dalam X menit."
- **Mencapai batas**: "Terlalu banyak percobaan login yang gagal. Akun Anda diblokir selama 1 menit."

## Instalasi

1. Jalankan script SQL untuk membuat tabel:

   ```bash
   mysql -u username -p database_name < login_attempts_table.sql
   ```

2. File yang dimodifikasi:
   - `api/mobile/candidate/login.php`

## Keamanan Tambahan

- Sistem mencatat percobaan gagal bahkan untuk email yang tidak terdaftar
- Menggunakan prepared statements untuk mencegah SQL injection
- Waktu pemblokiran otomatis berakhir tanpa intervensi manual
- Counter direset setelah login berhasil untuk mencegah akumulasi yang tidak perlu

## Konfigurasi

- **Jumlah percobaan maksimal**: 5 kali (dapat diubah dalam kode)
- **Durasi pemblokiran**: 1 menit (dapat diubah dalam kode)
- **Reset otomatis**: Ya, setelah pemblokiran berakhir atau login berhasil
