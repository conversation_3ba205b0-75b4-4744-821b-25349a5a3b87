<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

$func = $_GET['func'];

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

if ($func == 'getLokasi') {
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $perPage = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 5;
    $offset = ($page - 1) * $perPage;


    $sql = "SELECT id, `name` FROM regencies";
    $countQuery = "SELECT COUNT(*) as total FROM regencies";

    if (!empty($search)) {
        $sql .= " WHERE `name` LIKE ? ";
        $countQuery .= " WHERE `name` LIKE ? ";
    }

    // PENTING: ORDER BY harus setelah WHERE, bukan sebelum
    $sql .= " ORDER BY id LIMIT ?, ? ";

    // Persiapkan statement untuk query utama
    $stmt = $conn->prepare($sql);
    if (!empty($search)) {
        $searchTerm = "%$search%";
        $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    } else {
        $stmt->bind_param("ii", $offset, $perPage);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => $row['id'], "text" => $row['name']];
    }

    $stmt = $conn->prepare($countQuery);
    if (!empty($search)) {
        $stmt->bind_param("s", $searchTerm);
    }
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = isset($countResult['total']) ? $countResult['total'] : 0;

    // Log activity
    $searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
    $messages = "Mengakses data lokasi/kabupaten $searchLog (halaman $page, $totalRows total data)";
    $extra_info = "API";
    $level = "INFO";
    logActivity($conn, $userData->id, $level, $messages, $extra_info);

    // Format response JSON
    $response = [
        "status" => true,
        "message" => "fetch data berhasil",
        "data" => $lokasi,
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}


if ($func == 'getProvinsi') {
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $perPage = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 5;
    $offset = ($page - 1) * $perPage;

    // Jika search diisi tetapi kurang dari 3 karakter, kembalikan error
    // if (!empty($search) && strlen($search) < 3) {
    //     header('Content-Type: application/json');
    //     echo json_encode([
    //         "success" => false,
    //         "message" => "Harus lebih dari 3 karakter",
    //         "data" => [],
    //         "page" => $page,
    //         "page_size" => $perPage,
    //         "total_data" => 0
    //     ], JSON_PRETTY_PRINT);
    //     exit;
    // }

    $sql = "SELECT id, `name` FROM provinces";
    $countQuery = "SELECT COUNT(*) as total FROM provinces";

    // Tambahkan kondisi WHERE jika ada pencarian minimal 3 karakter
    if (!empty($search)) {
        $sql .= " WHERE `name` LIKE ?";
        $countQuery .= " WHERE `name` LIKE ?";
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";

    // Persiapkan statement untuk query utama
    $stmt = $conn->prepare($sql);
    if (!empty($search)) {
        $searchTerm = "%$search%";
        $stmt->bind_param("sii", $searchTerm, $offset, $perPage);
    } else {
        $stmt->bind_param("ii", $offset, $perPage);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => $row['id'], "nama" => $row['name']];
    }

    $stmt = $conn->prepare($countQuery);
    if (!empty($search)) {
        $stmt->bind_param("s", $searchTerm);
    }
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = isset($countResult['total']) ? $countResult['total'] : 0;

    // Log activity for getProvinsi
    $searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
    $messages = "Mengakses data provinsi $searchLog (halaman $page, $totalRows total data)";
    $extra_info = "API";
    $level = "INFO";
    logActivity($conn, $userData->id, $level, $messages, $extra_info);

    // Format response JSON
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $lokasi,
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'getKota') {
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $perPage = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 5;
    $offset = ($page - 1) * $perPage;
    $provinceId = isset($_GET['province_id']) ? $_GET['province_id'] : null;

    // Jika search diisi tetapi kurang dari 3 karakter, kembalikan error
    if (!empty($search) && strlen($search) < 3) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Harus lebih dari 3 karakter",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Query dasar
    $sql = "SELECT id, `name` FROM regencies";
    $countQuery = "SELECT COUNT(*) as total FROM regencies";
    $params = [];
    $types = "";

    // Tambahkan filter berdasarkan province_id jika ada
    if ($provinceId) {
        $sql .= " WHERE province_id = ?";
        $countQuery .= " WHERE province_id = ?";
        $params[] = $provinceId;
        $types .= "i"; // province_id adalah integer
    }

    // Tambahkan filter berdasarkan search jika ada
    if (!empty($search)) {
        if (strpos($sql, "WHERE") !== false) {
            $sql .= " AND `name` LIKE ?";
            $countQuery .= " AND `name` LIKE ?";
        } else {
            $sql .= " WHERE `name` LIKE ?";
            $countQuery .= " WHERE `name` LIKE ?";
        }
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $types .= "s"; // search adalah string
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii"; // offset dan perPage adalah integer

    // Persiapkan statement untuk query utama
    $stmt = $conn->prepare($sql);
    if ($types) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => $row['id'], "nama" => $row['name']];
    }

    // Persiapkan statement untuk menghitung total data
    $stmt = $conn->prepare($countQuery);
    if ($types) {
        $bindParams = array_slice($params, 0, -2); // Hilangkan limit & offset
        $stmt->bind_param(substr($types, 0, -2), ...$bindParams);
    }
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = isset($countResult['total']) ? $countResult['total'] : 0;

    // Log activity for getKota
    $searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
    $provinceLog = $provinceId ? "provinsi ID: $provinceId" : "semua provinsi";
    $messages = "Mengakses data kota/kabupaten dari $provinceLog $searchLog (halaman $page, $totalRows total data)";
    $extra_info = "API";
    $level = "INFO";
    logActivity($conn, $userData->id, $level, $messages, $extra_info);

    // Format response JSON
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $lokasi,
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'getKecamatan') {
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $perPage = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 5;
    $offset = ($page - 1) * $perPage;
    $provinceId = isset($_GET['regency_id']) ? $_GET['regency_id'] : null;

    // Jika search diisi tetapi kurang dari 3 karakter, kembalikan error
    if (!empty($search) && strlen($search) < 3) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Harus lebih dari 3 karakter",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Query dasar
    $sql = "SELECT id, `name` FROM districts";
    $countQuery = "SELECT COUNT(*) as total FROM districts";
    $params = [];
    $types = "";

    // Tambahkan filter berdasarkan regency_id jika ada
    if ($provinceId) {
        $sql .= " WHERE regency_id = ?";
        $countQuery .= " WHERE regency_id = ?";
        $params[] = $provinceId;
        $types .= "i"; // regency_id adalah integer
    }

    // Tambahkan filter berdasarkan search jika ada
    if (!empty($search)) {
        if (strpos($sql, "WHERE") !== false) {
            $sql .= " AND `name` LIKE ?";
            $countQuery .= " AND `name` LIKE ?";
        } else {
            $sql .= " WHERE `name` LIKE ?";
            $countQuery .= " WHERE `name` LIKE ?";
        }
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $types .= "s"; // search adalah string
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii"; // offset dan perPage adalah integer

    // Persiapkan statement untuk query utama
    $stmt = $conn->prepare($sql);
    if ($types) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => $row['id'], "nama" => $row['name']];
    }

    // Persiapkan statement untuk menghitung total data
    $stmt = $conn->prepare($countQuery);
    if ($types) {
        $bindParams = array_slice($params, 0, -2); // Hilangkan limit & offset
        $stmt->bind_param(substr($types, 0, -2), ...$bindParams);
    }
    $stmt->execute();
    $countResult = $stmt->get_result()->fetch_assoc();
    $totalRows = isset($countResult['total']) ? $countResult['total'] : 0;

    // Log activity for getKecamatan
    $searchLog = !empty($search) ? "dengan pencarian: '$search'" : "tanpa filter pencarian";
    $regencyLog = $provinceId ? "kabupaten ID: $provinceId" : "semua kabupaten";
    $messages = "Mengakses data kecamatan dari $regencyLog $searchLog (halaman $page, $totalRows total data)";
    $extra_info = "API";
    $level = "INFO";
    logActivity($conn, $userData->id, $level, $messages, $extra_info);

    // Format response JSON
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $lokasi,
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}

$conn->close();
exit;
