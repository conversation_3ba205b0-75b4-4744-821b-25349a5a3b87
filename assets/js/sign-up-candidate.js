//start function untuk mengambil angka saja
function hanyaAngka(evt) {
  var charCode = evt.which ? evt.which : event.keyCode;

  if (charCode < 48 || charCode > 57) return false;
  return true;
}
//end function untuk mengambil angka saja

//start funciton untuk menampilkan password saat melakukan input
function showPass(id, btn) {
  var input = document.getElementById(id);
  btn = document.getElementById(btn);

  if (input.type === "password") {
    input.type = "text";
    btn.innerHTML = '<i class="bi bi-eye"></i>';
  } else {
    input.type = "password";
    btn.innerHTML = '<i class="bi bi-eye-slash"></i>';
  }
}
//end funciton untuk menampilkan password saat melakukan input

//start fungsi untuk kirim OTP
$("#btn-otp").on("click", function () {
  let email = $("[name=email]").val();
  let phoneNumber = $("[name=no_tlp]").val().replace(/^0+/, ""); // Hilangkan angka 0 di awal
  let countryCode = $("#country-code").val();
  let no_tlp = countryCode + phoneNumber;

  function validateEmail($email) {
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
    return emailReg.test($email);
  }

  if (email == "" || no_tlp.substring(2) == "") {
    toastr["error"](window.appData.alert1);
  } else {
    if (!validateEmail(email)) {
      toastr["error"](window.appData.alert2);
    } else {
      var button = $("#btn-otp");
      var seconds = 60; // Countdown time
      button
        .prop("disabled", true)
        .text(window.appData.kirimOTP + " (60s)")
        .css("background-color", "#BDBDBD");
      var countdown = setInterval(function () {
        seconds--;
        button.text(window.appData.kirimOTP + " (" + seconds + "s)");

        if (seconds <= 0) {
          clearInterval(countdown);
          button
            .prop("disabled", false)
            .text(window.appData.kirimOTP)
            .css("background-color", "#FF9800");
        }
      }, 1000);
      $("#btn-otp").html(
        '<div class="spinner-border spinner-border-sm" role="status" style="padding-right: 5px;"><span class="visually-hidden">Loading...</span></div> &nbsp;Proses'
      );
      $("#btn-otp").prop("disabled", true);

      grecaptcha.ready(function () {
        grecaptcha
          .execute("6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF", {
            action: "submit",
          })
          .then(function (token) {
            $.ajax({
              url: "../controller/sign-up/sendOTP",
              type: "post",
              data: {
                email: email,
                no_hp_pic: no_tlp,
                recaptcha_response: token,
                for: "candidate",
              },
              success: function (result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status != "success") {
                  toastr["error"](obj.message);
                  $("[name=email]").val("");

                  clearInterval(countdown);
                  button
                    .prop("disabled", false)
                    .text(window.appData.kirimOTP)
                    .css("background-color", "#FF9800");
                } else {
                  toastr["success"](obj.message);
                }
              },
            });
          });
      });
    }
  }
});
//end fungsi untuk kirim OTP

//start fungsi submit daftar candidate
$("#form-daftar").on("submit", function (event) {
  event.preventDefault(); // Mencegah reload form
  event.stopPropagation(); // Menghentikan event bubbling

  if (this.checkValidity() === false) {
    $(this).addClass("was-validated");
    return;
  }

  var nama = $("[name=nama_lengkap]").val();
  var email = $("[name=email]").val();
  var otp = $("[name=otp]").val();
  let phoneNumber = $("[name=no_tlp]").val().replace(/^0+/, ""); // Hilangkan angka 0 di awal
  let countryCode = $("#country-code").val();
  let no_tlp = countryCode + phoneNumber;

  var password = $("[name=password]").val();
  var kom_pass = $("[name=kon_password]").val();

  // cek password dan konfirmasi password apakah sudah sama
  if (password != kom_pass) {
    toastr["warning"](window.appData.alert3);
  } else {
    $("#btn-daftar").html(window.appData.harapTunggu + "....");
    $("#btn-daftar").attr("disabled", true);
    $.ajax({
      type: "post",
      url: "../controller/sign-up/registerCandidate",
      data: {
        nama: nama,
        email: email,
        no_tlp: no_tlp,
        password: password,
        otp: otp,
      },
      success: function (result) {
        var obj = JSON.parse(JSON.stringify(result));

        if (obj.status == "success") {
          Swal.fire({
            title: window.appData.berhasil + ".",
            text: window.appData.textberhasil + ".",
            icon: "success",
          }).then((result) => {
            window.location.href = "login";
          });
        } else {
          toastr["error"](obj.message);
          $("#btn-daftar").html(window.appData.daftar);
          $("#btn-daftar").prop("disabled", false);
        }
      },
    });
  }
});
//end fungsi submit daftar candidate
