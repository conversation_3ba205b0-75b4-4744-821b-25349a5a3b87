<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';

// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;

// Legacy variables for backward compatibility
$id_pegawai = $userData->id;

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function get_email_from_lamar($id_lamar, $conn)
{
    $sql = "SELECT
                uk.email
            FROM
                `users_lamar`  ul
                JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
            WHERE
                id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $email = $row['email'];
    } else {
        $email = null;
    }
    return $email;
}

function get_posisi($id_req, $conn)
{
    $sql = "SELECT posisi FROM list_request WHERE id_req = '$id_req'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $posisi = $row['posisi'];
    } else {
        $posisi = null;
    }
    return $posisi;
}

function get_nama_kandidat($id_lamar, $conn)
{
    $sql = "SELECT
        uk.nama_lengkap
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $nama_lengkap = $row['nama_lengkap'];
    } else {
        $nama_lengkap = null;
    }
    return $nama_lengkap;
}

function update_proses_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "On Proccess Digitalcv";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "On Proccess Interview";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "On Proccess Psikotes";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "On Proccess Validasi";
    } elseif ($text == 'VALIDASI') {
        $status_update = "On Proccess Offering";
    } elseif ($text == 'OFFERING') {
        $status_update = "On Proccess Medical";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Sudah Diterima";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_close_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_terima_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Terima Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Terima Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Terima Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Terima Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Terima Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Terima Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Terima Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Terima Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_tolak_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Tolak Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function get_pin($id_lamar, $conn)
{
    $sql = "SELECT
        uk.pin
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $pin = $row['pin'];
    } else {
        $pin = null;
    }
    return $pin;
}

function get_company($company, $conn)
{
    $data = [];
    $sql = "SELECT * FROM koordinator WHERE id_koordinator = '$company'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $data['nama_perusahaan'] = $row['label'];
        $data['img'] = $row['img'];
        $data['alamat'] = $row['alamat'];
    } else {
        $data = null;
    }
    return $data;
}


function cek_feedback_hrd($conn, $id_lamar)
{
    //cek review = HRD
    $sql = "SELECT approval FROM feedback WHERE id_lamar = '$id_lamar' AND review = 'HRD'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['approval'];
}

function getInitials($name)
{
    $words = explode(' ', strtoupper($name));
    $initials = '';
    foreach ($words as $w) {
        if ($w !== '') {
            $initials .= $w[0];
        }
    }
    return substr($initials, 0, 2); // maksimal 2 huruf
}

function selisihTahunBulan($tanggal_awal, $tanggal_akhir)
{
    $tanggal_awal = DateTime::createFromFormat('m-Y', $tanggal_awal);
    $tanggal_awal = $tanggal_awal->format('Y-m-d');

    $tanggal_akhir = DateTime::createFromFormat('m-Y', $tanggal_akhir);
    $tanggal_akhir = $tanggal_akhir->format('Y-m-d');

    $awal  = new DateTime($tanggal_awal);
    $akhir = new DateTime($tanggal_akhir);

    if ($awal > $akhir) {
        // Tukar jika tanggal awal lebih besar dari akhir
        $temp = $awal;
        $awal = $akhir;
        $akhir = $temp;
    }

    $selisih = $awal->diff($akhir);

    $tahun = $selisih->y;
    $bulan = $selisih->m;

    $output = "";

    if ($tahun > 0) {
        $output .= $tahun . " Tahun";
    }

    if ($bulan > 0) {
        if ($output !== "") {
            $output .= " ";
        }
        $output .= $bulan . " Bulan";
    }

    return $output === "" ? "0 Bulan" : $output;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

// Get function from URL parameter
$func = $_GET['func'] ?? '';

if ($func == 'submitJobPosting') {
    $response = [];
    $status = false;
    $id_req = "";

    try {

        $conn->begin_transaction();

        $company = $id_koordinator;

        // Ambil data perusahaan + paket
        $sqlCompany = "SELECT id_koordinator, label, paket FROM koordinator WHERE id_koordinator = '$company'";
        $queryCompany = $conn->query($sqlCompany);

        if (mysqli_num_rows($queryCompany) === 0) {
            throw new Exception("Anda tidak terdaftar!");
        }

        $rowCompany = mysqli_fetch_assoc($queryCompany);
        $perusahaan = $rowCompany['label'];
        $paket = isset($rowCompany['paket']) ? $rowCompany['paket'] : 'C1';

        // Generate ID Request
        $sql = "SELECT MAX(id_req) AS maxKode FROM list_request";
        $result = $conn->query($sql);
        $arrKode = mysqli_fetch_array($result);
        $kode = $arrKode['maxKode'];
        $noUrut = (int) substr($kode, 3);
        $noUrut++;
        $id_req = "REQ" . sprintf("%03s", $noUrut);

        $cekId = $conn->query("SELECT id_req FROM list_request WHERE id_req = '$id_req'");
        if ($cekId->num_rows > 0) {
            throw new Exception("Input job gagal (ID duplikat)!");
        }

        // Ambil data dari form
        $tglCreate = date('Y-m-d H:i:s');
        $expired_date = date("Y-m-d", strtotime('+30 days')) . " 00:00:00";

        $jmlh_kandidat    = isset($_POST['jmlh_kandidat']) ? $_POST['jmlh_kandidat'] : '';

        $department       = "";
        $posisi           = $_POST['posisi'];
        $atasan           = "";
        $bawahan          = "";
        $lokasi_k         = is_array($_POST['lokasi_k']) ? implode(",", $_POST['lokasi_k']) : $_POST['lokasi_k'];
        $sistem_k         = $_POST['sistem_k'];
        $k_usia           = is_array($_POST['k_umur']) ? implode("-", $_POST['k_umur']) : $_POST['k_umur'];
        $k_pendidikan     = $_POST['k_pendidikan'];
        $k_jurusan        = is_array($_POST['k_jurusan']) ? implode("|", $_POST['k_jurusan']) : $_POST['k_jurusan'];
        $k_sekolah        = is_array($_POST['k_sekolah']) ? implode("|", $_POST['k_sekolah']) : $_POST['k_sekolah'];
        $k_jk             = $_POST['k_jk'];
        $k_pengalaman     = $_POST['k_pengalaman'];
        $k_pengalaman_sama = "0";
        $k_status         = $_POST['k_status'];
        $k_sim            = isset($_POST['k_sim']) && $_POST['k_sim'] != ''
            ? (is_array($_POST['k_sim']) ? implode(',', $_POST['k_sim']) : $_POST['k_sim'])
            : '-';

        $vPendidikan      = "";
        $vPengalaman      = "";
        $vUsiaLebih       = $_POST['vUsiaLebih'];
        $vUsiaKurang      = $_POST['vUsiaKurang'];
        $vJurusan         = $_POST['vJurusan'];
        $vSekolah         = $_POST['vSekolah'];
        $vMinPendidikan   = $_POST['vMinPendidikan'];

        $kk               = is_array($_POST['kk']) ? implode(",", $_POST['kk']) : $_POST['kk'];
        $kb               = is_array($_POST['kb']) ? implode(',', $_POST['kb']) : $_POST['kb'];
        $km               = $_POST['km'];
        $kbdu             = $_POST['kbdu'];
        $rlp              = (isset($_POST['rlp']) && is_array($_POST['rlp'])) ? implode(",", $_POST['rlp']) : (isset($_POST['rlp']) ? $_POST['rlp'] : '');

        $deskripsi        = $_POST['deskripsi'];
        $check_screening  = is_array($_POST['check_screening']) ? implode("|", $_POST['check_screening']) : $_POST['check_screening'];
        $k_tambahan       = is_array($_POST['k_tambahan']) ? implode("|", $_POST['k_tambahan']) : $_POST['k_tambahan'];
        $persetujuan      = "";
        $alasan_persetujuan = "";
        $check_lowongan   = is_array($_POST['checkLowongan']) ? implode("|", $_POST['checkLowongan']) : $_POST['checkLowongan'];
        $tipe_pekerjaan   = $_POST['tipe_pekerjaan'];

        // Cek apakah lowongan dengan posisi sama sudah ada
        $sqlCek = "
            SELECT id_req 
            FROM list_request 
            WHERE status = 'On Proccess' 
            AND id_koordinator = ? 
            AND posisi = ?
        ";
        $sqlCek = $conn->prepare($sqlCek);
        $sqlCek->bind_param("ss", $id_koordinator, $posisi);
        $sqlCek->execute();
        $queryCek = $sqlCek->get_result();
        $sqlCek->close();

        if ($queryCek->num_rows > 0) {
            throw new Exception("Lowongan sudah ada!");
        }

        // Cek limit job berdasarkan paket
        $cekLimit = $conn->prepare("SELECT id_req FROM list_request WHERE status = 'On Proccess' AND id_koordinator = ?");
        $cekLimit->bind_param("s", $id_koordinator);
        $cekLimit->execute();
        $queryCek = $cekLimit->get_result();
        $cekLimit->close();

        $jml_job = $queryCek->num_rows;

        $limitMap = [
            "C1" => 3,
            "C2" => 10,
            "C3" => 20,
        ];

        if (isset($limitMap[$paket]) && $jml_job >= $limitMap[$paket]) {
            throw new Exception("Limit job posting: $jml_job/{$limitMap[$paket]}");
        }

        // Insert ke list_request
        $insertRequest = "
            INSERT INTO list_request (
                id_req, perusahaan, department, posisi, atasan, bawahan, jmlh_kandidat, lokasi_kerja,
                sistem_kerja, k_pendidikan, k_usia, k_sim, k_jk, k_pengalaman, k_pengalaman_sama,
                k_status, status, k_jurusan, v_pendidikan, v_pengalaman, v_usia_lebih, v_usia_kurang,
                id_koordinator, create_at, update_at, pembuat, divisi, expired_date, note,
                check_screening, k_khusus, v_jurusan, persetujuan, alasan_persetujuan,
                check_lowongan, k_sekolah, v_sekolah, v_min_pendidikan, tipe_pekerjaan
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?,
                ?, ?, 'On Proccess', ?, ?, ?,
                ?, ?, ?, ?, '', ?, ?,
                ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?,
                ?, ?
            )
        ";
        $insertRequest = $conn->prepare($insertRequest);
        $insertRequest->bind_param("sssssssssssssssssssssssssssssssssssss", $id_req, $perusahaan, $department, $posisi, $atasan, $bawahan, $jmlh_kandidat, $lokasi_k, $sistem_k, $k_pendidikan, $k_usia, $k_sim, $k_jk, $k_pengalaman, $k_pengalaman_sama, $k_status, $k_jurusan, $vPendidikan, $vPengalaman, $vUsiaLebih, $vUsiaKurang, $id_koordinator, $tglCreate, $id_pegawai, $divisi, $expired_date, $deskripsi, $check_screening, $k_tambahan, $vJurusan, $persetujuan, $alasan_persetujuan, $check_lowongan, $k_sekolah, $vSekolah, $vMinPendidikan, $tipe_pekerjaan);

        // Insert ke list_kriteria
        $insertKriteria = "
            INSERT INTO list_kriteria (
                posisi, kk, kb, km, kbdu, rlp, id_koordinator, id_req
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?
            )
        ";
        $insertKriteria = $conn->prepare($insertKriteria);
        $insertKriteria->bind_param("ssssssss", $posisi, $kk, $kb, $km, $kbdu, $rlp, $id_koordinator, $id_req);

        if ($insertRequest->execute() && $insertKriteria->execute()) {
            $insertRequest->close();
            $insertKriteria->close();

            $conn->commit();
            $response = [
                'status' => true,
                'message' => "Input job berhasil!",
                'data' => []
            ];
        } else {
            throw new Exception("Input job gagal!");
        }
    } catch (Exception $e) {
        $conn->rollback();
        $response = [
            'status' => false,
            'message' => $e->getMessage(),
            'data' => []
        ];
    }

    echo json_encode($response, JSON_PRETTY_PRINT);
}


if ($func == 'getListRequest') {
    header('Content-Type: application/json');

    $data = [];
    $no = 0;

    // PAGINATION PARAMS
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $page_size = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
    $offset = ($page - 1) * $page_size;
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch .= " AND (
            department LIKE '%$q%' OR 
            posisi LIKE '%$q%' OR 
            status LIKE '%$q%'
        )";
    }

    // Hitung total data
    $sqlTotalQuery = "SELECT COUNT(*) as total FROM list_request 
                 WHERE status != 'Deleted' AND status != '' AND id_req != '' AND id_koordinator = ? $querySearch";
    $sqlTotal = $conn->prepare($sqlTotalQuery);
    $sqlTotal->bind_param("s", $id_koordinator);
    $sqlTotal->execute();
    $resultTotal = $sqlTotal->get_result();
    $sqlTotal->close();

    $totalData = ($row = $resultTotal->fetch_assoc()) ? (int)$row['total'] : 0;

    // Hitung total halaman
    $totalPage = ceil($totalData / $page_size);

    // Ambil data dengan limit dan offset
    $sqlData = $conn->prepare("SELECT
					id_req,
					department,
					posisi,
					create_at,
					jmlh_kandidat,
					status AS status_permintaan,
                    expired_date
				FROM
					list_request 
				WHERE
					status != 'Deleted' AND status != '' AND id_req != '' AND id_koordinator = ?
                    $querySearch
				ORDER BY
					status, create_at DESC
                LIMIT ?, ?");
    $sqlData->bind_param("sii", $id_koordinator, $offset, $page_size);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    if ($queryData->num_rows > 0) {
        while ($row = $queryData->fetch_assoc()) {
            $data[] = [
                'id_req' => $row['id_req'],
                'department' => $row['department'],
                'posisi' => $row['posisi'],
                'tanggal_permintaan' => date("d M Y", strtotime($row['create_at'])),
                'expired_date' => date("d M Y", strtotime($row['expired_date'])),
                'jumlah_kandidat' => (int)$row['jmlh_kandidat'],
                'status' => $row['status_permintaan'],
            ];
        }
    }

    $response = [
        'status' => true,
        'message' => 'Berhasil mengambil data',
        'data' => $data,
        'page' => $page,
        'page_size' => $page_size,
        'total_page' => $totalPage,
        'total_data' => $totalData
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'detailLowongan') {
    $data = array();
    $id_req = $_GET['id_req'];
    $sqlData = "SELECT
					a.*,
					b.kk,
					b.kb,
					b.km,
					b.kbdu,
					b.rlp
				FROM
					list_request a
					JOIN list_kriteria b ON a.id_req = b.id_req
				WHERE
					a.id_req = ? AND a.id_koordinator = ?";
    $sqlData = $conn->prepare($sqlData);
    $sqlData->bind_param("ss", $id_req, $id_koordinator);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    if ($queryData->num_rows > 0) {
        $row = mysqli_fetch_assoc($queryData);

        // Konversi langsung tanpa HTML
        $checkScreening = explode("|", $row['check_screening']);
        $kKhusus = $row['k_khusus'];
        $pertanyaanKhusus = $kKhusus != '' ? explode("|", $kKhusus) : [];

        $kk = [];
        if (!empty($row['kk'])) {
            $arr_kk = explode(",", $row['kk']);
            foreach ($arr_kk as $val) {
                switch ($val) {
                    case '1':
                        $kk[] = "Mengetahui dasar dan pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)";
                        break;
                    case '2':
                        $kk[] = "Mengerti dan Menguasai pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)";
                        break;
                    case '3':
                        $kk[] = "Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya";
                        break;
                    case '4':
                        $kk[] = "Menguasai Pemrograman dan Analisis Pemrograman";
                        break;
                    case '5':
                        $kk[] = "Spesialis Komputer Sains";
                        break;
                    case '6':
                        $kk[] = "Tidak diperlukan";
                        break;
                }
            }
        }

        $km_map = [
            '1' => 'Tidak Pernah/Tidak Perlu/Tidak Ada',
            '2' => '1-3 orang',
            '3' => '4-10 orang',
            '4' => '11-50 orang',
            '5' => 'Lebih dari 50 orang',
        ];
        $isi_km = isset($km_map[$row['km']]) ? $km_map[$row['km']] : '';

        $kbdu_map = [
            '1' => 'Tidak Diperlukan',
            '2' => 'Diperlukan',
            '3' => 'Sangat Diperlukan (Ahli)',
        ];
        $isi_kbdu = isset($kbdu_map[$row['kbdu']]) ? $kbdu_map[$row['kbdu']] : '';

        $rlp = [];
        if (!empty($row['rlp'])) {
            $rlp_ids = explode(",", addslashes($row['rlp']));
            foreach ($rlp_ids as $id_rlp) {
                $sqlRlp = "SELECT name FROM ruang_lingkup_pekerjaan WHERE id = '$id_rlp' GROUP BY id";
                $queryRlp = $conn->query($sqlRlp);
                if ($queryRlp && mysqli_num_rows($queryRlp) > 0) {
                    $rowRlp = mysqli_fetch_assoc($queryRlp);
                    $rlp[] = $rowRlp['name'];
                }
            }
        }
        $dibuat = waktuLalu($row['create_at']);
        $data = [
            "department" => $row['department'],
            "posisi" => $row['posisi'],
            "atasan" => $row['atasan'],
            "bawahan" => $row['bawahan'],
            "jmlh_kandidat" => $row['jmlh_kandidat'],
            "lokasi_kerja" => $row['lokasi_kerja'],
            "sistem_kerja" => $row['sistem_kerja'],
            "pendidikan" => $row['k_pendidikan'],
            "jurusan" => str_replace("|", ", ", $row['k_jurusan']),
            "sekolah" => str_replace("|", ", ", $row['k_sekolah']),
            "umur" => $row['k_usia'],
            "sim" => $row['k_sim'],
            "pengalaman" => $row['k_pengalaman'],
            "status" => $row['k_status'],
            "jk" => $row['k_jk'],
            "kk" => $kk,
            "km" => $isi_km,
            "kb" => str_replace(",", ", ", $row['kb']),
            "kbdu" => $isi_kbdu,
            "rlp" => $rlp,
            "note" => $row['note'],
            "vPengalaman" => $row['v_pengalaman'],
            "vPendidikan" => $row['v_pendidikan'],
            "vJurusan" => $row['v_jurusan'],
            "vUsiaLebih" => $row['v_usia_lebih'],
            "vUsiaKurang" => $row['v_usia_kurang'],
            "vMinPendidikan" => $row['v_min_pendidikan'],
            "vSekolah" => $row['v_sekolah'],
            "dataCheck" => $checkScreening,
            "pertanyaan" => $pertanyaanKhusus,
            "waktu_lalu" => $dibuat,
        ];

        $output = [
            "status" => true,
            "message" => "Detail lowongan ditemukan",
            "data" => [$data]
        ];
    } else {
        $output = [
            "status" => false,
            "message" => "Data tidak ditemukan",
            "data" => []
        ];
    }

    echo json_encode($output, JSON_PRETTY_PRINT);
}


if ($func == 'extendJob') {
    $data = array();

    $status = false;
    $message = "Proses memperpanjang masa aktif rekrutment gagal.";

    $tgl = date("Y-m-d H:i:s");
    $rentang_waktu = $_POST['expired_date'];
    $id_req = $_POST['id_req'];

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar
        $get = $conn->prepare("SELECT * FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $get->bind_param("ss", $id_pegawai, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows == 0) {
            throw new Exception("User tidak terdaftar. Silakan untuk login kembali.");
        }

        // Cek limit perpanjangan berdasarkan paket
        $sqlCompany = "SELECT paket FROM koordinator WHERE id_koordinator = ?";
        $stmtCompany = $conn->prepare($sqlCompany);
        $stmtCompany->bind_param("s", $id_koordinator);
        $stmtCompany->execute();
        $resultCompany = $stmtCompany->get_result();
        $stmtCompany->close();

        if ($resultCompany->num_rows == 0) {
            throw new Exception("Data perusahaan tidak ditemukan.");
        }

        $rowCompany = $resultCompany->fetch_assoc();
        $paket = isset($rowCompany['paket']) ? $rowCompany['paket'] : 'C1';

        // Cek jumlah lowongan aktif untuk paket C1
        if ($paket == 'C1') {
            $sqlActiveJobs = "SELECT COUNT(*) as total FROM list_request WHERE status = 'On Proccess' AND id_koordinator = ?";
            $stmtActiveJobs = $conn->prepare($sqlActiveJobs);
            $stmtActiveJobs->bind_param("s", $id_koordinator);
            $stmtActiveJobs->execute();
            $resultActiveJobs = $stmtActiveJobs->get_result();
            $stmtActiveJobs->close();
            
            $activeJobsCount = $resultActiveJobs->fetch_assoc()['total'];
            
            if ($activeJobsCount >= 3) {
                throw new Exception("Tidak dapat memperpanjang lowongan. Akun Free");
            }
        }

        // pastikan request job posting terdaftar
        $get = $conn->prepare("SELECT * FROM list_request WHERE id_req = ? AND id_koordinator = ?");
        $get->bind_param("ss", $id_req, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows == 0) {
            throw new Exception("Rekrutmen tidak terdaftar.");
        } else {
            $row = mysqli_fetch_array($result);
            if ($row['status'] != 'Selesai') {
                throw new Exception("Proses memperpanjang masa aktif rekrutment gagal. Status rekrutmen masih dalam proses.");
            }
        }

        // update status rekrutment
        $expired_date = date("Y-m-d", strtotime(' + ' . $rentang_waktu . ' days')) . " 00:00:00";
        $update = $conn->prepare("UPDATE list_request SET `status` = 'On Proccess', expired_date = '$expired_date', update_at = '$tgl' WHERE id_req = ? AND id_koordinator = ? AND `status` = 'Selesai'");
        $update->bind_param("ss", $id_req, $id_koordinator);
        if ($update->execute()) {
            $update->close();

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            //insert log aktifitas
            $messages = 'Melakukan proses masa perpanjangan masa aktif rekrutmen ' . $id_req . ' sampai dengan tanggal ' . $expired_date . '.';


            $status = true;
            $message = "Proses perpanjang masa aktif rekrutment berhasil.";
        } else {
            throw new Exception("Proses memperpanjang masa aktif rekrutment gagal.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = false;
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'tutupLowongan') {
    $data = array();

    $status = false;
    $message = "Proses tutup rekrutment gagal.";

    $id_req = $_POST['id_req'];
    $tgl = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar
        $get = $conn->prepare("SELECT * FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $get->bind_param("ss", $id_pegawai, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows == 0) {
            throw new Exception("User tidak terdaftar. Silakan untuk login kembali.");
        }

        // pastikan request job posting terdaftar
        $get = $conn->prepare("SELECT * FROM list_request WHERE id_req = ? AND id_koordinator = ?");
        $get->bind_param("ss", $id_req, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows == 0) {
            throw new Exception("Rekrutmen tidak terdaftar.");
        }

        // update status rekrutment
        $update = $conn->prepare("UPDATE list_request SET `status` = 'Selesai', update_at = '$tgl' WHERE id_req = ? AND id_koordinator = ?");
        $update->bind_param("ss", $id_req, $id_koordinator);
        if ($update->execute()) {
            $update->close();

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            //insert log aktifitas
            $messages = 'Melakukan proses tutup rekrutmen ' . $id_req . '.';

            $status = true;
            $message = "Proses tutup rekrutment berhasil.";
        } else {
            throw new Exception("Proses tutup rekrutment gagal.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = false;
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'getJurusan') {
    $pendidikan = isset($_GET['pendidikan']) ? $_GET['pendidikan'] : '';
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2

    $sekolah = in_array($pendidikan, ['D1', 'D2', 'D3', 'D4']) ? 'Diploma' : $pendidikan;

    $sql = "SELECT id, jurusan FROM jurusan_sekolah WHERE sekolah = ?";
    if ($search != "") {
        $sql = $sql . " AND jurusan LIKE ? ORDER BY FIELD(jurusan, 'Semua Jurusan') LIMIT 15";

        $stmt = $conn->prepare($sql);
        $searchTerm = "%$search%";
        $stmt->bind_param("ss", $sekolah, $searchTerm);
    } else {
        $sql = $sql . " LIMIT 15";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $sekolah);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $jurusanList = [];
    while ($row = $result->fetch_assoc()) {
        $jurusanList[] = ['id' => $row['jurusan'], 'text' => $row['jurusan']];
    }

    $data['status'] = true;
    $data['message'] = "Berhasil menampilkan data jurusan!";
    $data['data'] = $jurusanList;

    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'getPosisi') {
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2

    $sql = "SELECT id, posisi FROM list_request WHERE posisi LIKE ? GROUP BY posisi LIMIT 15";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("s", $searchTerm);
    $stmt->execute();
    $result = $stmt->get_result();

    $jurusanList = [];
    while ($row = $result->fetch_assoc()) {
        $jurusanList[] = ['id' => $row['posisi'], 'text' => $row['posisi']];
    }


    $data['status'] = true;
    $data['message'] = "Berhasil menampilkan data posisi!";
    $data['data'] = $jurusanList;

    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'getSekolah') {
    $pendidikan = isset($_GET['pendidikan']) ? $_GET['pendidikan'] : '';
    $search = isset($_GET['q']) ? $_GET['q'] : ''; // Kata kunci pencarian dari Select2

    $jenjang = in_array($pendidikan, ['D1', 'D2', 'D3', 'D4', 'S1', 'S2', 'S3']) ? 'Universitas' : $pendidikan;

    $sql = "SELECT id, nama FROM list_sekolah WHERE jenjang = ? AND nama LIKE ? ORDER BY FIELD(nama, 'Semua Sekolah') DESC LIMIT 15";
    $stmt = $conn->prepare($sql);
    $searchTerm = "%$search%";
    $stmt->bind_param("ss", $jenjang, $searchTerm);
    $stmt->execute();
    $result = $stmt->get_result();

    $sekolah = [];
    while ($row = $result->fetch_assoc()) {
        $sekolah[] = ['id' => $row['nama'], 'text' => $row['nama']];
    }

    $data['status'] = true;
    $data['message'] = "Berhasil menampilkan data sekolah!";
    $data['data'] = $sekolah;

    echo json_encode($data, JSON_PRETTY_PRINT);
}

$conn->close();
exit;
