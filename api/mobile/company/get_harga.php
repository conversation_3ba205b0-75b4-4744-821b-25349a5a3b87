<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

$userData = requireAuth();
$data = [];
$func = $_GET['func'];
$status = 'gagal';
$message = 'gagal mengambil data';

if ($func == 'getHargaPsikotes') {
    $paket = 'Psikotes';
    $sqlData = "SELECT price, keterangan FROM pricing where paket = ?";
    $get = $conn->prepare($sqlData);
    $get->bind_param("s", $paket);

    $get->execute();
    $result = $get->get_result();
    $get->close();
    if ($result->num_rows > 0) {
        $status = 'success';
        $message = 'Berhasil mengambil data';
        $row = mysqli_fetch_assoc($result); // Ambil sekali saja

        $harga = (int) $row['price'];
        $keterangan = $row['keterangan'];

        $data = [
            "harga" => $harga,
            "keterangan" => $keterangan
        ];
    } else {
        $status = 'gagal';
        $message = 'gagal mengambil data';
    }
}

if ($func == 'getHargaPencarianKandidat') {
    $paket = 'Cari Kandidat';
    $sqlData = "SELECT price, keterangan  FROM pricing where paket = ?";
    $get = $conn->prepare($sqlData);
    $get->bind_param("s", $paket);

    $get->execute();
    $result = $get->get_result();
    $get->close();
    if ($result->num_rows > 0) {
        $status = 'success';
        $message = 'Berhasil mengambil data';
        $row = mysqli_fetch_assoc($result); // Ambil sekali saja

        $harga = (int) $row['price'];
        $keterangan = $row['keterangan'];

        $data = [
            "harga" => $harga,
            "keterangan" => $keterangan
        ];
    } else {
        $status = 'gagal';
        $message = 'gagal mengambil data';
    }
}

$response = [
    "status" => $status,
    "message" => $message,
    "data" => $data
];


echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
exit;
