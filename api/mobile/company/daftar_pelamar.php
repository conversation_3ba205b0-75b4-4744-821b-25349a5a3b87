<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
require '../../fcm_helper.php';
include '../../jwt_helper.php';

// Import AWS Exception
use Aws\Exception\AwsException;

// Verify JWT token and get user data
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi ?? "";
$id_pegawai = $userData->id ?? "";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function get_email_from_lamar($id_lamar, $conn)
{
    $sql = "SELECT
                uk.email
            FROM
                `users_lamar`  ul
                JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
            WHERE
                id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $email = $row['email'];
    } else {
        $email = null;
    }
    return $email;
}

function get_posisi($id_req, $conn)
{
    $sql = "SELECT posisi FROM list_request WHERE id_req = '$id_req'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $posisi = $row['posisi'];
    } else {
        $posisi = null;
    }
    return $posisi;
}

function get_nama_kandidat($id_lamar, $conn)
{
    $sql = "SELECT
        uk.nama_lengkap
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $nama_lengkap = $row['nama_lengkap'];
    } else {
        $nama_lengkap = null;
    }
    return $nama_lengkap;
}

function update_proses_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "On Proccess Digitalcv";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "On Proccess Interview";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "On Proccess Psikotes";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "On Proccess Validasi";
    } elseif ($text == 'VALIDASI') {
        $status_update = "On Proccess Offering";
    } elseif ($text == 'OFFERING') {
        $status_update = "On Proccess Medical";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Sudah Diterima";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_close_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_terima_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Terima Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Terima Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Terima Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Terima Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Terima Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Terima Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Terima Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Terima Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_tolak_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Tolak Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function get_pin($id_lamar, $conn)
{
    $sql = "SELECT
        uk.pin
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $pin = $row['pin'];
    } else {
        $pin = null;
    }
    return $pin;
}

function get_company($company, $conn)
{
    $data = [];
    $sql = "SELECT * FROM koordinator WHERE id_koordinator = '$company'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $data['nama_perusahaan'] = $row['label'];
        $data['img'] = $row['img'];
        $data['alamat'] = $row['alamat'];
    } else {
        $data = null;
    }
    return $data;
}


function cek_feedback_hrd($conn, $id_lamar)
{
    //cek review = HRD
    $sql = "SELECT approval FROM feedback WHERE id_lamar = '$id_lamar' AND review = 'HRD'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['approval'];
}

function getInitials($name)
{
    $words = explode(' ', strtoupper($name));
    $initials = '';
    foreach ($words as $w) {
        if ($w !== '') {
            $initials .= $w[0];
        }
    }
    return substr($initials, 0, 2); // maksimal 2 huruf
}

function selisihTahunBulan($tanggal_awal, $tanggal_akhir)
{
    $tanggal_awal = DateTime::createFromFormat('m-Y', $tanggal_awal);
    $tanggal_awal = $tanggal_awal->format('Y-m-d');

    $tanggal_akhir = DateTime::createFromFormat('m-Y', $tanggal_akhir);
    $tanggal_akhir = $tanggal_akhir->format('Y-m-d');

    $awal  = new DateTime($tanggal_awal);
    $akhir = new DateTime($tanggal_akhir);

    if ($awal > $akhir) {
        // Tukar jika tanggal awal lebih besar dari akhir
        $temp = $awal;
        $awal = $akhir;
        $akhir = $temp;
    }

    $selisih = $awal->diff($akhir);

    $tahun = $selisih->y;
    $bulan = $selisih->m;

    $output = "";

    if ($tahun > 0) {
        $output .= $tahun . " Tahun";
    }

    if ($bulan > 0) {
        if ($output !== "") {
            $output .= " ";
        }
        $output .= $bulan . " Bulan";
    }

    return $output === "" ? "0 Bulan" : $output;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

function decrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $data = strtr($data, '-_,', '+/=');
    $decoded = base64_decode($data);
    $iv = substr($decoded, 0, 16);
    $encrypted = substr($decoded, 16);
    return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
}

// fungsi untuk validasi password
function validatePassword($password)
{
    $errors = [];

    // Cek minimal 8 karakter
    if (strlen($password) < 8) {
        $errors[] = "Password minimal 8 karakter";
    }

    // Cek mengandung minimal 1 huruf besar
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 huruf besar";
    }

    // Cek mengandung minimal 1 angka
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 angka";
    }

    // Cek mengandung minimal 1 karakter khusus
    if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 karakter khusus (!@#$%^&*()_+-=[]{}|;:,.<>?)";
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}


if (!function_exists('ProsesScreening')) {
    // function buat pincode
    function ProsesScreening($conn, $pin, $id_req)
    {
        $getRule = $conn->prepare("SELECT * FROM `list_request` where id_req = ?");
        $getRule->bind_param("s", $id_req);
        $getRule->execute();
        $queryRule = $getRule->get_result();
        $getRule->close();
        $rowRule = mysqli_fetch_assoc($queryRule);

        //get data from databse
        $id_koordinator           = $rowRule['id_koordinator'];
        $cekScreening             = explode("|", $rowRule['check_screening']);
        $lokasiKerjaRule          = explode(",", $rowRule['lokasi_kerja']);
        $usiaRule                 = explode("-", $rowRule['k_usia']);
        $minUsia                  = $usiaRule[0];
        $maxUsia                  = $usiaRule[1];
        $jkRule                   = $rowRule['k_jk'];
        $pengalamanTotalRule      = $rowRule['k_pengalaman'];
        $pengalamanSamaRule       = $rowRule['k_pengalaman_sama'];
        $statusRule               = $rowRule['k_status'];
        $pendidikanRule           = $rowRule['k_pendidikan'];
        $jurusanPendidikan        = explode("|", $rowRule['k_jurusan']);
        $sekolahRule              = explode("|", $rowRule['k_sekolah']);
        $simRule                  = explode(",", $rowRule['k_sim']);
        $sistem_kerja             = $rowRule['sistem_kerja'];
        $statusJurusan = '';

        $vPengalaman              = $rowRule['v_pengalaman'];
        $vMinPendidikan           = $rowRule['v_min_pendidikan'];
        $vUsiaLebih               = $rowRule['v_usia_lebih'];
        $vUsiaKurang              = $rowRule['v_usia_kurang'];
        $vJurusan                 = $rowRule['v_jurusan'];
        $vSekolah                 = $rowRule['v_sekolah'];

        $getRule2 = "SELECT * FROM `list_kriteria` WHERE id_req = '$id_req'";
        $queryRule2 = $conn->query($getRule2);
        $rowRule2 = mysqli_fetch_assoc($queryRule2);

        //get data from databasae
        $kkRule       = $rowRule2['kk'];
        $kbRule       = explode(",", $rowRule2['kb']);
        $kmRule       = $rowRule2['km'];
        $kbduRule     = $rowRule2['kbdu'];
        $rlpRule      = explode(',', $rowRule2['rlp']);

        $data = array();
        $statusPendidikan = '';
        $statusPengalaman = '';
        $isUmur = '';

        // get data kandidat
        $get = $conn->prepare("SELECT
                                uk.pin as id_akun,
                                rh.nama as nama_lengkap,
                                rh.tempat_lahir,
                                rh.tgl_lahir,
                                rh.kota as kota_domisili,
                                rh.kecamatan as kec_domisili,
                                rh.pendidikan_terakhir,
                                IF(rp.jurusan IS NULL,'', GROUP_CONCAT(rp.jurusan)) as jurusan,
                                IF(rp.nama_sekolah IS NULL,'', GROUP_CONCAT(rp.nama_sekolah)) as nama_sekolah,
                                rh.status_pernikahan as status_marital,
                                rh.jenis_kelamin as gender,
                                rh.sim,
                                rh.lama_pengalaman_kerja as total_pengalaman,
                                rh.lama_posisi_kerja as total_pengalama_sama,
                                rh.minat_lokasi_kerja as penempatan,
                                rh.ilmu_komputerisasi as skill_komputer,
                                IF(pb.bahasa IS NULL,'',pb.bahasa) as skill_bahasa,
                                rh.memimpin_tim as skill_pimpin,
                                rh.kemampuan_presentasi as skill_public,
                                rh.lingkup_pekerjaan as skill_lingkup_pekerjaan,
                                rh.minat_lokasi_kerja as lokasi_kerja,
                                rh.perjalanan_dinas as kebutuhan_traveling
                            FROM
                                users_kandidat uk
                                JOIN rh ON rh.id = uk.pin
                                LEFT JOIN (SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')) as rp ON rp.id = uk.pin
                                LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE id = ? GROUP BY id) pb ON pb.id = uk.pin
                            WHERE
                                uk.pin = ?");
        $get->bind_param("sss", $pin, $pin, $pin);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $poinScreening = 0;
        $poinValidasi = 0;

        while ($row = mysqli_fetch_array($result)) {
            $namaLengkap        = $row['nama_lengkap'];
            $tgl_lahir          = $row['tgl_lahir'];
            $kota_domisili      = $row['kota_domisili'];
            $pendidkan_terakhir = $row['pendidikan_terakhir'];
            $jurusan            = explode(",", $row['jurusan']);
            $nama_sekolah       = explode(",", $row['nama_sekolah']);
            $status_marital     = $row['status_marital'];
            $gender             = $row['gender'];
            $sim                = explode(",", $row['sim']);
            $lokasi             = explode("|", $row['penempatan']);
            $total_pengalaman   = $row['total_pengalaman'];
            $skill_komputer     = $row['skill_komputer'];
            $skill_bahasa       = explode(",", $row['skill_bahasa']);
            $skill_pimpin       = $row['skill_pimpin'];
            $skill_public       = $row['skill_public'];
            $skill_lingkup      = explode(",", $row['skill_lingkup_pekerjaan']);
            $namaAkun           = $row['nama_lengkap'];
            $lokasi_kerja       = $row['lokasi_kerja'];
            $kebutuhan_traveling = $row['kebutuhan_traveling'];

            $data_riwayat_screening = array();

            for ($i = 0; $i < count($cekScreening); $i++) {
                $arrValidasi = $cekScreening[$i];
                $poin_validasi = 1;
                $poin_screening = 0;

                // cek screening untuk kebutuhan traveling
                if ($arrValidasi == 'Aktifitas Kerja') {
                    $poinValidasi++;

                    if ($kebutuhan_traveling == 'Ya') {
                        $poinScreening++;
                        $poin_screening = 1;
                    } elseif ($sistem_kerja == $kebutuhan_traveling) {
                        $poinScreening++;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Aktifitas Kerja', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk kebutuhan traveling

                // cek screening untuk lokasi kerja
                if ($arrValidasi == 'Lokasi Kerja') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($lokasi_kerja == 'DIMANA SAJA') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $resultLokasi = array();
                        //valdasi lokasi kerja
                        for ($k = 0; $k < count($lokasiKerjaRule); $k++) {
                            $arrLokasiKerja = $lokasiKerjaRule[$k];

                            for ($j = 0; $j < count($lokasi); $j++) {
                                $arrPenempatan = $lokasi[$j];

                                if ($arrLokasiKerja == $arrPenempatan) {
                                    $resultLokasi[] = "Yes";
                                } else {
                                    $resultLokasi[] = "No";
                                }
                            }
                        }

                        if (in_array("Yes", $resultLokasi)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Lokasi Kerja', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk lokasi kerja

                // cek screening untuk umur
                if ($arrValidasi == 'Umur Kandidat') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi Usia
                    $dateLahir = new DateTime($tgl_lahir);
                    $dateToday = new DateTime();
                    $diff = $dateToday->diff($dateLahir);
                    $umurKandidat = $diff->y;

                    if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                        $isUmur = 'true1';
                    } else if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Tidak Izinkan') {
                        if ($minUsia <= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true2';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false2';
                        }
                    } else if ($vUsiaLebih == 'Tidak Izinkan' && $vUsiaKurang == 'Izinkan') {
                        if ($maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true3';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false3';
                        }
                    } else {
                        if ($minUsia <= $umurKandidat && $maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true4';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false4';
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Umur Kandidat', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk umur

                // cek screening untuk jenis kelamin
                if ($arrValidasi == 'Jenis Kelamin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi gender
                    if ($jkRule == 'Keduanya' || $jkRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($jkRule == 'Laki-Laki') {
                        if ($gender == 'Laki-Laki') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($jkRule == 'Perempuan') {
                        if ($gender == 'Perempuan') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jenis Kelamin', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk jenis kelamin

                // cek screening untuk status pernikahan
                if ($arrValidasi == 'Status Pernikahan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi status
                    if ($statusRule == 'Tidak' || $statusRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($statusRule == 'Belum Menikah') {
                        if ($status_marital == 'Belum Menikah') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($statusRule == 'Menikah') {
                        if ($status_marital == 'Menikah' || $status_marital == 'Duda' || $status_marital == 'Janda') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Status Pernikahan', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk status pernikahan

                // cek screening untuk bahasa yang dikuasai
                if ($arrValidasi == 'Bahasa yang dikuasai') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi skill bahasa
                    $resultBahasa = array();
                    for ($h = 0; $h < count($kbRule); $h++) {
                        $arrKriteriaKb = $kbRule[$h];

                        for ($l = 0; $l < count($skill_bahasa); $l++) {
                            $arrskillBahasa = $skill_bahasa[$l];

                            if ($arrKriteriaKb == $arrskillBahasa) {
                                $resultBahasa[] = "Yes";
                            } else {
                                $resultBahasa[] = "No";
                            }
                        }
                    }

                    if (in_array("Yes", $resultBahasa)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Bahasa yang dikuasai', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk bahasa yang dikuasai

                // cek screening untuk sim
                if ($arrValidasi == 'SIM') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi SIM
                    if (in_array('Tidak Ada', $simRule)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $countyes = 0;
                        for ($j = 0; $j < count($sim); $j++) {
                            $arrsim = $sim[$j];

                            if (in_array($arrsim, $simRule)) {
                                $countyes++;
                            }
                        }

                        if ($countyes == count($simRule)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $poinScreening += 0;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'SIM', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk sim

                // cek screening untuk pendidikan
                if ($arrValidasi == 'Pendidikan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi pendidikan
                    if ($vMinPendidikan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if ($pendidikanRule == 'SD') {
                            if ($pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMP') {
                            if ($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMA' || $pendidikanRule == 'SMK') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'Diploma') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S1') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S2') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S3') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1' && $pendidkan_terakhir != 'S2') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Pendidikan', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk pendidikan

                // cek screening untuk jurusan pendidikan
                if ($arrValidasi == 'Jurusan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi jurusan
                    if ($vJurusan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if (in_array('Semua Jurusan', $jurusanPendidikan)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $temp_arr_jurusan = array_intersect($jurusanPendidikan, $jurusan);

                            if (!empty($temp_arr_jurusan)) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jurusan', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk jurusan pendidikan

                // cek screening untuk nama sekolah
                if ($arrValidasi == 'Sekolah') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi sekolah
                    if ($vSekolah == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if (in_array('Semua Sekolah', $sekolahRule)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $temp_arr_sekolah = array_intersect($sekolahRule, $nama_sekolah);
                            if (!empty($temp_arr_sekolah)) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Sekolah', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk nama sekolah

                // cek screening untuk pengalaman
                if ($arrValidasi == 'Pengalaman') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($total_pengalaman >= $pengalamanTotalRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Pengalaman', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk pengalaman

                // cek screening untuk Kemampuan Komputer
                if ($arrValidasi == 'Kemampuan Komputer') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi skill komputer
                    if ($kkRule == 6) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if ($skill_komputer < 6) {
                            if ($skill_komputer >= $kkRule) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Komputer', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Komputer

                // cek screening untuk Kemampuan Memimpin
                if ($arrValidasi == 'Kemampuan Memimpin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_pimpin >= $kmRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Memimpin', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Memimpin

                // cek screening untuk Kemampuan berbicara didepan umum
                if ($arrValidasi == 'Kemampuan berbicara didepan umum') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_public >= $kbduRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan berbicara didepan umum', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // cek screening untuk Kemampuan berbicara didepan umum

                // cek screening untuk Ruang lingkup pekerjaan
                if ($arrValidasi == 'Ruang lingkup pekerjaan' && count($rlpRule) > 0) {
                    $poinValidasi += count($rlpRule);
                    $poin_validasi = count($rlpRule);
                    $poin_screening = 0;

                    //validasi skill komputer
                    $temp_arr_rlp = array_intersect($rlpRule, $skill_lingkup);

                    if (count($temp_arr_rlp) > 0) {
                        $poinScreening += count($temp_arr_rlp);
                        $poin_screening += count($temp_arr_rlp);
                    }

                    $data_riwayat_screening[] = array('title' => 'Ruang lingkup pekerjaan', 'poin' => $poin_screening . '|' . $poin_validasi);
                }
                // end cek screening untuk Ruang lingkup pekerjaan
            }
        }

        $presentase = round(($poinScreening / $poinValidasi) * 100);

        if ($presentase < 0) {
            $presentase = 0;
        }

        $data = array();
        $data['persentase'] = $presentase;
        $data['hasil_screening'] = $data_riwayat_screening;

        return $data;
    }
}

// Note: id_koordinator, divisi, dan id_pegawai sudah diambil dari JWT token di bagian atas
// Data user yang terverifikasi melalui JWT:
// - id_koordinator: {$id_koordinator}
// - divisi: {$divisi} 
// - id_pegawai: {$id_pegawai}

// Override id_pegawai jika ada di POST (untuk kompatibilitas)
// if (isset($_POST['id'])) {
//     $id_pegawai = $_POST['id'];
// }

if (isset($_POST['akses'])) {
    $akses = $_POST['akses'];
} else {
    $akses = "";
}

$func = $_GET['func'];

if ($func == 'getDataPelamar') {
    header('Content-Type: application/json');
    $data = [];

    // Menggunakan id_koordinator dari JWT token, bukan dari $_GET
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $pageSize = isset($_GET['page_size']) ? (int) $_GET['page_size'] : 5;
    $offset = ($page - 1) * $pageSize;

    // Tambahkan parameter search
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : "";
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch = " AND a.posisi LIKE '%$q%'";
    }

    // Query dengan pagination dan search
    $sqlData = $conn->prepare("SELECT
                    a.id_req,
                    a.posisi,
                    sum(if(b.`status`='On Proccess Digitalcv',1,0)) as jumlah,
                    a.create_at
                FROM
                    list_request a
                    LEFT JOIN users_lamar b ON b.id_req = a.id_req
                WHERE
                    a.id_koordinator = ?
                    AND a.`status` = 'On Proccess'
                    $querySearch
                GROUP BY
                    a.id_req
                ORDER BY a.posisi
                LIMIT ?, ?");
    $sqlData->bind_param("sii", $id_koordinator, $offset, $pageSize);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    while ($row = mysqli_fetch_assoc($queryData)) {
        $data[] = [
            'id_req' => $row['id_req'],
            'posisi' => $row['posisi'],
            'jumlah_pelamar' => (int) $row['jumlah'],
            'waktu_lalu' => waktuLalu($row['create_at'])
        ];
    }

    // Hitung total data dengan search
    $sqlTotal = $conn->prepare("SELECT COUNT(*) as total FROM (
                    SELECT a.id_req
                    FROM list_request a
                    LEFT JOIN users_lamar b ON b.id_req = a.id_req
                    WHERE a.id_koordinator = ?
                    AND a.status = 'On Proccess'
                    $querySearch
                    GROUP BY a.id_req
                ) AS sub");
    $sqlTotal->bind_param("s", $id_koordinator);
    $sqlTotal->execute();
    $resultTotal = $sqlTotal->get_result();
    $sqlTotal->close();

    $totalRow = mysqli_fetch_array($resultTotal);
    $totalData = (int) $totalRow['total'];
    $totalPage = ceil($totalData / $pageSize);

    // Output JSON response
    $response = [
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

if ($func == 'getListRequestPelamar') {
    header('Content-Type: application/json');

    $data = array();
    // Menggunakan id_koordinator dari JWT token, bukan dari $_GET
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $page_size = isset($_GET['page_size']) ? max(1, (int)$_GET['page_size']) : 5;
    $offset = ($page - 1) * $page_size;

    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch .= " AND (
            a.posisi LIKE '%$q%' 
        )";
    }

    // Hitung total data
    $sqlTotal = $conn->prepare("SELECT COUNT(DISTINCT a.id_req) AS total
                 FROM list_request a
                 WHERE a.status != '' AND a.id_req != '' AND a.id_koordinator = ? $querySearch");
    $sqlTotal->bind_param("s", $id_koordinator);
    $sqlTotal->execute();
    $resultTotal = $sqlTotal->get_result();
    $sqlTotal->close();

    $rowTotal = mysqli_fetch_array($resultTotal);
    $total_data = (int) $rowTotal['total'];
    $total_page = ceil($total_data / $page_size);

    // Ambil data dengan paginasi
    $sqlData = $conn->prepare("SELECT
                    a.id_req,
                    a.posisi,
                    a.create_at,
                    COUNT(ul.id_gestalt) as jmlh_kandidat
                FROM list_request a
                LEFT JOIN users_lamar ul ON a.id_req = ul.id_req
                WHERE a.`status` = 'On Proccess'
                    AND a.id_req != '' 
                    AND a.id_koordinator = ?
                    $querySearch
                GROUP BY a.id_req
                ORDER BY a.status, a.create_at DESC
                LIMIT ? OFFSET ?");
    $sqlData->bind_param("sii", $id_koordinator, $page_size, $offset);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    while ($row = mysqli_fetch_assoc($queryData)) {
        $data[] = [
            'id_req' => $row['id_req'],
            'posisi' => $row['posisi'],
            'tanggal_dibuat' => $row['create_at'],
            'jumlah_kandidat' => (int)$row['jmlh_kandidat']
        ];
    }

    // Output JSON standar
    $output = [
        'status' => true,
        'message' => 'Berhasil mengambil data',
        'data' => $data,
        'page' => $page,
        'page_size' => $page_size,
        'total_page' => $total_page,
        'total_data' => $total_data
    ];

    echo json_encode($output, JSON_PRETTY_PRINT);
}


if ($func == 'getCVKandidat') {
    header('Content-Type: application/json');

    $data = [];
    // Menggunakan id_koordinator dari JWT token, bukan dari $_GET
    $flag = isset($_GET['flag']) ? $_GET['flag'] : '';

    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $pageSize = isset($_GET['page_size']) ? (int) $_GET['page_size'] : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $whereSearch = "";
    $havingSearch = "";
    if (!empty($q)) {
        $whereSearch .= " AND (
            rh.nama LIKE '%$q%' OR 
            lr.posisi LIKE '%$q%' OR 
            ul.status LIKE '%$q%' OR 
            rh.pendidikan_terakhir LIKE '%$q%'
        )";
    }

    $id_req = '';
    if (isset($_GET['id_req'])) {
        $id_req = " AND ul.id_req = '" . addslashes(base64_decode($_GET['id_req'])) . "' ";
    }

    // Query data dengan LIMIT
    $sqlData = $conn->prepare("SELECT
                    ul.id_lamar,
                    rh.id,
                    rh.nama,
                    GROUP_CONCAT(DISTINCT lr.posisi) as posisi_lamar,
                    IF(study.id IS NOT NULL, study.jenjang, rh.pendidikan_terakhir) as pendidikan_terakhir,
                    IF(study.id IS NOT NULL, study.jurusan, '') as jurusan,
                    rh.tgl_lahir,
                    ul.`status` as status_lamar
                FROM
                    users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN rh ON ul.id_gestalt = rh.id
                    LEFT JOIN riwayat_pendidikan study ON study.id = ul.id_gestalt AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                    SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                    FROM riwayat_pendidikan
                    WHERE id = ul.id_gestalt)
                WHERE
                    ul.id_koordinator = ?
                    AND lr.`status` = 'On Proccess'
                    $whereSearch
                    $id_req
                GROUP BY
                    ul.id_gestalt
                ORDER BY rh.nama, lr.posisi, ul.status, rh.pendidikan_terakhir
                LIMIT ?, ?");
    $sqlData->bind_param("sii", $id_koordinator, $offset, $pageSize);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    if ($queryData->num_rows == 0) {
        $response = [
            "status" => false,
            "message" => "Data tidak ditemukan.",
            "data" => [],
        ];
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }

    while ($row = mysqli_fetch_assoc($queryData)) {
        $tgl_lahir = new DateTime($row['tgl_lahir']);
        $sekarang = new DateTime();
        $umur = $sekarang->diff($tgl_lahir)->y;

        $data[] = [
            'id_lamar' => $row['id_lamar'],
            'id' => $row['id'],
            'nama' => $row['nama'],
            'posisi_lamar' => str_replace(",", ", ", $row['posisi_lamar']),
            'pendidikan_terakhir' => $row['pendidikan_terakhir'],
            'jurusan' => $row['jurusan'],
            'umur' => $umur,
            'status_lamar' => $row['status_lamar'],
        ];
    }

    // Total data
    $sqlTotal = $conn->prepare("SELECT COUNT(*) as total FROM (
                    SELECT ul.id_gestalt
                    FROM users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN rh ON ul.id_gestalt = rh.id
                    WHERE ul.id_koordinator = ?
                    $whereSearch
                    $id_req 
                    GROUP BY ul.id_gestalt
                ) as sub");
    $sqlTotal->bind_param("s", $id_koordinator);
    $sqlTotal->execute();
    $resultTotal = $sqlTotal->get_result();
    $sqlTotal->close();

    $totalRow = mysqli_fetch_array($resultTotal);
    $totalData = (int) $totalRow['total'];
    $totalPage = ceil($totalData / $pageSize);

    // Output response
    $response = [
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}


if ($func == 'filterHasilScreening') {
    header('Content-Type: application/json');

    $response = [
        "status" => false,
        "message" => "Data tidak ditemukan.",
        "data" => [],
    ];

    $id_req = isset($_GET['id_req']) ? $_GET['id_req'] : "";


    if ($id_req != "") {

        // Cek apakah lowongan ada dan pelamar tersedia
        $check = $conn->prepare("SELECT lr.id_req FROM list_request lr LEFT JOIN users_lamar ul ON lr.id_req = ul.id_req WHERE lr.id_req = ? GROUP BY lr.id_req");
        $check->bind_param("s", $id_req);
        $check->execute();
        $check_result = $check->get_result();
        $check->close();

        if ($check_result->num_rows > 0) {

            // Ambil data paginated
            $query = $conn->prepare("
                SELECT
                    ul.id_gestalt,
                    rh.perjalanan_dinas AS aktifitas_kerja,
                    rh.minat_lokasi_kerja AS lokasi_kerja,
                    TIMESTAMPDIFF(YEAR, rh.tgl_lahir, CURDATE()) AS umur,
                    rh.jenis_kelamin,
                    rh.status_pernikahan,
                    pb.bahasa,
                    rh.sim,
                    rh.pendidikan_terakhir AS pendidikan,
                    study.jurusan,
                    study.nama_sekolah AS sekolah,
                    rh.lama_pengalaman_kerja AS pengalaman_kerja,
                    rh.ilmu_komputerisasi AS kemampuan_komputer,
                    rh.memimpin_tim AS kemampuan_memimpin,
                    rh.kemampuan_presentasi AS kemampuan_bicara,
                    rh.lingkup_pekerjaan AS rlp
                FROM users_lamar ul
                JOIN rh ON rh.id = ul.id_gestalt
                LEFT JOIN (
                    SELECT id, GROUP_CONCAT(bahasa) AS bahasa FROM penguasaan_bahasa GROUP BY id
                ) pb ON pb.id = ul.id_gestalt
                LEFT JOIN riwayat_pendidikan study ON study.id = ul.id_gestalt
                    AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                        SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                        FROM riwayat_pendidikan
                        WHERE id = ul.id_gestalt
                    )
                WHERE ul.id_req = ?
                AND ul.status = 'On Proccess Digitalcv'
                GROUP BY ul.id_gestalt
            ");
            $query->bind_param("s", $id_req);
            $query->execute();
            $result = $query->get_result();

            $data = [];
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
            $query->close();

            if (!empty($data)) {
                $response['status'] = true;
                $response['message'] = "Data ditemukan.";
                $response['data'] = $data;
            } else {
                $response['status'] = true;
                $response['message'] = "Data Kosong!";
                $response['data'] = [];
            }
        } else {
            $response['status'] = true;
            $response['message'] = "Data List Request Tidak Ditemukan!";
            $response['data'] = [];
        }
    }

    echo json_encode($response, JSON_PRETTY_PRINT);
}


if ($func == 'paginationListPelamar') {

    // Ambil parameter dari query
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $page_size = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
    $start = ($page - 1) * $page_size;


    $id_req = "";
    if (isset($_POST['id_req'])) {
        $temp_id_req = addslashes($_POST['id_req']);
        if ($temp_id_req != "") {
            $id_req = "a.id_req = '" . $temp_id_req . "'";
        }
    }

    $FilterAktifitasKerja = "";
    if (isset($_POST['FilterAktifitasKerja'])) {
        $temp = array();
        if (count($_POST['FilterAktifitasKerja']) > 0) {
            for ($i = 0; $i < count($_POST['FilterAktifitasKerja']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterAktifitasKerja'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterAktifitasKerja = "AND rh.perjalanan_dinas IN (" . $temp . ")";
        }
    }

    $FilterLokasiKeja = "";
    if (isset($_POST['FilterLokasiKeja'])) {
        $temp = array();
        if (count($_POST['FilterLokasiKeja']) > 0) {
            for ($i = 0; $i < count($_POST['FilterLokasiKeja']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterLokasiKeja'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterLokasiKeja = "AND rh.minat_lokasi_kerja IN (" . $temp . ")";
        }
    }

    $FilterUmurKandidat = "";
    if (isset($_POST['FilterUmurKandidat']) && $_POST['FilterUmurKandidat'] != '') {
        $temp = array();
        $umurMin = (int) $_POST['FilterUmurKandidat'][0];
        $umurMax = (int) $_POST['FilterUmurKandidat'][1];

        if ($umurMin == 0 && $umurMax != '') {
            $umurMin = 1;
        }
        if ($umurMax == 0 && $umurMin != '') {
            $umurMax = 80;
        }

        if ($umurMin <= $umurMax && ($umurMin != '' || $umurMax != '')) {
            if (count($_POST['FilterUmurKandidat']) > 0) {
                for ($i = $umurMin; $i <= $umurMax; $i++) {
                    $temp[] = addslashes($i);
                }

                $temp = implode(",", $temp);
                $FilterUmurKandidat = "AND TIMESTAMPDIFF(YEAR, rh.tgl_lahir, CURDATE()) IN (" . $temp . ")";
            }
        }
    }

    $FilterJenisKelamin = "";
    if (isset($_POST['FilterJenisKelamin'])) {
        $temp = array();
        if (count($_POST['FilterJenisKelamin']) > 0) {
            for ($i = 0; $i < count($_POST['FilterJenisKelamin']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterJenisKelamin'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterJenisKelamin = "AND rh.jenis_kelamin IN (" . $temp . ")";
        }
    }

    $FilterStatusPernikahan = "";
    if (isset($_POST['FilterStatusPernikahan'])) {
        $temp = array();
        if (count($_POST['FilterStatusPernikahan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterStatusPernikahan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterStatusPernikahan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterStatusPernikahan = "AND rh.status_pernikahan IN (" . $temp . ")";
        }
    }

    $FilterBahasa = "";
    if (isset($_POST['FilterBahasa'])) {
        $temp = array();
        if (count($_POST['FilterBahasa']) > 0) {
            for ($i = 0; $i < count($_POST['FilterBahasa']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterBahasa'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterBahasa = "JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE bahasa IN (" . $temp . ") GROUP BY id) pb ON pb.id = a.id_gestalt";
        }
    }

    if ($FilterBahasa == "") {
        $FilterBahasa = "LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa GROUP BY id) pb ON pb.id = a.id_gestalt";
    }

    $FilterSIM = "";
    if (isset($_POST['FilterSIM'])) {
        $temp = array();
        if (count($_POST['FilterSIM']) > 0) {
            for ($i = 0; $i < count($_POST['FilterSIM']); $i++) {
                $temp[] = "FIND_IN_SET('" . addslashes($_POST['FilterSIM'][$i]) . "', rh.sim) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterSIM = "AND (" . $temp . ")";
        }
    }

    $FilterPendidikan = "";
    if (isset($_POST['FilterPendidikan'])) {
        $temp = array();
        if (count($_POST['FilterPendidikan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterPendidikan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterPendidikan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterPendidikan = "AND rh.pendidikan_terakhir IN (" . $temp . ")";
        }
    }

    $FilterJurusan = "";
    if (isset($_POST['FilterJurusan'])) {
        $temp = array();
        if (count($_POST['FilterJurusan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterJurusan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterJurusan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterJurusan = "WHERE jurusan IN (" . $temp . ")";
        }
    }

    $FilterSekolah = "";
    if (isset($_POST['FilterSekolah'])) {
        $temp = array();
        if (count($_POST['FilterSekolah']) > 0) {
            for ($i = 0; $i < count($_POST['FilterSekolah']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterSekolah'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            if ($FilterJurusan != "") {
                $FilterSekolah = "OR nama_sekolah IN (" . $temp . ")";
            } else {
                $FilterSekolah = "WHERE nama_sekolah IN (" . $temp . ")";
            }
        }
    }

    if ($FilterJurusan != "" || $FilterSekolah != "") {
        $table_riwayat_pendidikan = "JOIN (SELECT id, GROUP_CONCAT(nama_sekolah) as nama_sekolah, GROUP_CONCAT(jurusan) as jurusan FROM riwayat_pendidikan $FilterJurusan $FilterSekolah GROUP BY id) sekolah ON sekolah.id = a.id_gestalt";
    } else {
        $table_riwayat_pendidikan = "LEFT JOIN (SELECT id, GROUP_CONCAT(nama_sekolah) as nama_sekolah, GROUP_CONCAT(jurusan) as jurusan FROM riwayat_pendidikan GROUP BY id) sekolah ON sekolah.id = a.id_gestalt";
    }

    $FilterPengalaman = "";
    if (isset($_POST['FilterPengalaman'])) {
        $temp = array();
        if (count($_POST['FilterPengalaman']) > 0) {
            for ($i = 0; $i < count($_POST['FilterPengalaman']); $i++) {
                $temp[] = addslashes($_POST['FilterPengalaman'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterPengalaman = "AND rh.lama_pengalaman_kerja IN (" . $temp . ")";
        }
    }

    $FilterKemampuanKomputer = "";
    if (isset($_POST['FilterKemampuanKomputer'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanKomputer']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanKomputer']); $i++) {
                $temp[] = "FIND_IN_SET('" . $_POST['FilterKemampuanKomputer'][$i] . "', rh.ilmu_komputerisasi) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterKemampuanKomputer = "AND (" . $temp . ")";
        }
    }

    $FilterKemampuanMemimpin = "";
    if (isset($_POST['FilterKemampuanMemimpin'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanMemimpin']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanMemimpin']); $i++) {
                $temp[] = addslashes($_POST['FilterKemampuanMemimpin'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterKemampuanMemimpin = "AND rh.memimpin_tim IN (" . $temp . ")";
        }
    }

    $FilterKemampuanBicara = "";
    if (isset($_POST['FilterKemampuanBicara'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanBicara']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanBicara']); $i++) {
                $temp[] = addslashes($_POST['FilterKemampuanBicara'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterKemampuanBicara = "AND rh.kemampuan_presentasi IN (" . $temp . ")";
        }
    }

    $FilterRLP = "";
    if (isset($_POST['FilterRLP'])) {
        $temp = array();
        if (count($_POST['FilterRLP']) > 0) {
            for ($i = 0; $i < count($_POST['FilterRLP']); $i++) {
                $temp[] = "FIND_IN_SET('" . addslashes($_POST['FilterRLP'][$i]) . "', rh.lingkup_pekerjaan) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterRLP = "AND (" . $temp . ")";
        }
    }

    // Ambil data dari database
    $sql = "SELECT
                a.*,
                lr.check_screening,
                b.percentase,
                c.nama_lengkap,
                s.penempatan,
                IF(rp.id IS NOT NULL, CONCAT(rp.jabatan,' di ',rp.nama_perusahaan),rh.pengalaman_kerja) as pengalaman_kerja_terakhir,
                IF(rp.id IS NOT NULL,CONCAT(rp.tahun_mulai,'|',rp.tahun_selesai),'') as periode_terakhir_kerja,
                CONCAT(rh.kota,', ',rh.provinsi) as domisili,
                IF(study.id IS NOT NULL,CONCAT(study.jenjang,' ',study.jurusan,', ',study.tahun_selesai),'') as riwayat_pendidikan_terakhir,
                rs.tipe_screening,
                rs.poin_screening,
                lr.sistem_kerja,
                rh.perjalanan_dinas,
                lr.lokasi_kerja,
                rh.minat_lokasi_kerja,
                lr.k_usia as rule_umur,
                lr.v_usia_lebih,
                lr.v_usia_kurang,
                rh.tgl_lahir,
                rh.jenis_kelamin,
                rh.status_pernikahan,
                lk.kb as rule_bahasa,
                IF(pb.id IS NOT NULL,pb.bahasa,'Tidak menguasai bahasa asing') as bahasa,
                lr.k_sim as rule_sim,
                rh.sim,
                lr.k_pendidikan as rule_pendidikan,
                lr.v_min_pendidikan,
                rh.pendidikan_terakhir,
                lr.k_jurusan as rule_jurusan,
                lr.v_jurusan,
                IF(sekolah.id IS NOT NULL,sekolah.jurusan,'') as jurusan,
                lr.k_sekolah as rule_sekolah,
                lr.v_sekolah,
                IF(sekolah.id IS NOT NULL,sekolah.nama_sekolah,'') as nama_sekolah,
                lr.k_pengalaman as rule_lama_pengalaman_kerja,
                rh.pengalaman_kerja,
                rh.lama_pengalaman_kerja,
                lk.km as rule_pemimpin,
                rh.memimpin_tim,
                lk.kbdu as rule_kemampuan_presentasi,
                rh.kemampuan_presentasi,
                lk.kk as rule_kk,
                rh.ilmu_komputerisasi,
                lk.rlp as rule_rlp,
                rh.lingkup_pekerjaan,
                lr.k_khusus,
                c.foto
            FROM
                users_lamar a
                JOIN list_request lr ON a.id_req = lr.id_req AND a.id_koordinator = lr.id_koordinator 
                JOIN list_kriteria lk ON a.id_req = lk.id_req AND a.id_koordinator = lk.id_koordinator 
                JOIN hasil_screening b ON a.id_gestalt = b.id_gestalt AND a.id_req = b.id_req AND a.id_koordinator = b.id_koordinator
                JOIN users_kandidat c ON a.id_gestalt = c.pin
                JOIN screening_recruitment s ON b.id_screening = s.id_screening
                JOIN (SELECT id_screening, id_req, id_gestalt, GROUP_CONCAT(title) as tipe_screening, GROUP_CONCAT(poin_screening) as poin_screening FROM riwayat_screening GROUP BY id_screening) rs ON rs.id_screening = b.id_screening
                JOIN rh ON rh.id = a.id_gestalt
                LEFT JOIN riwayat_pekerjaan rp ON rp.id = a.id_gestalt AND STR_TO_DATE(rp.tahun_selesai, '%m-%Y') = (
                        SELECT MAX(STR_TO_DATE(tahun_selesai, '%m-%Y'))
                        FROM riwayat_pekerjaan
                        WHERE id = a.id_gestalt
                        AND tahun_selesai IS NOT NULL AND tahun_selesai != '')
                LEFT JOIN riwayat_pendidikan study ON study.id = a.id_gestalt AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                        SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                        FROM riwayat_pendidikan
                        WHERE id = a.id_gestalt)
                $FilterBahasa
                $table_riwayat_pendidikan
            WHERE
            $id_req
            AND a.`status` = 'On Proccess Digitalcv'
            $FilterAktifitasKerja
            $FilterLokasiKeja
            $FilterUmurKandidat
            $FilterJenisKelamin
            $FilterStatusPernikahan
            $FilterSIM
            $FilterPendidikan
            $FilterPengalaman
            $FilterKemampuanKomputer
            $FilterKemampuanMemimpin
            $FilterKemampuanBicara
            $FilterRLP
            LIMIT $start,$page_size";
    $result = $conn->query($sql);

    // Hitung total data
    $total_query = "SELECT
                        COUNT(a.id_gestalt) AS total
                    FROM
                        users_lamar a
                        JOIN list_request lr ON a.id_req = lr.id_req AND a.id_koordinator = lr.id_koordinator 
                        JOIN list_kriteria lk ON a.id_req = lk.id_req AND a.id_koordinator = lk.id_koordinator 
                        JOIN hasil_screening b ON a.id_gestalt = b.id_gestalt AND a.id_req = b.id_req AND a.id_koordinator = b.id_koordinator
                        JOIN users_kandidat c ON a.id_gestalt = c.pin
                        JOIN screening_recruitment s ON b.id_screening = s.id_screening
                        JOIN (SELECT id_screening, id_req, id_gestalt, GROUP_CONCAT(title) as tipe_screening, GROUP_CONCAT(poin_screening) as poin_screening FROM riwayat_screening GROUP BY id_screening) rs ON rs.id_screening = b.id_screening
                        JOIN rh ON rh.id = a.id_gestalt
                        LEFT JOIN riwayat_pekerjaan rp ON rp.id = a.id_gestalt AND STR_TO_DATE(rp.tahun_selesai, '%m-%Y') = (
                                SELECT MAX(STR_TO_DATE(tahun_selesai, '%m-%Y'))
                                FROM riwayat_pekerjaan
                                WHERE id = a.id_gestalt
                                AND tahun_selesai IS NOT NULL AND tahun_selesai != '')
                        LEFT JOIN riwayat_pendidikan study ON study.id = a.id_gestalt AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                                SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                                FROM riwayat_pendidikan
                                WHERE id = a.id_gestalt)
                        $FilterBahasa
                        $table_riwayat_pendidikan
                    WHERE
                    $id_req
                    AND a.`status` = 'On Proccess Digitalcv'
                    $FilterAktifitasKerja
                    $FilterLokasiKeja
                    $FilterUmurKandidat
                    $FilterJenisKelamin
                    $FilterStatusPernikahan
                    $FilterSIM
                    $FilterPendidikan
                    $FilterPengalaman
                    $FilterKemampuanKomputer
                    $FilterKemampuanMemimpin
                    $FilterKemampuanBicara
                    $FilterRLP";
    $total_result = $conn->query($total_query);
    $total_row = $total_result->fetch_assoc();
    $total_data = $total_row['total'];
    $total_pages = ceil($total_data / $page_size);

    // Ambil data dan simpan dalam array
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $pin = $row['id_gestalt'];
        $nama_kandidat = $row['nama_lengkap'];
        $inisial = getInitials($nama_kandidat);

        // Ambil hasil screening dari fungsi
        $arr_proses_screening = ProsesScreening($conn, $pin, $_POST['id_req']);
        $percentase = $arr_proses_screening['persentase'];
        $arr_screening = $arr_proses_screening['hasil_screening'];

        // ===============================
        // CEK CHECK SCREENING + FILTER
        // ===============================
        $check_screening = [];
        if (!empty($row['check_screening'])) {
            $check_screening = explode("|", $row['check_screening']);
        }

        $acuan_screening = [
            'Aktifitas Kerja',
            'Lokasi Kerja',
            'Umur Kandidat',
            'Jenis Kelamin',
            'Status Pernikahan',
            'Bahasa yang dikuasai',
            'SIM',
            'Pendidikan',
            'Jurusan',
            'Sekolah',
            'Pengalaman',
            'Kemampuan Memimpin',
            'Kemampuan berbicara didepan umum'
        ];

        $screening = array_values(array_intersect($check_screening, $acuan_screening));

        // ===============================
        // HASIL SCREENING KANDIDAT
        // ===============================
        $tipe_screening = [];
        $poin_screening = [];

        foreach ($arr_screening as $s) {
            $tipe_screening[] = $s['title'];
            $poin_screening[] = explode("|", $s['poin'])[0];
        }

        // hasil_screening array [tipe, poin]
        $hasil_screening_array = array_map(function ($a, $b) {
            return [$a, $b];
        }, $tipe_screening, $poin_screening);

        $nilai_screening = [];
        $jml_cocok_screening = 0;

        foreach ($hasil_screening_array as $hs) {
            $tipe = $hs[0];
            $poin = $hs[1];
            $nilai_screening[$tipe] = $poin;

            if ($poin == 1 && in_array($tipe, $screening)) {
                $jml_cocok_screening++;
            }
        }

        // ===============================
        // FOTO KANDIDAT
        // ===============================
        $url_foto_kandidat = '';
        if (!empty($row['foto'])) {
            $key = 'kandidat/foto-profil/' . $row['foto'];
            if ($s3->doesObjectExist($bucket, $key)) {
                $cmd = $s3->getCommand('GetObject', ['Bucket' => $bucket, 'Key' => $key]);
                $request = $s3->createPresignedRequest($cmd, '+24 hours');
                $url_foto_kandidat = (string) $request->getUri();
            } else {
                $url_foto_kandidat = $row['foto'];
            }
        }

        // ===============================
        // Format hasil screening JSON
        // ===============================
        $hasil_screening = [];
        for ($i = 0; $i < count($tipe_screening); $i++) {
            $hasil_screening[] = [
                'tipe' => $tipe_screening[$i],
                'poin' => (int)$poin_screening[$i]
            ];
        }

        if ($row['periode_terakhir_kerja'] != "") {
            $arr_periode_terakhir_kerja = explode("|", $row['periode_terakhir_kerja']);
            $riwayat_pekerjaan = $row['pengalaman_kerja_terakhir'] . ' (' . selisihTahunBulan($arr_periode_terakhir_kerja[0], $arr_periode_terakhir_kerja[1]) . ')';
        } else {
            if ($row['pengalaman_kerja_terakhir'] == 'Tidak') {
                $riwayat_pekerjaan = '';
            } else {
                $riwayat_pekerjaan = $row['pengalaman_kerja_terakhir'];
            }
        }

        $row['persentase_screening'] = $percentase;
        $row['check_screening'] = $check_screening;
        $row['screening_terpakai'] = $screening;
        $row['hasil_screening'] = $hasil_screening;
        $row['jumlah_screening_cocok'] = $jml_cocok_screening;
        $row['url_foto'] = $url_foto_kandidat;
        $data[] =
            $row
            // tambahkan field lain jika perlu
        ;
    }

    // Format response
    $response = [
        'status' => true,
        'message' => 'Data berhasil diambil',
        'data' => $data,
        'page' => $page,
        'page_size' => $page_size,
        'total_page' => $total_pages,
        'total_data' => $total_data
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
}


//base64encode id_lamar
if ($func == 'terimaKandidat') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
                                kp.id_pic,
                                k.img,
                                k.img_banner
                            FROM
                                koordinator k
                                JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
                            WHERE
                                kp.id_pic = ? 
                                AND k.id_koordinator = ?
                            GROUP BY
                                k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        if (!isset($_POST['id_lamar'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $id_lamar = $_POST['id_lamar'];

        // get data lamar
        $sql = $conn->prepare("SELECT
                                ul.*,
                                lr.posisi
                            FROM
                                users_lamar ul
                                JOIN list_request lr ON ul.id_req = lr.id_req
                            WHERE
                                ul.id_lamar = ?
                                AND ul.id_koordinator = ?");
        $sql->bind_param("ss", $id_lamar, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $row = mysqli_fetch_array($result);
        $id_gestalt = $row['id_gestalt'];
        $id_req = $row['id_req'];
        $posisiLamar = $row['posisi'];
        $status_lamaran = 'Terima Rekrutmen';

        // update status lamaran user
        $update = $conn->prepare("UPDATE users_lamar SET `status` = ? WHERE id_lamar = ? AND id_koordinator = ?");
        $update->bind_param("sss", $status_lamaran, $id_lamar, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan riwayat perubahan status lamaran users
            $insert = $conn->prepare("INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`,`pic`)
                    VALUES (?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssssss", $id_lamar, $id_gestalt, $id_req, $id_koordinator, $status_lamaran, $created_at, $id_pegawai);

            if ($insert->execute()) {
                $insert->close();

                // simpan log aktivitas
                $aktivitas = 'Memilih kandidat ' . $id_gestalt . ' pada rekrutmen lowongan ' . $id_req . '.';
                $pesan_notif = "Selamat Anda lolos rekrutmen untuk posisi " . $posisiLamar . ".";

                // kirim ucapan email terima kasih
                $insert = $conn->prepare("INSERT INTO email_queue (`id_lamar`,`ket`,`status`,`created_at`) VALUES (?, 'Terima Rekrutmen', 'pending', ?)");
                $insert->bind_param("ss", $id_lamar, $created_at);
                if ($insert->execute()) {
                    $insert->close();
                    $res = 'true';
                } else {
                    $res = 'false';
                }

                if ($res == 'true') {
                    $extra_info = "HRD";
                    $level = "INFO";
                    logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

                    // kirim notifikasi, kalo mobile harus pake firebase
                    $id_referensi = $id_lamar;
                    $kirim_notif = $conn->prepare("INSERT INTO `notif_summary`(`pengirim`, `penerima`, `judul`, `isi`, `id_referensi`, `status`, `link`, `create_at`) VALUES (?, ?, 'Pengumuman Hasil Seleksi', ?, ?, 'Dikirim', '', ?)");
                    $kirim_notif->bind_param("sssss", $id_koordinator, $id_gestalt, $pesan_notif, $id_referensi, $created_at);
                    if ($kirim_notif->execute()) {
                        $kirim_notif->close();
                        $apiKeyWS = '57pOr213C&^XM701%(*U4';
                        $urlWS = $baseURL . 'api/WebSocket/send.php';

                        $dataWS = [
                            'to' => urlencode($id_koordinator),
                            'message' => $pesan_notif
                        ];

                        $optionsWS = [
                            'http' => [
                                'method'  => 'POST',
                                'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                                'content' => http_build_query($dataWS)
                            ],
                            'ssl' => [
                                'verify_peer' => false,
                                'verify_peer_name' => false
                            ]
                        ];

                        $contextWS = stream_context_create($optionsWS);
                        file_get_contents($urlWS, false, $contextWS);
                    }
                    $dataPayload = [
                        'tipe' => 'lamaran',
                        'id_req' => $id_req,
                        'id_lamar' => $id_lamar,
                    ];

                    try {
                        $result = sendNotifikasiFromCompany($id_gestalt, "Progress Lamaran Anda", $pesan_notif, $dataPayload, $conn);
                    } catch (Exception $e) {
                        $message = $message . " Gagal mengirim notifikasi: " . $e->getMessage();
                    }

                    $apiKeyWS = '57pOr213C&^XM701%(*U4';
                    $urlWS = $baseURL . 'api/WebSocket/send.php';

                    $dataWS = [
                        'to' => urlencode($id_koordinator),
                        'message' => $pesan_notif
                    ];

                    $optionsWS = [
                        'http' => [
                            'method'  => 'POST',
                            'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                            'content' => http_build_query($dataWS)
                        ],
                        'ssl' => [
                            'verify_peer' => false,
                            'verify_peer_name' => false
                        ]
                    ];

                    $contextWS = stream_context_create($optionsWS);
                    file_get_contents($urlWS, false, $contextWS);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = "Kandidat berhasil diproses.";
                } else {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }
            } else {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'tolakKandidat') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
                                kp.id_pic,
                                k.img,
                                k.img_banner
                            FROM
                                koordinator k
                                JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
                            WHERE
                                kp.id_pic = ? 
                                AND k.id_koordinator = ?
                            GROUP BY
                                k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        if (!isset($_POST['id_lamar']) || !isset($_POST['tahap'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $id_lamar = $_POST['id_lamar'];
        $tahap = $_POST['tahap'];

        // get data lamar
        $sql = $conn->prepare("SELECT
                                ul.*,
                                lr.posisi
                            FROM
                                users_lamar ul
                                JOIN list_request lr ON ul.id_req = lr.id_req
                            WHERE
                                ul.id_lamar = ?
                                AND ul.id_koordinator = ?");
        $sql->bind_param("ss", $id_lamar, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $row = mysqli_fetch_array($result);
        $id_gestalt = $row['id_gestalt'];
        $id_req = $row['id_req'];
        $posisiLamar = $row['posisi'];
        $status_lamaran = 'Tolak Rekrutmen';

        // update status lamaran user
        $update = $conn->prepare("UPDATE users_lamar SET `status` = ? WHERE id_lamar = ? AND id_koordinator = ?");
        $update->bind_param("sss", $status_lamaran, $id_lamar, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan riwayat perubahan status lamaran users
            $insert = $conn->prepare("INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`,`pic`)
                    VALUES (?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssssss", $id_lamar, $id_gestalt, $id_req, $id_koordinator, $status_lamaran, $created_at, $id_pegawai);

            if ($insert->execute()) {
                $insert->close();

                // simpan log aktivitas
                if ($tahap == 'psikotes') {
                    $aktivitas = 'Tidak memilih kandidat ' . $id_gestalt . ' pada tahap psikotes lowongan ' . $id_req . '.';
                    $pesan_notif = "Mohon maaf, Anda tidak lolos pada tahap psikotes untuk posisi " . $posisiLamar . ".";
                } elseif ($tahap == 'screening') {
                    $aktivitas = 'Tidak memilih kandidat ' . $id_gestalt . ' pada tahap screening CV lowongan ' . $id_req . '.';
                    $pesan_notif = "Mohon maaf, Anda tidak lolos pada tahap screening CV untuk posisi " . $posisiLamar . ".";
                } else {
                    $aktivitas = 'Tidak memilih kandidat ' . $id_gestalt . ' pada rekrutmen lowongan ' . $id_req . '.';
                    $pesan_notif = "Mohon maaf, Anda tidak lolos rekrutmen untuk posisi " . $posisiLamar . ".";
                }

                // kirim ucapan email terima kasih
                $insert = $conn->prepare("INSERT INTO email_queue (`id_lamar`,`ket`,`status`,`created_at`) VALUES (?, 'Ucapan Terima Kasih', 'pending', ?)");
                $insert->bind_param("ss", $id_lamar, $created_at);
                if ($insert->execute()) {
                    $insert->close();
                    $res = 'true';
                } else {
                    $res = 'false';
                }

                if ($res == 'true') {
                    $extra_info = "HRD";
                    $level = "INFO";
                    logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

                    // kirim notifikasi
                    $id_referensi = $id_lamar;
                    $kirim_notif = $conn->prepare("INSERT INTO `notif_summary`(`pengirim`, `penerima`, `judul`, `isi`, `id_referensi`, `status`, `link`, `create_at`) VALUES (?, ?, 'Pengumuman Hasil Seleksi', ?, ?, 'Dikirim', '', ?)");
                    $kirim_notif->bind_param("sssss", $id_koordinator, $id_gestalt, $pesan_notif, $id_referensi, $created_at);
                    if ($kirim_notif->execute()) {
                        $kirim_notif->close();
                        $apiKeyWS = '57pOr213C&^XM701%(*U4';
                        $urlWS = $baseURL . 'api/WebSocket/send.php';

                        $dataWS = [
                            'to' => urlencode($id_koordinator),
                            'message' => $pesan_notif
                        ];

                        $optionsWS = [
                            'http' => [
                                'method'  => 'POST',
                                'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                                'content' => http_build_query($dataWS)
                            ],
                            'ssl' => [
                                'verify_peer' => false,
                                'verify_peer_name' => false
                            ]
                        ];

                        $contextWS = stream_context_create($optionsWS);
                        file_get_contents($urlWS, false, $contextWS);
                    }

                    $dataPayload = [
                        'tipe' => 'lamaran',
                        'id_req' => $id_req,
                        'id_lamar' => $id_lamar,
                    ];
                    try {
                        $result = sendNotifikasiFromCompany($id_gestalt, "Progress Lamaran Anda", $pesan_notif, $dataPayload, $conn);
                    } catch (Exception $e) {
                        $message = $message . " Gagal mengirim notifikasi: " . $e->getMessage();
                    }

                    $apiKeyWS = '57pOr213C&^XM701%(*U4';
                    $urlWS = $baseURL . 'api/WebSocket/send.php';

                    $dataWS = [
                        'to' => urlencode($id_koordinator),
                        'message' => $pesan_notif
                    ];

                    $optionsWS = [
                        'http' => [
                            'method'  => 'POST',
                            'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                            'content' => http_build_query($dataWS)
                        ],
                        'ssl' => [
                            'verify_peer' => false,
                            'verify_peer_name' => false
                        ]
                    ];

                    $contextWS = stream_context_create($optionsWS);
                    file_get_contents($urlWS, false, $contextWS);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = "Kandidat berhasil diproses.";
                } else {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }
            } else {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

//base64encode id_lamar|boolean
if ($func == 'submitHasilScreening') {

    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data. Silakan untuk login kembali.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah data ada
        if (!isset($_POST['id_lamar']) || !isset($_POST['ket'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $id_lamar = $_POST['id_lamar'];
        $ket = $_POST['ket'];

        // cek apakah job posting masih tersedia atau tidak
        $get = $conn->prepare("SELECT
                                    ul.*,
                                    uk.nama_lengkap,
                                    uk.email,
                                    lr.posisi,
                                    lr.perusahaan
                                FROM
                                    `users_lamar`  ul
                                    JOIN users_kandidat uk ON uk.pin = ul.id_gestalt
                                    JOIN list_request lr ON ul.id_req = lr.id_req
                                WHERE
                                    ul.id_lamar = ?
                                    AND ul.id_koordinator = ?
                                    AND lr.`status` = 'On Proccess'");
        $get->bind_param("ss", $id_lamar, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Lowongan sudah ditutup.");
        }

        // get data user lamar
        $row = mysqli_fetch_array($result);
        $id_gestalt = $row['id_gestalt'];
        $id_req = $row['id_req'];
        $id_koordinator = $row['id_koordinator'];
        $emailKandidat = $row['email'];
        $namaKandidat = $row['nama_lengkap'];
        $posisiLamar = $row['posisi'];
        $namaPerusahaan = $row['perusahaan'];

        // cek status lamaran users
        if ($ket == 'true') {
            $status_lamaran = 'On Proccess Psikotes';
        } elseif ($ket == 'false') {
            $status_lamaran = 'Tolak Digitalcv';
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // update status lamaran user
        $update = $conn->prepare("UPDATE users_lamar SET `status` = ? WHERE id_lamar = ? AND id_koordinator = ?");
        $update->bind_param("sss", $status_lamaran, $id_lamar, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan riwayat perubahan status lamaran users
            $insert = $conn->prepare("INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`,`pic`)
            VALUES (?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssssss", $id_lamar, $id_gestalt, $id_req, $id_koordinator, $status_lamaran, $created_at, $id_pegawai);

            if ($insert->execute()) {
                $insert->close();

                // simpan log aktivitas
                if ($ket == 'true') {
                    $aktivitas = 'Memilih kandidat ' . $id_gestalt . ' pada tahap screening lowongan ' . $id_req . '.';
                    $pesan_notif = "Selamat Anda lolos tahap seleksi screening CV untuk posisi " . $posisiLamar . ".";

                    $res = 'true';
                } else {
                    $aktivitas = 'Tidak memilih kandidat ' . $id_gestalt . ' pada tahap screening lowongan ' . $id_req . '.';
                    $pesan_notif = "Mohon maaf, Anda tidak lolos seleksi screening CV untuk posisi " . $posisiLamar . ".";

                    // kirim ucapan email terima kasih
                    $insert = $conn->prepare("INSERT INTO email_queue (`id_lamar`,`ket`,`status`,`created_at`) VALUES (?, 'Ucapan Terima Kasih', 'pending', ?)");
                    $insert->bind_param("ss", $id_lamar, $created_at);
                    if ($insert->execute()) {
                        $insert->close();
                        $res = 'true';
                    } else {
                        $res = 'false';
                    }
                }

                if ($res == 'true') {
                    $extra_info = "HRD";
                    $level = "INFO";
                    logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

                    // kirim notifikasi
                    $id_referensi = $id_lamar;
                    $kirim_notif = $conn->prepare("INSERT INTO `notif_summary`(`pengirim`, `penerima`, `judul`, `isi`, `id_referensi`, `status`, `link`, `create_at`) VALUES (?, ?, 'Pengumuman Hasil Seleksi', ?, ?, 'Dikirim', '', ?)");
                    $kirim_notif->bind_param("sssss", $id_koordinator, $id_gestalt, $pesan_notif, $id_referensi, $created_at);
                    if ($kirim_notif->execute()) {
                        $kirim_notif->close();
                        $apiKeyWS = '57pOr213C&^XM701%(*U4';
                        $urlWS = $baseURL . 'api/WebSocket/send.php';

                        $dataWS = [
                            'to' => urlencode($id_koordinator),
                            'message' => $pesan_notif
                        ];

                        $optionsWS = [
                            'http' => [
                                'method'  => 'POST',
                                'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                                'content' => http_build_query($dataWS)
                            ],
                            'ssl' => [
                                'verify_peer' => true,
                                'verify_peer_name' => true
                            ]
                        ];

                        $contextWS = stream_context_create($optionsWS);
                        file_get_contents($urlWS, false, $contextWS);
                    }


                    $dataPayload = [
                        'tipe' => 'lamaran',
                        'id_req' => $id_req,
                        'id_lamar' => $id_lamar,
                    ];
                    try {
                        $result = sendNotifikasiFromCompany($id_gestalt, "Progress Lamaran Anda", $pesan_notif, $dataPayload, $conn);
                    } catch (Exception $e) {
                        $message = $message . " Gagal mengirim notifikasi: " . $e->getMessage();
                    }

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = "Kandidat berhasil diproses.";
                } else {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }
            } else {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'paginationCVUniverse') {


    // Ambil parameter pagination
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $page_size = isset($_GET['page_size']) ? (int) $_GET['page_size'] : 10;
    $offset = ($page - 1) * $page_size;


    $id_req = "";
    $temp_id_req = "";
    if (isset($_POST['id_req'])) {
        $temp_id_req = $_POST['id_req'];
        if ($temp_id_req != "") {
            $id_req = "id_req = '" . addslashes($temp_id_req) . "' AND id_koordinator = '" . $id_koordinator . "'";
        }
    }

    $FilterAktifitasKerja = "";
    if (isset($_POST['FilterAktifitasKerja'])) {
        $temp = array();
        if (count($_POST['FilterAktifitasKerja']) > 0) {
            for ($i = 0; $i < count($_POST['FilterAktifitasKerja']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterAktifitasKerja'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterAktifitasKerja = "AND rh.perjalanan_dinas IN (" . $temp . ")";
        }
    }

    $FilterLokasiKeja = "";
    if (isset($_POST['FilterLokasiKeja'])) {
        $temp = array();
        if (count($_POST['FilterLokasiKeja']) > 0) {
            for ($i = 0; $i < count($_POST['FilterLokasiKeja']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterLokasiKeja'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterLokasiKeja = "AND rh.minat_lokasi_kerja IN (" . $temp . ")";
        }
    }

    $FilterUmurKandidat = "";
    if (isset($_POST['FilterUmurKandidat']) && $_POST['FilterUmurKandidat'] != '') {
        $temp = array();
        $umurMin = (int) $_POST['FilterUmurKandidat'][0];
        $umurMax = (int) $_POST['FilterUmurKandidat'][1];

        if ($umurMin == 0 && $umurMax != '') {
            $umurMin = 1;
        }
        if ($umurMax == 0 && $umurMin != '') {
            $umurMax = 80;
        }

        if ($umurMin <= $umurMax && ($umurMin != '' || $umurMax != '')) {
            if (count($_POST['FilterUmurKandidat']) > 0) {
                for ($i = $umurMin; $i <= $umurMax; $i++) {
                    $temp[] = addslashes($i);
                }

                $temp = implode(",", $temp);
                $FilterUmurKandidat = "AND TIMESTAMPDIFF(YEAR, rh.tgl_lahir, CURDATE()) IN (" . $temp . ")";
            }
        }
    }

    $FilterJenisKelamin = "";
    if (isset($_POST['FilterJenisKelamin'])) {
        $temp = array();
        if (count($_POST['FilterJenisKelamin']) > 0) {
            for ($i = 0; $i < count($_POST['FilterJenisKelamin']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterJenisKelamin'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterJenisKelamin = "AND rh.jenis_kelamin IN (" . $temp . ")";
        }
    }

    $FilterStatusPernikahan = "";
    if (isset($_POST['FilterStatusPernikahan'])) {
        $temp = array();
        if (count($_POST['FilterStatusPernikahan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterStatusPernikahan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterStatusPernikahan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterStatusPernikahan = "AND rh.status_pernikahan IN (" . $temp . ")";
        }
    }

    $FilterBahasa = "";
    if (isset($_POST['FilterBahasa'])) {
        $temp = array();
        if (count($_POST['FilterBahasa']) > 0) {
            for ($i = 0; $i < count($_POST['FilterBahasa']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterBahasa'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterBahasa = "JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE bahasa IN (" . $temp . ") GROUP BY id) pb ON pb.id = rh.id";
        }
    }

    if ($FilterBahasa == "") {
        $FilterBahasa = "LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa GROUP BY id) pb ON pb.id = rh.id";
    }

    $FilterSIM = "";
    if (isset($_POST['FilterSIM'])) {
        $temp = array();
        if (count($_POST['FilterSIM']) > 0) {
            for ($i = 0; $i < count($_POST['FilterSIM']); $i++) {
                $temp[] = "FIND_IN_SET('" . addslashes($_POST['FilterSIM'][$i]) . "', rh.sim) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterSIM = "AND (" . $temp . ")";
        }
    }

    $FilterPendidikan = "";
    if (isset($_POST['FilterPendidikan'])) {
        $temp = array();
        if (count($_POST['FilterPendidikan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterPendidikan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterPendidikan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterPendidikan = "AND rh.pendidikan_terakhir IN (" . $temp . ")";
        }
    }

    $FilterJurusan = "";
    if (isset($_POST['FilterJurusan'])) {
        $temp = array();
        if (count($_POST['FilterJurusan']) > 0) {
            for ($i = 0; $i < count($_POST['FilterJurusan']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterJurusan'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            $FilterJurusan = "WHERE jurusan IN (" . $temp . ")";
        }
    }

    $FilterSekolah = "";
    if (isset($_POST['FilterSekolah'])) {
        $temp = array();
        if (count($_POST['FilterSekolah']) > 0) {
            for ($i = 0; $i < count($_POST['FilterSekolah']); $i++) {
                $temp[] = "'" . addslashes($_POST['FilterSekolah'][$i]) . "'";
            }

            $temp = implode(",", $temp);
            if ($FilterJurusan != "") {
                $FilterSekolah = "OR nama_sekolah IN (" . $temp . ")";
            } else {
                $FilterSekolah = "WHERE nama_sekolah IN (" . $temp . ")";
            }
        }
    }

    if ($FilterJurusan != "" || $FilterSekolah != "") {
        $table_riwayat_pendidikan = "JOIN (SELECT id, GROUP_CONCAT(nama_sekolah) as nama_sekolah, GROUP_CONCAT(jurusan) as jurusan FROM riwayat_pendidikan $FilterJurusan $FilterSekolah GROUP BY id) sekolah ON sekolah.id = rh.id";
    } else {
        $table_riwayat_pendidikan = "LEFT JOIN (SELECT id, GROUP_CONCAT(nama_sekolah) as nama_sekolah, GROUP_CONCAT(jurusan) as jurusan FROM riwayat_pendidikan GROUP BY id) sekolah ON sekolah.id = rh.id";
    }

    $FilterPengalaman = "";
    if (isset($_POST['FilterPengalaman'])) {
        $temp = array();
        if (count($_POST['FilterPengalaman']) > 0) {
            for ($i = 0; $i < count($_POST['FilterPengalaman']); $i++) {
                $temp[] = addslashes($_POST['FilterPengalaman'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterPengalaman = "AND rh.lama_pengalaman_kerja IN (" . $temp . ")";
        }
    }

    $FilterKemampuanKomputer = "";
    if (isset($_POST['FilterKemampuanKomputer'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanKomputer']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanKomputer']); $i++) {
                $temp[] = "FIND_IN_SET('" . addslashes($_POST['FilterKemampuanKomputer'][$i]) . "', rh.ilmu_komputerisasi) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterKemampuanKomputer = "AND (" . $temp . ")";
        }
    }

    $FilterKemampuanMemimpin = "";
    if (isset($_POST['FilterKemampuanMemimpin'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanMemimpin']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanMemimpin']); $i++) {
                $temp[] = addslashes($_POST['FilterKemampuanMemimpin'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterKemampuanMemimpin = "AND rh.memimpin_tim IN (" . $temp . ")";
        }
    }

    $FilterKemampuanBicara = "";
    if (isset($_POST['FilterKemampuanBicara'])) {
        $temp = array();
        if (count($_POST['FilterKemampuanBicara']) > 0) {
            for ($i = 0; $i < count($_POST['FilterKemampuanBicara']); $i++) {
                $temp[] = addslashes($_POST['FilterKemampuanBicara'][$i]);
            }

            $temp = implode(",", $temp);
            $FilterKemampuanBicara = "AND rh.kemampuan_presentasi IN (" . $temp . ")";
        }
    }

    $FilterRLP = "";
    if (isset($_POST['FilterRLP'])) {
        $temp = array();
        if (count($_POST['FilterRLP']) > 0) {
            for ($i = 0; $i < count($_POST['FilterRLP']); $i++) {
                $temp[] = "FIND_IN_SET('" . addslashes($_POST['FilterRLP'][$i]) . "', rh.lingkup_pekerjaan) > 0";
            }

            $temp = implode(" OR ", $temp);
            $FilterRLP = "AND (" . $temp . ")";
        }
    }

    $di = translate('di');
    // Query utama dengan pagination
    $sql = "SELECT
                    rh.`no` as no_urut,
                    rh.id as pin,
                    c.nama_lengkap,
                    IF(rp.id IS NOT NULL, CONCAT(rp.jabatan,' $di ',rp.nama_perusahaan),rh.pengalaman_kerja) as pengalaman_kerja_terakhir,
                    IF(rp.id IS NOT NULL,CONCAT(rp.tahun_mulai,'|',rp.tahun_selesai),'') as periode_terakhir_kerja,
                    CONCAT(rh.kota,', ',rh.provinsi) as domisili,
                    IF(study.id IS NOT NULL,CONCAT(study.jenjang,' ',study.jurusan,', ',study.tahun_selesai),'') as riwayat_pendidikan_terakhir,
                    rh.minat_lokasi_kerja,
                    rh.tgl_lahir,
                    rh.jenis_kelamin,
                    rh.status_pernikahan,
                    IF(pb.id IS NOT NULL,pb.bahasa,'Tidak menguasai bahasa asing') as bahasa,
                    rh.sim,
                    rh.pendidikan_terakhir,
                    IF(sekolah.id IS NOT NULL,sekolah.jurusan,'') as jurusan,
                    IF(sekolah.id IS NOT NULL,sekolah.nama_sekolah,'') as nama_sekolah,
                    rh.pengalaman_kerja,
                    rh.lama_pengalaman_kerja,
                    rh.memimpin_tim,
                    rh.kemampuan_presentasi,
                    rh.ilmu_komputerisasi,
                    rh.lingkup_pekerjaan,
                    c.foto
                FROM
                    rh
                    JOIN users_kandidat c ON rh.id = c.pin
                    LEFT JOIN riwayat_pekerjaan rp ON rp.id = rh.id AND STR_TO_DATE(rp.tahun_selesai, '%m-%Y') = (
                                    SELECT MAX(STR_TO_DATE(tahun_selesai, '%m-%Y'))
                                    FROM riwayat_pekerjaan
                                    WHERE id = rh.id
                                    AND tahun_selesai IS NOT NULL AND tahun_selesai != '')
                    LEFT JOIN riwayat_pendidikan study ON study.id = rh.id AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                                    SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                                    FROM riwayat_pendidikan
                                    WHERE id = rh.id)
                    $FilterBahasa
                    $table_riwayat_pendidikan
                WHERE
                c.visibilitas='1'
                AND rh.id NOT IN (SELECT id_gestalt FROM users_lamar WHERE $id_req AND `status` = 'On Proccess Digitalcv')
                $FilterAktifitasKerja
                $FilterLokasiKeja
                $FilterUmurKandidat
                $FilterJenisKelamin
                $FilterStatusPernikahan
                $FilterSIM
                $FilterPendidikan
                $FilterPengalaman
                $FilterKemampuanKomputer
                $FilterKemampuanMemimpin
                $FilterKemampuanBicara
                $FilterRLP
                ORDER BY rh.`no`";

    $result = $conn->query($sql);

    // get data screening
    $sqlScreening = $conn->prepare("SELECT
                                        lr.check_screening,
                                        lr.lokasi_kerja,
                                        lr.k_usia AS rule_umur,
                                        lr.v_usia_lebih,
                                        lr.v_usia_kurang,
                                        lk.kb AS rule_bahasa,
                                        lr.k_sim AS rule_sim,
                                        lr.k_pendidikan AS rule_pendidikan,
                                        lr.v_min_pendidikan,
                                        lr.k_jurusan AS rule_jurusan,
                                        lr.v_jurusan,
                                        lr.k_sekolah AS rule_sekolah,
                                        lr.v_sekolah,
                                        lr.k_pengalaman AS rule_lama_pengalaman_kerja,
                                        lk.km AS rule_pemimpin,
                                        lk.kbdu AS rule_kemampuan_presentasi,
                                        lk.kk AS rule_kk,
                                        lk.rlp AS rule_rlp,
                                        lr.k_khusus
                                    FROM
                                        list_request lr 
                                        JOIN list_kriteria lk ON lr.id_req = lk.id_req AND lr.id_koordinator = lk.id_koordinator
                                    WHERE
                                        lr.id_req = ?
                                        AND lr.id_koordinator = ?");
    $sqlScreening->bind_param("ss", $temp_id_req, $id_koordinator);
    $sqlScreening->execute();
    $resultScreening = $sqlScreening->get_result();
    $sqlScreening->close();

    $rowScreening = mysqli_fetch_array($resultScreening);

    // Format data
    $data = [];
    if ($result->num_rows > 0 && $resultScreening->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $pin = $row['pin'];
            $nama_kandidat = $row['nama_lengkap'];
            $inisial = getInitials($nama_kandidat);

            // Ambil hasil screening dari fungsi
            $arr_proses_screening = ProsesScreening($conn, $pin, $_POST['id_req']);
            $percentase = $arr_proses_screening['persentase'];
            $arr_screening = $arr_proses_screening['hasil_screening'];

            // ===============================
            // CEK CHECK SCREENING + FILTER
            // ===============================
            $check_screening = [];
            if (!empty($rowScreening['check_screening'])) {
                $check_screening = explode("|", $rowScreening['check_screening']);
            }

            $acuan_screening = [
                'Aktifitas Kerja',
                'Lokasi Kerja',
                'Umur Kandidat',
                'Jenis Kelamin',
                'Status Pernikahan',
                'Bahasa yang dikuasai',
                'SIM',
                'Pendidikan',
                'Jurusan',
                'Sekolah',
                'Pengalaman',
                'Kemampuan Memimpin',
                'Kemampuan berbicara didepan umum'
            ];

            $screening = array_values(array_intersect($check_screening, $acuan_screening));

            // ===============================
            // HASIL SCREENING KANDIDAT
            // ===============================
            $tipe_screening = [];
            $poin_screening = [];

            foreach ($arr_screening as $s) {
                $tipe_screening[] = $s['title'];
                $poin_screening[] = explode("|", $s['poin'])[0];
            }

            // hasil_screening array [tipe, poin]
            $hasil_screening_array = array_map(function ($a, $b) {
                return [$a, $b];
            }, $tipe_screening, $poin_screening);

            $nilai_screening = [];
            $jml_cocok_screening = 0;

            foreach ($hasil_screening_array as $hs) {
                $tipe = $hs[0];
                $poin = $hs[1];
                $nilai_screening[$tipe] = $poin;

                if ($poin == 1 && in_array($tipe, $screening)) {
                    $jml_cocok_screening++;
                }
            }

            // ===============================
            // FOTO KANDIDAT
            // ===============================
            $url_foto_kandidat = '';
            if (!empty($row['foto'])) {
                $key = 'kandidat/foto-profil/' . $row['foto'];
                if ($s3->doesObjectExist($bucket, $key)) {
                    $cmd = $s3->getCommand('GetObject', ['Bucket' => $bucket, 'Key' => $key]);
                    $request = $s3->createPresignedRequest($cmd, '+24 hours');
                    $url_foto_kandidat = (string) $request->getUri();
                } else {
                    $url_foto_kandidat = $row['foto'];
                }
            }

            // ===============================
            // Format hasil screening JSON
            // ===============================
            $hasil_screening = [];
            for ($i = 0; $i < count($tipe_screening); $i++) {
                $hasil_screening[] = [
                    'tipe' => $tipe_screening[$i],
                    'poin' => (int)$poin_screening[$i]
                ];
            }

            if ($row['periode_terakhir_kerja'] != "") {
                $arr_periode_terakhir_kerja = explode("|", $row['periode_terakhir_kerja']);
                $riwayat_pekerjaan = $row['pengalaman_kerja_terakhir'] . ' (' . selisihTahunBulan($arr_periode_terakhir_kerja[0], $arr_periode_terakhir_kerja[1]) . ')';
            } else {
                if ($row['pengalaman_kerja_terakhir'] == 'Tidak') {
                    $riwayat_pekerjaan = '';
                } else {
                    $riwayat_pekerjaan = $row['pengalaman_kerja_terakhir'];
                }
            }

            $row['pengalaman_kerja_terakhir'] = $riwayat_pekerjaan;

            // ===============================
            // Masukkan ke JSON data kandidat
            // ===============================
            $row['persentase_screening'] = $percentase;
            $row['check_screening'] = $check_screening;
            $row['screening_terpakai'] = $screening;
            $row['hasil_screening'] = $hasil_screening;
            $row['jumlah_screening_cocok'] = $jml_cocok_screening;
            $row['url_foto'] = $url_foto_kandidat;
            $row['screening_request'] = [$rowScreening];

            if ($percentase >= 75) {
                $data[] = $row;
            }
        }
    }

    // Calculate total data and total pages from filtered results
    $total_data = count($data);
    $total_page = ceil($total_data / $page_size);

    // Apply pagination to the filtered data
    $data = array_slice($data, ($page - 1) * $page_size, $page_size);

    // Return JSON
    echo json_encode([
        "status" => true,
        "message" => "Data berhasil diambil",
        "data" => $data,
        "page" => $page,
        "page_size" => $page_size,
        "total_page" => $total_page,
        "total_data" => (string) $total_data
    ], JSON_PRETTY_PRINT);
}

if ($func == 'filterCVUniverse') {
    header('Content-Type: application/json');

    $response = [
        "status" => false,
        "message" => "Data tidak ditemukan.",
        "data" => [],
    ];

    if (isset($_GET['id_req'])) {
        $id_req = $_GET['id_req'];

        // cek apakah id lowongan ada dan sudah ada yang melamar
        $get = $conn->prepare("SELECT lr.* FROM list_request lr LEFT JOIN users_lamar ul ON lr.id_req = ul.id_req WHERE lr.id_req = ? AND lr.id_koordinator = ? GROUP BY lr.id_req");
        $get->bind_param("ss", $id_req, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {

            // ambil data filter dengan pagination
            $get = $conn->prepare("
                SELECT
                    rh.id,
                    TIMESTAMPDIFF(YEAR, rh.tgl_lahir, CURDATE()) AS umur,
                    rh.perjalanan_dinas,
                    rh.minat_lokasi_kerja,
                    rh.jenis_kelamin,
                    rh.status_pernikahan,
                    pb.bahasa,
                    rh.sim,
                    rh.pendidikan_terakhir,
                    study.jurusan,
                    study.nama_sekolah,
                    rh.lama_pengalaman_kerja,
                    rh.ilmu_komputerisasi,
                    rh.memimpin_tim,
                    rh.kemampuan_presentasi,
                    rh.lingkup_pekerjaan
                FROM rh
                LEFT JOIN (
                    SELECT id, GROUP_CONCAT(bahasa) as bahasa 
                    FROM penguasaan_bahasa 
                    GROUP BY id
                ) pb ON pb.id = rh.id
                LEFT JOIN riwayat_pendidikan study ON study.id = rh.id
                AND FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') = (
                    SELECT MAX(FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3'))
                    FROM riwayat_pendidikan
                    WHERE id = rh.id
                )
                WHERE rh.id NOT IN (
                    SELECT id_gestalt 
                    FROM users_lamar 
                    WHERE id_req = ? AND id_koordinator = ? AND status = 'On Proccess Digitalcv'
                )
            ");
            $get->bind_param("ss", $id_req, $id_koordinator);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows > 0) {
                $data = [];
                while ($row = $result->fetch_assoc()) {
                    $data[] = [
                        "id_gestalt" => $row['id'],
                        "umur" => $row['umur'],
                        "aktifitas_kerja" => $row['perjalanan_dinas'],
                        "lokasi_kerja" => $row['minat_lokasi_kerja'],
                        "jenis_kelamin" => $row['jenis_kelamin'],
                        "status_pernikahan" => $row['status_pernikahan'],
                        "bahasa" => $row['bahasa'],
                        "sim" => $row['sim'],
                        "pendidikan" => $row['pendidikan_terakhir'],
                        "jurusan" => $row['jurusan'],
                        "sekolah" => $row['nama_sekolah'],
                        "pengalaman_kerja" => $row['lama_pengalaman_kerja'],
                        "kemampuan_komputer" => $row['ilmu_komputerisasi'],
                        "kemampuan_memimpin" => $row['memimpin_tim'],
                        "kemampuan_bicara" => $row['kemampuan_presentasi'],
                        "rlp" => $row['lingkup_pekerjaan']
                    ];
                }

                $response['status'] = true;
                $response['message'] = "Data ditemukan.";
                $response['data'] = $data;
            }
        } else {

            $response['status'] = true;
            $response['message'] = "Data Kosong!";
            $response['data'] = [];
        }
    }

    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'getTimeline') {
    $response = [
        "status" => false,
        "message" => "Data tidak ditemukan.",
        "data" => []
    ];

    if (isset($_GET['id_lamar'])) {
        $id_lamar = base64_decode($_GET['id_lamar']);

        // Ambil paket dari lowongan
        $stmt = $conn->prepare("SELECT k.paket FROM users_lamar ul 
                                JOIN list_request lr ON ul.id_req = lr.id_req 
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                WHERE ul.id_lamar = ? AND ul.id_koordinator = ?");
        $stmt->bind_param("ss", $id_lamar, $id_koordinator);
        $stmt->execute();
        $res = $stmt->get_result();
        $stmt->close();

        if ($res->num_rows > 0) {

            $paket = $res->fetch_assoc()['paket'];

            // Ambil tahapan proses
            $stmt = $conn->prepare("SELECT
                                    IF(t1.`status` IS NULL,'',t1.`status`) as tahap_1,
                                    IF(t2.`status` IS NULL,'',t2.`status`) as tahap_2,
                                    IF(t3.`status` IS NULL,'',t3.`status`) as tahap_3,
                                    IF(t4.`status` IS NULL,'',t4.`status`) as tahap_4,
                                    IF(t1.`tgl` IS NULL,'',t1.`tgl`) as tgl_tahap_1,
                                    IF(t2.`tgl` IS NULL,'',t2.`tgl`) as tgl_tahap_2,
                                    IF(t3.`tgl` IS NULL,'',t3.`tgl`) as tgl_tahap_3,
                                    IF(t4.`tgl` IS NULL,'',t4.`tgl`) as tgl_tahap_4
                                FROM
                                    users_lamar ul
                                    LEFT JOIN users_lamar_history t1 ON ul.id_lamar = t1.id_lamar AND t1.`status` = 'Lamaran Dikirim'
                                    LEFT JOIN users_lamar_history t2 ON ul.id_lamar = t2.id_lamar AND (t2.`status` = 'On Proccess Psikotes' OR t2.`status` = 'Tolak Digitalcv')
                                    LEFT JOIN users_lamar_history t3 ON ul.id_lamar = t3.id_lamar AND t3.`status` = 'Psikotes Gestalt'
                                    LEFT JOIN users_lamar_history t4 ON ul.id_lamar = t4.id_lamar AND (t4.`status` = 'Terima Rekrutmen' OR t4.`status` = 'Tolak Rekrutmen')
                                WHERE
                                    ul.id_lamar = ?");
            $stmt->bind_param("s", $id_lamar);
            $stmt->execute();
            $res = $stmt->get_result();
            $stmt->close();

            if ($res->num_rows > 0) {
                $row = $res->fetch_assoc();

                function safeFormat($tgl)
                {
                    return $tgl ? formatTanggal($tgl) : "";
                }

                $tahapan = [
                    [
                        "tahap" => "tahap_1",
                        "nama" => "Lamaran Dikirim",
                        "status" => $row['tahap_1'] ? "selesai" : "belumtersedia",
                        "tanggal" => $row['tgl_tahap_1'] ? safeFormat($row['tgl_tahap_1']) : "",
                    ],
                    [
                        "tahap" => "tahap_2",
                        "nama" => "Proses Screening CV",
                        "status" => (function () use ($row) {
                            if ($row['tahap_2'] === "On Proccess Psikotes") {
                                return "selesai";
                            } elseif ($row['tahap_2'] === "Tolak Digitalcv") {
                                return "gagal";
                            } elseif ($row['tahap_4'] === "Tolak Rekrutmen") {
                                return "gagal";
                            } elseif ($row['tahap_4'] !== "") {
                                return "selesai";
                            } elseif ($row['tahap_1'] !== "") {
                                return "mendatang";
                            } else {
                                return "belumtersedia";
                            }
                        })(),
                        "tanggal" => (function () use ($row) {
                            if ($row['tahap_2'] === "Tolak Digitalcv") {
                                return "Tidak Lolos";
                            } elseif ($row['tgl_tahap_2'] !== "") {
                                return safeFormat($row['tgl_tahap_2']);
                            } elseif ($row['tahap_4'] !== "") {
                                return safeFormat($row['tgl_tahap_4']);
                            } else {
                                return "";
                            }
                        })(),
                    ],
                    [
                        "tahap" => "tahap_3",
                        "nama" => "Tes Psikotes",
                        "status" => (function () use ($row, $paket) {
                            if ($row['tahap_3'] !== "" && $row['tahap_4'] === "") {
                                return "mendatang";
                            } elseif ($row['tahap_3'] !== "") {
                                return "selesai";
                            } elseif ($row['tahap_4'] !== "") {
                                return ($paket === 'C2' || $paket === 'C3') ? "disembunyikan" : "belumtersedia";
                            } elseif ($row['tahap_2'] !== "" && $row['tahap_2'] !== "Tolak Digitalcv") {
                                return "mendatang";
                            } else {
                                return "belumtersedia";
                            }
                        })(),
                        "tanggal" => $row['tgl_tahap_3'] ? safeFormat($row['tgl_tahap_3']) : "",
                    ],
                    [
                        "tahap" => "tahap_4",
                        "nama" => (function () use ($row) {
                            if ($row['tahap_4'] === "Terima Rekrutmen") {
                                return "Kandidat Diterima";
                            } elseif ($row['tahap_4'] === "Tolak Rekrutmen") {
                                return "Kandidat Tidak Diterima";
                            } else {
                                return "Kandidat Dalam Prosess";
                            }
                        })(),
                        "status" => (function () use ($row) {
                            if ($row['tahap_4'] === "Terima Rekrutmen") {
                                return "selesai";
                            } elseif ($row['tahap_4'] === "Tolak Rekrutmen") {
                                return "gagal";
                            } else {
                                return "belumtersedia";
                            }
                        })(),
                        "tanggal" => $row['tgl_tahap_4'] ? safeFormat($row['tgl_tahap_4']) : "",
                    ]
                ];



                // Filter berdasarkan paket
                if ($paket == 'C1') {
                    $tahapan = array_slice($tahapan, 0, 1); // hanya tahap 1
                } elseif ($paket == 'C2' || $paket == 'C3') {
                    unset($tahapan[2]); // hapus tahap psikotes
                    $tahapan = array_values($tahapan); // reset index array
                }

                $response["status"] = true;
                $response["message"] = "Data ditemukan.";
                $response["data"] = $tahapan;
            }
        }
    }

    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == "getLimit") {
    $temp_bulan = date("Y-m");
    $getLimit = $conn->prepare("SELECT * FROM limit_cv_universe WHERE id_koordinator = ? AND bulan = ?");
    $getLimit->bind_param("ss", $id_koordinator, $temp_bulan);
    $getLimit->execute();
    $resultLimit = $getLimit->get_result();
    $getLimit->close();

    if ($resultLimit->num_rows > 0) {
        $rowLimit = $resultLimit->fetch_assoc();
        $limit = (int)$rowLimit['jml']; // Pastikan field-nya `limit_cv`, sesuaikan jika berbeda

        echo json_encode([
            "status" => true,
            "message" => "Berhasil mengambil data",
            "limit" => $limit
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            "status" => true,
            "message" => "Tidak ada data limit untuk bulan ini",
            "limit" => 0
        ], JSON_PRETTY_PRINT);
    }
}

if ($func == 'KirimEmailAjukanPekerjaan') { //Ajukan Pekerjaan
    $data = array();

    $status = "gagal";
    $message = "Gagal mengajukan pekerjaan. Silakan hubungi administrator.";
    $created_at = date("Y-m-d H:i:s");
    $temp_jml = 0;

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['id']) && !isset($_POST['id_req'])) {
            throw new Exception("Tidak dapat mengajukan pekerjaan. Silakan untuk login kembali.");
        }

        $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
        $q = decrypt_url($_POST['id'], $key);
        $id_req = $_POST['id_req'];

        if ($q != "") {
            // cek apakah kandidat terdaftar
            $get = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
            $get->bind_param("s", $q);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows == 0) {
                throw new Exception("Gagal mengajukan pekerjaan. Silakan hubungi administrator.");
            }

            // data kandidat
            $rowKandidat = mysqli_fetch_array($result);
            $emailKandidat = $rowKandidat['email'];
            $namaKandidat = $rowKandidat['nama_lengkap'];
            $notelpKandidat = $rowKandidat['no_telp'];

            // cek apakah posisi lamar ada
            $get = $conn->prepare("SELECT * FROM list_request WHERE id_req = ? AND id_koordinator = ?");
            $get->bind_param("ss", $id_req, $id_koordinator);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows == 0) {
                throw new Exception("Gagal mengajukan pekerjaan. Silakan hubungi administrator.");
            }

            // ambil paket perusahaan
            $getKoor = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
            $getKoor->bind_param("s", $id_koordinator);
            $getKoor->execute();
            $res = $getKoor->get_result();
            $getKoor->close();

            $rowKoor = mysqli_fetch_array($res);
            $paket = $rowKoor['paket'];

            $update_limit = "";
            $temp_bulan = date("Y-m");
            if ($paket == 'C3') {
                $getLimit = $conn->prepare("SELECT * FROM limit_cv_universe WHERE id_koordinator = ? AND bulan = ?");
                $getLimit->bind_param("ss", $id_koordinator, $temp_bulan);
                $getLimit->execute();
                $resLimit = $getLimit->get_result();
                $getLimit->close();

                if ($resLimit->num_rows > 0) {
                    $rowLimit = mysqli_fetch_array($resLimit);
                    if ($rowLimit['jml'] == 10) {
                        throw new Exception("Tidak dapat mengajukan pekerjaan. Limit bulan ini sudah terpenuhi.");
                    }
                }

                // cek apakah data limit cv universe sudah ada
                $get = $conn->prepare("SELECT * FROM limit_cv_universe WHERE id_koordinator = ?");
                $get->bind_param("s", $id_koordinator);
                $get->execute();
                $cek = $get->get_result();
                $get->close();

                if ($cek->num_rows > 0) {
                    $rowCek = mysqli_fetch_array($cek);

                    if ($rowCek['bulan'] != $temp_bulan) {
                        $temp_jml = 1;
                    } else {
                        $temp_jml = $rowCek['jml'] + 1;
                    }

                    $update = $conn->prepare("UPDATE limit_cv_universe SET jml = ?, bulan = ?  WHERE id_koordinator = ?");
                    $update->bind_param("sss", $temp_jml, $temp_bulan, $id_koordinator);
                    $update->execute();
                    $update->close();
                } else {
                    $insert = $conn->prepare("INSERT INTO limit_cv_universe (`id_koordinator`,`jml`,`bulan`,`created_at`) VALUES ( ?, '1', ?, ?)");
                    $insert->bind_param("sss", $id_koordinator, $temp_bulan, $created_at);
                    $insert->execute();
                    $insert->close();
                }
            }

            // data lamar
            $rowLamar = mysqli_fetch_array($result);
            $posisiLamar = $rowLamar['posisi'];
            $namaPerusahaan = $rowLamar['perusahaan'];

            if (kirim_email_ajukan_pekerjaan($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $q, $id_req, $SesClient, $notelpKandidat)) {
                $aktivitas = 'Mekajukan pekerjaan untuk kandidat ' . $q . ' diposisi lamaran ' . $posisiLamar . ' [' . $id_req . '].';
                $extra_info = "HRD";
                $level = "INFO";
                logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = "Pengajuan pekerjaan untuk kandidat berhasil.";

                $dataPayload = [
                    'tipe' => 'ajukan',
                    'id_req' => $id_req,
                    'posisi' => $posisiLamar,
                    'perusahaan' => $namaPerusahaan,
                ];

                try {
                    $result = sendNotifikasiAjukanPekerjaan($q, "Pengajuan Pekerjaan", $namaPerusahaan . " mengajukan pekerjaan kepada kamu untuk posisi " . $posisiLamar . ". Cek email kamu sekarang!", $dataPayload, $conn);
                } catch (Exception $e) {
                    throw new Exception("Gagal mengajukan pekerjaan. Silakan hubungi administrator. " . $e->getMessage());
                }
            } else {
                throw new Exception("Gagal mengajukan pekerjaan. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat mengajukan pekerjaan. Silakan untuk login kembali.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    // get jumlah limit
    $cek = $conn->query("SELECT * FROM limit_cv_universe WHERE id_koordinator = '$id_koordinator'");
    if ($cek->num_rows > 0) {
        $rowCek = mysqli_fetch_array($cek);
        $temp_jml = $rowCek['jml'];
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];
    $data['info_limit'] = 'Limit pengajuan pekerjaan bulan ini (' . $temp_jml . '/10)';

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'KirimEmailTerimaKasih') {
    $data = array();
    if (isset($_POST['id_lamar'])) {
        $id_lamar = $_POST['id_lamar'];

        // cek apakah job posting masih tersedia atau tidak
        $get = $conn->prepare("SELECT
                                    ul.*,
                                    uk.nama_lengkap,
                                    uk.email,
                                    lr.posisi,
                                    lr.perusahaan
                                FROM
                                    `users_lamar`  ul
                                    JOIN users_kandidat uk ON uk.pin = ul.id_gestalt
                                    JOIN list_request lr ON ul.id_req = lr.id_req
                                WHERE
                                    ul.id_lamar = ? AND ul.id_koordinator = ?");
        $get->bind_param("ss", $id_lamar, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $data['status'] = false;
        $data['message'] = "Gagal mengirim email ucapan terima kasih.";
        $data['data'] = [];
        if ($result->num_rows > 0) {
            // get data user lamar
            $row = mysqli_fetch_array($result);
            $id_gestalt = $row['id_gestalt'];
            $id_req = $row['id_req'];
            $id_koordinator = $row['id_koordinator'];
            $emailKandidat = $row['email'];
            $namaKandidat = $row['nama_lengkap'];
            $posisiLamar = $row['posisi'];
            $namaPerusahaan = $row['perusahaan'];
            $no_telp = $row['no_telp'];

            // cek email antriannya
            $get = $conn->prepare("SELECT * FROM email_queue WHERE id_lamar = ? AND ket = 'Ucapan Terima Kasih' AND `status` = 'pending'");
            $get->bind_param("s", $id_lamar);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows > 0) {
                if (kirim_email_terima_kasih($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient, $no_telp)) {
                    $update = $conn->prepare("UPDATE email_queue SET `status` = 'finish' WHERE id_lamar = ? AND ket = 'Ucapan Terima Kasih' AND `status` = 'pending'");
                    $update->bind_param("s", $id_lamar);

                    if ($update->execute()) {
                        $update->close();


                        $data['status'] = true;
                        $data['message'] = "Berhasil mengirim email ucapan terima kasih.";
                        $data['data'] = [];
                    }
                } else {
                    $data['status'] = false;
                    $data['message'] = "Gagal mengirim email ucapan terima kasih.";
                    $data['data'] = [];
                }
            }
        }
        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    }
}

if ($func == 'KirimEmailTerimaRekrutmen') {
    $data = array();
    if (isset($_POST['id_lamar'])) {
        $id_lamar = $_POST['id_lamar'];

        // cek apakah job posting masih tersedia atau tidak
        $get = $conn->prepare("SELECT
                                    ul.*,
                                    uk.nama_lengkap,
                                    uk.email,
                                    uk.no_telp,
                                    lr.posisi,
                                    lr.perusahaan,
                                    k.tlp
                                FROM
                                    `users_lamar`  ul
                                    JOIN users_kandidat uk ON uk.pin = ul.id_gestalt
                                    JOIN list_request lr ON ul.id_req = lr.id_req
                                    JOIN koordinator k on k.id_koordinator=ul.id_koordinator
                                WHERE
                                    ul.id_lamar = ?
                                    AND ul.id_koordinator = ?");
        $get->bind_param("ss", $id_lamar, $id_koordinator);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $data['status'] = false;
        $data['message'] = "Gagal mengirim email ucapan terima kasih.";
        $data['data'] = [];
        if ($result->num_rows > 0) {
            // get data user lamar
            $row = mysqli_fetch_array($result);
            $id_gestalt = $row['id_gestalt'];
            $id_req = $row['id_req'];
            $id_koordinator = $row['id_koordinator'];
            $emailKandidat = $row['email'];
            $namaKandidat = $row['nama_lengkap'];
            $posisiLamar = $row['posisi'];
            $namaPerusahaan = $row['perusahaan'];
            $tlp = $row['tlp'];
            $notelpKandidat = $row['no_telp'];

            // cek email antriannya
            $get = $conn->prepare("SELECT * FROM email_queue WHERE id_lamar = ? AND ket = 'Terima Rekrutmen' AND `status` = 'pending'");
            $get->bind_param("s", $id_lamar);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            if ($result->num_rows > 0) {
                if (kirim_email_terima_rekrutmen($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient, $tlp, $notelpKandidat)) {
                    $update = $conn->prepare("UPDATE email_queue SET `status` = 'finish' WHERE id_lamar = ? AND ket = 'Terima Rekrutmen' AND `status` = 'pending'");
                    $update->bind_param("s", $id_lamar);

                    if ($update->execute()) {
                        $update->close();

                        $data['status'] = true;
                        $data['message'] = "Berhasil mengirim email ucapan terima kasih.";
                        $data['data'] = [];
                    }
                } else {
                    $data['status'] = false;
                    $data['message'] = "Gagal mengirim email ucapan terima kasih.";
                    $data['data'] = [];
                }
            }
        }
        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    }
}

function kirim_email_terima_rekrutmen($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient, $tlp, $notelpKandidat)
{

    //send otp via wa
    $url = "https://api.barantum.com/api/v1/send-message-template-custom";
    $data_otp = [
        "company_uuid" => "50f3086041a90d7edad22ea3c5bd733c73905a14006a695144db5dcf092ed404",
        "agent_id" => "",
        "contacts" => [
            [
                "user_name" => $namaKandidat,
                "number" => $notelpKandidat,
                "variabel" => [
                    "{{1}}" => "(text)" . $posisiLamar,
                    "{{2}}" => "(text)" . $namaPerusahaan,
                    "{{3}}" => "(text)" . $tlp,
                ]
            ]
        ],
        "template_uuid" => "0407a684-5cdc-48ed-a271-57e096d4abf4",
        "chat_bot_uuid" => "51ac1ccc-2ea1-4d0e-951d-0ca4e30cad31"
    ];

    $payload = json_encode($data_otp);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

    curl_exec($ch);
    curl_error($ch);
    curl_close($ch);

    $emailParams = [
        'Destination' => [
            'ToAddresses' => [$emailKandidat],
        ],
        'Message' => [
            'Body' => [
                'Html' => ['Data' => '<!DOCTYPE html
                                            PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                        <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                        <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                        <head>
                                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                            <!--[if !mso]><!-->
                                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                            <!--<![endif]-->
                                            <meta name="viewport" content="width=device-width">
                                            <style type="text/css">
                                                @media only screen and (min-width: 620px) {
                                                    .wrapper {
                                                        min-width: 600px !important
                                                    }

                                                    .wrapper h1 {}

                                                    .wrapper h1 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper h2 {}

                                                    .wrapper h2 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper h3 {}

                                                    .column {}

                                                    .wrapper .size-8 {
                                                        font-size: 8px !important;
                                                        line-height: 14px !important
                                                    }

                                                    .wrapper .size-9 {
                                                        font-size: 9px !important;
                                                        line-height: 16px !important
                                                    }

                                                    .wrapper .size-10 {
                                                        font-size: 10px !important;
                                                        line-height: 18px !important
                                                    }

                                                    .wrapper .size-11 {
                                                        font-size: 11px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-12 {
                                                        font-size: 12px !important;
                                                        line-height: 19px !important
                                                    }

                                                    .wrapper .size-13 {
                                                        font-size: 13px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-14 {
                                                        font-size: 14px !important;
                                                        line-height: 21px !important
                                                    }

                                                    .wrapper .size-15 {
                                                        font-size: 15px !important;
                                                        line-height: 23px !important
                                                    }

                                                    .wrapper .size-16 {
                                                        font-size: 16px !important;
                                                        line-height: 24px !important
                                                    }

                                                    .wrapper .size-17 {
                                                        font-size: 17px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-18 {
                                                        font-size: 18px !important;
                                                        line-height: 26px !important
                                                    }

                                                    .wrapper .size-20 {
                                                        font-size: 20px !important;
                                                        line-height: 28px !important
                                                    }

                                                    .wrapper .size-22 {
                                                        font-size: 22px !important;
                                                        line-height: 31px !important
                                                    }

                                                    .wrapper .size-24 {
                                                        font-size: 24px !important;
                                                        line-height: 32px !important
                                                    }

                                                    .wrapper .size-26 {
                                                        font-size: 26px !important;
                                                        line-height: 34px !important
                                                    }

                                                    .wrapper .size-28 {
                                                        font-size: 28px !important;
                                                        line-height: 36px !important
                                                    }

                                                    .wrapper .size-30 {
                                                        font-size: 30px !important;
                                                        line-height: 38px !important
                                                    }

                                                    .wrapper .size-32 {
                                                        font-size: 32px !important;
                                                        line-height: 40px !important
                                                    }

                                                    .wrapper .size-34 {
                                                        font-size: 34px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-36 {
                                                        font-size: 36px !important;
                                                        line-height: 43px !important
                                                    }

                                                    .wrapper .size-40 {
                                                        font-size: 40px !important;
                                                        line-height: 47px !important
                                                    }

                                                    .wrapper .size-44 {
                                                        font-size: 44px !important;
                                                        line-height: 50px !important
                                                    }

                                                    .wrapper .size-48 {
                                                        font-size: 48px !important;
                                                        line-height: 54px !important
                                                    }

                                                    .wrapper .size-56 {
                                                        font-size: 56px !important;
                                                        line-height: 60px !important
                                                    }

                                                    .wrapper .size-64 {
                                                        font-size: 64px !important;
                                                        line-height: 63px !important
                                                    }
                                                }
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    margin: 0;
                                                    padding: 0;
                                                }

                                                table {
                                                    border-collapse: collapse;
                                                    table-layout: fixed;
                                                }

                                                * {
                                                    line-height: inherit;
                                                }

                                                [x-apple-data-detectors],
                                                [href^="tel"],
                                                [href^="sms"] {
                                                    color: inherit !important;
                                                    text-decoration: none !important;
                                                }

                                                .wrapper .footer__share-button a:hover,
                                                .wrapper .footer__share-button a:focus {
                                                    color: #ffffff !important;
                                                }

                                                .btn a:hover,
                                                .btn a:focus,
                                                .footer__share-button a:hover,
                                                .footer__share-button a:focus,
                                                .email-footer__links a:hover,
                                                .email-footer__links a:focus {
                                                    opacity: 0.8;
                                                }

                                                .preheader,
                                                .header,
                                                .layout,
                                                .column {
                                                    transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                                }

                                                .preheader td {
                                                    padding-bottom: 8px;
                                                }

                                                .layout,
                                                div.header {
                                                    max-width: 400px !important;
                                                    -fallback-width: 95% !important;
                                                    width: calc(100% - 20px) !important;
                                                }

                                                div.preheader {
                                                    max-width: 360px !important;
                                                    -fallback-width: 90% !important;
                                                    width: calc(100% - 60px) !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    Float: none !important;
                                                }

                                                .column {
                                                    max-width: 400px !important;
                                                    width: 100% !important;
                                                }

                                                .fixed-width.has-border {
                                                    max-width: 402px !important;
                                                }

                                                .fixed-width.has-border .layout__inner {
                                                    box-sizing: border-box;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 50% !important;
                                                }

                                                .ie .btn {
                                                    width: 100%;
                                                }

                                                [owa] .column div,
                                                [owa] .column button {
                                                    display: block !important;
                                                }

                                                .ie .column,
                                                [owa] .column,
                                                .ie .gutter,
                                                [owa] .gutter {
                                                    display: table-cell;
                                                    float: none !important;
                                                    vertical-align: top;
                                                }

                                                .ie div.preheader,
                                                [owa] div.preheader,
                                                .ie .email-footer,
                                                [owa] .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .ie .snippet,
                                                [owa] .snippet,
                                                .ie .webversion,
                                                [owa] .webversion {
                                                    width: 280px !important;
                                                }

                                                .ie div.header,
                                                [owa] div.header,
                                                .ie .layout,
                                                [owa] .layout,
                                                .ie .one-col .column,
                                                [owa] .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .ie .fixed-width.has-border,
                                                [owa] .fixed-width.has-border,
                                                .ie .has-gutter.has-border,
                                                [owa] .has-gutter.has-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .ie .two-col .column,
                                                [owa] .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .ie .three-col .column,
                                                [owa] .three-col .column,
                                                .ie .narrow,
                                                [owa] .narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .ie .wide,
                                                [owa] .wide {
                                                    width: 400px !important;
                                                }

                                                .ie .two-col.has-gutter .column,
                                                [owa] .two-col.x_has-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .ie .three-col.has-gutter .column,
                                                [owa] .three-col.x_has-gutter .column,
                                                .ie .has-gutter .narrow,
                                                [owa] .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .ie .has-gutter .wide,
                                                [owa] .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .ie .two-col.has-gutter.has-border .column,
                                                [owa] .two-col.x_has-gutter.x_has-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .ie .three-col.has-gutter.has-border .column,
                                                [owa] .three-col.x_has-gutter.x_has-border .column,
                                                .ie .has-gutter.has-border .narrow,
                                                [owa] .has-gutter.x_has-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .ie .has-gutter.has-border .wide,
                                                [owa] .has-gutter.x_has-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }

                                                .ie .fixed-width .layout__inner {
                                                    border-left: 0 none white !important;
                                                    border-right: 0 none white !important;
                                                }

                                                .ie .layout__edges {
                                                    display: none;
                                                }

                                                .mso .layout__edges {
                                                    font-size: 0;
                                                }

                                                .layout-fixed-width,
                                                .mso .layout-full-width {
                                                    background-color: #ffffff;
                                                }

                                                @media only screen and (min-width: 620px) {

                                                    .column,
                                                    .gutter {
                                                        display: table-cell;
                                                        Float: none !important;
                                                        vertical-align: top;
                                                    }

                                                    div.preheader,
                                                    .email-footer {
                                                        max-width: 560px !important;
                                                        width: 560px !important;
                                                    }

                                                    .snippet,
                                                    .webversion {
                                                        width: 280px !important;
                                                    }

                                                    div.header,
                                                    .layout,
                                                    .one-col .column {
                                                        max-width: 600px !important;
                                                        width: 600px !important;
                                                    }

                                                    .fixed-width.has-border,
                                                    .fixed-width.ecxhas-border,
                                                    .has-gutter.has-border,
                                                    .has-gutter.ecxhas-border {
                                                        max-width: 602px !important;
                                                        width: 602px !important;
                                                    }

                                                    .two-col .column {
                                                        max-width: 300px !important;
                                                        width: 300px !important;
                                                    }

                                                    .three-col .column,
                                                    .column.narrow {
                                                        max-width: 200px !important;
                                                        width: 200px !important;
                                                    }

                                                    .column.wide {
                                                        width: 400px !important;
                                                    }

                                                    .two-col.has-gutter .column,
                                                    .two-col.ecxhas-gutter .column {
                                                        max-width: 290px !important;
                                                        width: 290px !important;
                                                    }

                                                    .three-col.has-gutter .column,
                                                    .three-col.ecxhas-gutter .column,
                                                    .has-gutter .narrow {
                                                        max-width: 188px !important;
                                                        width: 188px !important;
                                                    }

                                                    .has-gutter .wide {
                                                        max-width: 394px !important;
                                                        width: 394px !important;
                                                    }

                                                    .two-col.has-gutter.has-border .column,
                                                    .two-col.ecxhas-gutter.ecxhas-border .column {
                                                        max-width: 292px !important;
                                                        width: 292px !important;
                                                    }

                                                    .three-col.has-gutter.has-border .column,
                                                    .three-col.ecxhas-gutter.ecxhas-border .column,
                                                    .has-gutter.has-border .narrow,
                                                    .has-gutter.ecxhas-border .narrow {
                                                        max-width: 190px !important;
                                                        width: 190px !important;
                                                    }

                                                    .has-gutter.has-border .wide,
                                                    .has-gutter.ecxhas-border .wide {
                                                        max-width: 396px !important;
                                                        width: 396px !important;
                                                    }
                                                }

                                                @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                                only screen and (min--moz-device-pixel-ratio: 2),
                                                only screen and (-o-min-device-pixel-ratio: 2/1),
                                                only screen and (min-device-pixel-ratio: 2),
                                                only screen and (min-resolution: 192dpi),
                                                only screen and (min-resolution: 2dppx) {
                                                    .fblike {
                                                        background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .tweet {
                                                        background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .linkedinshare {
                                                        background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }

                                                    .forwardtoafriend {
                                                        background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                    }
                                                }

                                                @media (max-width: 321px) {
                                                    .fixed-width.has-border .layout__inner {
                                                        border-width: 1px 0 !important;
                                                    }

                                                    .layout,
                                                    .column {
                                                        min-width: 320px !important;
                                                        width: 320px !important;
                                                    }

                                                    .border {
                                                        display: none;
                                                    }
                                                }

                                                .mso div {
                                                    border: 0 none white !important;
                                                }

                                                .mso .w560 .divider {
                                                    Margin-left: 260px !important;
                                                    Margin-right: 260px !important;
                                                }

                                                .mso .w360 .divider {
                                                    Margin-left: 160px !important;
                                                    Margin-right: 160px !important;
                                                }

                                                .mso .w260 .divider {
                                                    Margin-left: 110px !important;
                                                    Margin-right: 110px !important;
                                                }

                                                .mso .w160 .divider {
                                                    Margin-left: 60px !important;
                                                    Margin-right: 60px !important;
                                                }

                                                .mso .w354 .divider {
                                                    Margin-left: 157px !important;
                                                    Margin-right: 157px !important;
                                                }

                                                .mso .w250 .divider {
                                                    Margin-left: 105px !important;
                                                    Margin-right: 105px !important;
                                                }

                                                .mso .w148 .divider {
                                                    Margin-left: 54px !important;
                                                    Margin-right: 54px !important;
                                                }

                                                .mso .size-8,
                                                .ie .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important;
                                                }

                                                .mso .size-9,
                                                .ie .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important;
                                                }

                                                .mso .size-10,
                                                .ie .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important;
                                                }

                                                .mso .size-11,
                                                .ie .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-12,
                                                .ie .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important;
                                                }

                                                .mso .size-13,
                                                .ie .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-14,
                                                .ie .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important;
                                                }

                                                .mso .size-15,
                                                .ie .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important;
                                                }

                                                .mso .size-16,
                                                .ie .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important;
                                                }

                                                .mso .size-17,
                                                .ie .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-18,
                                                .ie .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important;
                                                }

                                                .mso .size-20,
                                                .ie .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important;
                                                }

                                                .mso .size-22,
                                                .ie .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important;
                                                }

                                                .mso .size-24,
                                                .ie .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important;
                                                }

                                                .mso .size-26,
                                                .ie .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important;
                                                }

                                                .mso .size-28,
                                                .ie .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important;
                                                }

                                                .mso .size-30,
                                                .ie .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important;
                                                }

                                                .mso .size-32,
                                                .ie .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important;
                                                }

                                                .mso .size-34,
                                                .ie .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-36,
                                                .ie .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important;
                                                }

                                                .mso .size-40,
                                                .ie .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important;
                                                }

                                                .mso .size-44,
                                                .ie .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important;
                                                }

                                                .mso .size-48,
                                                .ie .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important;
                                                }

                                                .mso .size-56,
                                                .ie .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important;
                                                }

                                                .mso .size-64,
                                                .ie .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important;
                                                }
                                            </style>

                                            <!--[if !mso]><!-->
                                            <style type="text/css">
                                                @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                            </style>
                                            <style type="text/css">
                                                body {
                                                    background-color: #fff
                                                }

                                                .logo a:hover,
                                                .logo a:focus {
                                                    color: #1e2e3b !important
                                                }

                                                .mso .layout-has-border {
                                                    border-top: 1px solid #ccc;
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .layout-has-bottom-border {
                                                    border-bottom: 1px solid #ccc
                                                }

                                                .mso .border,
                                                .ie .border {
                                                    background-color: #ccc
                                                }

                                                .mso h1,
                                                .ie h1 {}

                                                .mso h1,
                                                .ie h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .mso h2,
                                                .ie h2 {}

                                                .mso h2,
                                                .ie h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .mso h3,
                                                .ie h3 {}

                                                .mso .layout__inner,
                                                .ie .layout__inner {}

                                                .mso .footer__share-button p {}

                                                .mso .footer__share-button p {
                                                    font-family: Cabin, Avenir, sans-serif
                                                }
                                            </style>
                                            <meta name="robots" content="noindex,nofollow">
                                            </meta>
                                            <meta property="og:title" content="Mail v.01">
                                            </meta>
                                        </head>

                                        <body
                                            style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                            <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                                <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                    <tr style="border-collapse:collapse;">
                                                        <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                            <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                        bgcolor="transparent">
                                                                        <div>
                                                                            <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                                cellspacing="0" width="600"
                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                                <tr style="border-collapse:collapse;">
                                                                                    <td align="left"
                                                                                        style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                            <tr style="border-collapse:collapse;">
                                                                                                <td width="560" align="center" valign="top"
                                                                                                    style="padding:0;Margin:0;">
                                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" style="padding:0;Margin:0;">
                                                                                                                <a target="_blank" href="https://digitalcv.id"
                                                                                                                    style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                    <img src="https://ultrajaya.digitalcv.id/id/assets/images/logo/logoDcv2.png"
                                                                                                                        alt
                                                                                                                        style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                        height="131">
                                                                                                                </a>
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                        <tr style="border-collapse:collapse;">
                                                                                                            <td align="center" height="42"
                                                                                                                style="padding:0;Margin:0;"></td>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center" bgcolor="transparent"
                                                                        style="padding:0;Margin:0;background-color:transparent;">
                                                                        <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <h1
                                                                                                                style="Margin:0;line-height:36px;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;font-size:30px;font-style:normal;font-weight:bold;color:#fbb116;">
                                                                                                                Dear, ' . $namaKandidat . '</h1>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center"
                                                                                                            style="padding:0;Margin:0;padding-top:20px;">
                                                                                                            <p
                                                                                                                style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <p
                                                                                                                style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                                Terima kasih telah mengikuti proses rekrutmen
                                                                                                                untuk posisi <b style="color:#fbb116">'
                    . $posisiLamar . '</b> di <b
                                                                                                                    style="color:#fbb116">' .
                    $namaPerusahaan . '</b>.<br>
                                                                                                                Kami dengan senang hati menginformasikan bahwa
                                                                                                                Anda dinyatakan lolos dalam proses seleksi dan
                                                                                                                diterima untuk mengisi
                                                                                                                posisi tersebut. <br>
                                                                                                                Selanjutnya, perusahaan akan menghubungi Anda
                                                                                                                untuk proses administrasi.<br><br>
                                                                                                                Jika ada pertanyaan, silakan hubungi
                                                                                                                <b style="color:#fbb116">' .
                    $namaPerusahaan . '</b> di <b
                                                                                                                    style="color:#fbb116">' . $tlp . '</b>
                                                                                                            </p>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="center"
                                                                        style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                        background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                        <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                    bgcolor="#ffffff">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="540" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="left" style="padding:0;Margin:0;">
                                                                                                            <hr>
                                                                                                            <div>digitalcv<br>
                                                                                                                Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                                Barat<br>
                                                                                                                Telepon: 022-32097159<br>
                                                                                                            </div>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="600" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="40"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </body>
                                        </table>
                                        <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                            src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                        </body>

                                        </html>'],
            ],
            'Subject' => ['Data' => 'Pengumuman Hasil Seleksi'],
        ],
        'Source' => 'digitalcv <<EMAIL>>',
    ];

    try {
        $result = $SesClient->sendEmail($emailParams);
        return true;
    } catch (AwsException $e) {
        return false;
    }
}

function kirim_email_terima_kasih($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient,  $notelpKandidat)
{

    //send otp via wa
    $url = "https://api.barantum.com/api/v1/send-message-template-custom";
    $data_otp = [
        "company_uuid" => "50f3086041a90d7edad22ea3c5bd733c73905a14006a695144db5dcf092ed404",
        "agent_id" => "",
        "contacts" => [
            [
                "user_name" => $namaKandidat,
                "number" => $notelpKandidat,
                "variabel" => [
                    "{{1}}" => "(text)" . $namaKandidat,
                    "{{2}}" => "(text)" . $posisiLamar,
                    "{{3}}" => "(text)" . $namaPerusahaan,
                ]
            ]
        ],
        "template_uuid" => "5fd8eecc-c411-4a1c-ad48-6d74ef69a5de",
        "chat_bot_uuid" => "51ac1ccc-2ea1-4d0e-951d-0ca4e30cad31"
    ];

    $payload = json_encode($data_otp);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

    curl_exec($ch);
    curl_error($ch);
    curl_close($ch);

    $emailParams = [
        'Destination' => [
            'ToAddresses' => [$emailKandidat],
        ],
        'Message' => [
            'Body' => [
                'Html' => ['Data' => '
                                    <!DOCTYPE html
                                PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                            <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                            <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                            <head>
                                <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                <!--[if !mso]><!-->
                                <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                <!--<![endif]-->
                                <meta name="viewport" content="width=device-width">
                                <style type="text/css">
                                    @media only screen and (min-width: 620px) {
                                        .wrapper {
                                            min-width: 600px !important
                                        }

                                        .wrapper h1 {}

                                        .wrapper h1 {
                                            font-size: 26px !important;
                                            line-height: 34px !important
                                        }

                                        .wrapper h2 {}

                                        .wrapper h2 {
                                            font-size: 20px !important;
                                            line-height: 28px !important
                                        }

                                        .wrapper h3 {}

                                        .column {}

                                        .wrapper .size-8 {
                                            font-size: 8px !important;
                                            line-height: 14px !important
                                        }

                                        .wrapper .size-9 {
                                            font-size: 9px !important;
                                            line-height: 16px !important
                                        }

                                        .wrapper .size-10 {
                                            font-size: 10px !important;
                                            line-height: 18px !important
                                        }

                                        .wrapper .size-11 {
                                            font-size: 11px !important;
                                            line-height: 19px !important
                                        }

                                        .wrapper .size-12 {
                                            font-size: 12px !important;
                                            line-height: 19px !important
                                        }

                                        .wrapper .size-13 {
                                            font-size: 13px !important;
                                            line-height: 21px !important
                                        }

                                        .wrapper .size-14 {
                                            font-size: 14px !important;
                                            line-height: 21px !important
                                        }

                                        .wrapper .size-15 {
                                            font-size: 15px !important;
                                            line-height: 23px !important
                                        }

                                        .wrapper .size-16 {
                                            font-size: 16px !important;
                                            line-height: 24px !important
                                        }

                                        .wrapper .size-17 {
                                            font-size: 17px !important;
                                            line-height: 26px !important
                                        }

                                        .wrapper .size-18 {
                                            font-size: 18px !important;
                                            line-height: 26px !important
                                        }

                                        .wrapper .size-20 {
                                            font-size: 20px !important;
                                            line-height: 28px !important
                                        }

                                        .wrapper .size-22 {
                                            font-size: 22px !important;
                                            line-height: 31px !important
                                        }

                                        .wrapper .size-24 {
                                            font-size: 24px !important;
                                            line-height: 32px !important
                                        }

                                        .wrapper .size-26 {
                                            font-size: 26px !important;
                                            line-height: 34px !important
                                        }

                                        .wrapper .size-28 {
                                            font-size: 28px !important;
                                            line-height: 36px !important
                                        }

                                        .wrapper .size-30 {
                                            font-size: 30px !important;
                                            line-height: 38px !important
                                        }

                                        .wrapper .size-32 {
                                            font-size: 32px !important;
                                            line-height: 40px !important
                                        }

                                        .wrapper .size-34 {
                                            font-size: 34px !important;
                                            line-height: 43px !important
                                        }

                                        .wrapper .size-36 {
                                            font-size: 36px !important;
                                            line-height: 43px !important
                                        }

                                        .wrapper .size-40 {
                                            font-size: 40px !important;
                                            line-height: 47px !important
                                        }

                                        .wrapper .size-44 {
                                            font-size: 44px !important;
                                            line-height: 50px !important
                                        }

                                        .wrapper .size-48 {
                                            font-size: 48px !important;
                                            line-height: 54px !important
                                        }

                                        .wrapper .size-56 {
                                            font-size: 56px !important;
                                            line-height: 60px !important
                                        }

                                        .wrapper .size-64 {
                                            font-size: 64px !important;
                                            line-height: 63px !important
                                        }
                                    }
                                </style>
                                <style type="text/css">
                                    body {
                                        margin: 0;
                                        padding: 0;
                                    }

                                    table {
                                        border-collapse: collapse;
                                        table-layout: fixed;
                                    }

                                    * {
                                        line-height: inherit;
                                    }

                                    [x-apple-data-detectors],
                                    [href^="tel"],
                                    [href^="sms"] {
                                        color: inherit !important;
                                        text-decoration: none !important;
                                    }

                                    .wrapper .footer__share-button a:hover,
                                    .wrapper .footer__share-button a:focus {
                                        color: #ffffff !important;
                                    }

                                    .btn a:hover,
                                    .btn a:focus,
                                    .footer__share-button a:hover,
                                    .footer__share-button a:focus,
                                    .email-footer__links a:hover,
                                    .email-footer__links a:focus {
                                        opacity: 0.8;
                                    }

                                    .preheader,
                                    .header,
                                    .layout,
                                    .column {
                                        transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                    }

                                    .preheader td {
                                        padding-bottom: 8px;
                                    }

                                    .layout,
                                    div.header {
                                        max-width: 400px !important;
                                        -fallback-width: 95% !important;
                                        width: calc(100% - 20px) !important;
                                    }

                                    div.preheader {
                                        max-width: 360px !important;
                                        -fallback-width: 90% !important;
                                        width: calc(100% - 60px) !important;
                                    }

                                    .snippet,
                                    .webversion {
                                        Float: none !important;
                                    }

                                    .column {
                                        max-width: 400px !important;
                                        width: 100% !important;
                                    }

                                    .fixed-width.has-border {
                                        max-width: 402px !important;
                                    }

                                    .fixed-width.has-border .layout__inner {
                                        box-sizing: border-box;
                                    }

                                    .snippet,
                                    .webversion {
                                        width: 50% !important;
                                    }

                                    .ie .btn {
                                        width: 100%;
                                    }

                                    [owa] .column div,
                                    [owa] .column button {
                                        display: block !important;
                                    }

                                    .ie .column,
                                    [owa] .column,
                                    .ie .gutter,
                                    [owa] .gutter {
                                        display: table-cell;
                                        float: none !important;
                                        vertical-align: top;
                                    }

                                    .ie div.preheader,
                                    [owa] div.preheader,
                                    .ie .email-footer,
                                    [owa] .email-footer {
                                        max-width: 560px !important;
                                        width: 560px !important;
                                    }

                                    .ie .snippet,
                                    [owa] .snippet,
                                    .ie .webversion,
                                    [owa] .webversion {
                                        width: 280px !important;
                                    }

                                    .ie div.header,
                                    [owa] div.header,
                                    .ie .layout,
                                    [owa] .layout,
                                    .ie .one-col .column,
                                    [owa] .one-col .column {
                                        max-width: 600px !important;
                                        width: 600px !important;
                                    }

                                    .ie .fixed-width.has-border,
                                    [owa] .fixed-width.has-border,
                                    .ie .has-gutter.has-border,
                                    [owa] .has-gutter.has-border {
                                        max-width: 602px !important;
                                        width: 602px !important;
                                    }

                                    .ie .two-col .column,
                                    [owa] .two-col .column {
                                        max-width: 300px !important;
                                        width: 300px !important;
                                    }

                                    .ie .three-col .column,
                                    [owa] .three-col .column,
                                    .ie .narrow,
                                    [owa] .narrow {
                                        max-width: 200px !important;
                                        width: 200px !important;
                                    }

                                    .ie .wide,
                                    [owa] .wide {
                                        width: 400px !important;
                                    }

                                    .ie .two-col.has-gutter .column,
                                    [owa] .two-col.x_has-gutter .column {
                                        max-width: 290px !important;
                                        width: 290px !important;
                                    }

                                    .ie .three-col.has-gutter .column,
                                    [owa] .three-col.x_has-gutter .column,
                                    .ie .has-gutter .narrow,
                                    [owa] .has-gutter .narrow {
                                        max-width: 188px !important;
                                        width: 188px !important;
                                    }

                                    .ie .has-gutter .wide,
                                    [owa] .has-gutter .wide {
                                        max-width: 394px !important;
                                        width: 394px !important;
                                    }

                                    .ie .two-col.has-gutter.has-border .column,
                                    [owa] .two-col.x_has-gutter.x_has-border .column {
                                        max-width: 292px !important;
                                        width: 292px !important;
                                    }

                                    .ie .three-col.has-gutter.has-border .column,
                                    [owa] .three-col.x_has-gutter.x_has-border .column,
                                    .ie .has-gutter.has-border .narrow,
                                    [owa] .has-gutter.x_has-border .narrow {
                                        max-width: 190px !important;
                                        width: 190px !important;
                                    }

                                    .ie .has-gutter.has-border .wide,
                                    [owa] .has-gutter.x_has-border .wide {
                                        max-width: 396px !important;
                                        width: 396px !important;
                                    }

                                    .ie .fixed-width .layout__inner {
                                        border-left: 0 none white !important;
                                        border-right: 0 none white !important;
                                    }

                                    .ie .layout__edges {
                                        display: none;
                                    }

                                    .mso .layout__edges {
                                        font-size: 0;
                                    }

                                    .layout-fixed-width,
                                    .mso .layout-full-width {
                                        background-color: #ffffff;
                                    }

                                    @media only screen and (min-width: 620px) {

                                        .column,
                                        .gutter {
                                            display: table-cell;
                                            Float: none !important;
                                            vertical-align: top;
                                        }

                                        div.preheader,
                                        .email-footer {
                                            max-width: 560px !important;
                                            width: 560px !important;
                                        }

                                        .snippet,
                                        .webversion {
                                            width: 280px !important;
                                        }

                                        div.header,
                                        .layout,
                                        .one-col .column {
                                            max-width: 600px !important;
                                            width: 600px !important;
                                        }

                                        .fixed-width.has-border,
                                        .fixed-width.ecxhas-border,
                                        .has-gutter.has-border,
                                        .has-gutter.ecxhas-border {
                                            max-width: 602px !important;
                                            width: 602px !important;
                                        }

                                        .two-col .column {
                                            max-width: 300px !important;
                                            width: 300px !important;
                                        }

                                        .three-col .column,
                                        .column.narrow {
                                            max-width: 200px !important;
                                            width: 200px !important;
                                        }

                                        .column.wide {
                                            width: 400px !important;
                                        }

                                        .two-col.has-gutter .column,
                                        .two-col.ecxhas-gutter .column {
                                            max-width: 290px !important;
                                            width: 290px !important;
                                        }

                                        .three-col.has-gutter .column,
                                        .three-col.ecxhas-gutter .column,
                                        .has-gutter .narrow {
                                            max-width: 188px !important;
                                            width: 188px !important;
                                        }

                                        .has-gutter .wide {
                                            max-width: 394px !important;
                                            width: 394px !important;
                                        }

                                        .two-col.has-gutter.has-border .column,
                                        .two-col.ecxhas-gutter.ecxhas-border .column {
                                            max-width: 292px !important;
                                            width: 292px !important;
                                        }

                                        .three-col.has-gutter.has-border .column,
                                        .three-col.ecxhas-gutter.ecxhas-border .column,
                                        .has-gutter.has-border .narrow,
                                        .has-gutter.ecxhas-border .narrow {
                                            max-width: 190px !important;
                                            width: 190px !important;
                                        }

                                        .has-gutter.has-border .wide,
                                        .has-gutter.ecxhas-border .wide {
                                            max-width: 396px !important;
                                            width: 396px !important;
                                        }
                                    }

                                    @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                    only screen and (min--moz-device-pixel-ratio: 2),
                                    only screen and (-o-min-device-pixel-ratio: 2/1),
                                    only screen and (min-device-pixel-ratio: 2),
                                    only screen and (min-resolution: 192dpi),
                                    only screen and (min-resolution: 2dppx) {
                                        .fblike {
                                            background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                        }

                                        .tweet {
                                            background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                        }

                                        .linkedinshare {
                                            background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                        }

                                        .forwardtoafriend {
                                            background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                        }
                                    }

                                    @media (max-width: 321px) {
                                        .fixed-width.has-border .layout__inner {
                                            border-width: 1px 0 !important;
                                        }

                                        .layout,
                                        .column {
                                            min-width: 320px !important;
                                            width: 320px !important;
                                        }

                                        .border {
                                            display: none;
                                        }
                                    }

                                    .mso div {
                                        border: 0 none white !important;
                                    }

                                    .mso .w560 .divider {
                                        Margin-left: 260px !important;
                                        Margin-right: 260px !important;
                                    }

                                    .mso .w360 .divider {
                                        Margin-left: 160px !important;
                                        Margin-right: 160px !important;
                                    }

                                    .mso .w260 .divider {
                                        Margin-left: 110px !important;
                                        Margin-right: 110px !important;
                                    }

                                    .mso .w160 .divider {
                                        Margin-left: 60px !important;
                                        Margin-right: 60px !important;
                                    }

                                    .mso .w354 .divider {
                                        Margin-left: 157px !important;
                                        Margin-right: 157px !important;
                                    }

                                    .mso .w250 .divider {
                                        Margin-left: 105px !important;
                                        Margin-right: 105px !important;
                                    }

                                    .mso .w148 .divider {
                                        Margin-left: 54px !important;
                                        Margin-right: 54px !important;
                                    }

                                    .mso .size-8,
                                    .ie .size-8 {
                                        font-size: 8px !important;
                                        line-height: 14px !important;
                                    }

                                    .mso .size-9,
                                    .ie .size-9 {
                                        font-size: 9px !important;
                                        line-height: 16px !important;
                                    }

                                    .mso .size-10,
                                    .ie .size-10 {
                                        font-size: 10px !important;
                                        line-height: 18px !important;
                                    }

                                    .mso .size-11,
                                    .ie .size-11 {
                                        font-size: 11px !important;
                                        line-height: 19px !important;
                                    }

                                    .mso .size-12,
                                    .ie .size-12 {
                                        font-size: 12px !important;
                                        line-height: 19px !important;
                                    }

                                    .mso .size-13,
                                    .ie .size-13 {
                                        font-size: 13px !important;
                                        line-height: 21px !important;
                                    }

                                    .mso .size-14,
                                    .ie .size-14 {
                                        font-size: 14px !important;
                                        line-height: 21px !important;
                                    }

                                    .mso .size-15,
                                    .ie .size-15 {
                                        font-size: 15px !important;
                                        line-height: 23px !important;
                                    }

                                    .mso .size-16,
                                    .ie .size-16 {
                                        font-size: 16px !important;
                                        line-height: 24px !important;
                                    }

                                    .mso .size-17,
                                    .ie .size-17 {
                                        font-size: 17px !important;
                                        line-height: 26px !important;
                                    }

                                    .mso .size-18,
                                    .ie .size-18 {
                                        font-size: 18px !important;
                                        line-height: 26px !important;
                                    }

                                    .mso .size-20,
                                    .ie .size-20 {
                                        font-size: 20px !important;
                                        line-height: 28px !important;
                                    }

                                    .mso .size-22,
                                    .ie .size-22 {
                                        font-size: 22px !important;
                                        line-height: 31px !important;
                                    }

                                    .mso .size-24,
                                    .ie .size-24 {
                                        font-size: 24px !important;
                                        line-height: 32px !important;
                                    }

                                    .mso .size-26,
                                    .ie .size-26 {
                                        font-size: 26px !important;
                                        line-height: 34px !important;
                                    }

                                    .mso .size-28,
                                    .ie .size-28 {
                                        font-size: 28px !important;
                                        line-height: 36px !important;
                                    }

                                    .mso .size-30,
                                    .ie .size-30 {
                                        font-size: 30px !important;
                                        line-height: 38px !important;
                                    }

                                    .mso .size-32,
                                    .ie .size-32 {
                                        font-size: 32px !important;
                                        line-height: 40px !important;
                                    }

                                    .mso .size-34,
                                    .ie .size-34 {
                                        font-size: 34px !important;
                                        line-height: 43px !important;
                                    }

                                    .mso .size-36,
                                    .ie .size-36 {
                                        font-size: 36px !important;
                                        line-height: 43px !important;
                                    }

                                    .mso .size-40,
                                    .ie .size-40 {
                                        font-size: 40px !important;
                                        line-height: 47px !important;
                                    }

                                    .mso .size-44,
                                    .ie .size-44 {
                                        font-size: 44px !important;
                                        line-height: 50px !important;
                                    }

                                    .mso .size-48,
                                    .ie .size-48 {
                                        font-size: 48px !important;
                                        line-height: 54px !important;
                                    }

                                    .mso .size-56,
                                    .ie .size-56 {
                                        font-size: 56px !important;
                                        line-height: 60px !important;
                                    }

                                    .mso .size-64,
                                    .ie .size-64 {
                                        font-size: 64px !important;
                                        line-height: 63px !important;
                                    }
                                </style>

                                <!--[if !mso]><!-->
                                <style type="text/css">
                                    @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                </style>
                                <style type="text/css">
                                    body {
                                        background-color: #fff
                                    }

                                    .logo a:hover,
                                    .logo a:focus {
                                        color: #1e2e3b !important
                                    }

                                    .mso .layout-has-border {
                                        border-top: 1px solid #ccc;
                                        border-bottom: 1px solid #ccc
                                    }

                                    .mso .layout-has-bottom-border {
                                        border-bottom: 1px solid #ccc
                                    }

                                    .mso .border,
                                    .ie .border {
                                        background-color: #ccc
                                    }

                                    .mso h1,
                                    .ie h1 {}

                                    .mso h1,
                                    .ie h1 {
                                        font-size: 26px !important;
                                        line-height: 34px !important
                                    }

                                    .mso h2,
                                    .ie h2 {}

                                    .mso h2,
                                    .ie h2 {
                                        font-size: 20px !important;
                                        line-height: 28px !important
                                    }

                                    .mso h3,
                                    .ie h3 {}

                                    .mso .layout__inner,
                                    .ie .layout__inner {}

                                    .mso .footer__share-button p {}

                                    .mso .footer__share-button p {
                                        font-family: Cabin, Avenir, sans-serif
                                    }
                                </style>
                                <meta name="robots" content="noindex,nofollow">
                                </meta>
                                <meta property="og:title" content="Mail v.01">
                                </meta>
                            </head>

                            <body
                                style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                    <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                        <tr style="border-collapse:collapse;">
                                            <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                    <tr style="border-collapse:collapse;">
                                                        <td align="center"
                                                            style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                            bgcolor="transparent">
                                                            <div>
                                                                <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                    cellspacing="0" width="600"
                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                    <tr style="border-collapse:collapse;">
                                                                        <td align="left"
                                                                            style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                <tr style="border-collapse:collapse;">
                                                                                    <td width="560" align="center" valign="top"
                                                                                        style="padding:0;Margin:0;">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                            <tr style="border-collapse:collapse;">
                                                                                                <td align="center" style="padding:0;Margin:0;">
                                                                                                    <a target="_blank" href="https://digitalcv.id"
                                                                                                        style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                        <img src="https://digitalcv.id/assets/images/logo/logoDcv2.png"
                                                                                                            alt
                                                                                                            style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                            height="131">
                                                                                                    </a>
                                                                                                </td>
                                                                                            </tr>
                                                                                            <tr style="border-collapse:collapse;">
                                                                                                <td align="center" height="42"
                                                                                                    style="padding:0;Margin:0;"></td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                    <tr style="border-collapse:collapse;">
                                                        <td align="center" bgcolor="transparent"
                                                            style="padding:0;Margin:0;background-color:transparent;">
                                                            <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                cellspacing="0" width="600"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="left"
                                                                        style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                        bgcolor="#ffffff">
                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td width="540" align="center" valign="top"
                                                                                    style="padding:0;Margin:0;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td align="center" style="padding:0;Margin:0;">
                                                                                                <h1
                                                                                                    style="Margin:0;line-height:36px;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;font-size:30px;font-style:normal;font-weight:bold;color:#fbb116;">
                                                                                                    Dear, ' . $namaKandidat . '</h1>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td align="center"
                                                                                                style="padding:0;Margin:0;padding-top:20px;">
                                                                                                <p
                                                                                                    style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                </p>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="left"
                                                                        style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td width="540" align="center" valign="top"
                                                                                    style="padding:0;Margin:0;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td align="center" style="padding:0;Margin:0;">
                                                                                                <p
                                                                                                    style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                    Terimakasih telah mengikuti proses rekrutmen
                                                                                                    untuk posisi <b style="color:#fbb116">' .
                    $posisiLamar . '</b> di <b
                                                                                                        style="color:#fbb116">'
                    . $namaPerusahaan . '</b>.<br> Sehubungan
                                                                                                    dengan posisi tersebut, saat ini Anda belum
                                                                                                    memenuhi kriteria yang dibutuhkan perusahaan.
                                                                                                    Data yang sudah Anda berikan akan tersimpan
                                                                                                    dalam database rekrutmen sistem kami. Apabila
                                                                                                    terdapat posisi yang sesuai dengan profil dan
                                                                                                    kriteria yang Anda miliki, kami akan menghubungi
                                                                                                    kembali melalui kontak yang sudah Anda isi pada
                                                                                                    data diri.
                                                                                                </p>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                    <tr style="border-collapse:collapse;">
                                                        <td align="center"
                                                            style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                            background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                            <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                cellspacing="0" width="600"
                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="left"
                                                                        style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                        bgcolor="#ffffff">
                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td width="540" align="center" valign="top"
                                                                                    style="padding:0;Margin:0;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td align="left" style="padding:0;Margin:0;">
                                                                                                <hr>
                                                                                                <div>digitalcv<br>
                                                                                                    Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                    Barat<br>
                                                                                                    Telepon: 022-32097159<br>
                                                                                                </div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                                <tr style="border-collapse:collapse;">
                                                                    <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                        <table cellpadding="0" cellspacing="0" width="100%"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td width="600" align="center" valign="top"
                                                                                    style="padding:0;Margin:0;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td align="center" height="40"
                                                                                                style="padding:0;Margin:0;"></td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </body>
                            </table>
                            <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                            </body>

                            </html>'],
            ],
            'Subject' => ['Data' => 'Pengumuman Hasil Seleksi'],
        ],
        'Source' => 'digitalcv <<EMAIL>>',
    ];

    try {
        $result = $SesClient->sendEmail($emailParams);
        return true;
    } catch (AwsException $e) {
        return false;
    }
}

function kirim_email_ajukan_pekerjaan($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $q, $id_req, $SesClient, $notelpKandidat)
{
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
    $pesan = $q . '|' . $id_req;
    $link = 'http://' . $_SERVER['HTTP_HOST'] . '/digitalcv/candidate/accept-invitation?q=' . encrypt_url($pesan, $key);

    //send otp via wa

    $url = "https://api.barantum.com/api/v1/send-message-template-custom";

    $data_otp = [
        "company_uuid" => "50f3086041a90d7edad22ea3c5bd733c73905a14006a695144db5dcf092ed404",
        "agent_id" => "",
        "contacts" => [
            [
                "user_name" => $namaKandidat,
                "number" => $notelpKandidat,
                "variabel" => [
                    "{{1}}" => "(text)" . $namaPerusahaan,
                    "{{2}}" => "(text)" . $posisiLamar,
                    "{{3}}" => "(text)" . $link,
                ]
            ]
        ],
        "template_uuid" => "3e0cbd3a-2bee-49fc-9ecf-b96d07e71830",
        "chat_bot_uuid" => "51ac1ccc-2ea1-4d0e-951d-0ca4e30cad31"
    ];

    $payload = json_encode($data_otp);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

    curl_exec($ch);
    curl_error($ch);
    curl_close($ch);
    $emailParams = [
        'Destination' => [
            'ToAddresses' => [$emailKandidat],
        ],
        'Message' => [
            'Body' => [
                'Html' => ['Data' => '
                                    <!DOCTYPE html
                                        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                    <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                    <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                    <head>
                                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                        <!--[if !mso]><!-->
                                        <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                        <!--<![endif]-->
                                        <meta name="viewport" content="width=device-width">
                                        <style type="text/css">
                                            @media only screen and (min-width: 620px) {
                                                .wrapper {
                                                    min-width: 600px !important
                                                }

                                                .wrapper h1 {}

                                                .wrapper h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper h2 {}

                                                .wrapper h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper h3 {}

                                                .column {}

                                                .wrapper .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important
                                                }

                                                .wrapper .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important
                                                }

                                                .wrapper .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important
                                                }

                                                .wrapper .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important
                                                }

                                                .wrapper .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important
                                                }

                                                .wrapper .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important
                                                }

                                                .wrapper .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important
                                                }

                                                .wrapper .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important
                                                }

                                                .wrapper .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important
                                                }

                                                .wrapper .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important
                                                }

                                                .wrapper .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important
                                                }

                                                .wrapper .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important
                                                }

                                                .wrapper .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important
                                                }

                                                .wrapper .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important
                                                }

                                                .wrapper .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important
                                                }
                                            }
                                        </style>
                                        <style type="text/css">
                                            body {
                                                margin: 0;
                                                padding: 0;
                                            }

                                            table {
                                                border-collapse: collapse;
                                                table-layout: fixed;
                                            }

                                            * {
                                                line-height: inherit;
                                            }

                                            [x-apple-data-detectors],
                                            [href^="tel"],
                                            [href^="sms"] {
                                                color: inherit !important;
                                                text-decoration: none !important;
                                            }

                                            .wrapper .footer__share-button a:hover,
                                            .wrapper .footer__share-button a:focus {
                                                color: #ffffff !important;
                                            }

                                            .btn a:hover,
                                            .btn a:focus,
                                            .footer__share-button a:hover,
                                            .footer__share-button a:focus,
                                            .email-footer__links a:hover,
                                            .email-footer__links a:focus {
                                                opacity: 0.8;
                                            }

                                            .preheader,
                                            .header,
                                            .layout,
                                            .column {
                                                transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                            }

                                            .preheader td {
                                                padding-bottom: 8px;
                                            }

                                            .layout,
                                            div.header {
                                                max-width: 400px !important;
                                                -fallback-width: 95% !important;
                                                width: calc(100% - 20px) !important;
                                            }

                                            div.preheader {
                                                max-width: 360px !important;
                                                -fallback-width: 90% !important;
                                                width: calc(100% - 60px) !important;
                                            }

                                            .snippet,
                                            .webversion {
                                                Float: none !important;
                                            }

                                            .column {
                                                max-width: 400px !important;
                                                width: 100% !important;
                                            }

                                            .fixed-width.has-border {
                                                max-width: 402px !important;
                                            }

                                            .fixed-width.has-border .layout__inner {
                                                box-sizing: border-box;
                                            }

                                            .snippet,
                                            .webversion {
                                                width: 50% !important;
                                            }

                                            .ie .btn {
                                                width: 100%;
                                            }

                                            [owa] .column div,
                                            [owa] .column button {
                                                display: block !important;
                                            }

                                            .ie .column,
                                            [owa] .column,
                                            .ie .gutter,
                                            [owa] .gutter {
                                                display: table-cell;
                                                float: none !important;
                                                vertical-align: top;
                                            }

                                            .ie div.preheader,
                                            [owa] div.preheader,
                                            .ie .email-footer,
                                            [owa] .email-footer {
                                                max-width: 560px !important;
                                                width: 560px !important;
                                            }

                                            .ie .snippet,
                                            [owa] .snippet,
                                            .ie .webversion,
                                            [owa] .webversion {
                                                width: 280px !important;
                                            }

                                            .ie div.header,
                                            [owa] div.header,
                                            .ie .layout,
                                            [owa] .layout,
                                            .ie .one-col .column,
                                            [owa] .one-col .column {
                                                max-width: 600px !important;
                                                width: 600px !important;
                                            }

                                            .ie .fixed-width.has-border,
                                            [owa] .fixed-width.has-border,
                                            .ie .has-gutter.has-border,
                                            [owa] .has-gutter.has-border {
                                                max-width: 602px !important;
                                                width: 602px !important;
                                            }

                                            .ie .two-col .column,
                                            [owa] .two-col .column {
                                                max-width: 300px !important;
                                                width: 300px !important;
                                            }

                                            .ie .three-col .column,
                                            [owa] .three-col .column,
                                            .ie .narrow,
                                            [owa] .narrow {
                                                max-width: 200px !important;
                                                width: 200px !important;
                                            }

                                            .ie .wide,
                                            [owa] .wide {
                                                width: 400px !important;
                                            }

                                            .ie .two-col.has-gutter .column,
                                            [owa] .two-col.x_has-gutter .column {
                                                max-width: 290px !important;
                                                width: 290px !important;
                                            }

                                            .ie .three-col.has-gutter .column,
                                            [owa] .three-col.x_has-gutter .column,
                                            .ie .has-gutter .narrow,
                                            [owa] .has-gutter .narrow {
                                                max-width: 188px !important;
                                                width: 188px !important;
                                            }

                                            .ie .has-gutter .wide,
                                            [owa] .has-gutter .wide {
                                                max-width: 394px !important;
                                                width: 394px !important;
                                            }

                                            .ie .two-col.has-gutter.has-border .column,
                                            [owa] .two-col.x_has-gutter.x_has-border .column {
                                                max-width: 292px !important;
                                                width: 292px !important;
                                            }

                                            .ie .three-col.has-gutter.has-border .column,
                                            [owa] .three-col.x_has-gutter.x_has-border .column,
                                            .ie .has-gutter.has-border .narrow,
                                            [owa] .has-gutter.x_has-border .narrow {
                                                max-width: 190px !important;
                                                width: 190px !important;
                                            }

                                            .ie .has-gutter.has-border .wide,
                                            [owa] .has-gutter.x_has-border .wide {
                                                max-width: 396px !important;
                                                width: 396px !important;
                                            }

                                            .ie .fixed-width .layout__inner {
                                                border-left: 0 none white !important;
                                                border-right: 0 none white !important;
                                            }

                                            .ie .layout__edges {
                                                display: none;
                                            }

                                            .mso .layout__edges {
                                                font-size: 0;
                                            }

                                            .layout-fixed-width,
                                            .mso .layout-full-width {
                                                background-color: #ffffff;
                                            }

                                            @media only screen and (min-width: 620px) {

                                                .column,
                                                .gutter {
                                                    display: table-cell;
                                                    Float: none !important;
                                                    vertical-align: top;
                                                }

                                                div.preheader,
                                                .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 280px !important;
                                                }

                                                div.header,
                                                .layout,
                                                .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .fixed-width.has-border,
                                                .fixed-width.ecxhas-border,
                                                .has-gutter.has-border,
                                                .has-gutter.ecxhas-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .three-col .column,
                                                .column.narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .column.wide {
                                                    width: 400px !important;
                                                }

                                                .two-col.has-gutter .column,
                                                .two-col.ecxhas-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .three-col.has-gutter .column,
                                                .three-col.ecxhas-gutter .column,
                                                .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .two-col.has-gutter.has-border .column,
                                                .two-col.ecxhas-gutter.ecxhas-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .three-col.has-gutter.has-border .column,
                                                .three-col.ecxhas-gutter.ecxhas-border .column,
                                                .has-gutter.has-border .narrow,
                                                .has-gutter.ecxhas-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .has-gutter.has-border .wide,
                                                .has-gutter.ecxhas-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }
                                            }

                                            @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                            only screen and (min--moz-device-pixel-ratio: 2),
                                            only screen and (-o-min-device-pixel-ratio: 2/1),
                                            only screen and (min-device-pixel-ratio: 2),
                                            only screen and (min-resolution: 192dpi),
                                            only screen and (min-resolution: 2dppx) {
                                                .fblike {
                                                    background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .tweet {
                                                    background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .linkedinshare {
                                                    background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .forwardtoafriend {
                                                    background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }
                                            }

                                            @media (max-width: 321px) {
                                                .fixed-width.has-border .layout__inner {
                                                    border-width: 1px 0 !important;
                                                }

                                                .layout,
                                                .column {
                                                    min-width: 320px !important;
                                                    width: 320px !important;
                                                }

                                                .border {
                                                    display: none;
                                                }
                                            }

                                            .mso div {
                                                border: 0 none white !important;
                                            }

                                            .mso .w560 .divider {
                                                Margin-left: 260px !important;
                                                Margin-right: 260px !important;
                                            }

                                            .mso .w360 .divider {
                                                Margin-left: 160px !important;
                                                Margin-right: 160px !important;
                                            }

                                            .mso .w260 .divider {
                                                Margin-left: 110px !important;
                                                Margin-right: 110px !important;
                                            }

                                            .mso .w160 .divider {
                                                Margin-left: 60px !important;
                                                Margin-right: 60px !important;
                                            }

                                            .mso .w354 .divider {
                                                Margin-left: 157px !important;
                                                Margin-right: 157px !important;
                                            }

                                            .mso .w250 .divider {
                                                Margin-left: 105px !important;
                                                Margin-right: 105px !important;
                                            }

                                            .mso .w148 .divider {
                                                Margin-left: 54px !important;
                                                Margin-right: 54px !important;
                                            }

                                            .mso .size-8,
                                            .ie .size-8 {
                                                font-size: 8px !important;
                                                line-height: 14px !important;
                                            }

                                            .mso .size-9,
                                            .ie .size-9 {
                                                font-size: 9px !important;
                                                line-height: 16px !important;
                                            }

                                            .mso .size-10,
                                            .ie .size-10 {
                                                font-size: 10px !important;
                                                line-height: 18px !important;
                                            }

                                            .mso .size-11,
                                            .ie .size-11 {
                                                font-size: 11px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-12,
                                            .ie .size-12 {
                                                font-size: 12px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-13,
                                            .ie .size-13 {
                                                font-size: 13px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-14,
                                            .ie .size-14 {
                                                font-size: 14px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-15,
                                            .ie .size-15 {
                                                font-size: 15px !important;
                                                line-height: 23px !important;
                                            }

                                            .mso .size-16,
                                            .ie .size-16 {
                                                font-size: 16px !important;
                                                line-height: 24px !important;
                                            }

                                            .mso .size-17,
                                            .ie .size-17 {
                                                font-size: 17px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-18,
                                            .ie .size-18 {
                                                font-size: 18px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-20,
                                            .ie .size-20 {
                                                font-size: 20px !important;
                                                line-height: 28px !important;
                                            }

                                            .mso .size-22,
                                            .ie .size-22 {
                                                font-size: 22px !important;
                                                line-height: 31px !important;
                                            }

                                            .mso .size-24,
                                            .ie .size-24 {
                                                font-size: 24px !important;
                                                line-height: 32px !important;
                                            }

                                            .mso .size-26,
                                            .ie .size-26 {
                                                font-size: 26px !important;
                                                line-height: 34px !important;
                                            }

                                            .mso .size-28,
                                            .ie .size-28 {
                                                font-size: 28px !important;
                                                line-height: 36px !important;
                                            }

                                            .mso .size-30,
                                            .ie .size-30 {
                                                font-size: 30px !important;
                                                line-height: 38px !important;
                                            }

                                            .mso .size-32,
                                            .ie .size-32 {
                                                font-size: 32px !important;
                                                line-height: 40px !important;
                                            }

                                            .mso .size-34,
                                            .ie .size-34 {
                                                font-size: 34px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-36,
                                            .ie .size-36 {
                                                font-size: 36px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-40,
                                            .ie .size-40 {
                                                font-size: 40px !important;
                                                line-height: 47px !important;
                                            }

                                            .mso .size-44,
                                            .ie .size-44 {
                                                font-size: 44px !important;
                                                line-height: 50px !important;
                                            }

                                            .mso .size-48,
                                            .ie .size-48 {
                                                font-size: 48px !important;
                                                line-height: 54px !important;
                                            }

                                            .mso .size-56,
                                            .ie .size-56 {
                                                font-size: 56px !important;
                                                line-height: 60px !important;
                                            }

                                            .mso .size-64,
                                            .ie .size-64 {
                                                font-size: 64px !important;
                                                line-height: 63px !important;
                                            }
                                        </style>

                                        <!--[if !mso]><!-->
                                        <style type="text/css">
                                            @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                        </style>
                                        <style type="text/css">
                                            body {
                                                background-color: #fff
                                            }

                                            .logo a:hover,
                                            .logo a:focus {
                                                color: #1e2e3b !important
                                            }

                                            .mso .layout-has-border {
                                                border-top: 1px solid #ccc;
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .layout-has-bottom-border {
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .border,
                                            .ie .border {
                                                background-color: #ccc
                                            }

                                            .mso h1,
                                            .ie h1 {}

                                            .mso h1,
                                            .ie h1 {
                                                font-size: 26px !important;
                                                line-height: 34px !important
                                            }

                                            .mso h2,
                                            .ie h2 {}

                                            .mso h2,
                                            .ie h2 {
                                                font-size: 20px !important;
                                                line-height: 28px !important
                                            }

                                            .mso h3,
                                            .ie h3 {}

                                            .mso .layout__inner,
                                            .ie .layout__inner {}

                                            .mso .footer__share-button p {}

                                            .mso .footer__share-button p {
                                                font-family: Cabin, Avenir, sans-serif
                                            }
                                        </style>
                                        <meta name="robots" content="noindex,nofollow">
                                        </meta>
                                        <meta property="og:title" content="Mail v.01">
                                        </meta>
                                    </head>

                                    <body
                                        style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                        <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                            <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                <tr style="border-collapse:collapse;">
                                                    <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                        <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                    bgcolor="transparent">
                                                                    <div>
                                                                        <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="560" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <a target="_blank" href="https://digitalcv.id"
                                                                                                                style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                <img src="https://ultrajaya.digitalcv.id/id/assets/images/logo/logoDcv2.png"
                                                                                                                    alt
                                                                                                                    style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                    height="131">
                                                                                                            </a>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="42"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center" bgcolor="transparent"
                                                                    style="padding:0;Margin:0;background-color:transparent;">
                                                                    <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" style="padding:0;Margin:0;">
                                                                                                        <h1
                                                                                                            style="Margin:0;line-height:36px;mso-line-height-rule:exactly;font-family:tahoma, verdana, segoe, sans-serif;font-size:30px;font-style:normal;font-weight:bold;color:#fbb116;">
                                                                                                            Dear, ' . $namaKandidat . '</h1>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center"
                                                                                                        style="padding:0;Margin:0;padding-top:20px;">
                                                                                                        <p
                                                                                                            style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" style="padding:0;Margin:0;">
                                                                                                        <p
                                                                                                            style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            <b style="color:#fbb116">' . $namaPerusahaan .
                    '</b> bermaksud mengundang Anda untuk
                                                                                                            mengikuti proses seleksi untuk posisi <b
                                                                                                                style="color:#fbb116">' . $posisiLamar .
                    '</b>.
                                                                                                            Apakah Anda tertarik mengikuti proses seleksi
                                                                                                            untuk posisi <b style="color:#fbb116">'
                    . $posisiLamar .
                    '</b> di <b style="color:#fbb116">' .
                    $namaPerusahaan .
                    '</b>?<br><br>
                                                                                                            Jika Anda tertarik, silakan klik tombol di bawah
                                                                                                            ini untuk mengikuti proses seleksi:
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" style="padding:0;Margin:0;">
                                                                                                        <p
                                                                                                            style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: center;">
                                                                                                            <a href="' . $link . '"
                                                                                                                style="display: inline-block; 
                                                                                                                                                padding: 10px 20px; 
                                                                                                                                                font-size: 14px; 
                                                                                                                                                color: #fff; 
                                                                                                                                                background-color: #ffc107; 
                                                                                                                                                text-decoration: none; 
                                                                                                                                                border-radius: 5px; 
                                                                                                                                                border: 1px solid #ffc107;">
                                                                                                                Klik di sini
                                                                                                            </a>
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                    background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                    <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="left" style="padding:0;Margin:0;">
                                                                                                        <hr>
                                                                                                        <div>digitalcv<br>
                                                                                                            Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                            Barat<br>
                                                                                                            Telepon: 022-32097159<br>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="600" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" height="40"
                                                                                                        style="padding:0;Margin:0;"></td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </body>
                                    </table>
                                    <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                        src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                    </body>

                                    </html>'],
            ],
            'Subject' => ['Data' => 'Rekomendasi Lowongan Pekerjaan'],
        ],
        'Source' => 'digitalcv <<EMAIL>>',
    ];

    try {
        $result = $SesClient->sendEmail($emailParams);
        return true;
    } catch (AwsException $e) {
        return false;
    }
}


$conn->close();
exit;
