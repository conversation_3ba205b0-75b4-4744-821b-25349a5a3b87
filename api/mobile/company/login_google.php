<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../jwt_helper.php';

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}
if (!function_exists('random_bytes')) {
    function random_bytes($length = 36)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $characters_length = strlen($characters);
        $output = '';
        for ($i = 0; $i < $length; $i++)
            $output .= $characters[rand(0, $characters_length - 1)];
        return $output;
    }
}

$email = $_POST['email'];

$koordinat = "";
if (isset($_POST['koordinat'])) {
    $koordinat = $_POST['koordinat'];
}
$device = "";
if (isset($_POST['device'])) {
    $device = $_POST['device'];
}
$ip = "";
if (isset($_POST['ip'])) {
    $ip = $_POST['ip'];
}


$status = false;
$message = "Login gagal.";
$link = "beranda/index";
$response['data'] = [];

try {
    $conn->begin_transaction();

    // Cek email user
    $sql = $conn->prepare("SELECT pic.*, k.label, k.paket
                           FROM koordinator_pic pic 
                           JOIN koordinator k ON pic.id_koordinator = k.id_koordinator 
                           WHERE pic.email = ?");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        $storedHash = $row['password'];
        $id_pic = $row['id_pic'];
        $id_koordinator = $row['id_koordinator'];
        $divisi = $row['posisi'];
        $nama = $row['nama'];
        $img = $row['img'];
        $label = $row['label'];
        $role = $row['role'];
        $paket = $row['paket'];
        $noHp = $row['no_hp'];
        $fcmToken = $row['fcm_token'];

        $fitur_ids = explode("|", $row['fitur']);
        $fitur_user = [];

        if (!empty($fitur_ids)) {
            // Buat string ID untuk digunakan dalam query SQL (pakai prepared statement lebih aman)
            // Tapi karena array dinamis, kita akan bangun query secara dinamis
            $placeholders = implode(',', array_fill(0, count($fitur_ids), '?'));
            $types = str_repeat('s', count($fitur_ids)); // tipe parameter

            $stmt = $conn->prepare("SELECT sub_menu FROM list_fitur WHERE id_fitur IN ($placeholders)");
            $stmt->bind_param($types, ...$fitur_ids);
            $stmt->execute();
            $resultFitur = $stmt->get_result();

            while ($rowFitur = $resultFitur->fetch_assoc()) {
                $fitur_user[] = $rowFitur['sub_menu'];
            }

            $stmt->close();
        }

        // Generate JWT tokens
        $accessTokenData = [
            'id' => $id_pic,
            'id_koordinator' => $id_koordinator,
            'divisi' => $divisi,
            'nama' => $nama,
            'email' => $email,
            'label' => $label,
            'tipe' => 'company',
            'role' => $role,
            'paket' => $paket,
            'no_hp' => $noHp,
            'fitur_user' => $fitur_user,
            'fcm_token' => $fcmToken,
        ];

        $accessToken = generateAccessToken($accessTokenData);
        $refreshToken = generateRefreshToken($accessTokenData);

        $response['data'] = [
            'user_info' => [
                'id' => $id_pic,
                'id_koordinator' => $id_koordinator,
                'divisi' => $divisi,
                'nama' => $nama,
                'email' => $email,
                'img' => $img,
                'label' => $label,
                'tipe' => 'company',
                'role' => $role,
                'no_hp' => $noHp,
                'paket' => $paket,
                'fitur_user' => $fitur_user,
                'fcm_token' => $fcmToken,
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken
            ],
        ];

        // Store refresh token in database
        $stmt = $conn->prepare("UPDATE koordinator_pic SET refresh_token = ? WHERE id_pic = ?");
        $stmt->bind_param("ss", $accessToken, $id_pic);
        $stmt->execute();
        $stmt->close();

        // simpan login history
        $id_log_hist = date("YmdHis");
        $created_at = date("Y-m-d H:i:s");

        $stmt = $conn->prepare("INSERT INTO login_history (`id`, `id_user`, `koordinat`, `device`, `ip`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $id_log_hist, $id_pic, $koordinat, $device, $ip, $created_at);
        $stmt->execute();
        $stmt->close();

        $conn->commit();

        $status = true;
        $message = "Login berhasil.";
    } else {
        $status = false;
        $message = "Email belum terdaftar.";
    }
} catch (Exception $e) {
    $conn->rollback();
    $status = false;
    $message = $e->getMessage();
}

$response = [
    'status' => $status,
    'message' => $message,
    'data' => $status ? $response['data'] : [],
];

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
exit();

function generateAccessToken($userData)
{
    try {
        require_once __DIR__ . '/../../../vendor/autoload.php';

        // Load environment variables
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../..');
        $dotenv->load();

        $secretKey = $_ENV['ACCESS_TOKEN_SECRET'] ?? 'default_secret_key_for_development_only';

        $issuer = "digitalcv";
        $audience = "company";
        $issuedAt = time();
        $expirationTime = $issuedAt + (60 * 60 * 24); // 24 jam

        $payload = [
            'iss' => $issuer,
            'aud' => $audience,
            'iat' => $issuedAt,
            'exp' => $expirationTime,
            'data' => $userData
        ];

        return Firebase\JWT\JWT::encode($payload, $secretKey, 'HS256');
    } catch (Exception $e) {
        error_log('JWT Generation Error: ' . $e->getMessage());
        throw new Exception('Gagal membuat access token');
    }
}

function generateRefreshToken($userData)
{
    try {
        require_once __DIR__ . '/../../../vendor/autoload.php';

        // Load environment variables
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../..');
        $dotenv->load();

        $secretKey = $_ENV['REFRESH_TOKEN_SECRET'] ?? 'default_refresh_secret_key_for_development_only';

        $issuer = "digitalcv";
        $audience = "company";
        $issuedAt = time();
        $expirationTime = $issuedAt + (60 * 60 * 24 * 7); // 7 hari

        $payload = [
            'iss' => $issuer,
            'aud' => $audience,
            'iat' => $issuedAt,
            'exp' => $expirationTime,
            'data' => $userData
        ];

        return Firebase\JWT\JWT::encode($payload, $secretKey, 'HS256');
    } catch (Exception $e) {
        error_log('Refresh JWT Generation Error: ' . $e->getMessage());
        throw new Exception('Gagal membuat refresh token');
    }
}
