<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../../model/database.php';
include '../../../../api/s3.php';
require '../../../../vendor/autoload.php';
include '../../../../api/ses.php';
require '../../../fcm_helper.php';
include '../../../jwt_helper.php';

$userData = requireAuth();
// $price = $_POST['price'];
$qty = $_POST['qty'];
$paket = $_POST['paket'];

$status = 'gagal';
$message = 'gagal';
try {

    // if (empty($price)) {
    //     throw new Exception("price tidak boleh kosong");
    // }

    if (empty($qty)) {
        throw new Exception("qty tidak boleh kosong");
    }


    $va           = '0000008117797779'; //get on iPaymu dashboard
    $apiKey       = 'SANDBOXEA517E38-52EA-42E5-88F4-1DDD3F7A6366'; //get on iPaymu dashboard

    $url          = 'https://sandbox.ipaymu.com/api/v2/payment-channels'; // for development mode

    $method       = 'GET'; //method

    $requestBody  = strtolower(hash('sha256', '{}'));

    $stringToSign = strtoupper($method) . ':' . $va . ':' . $requestBody . ':' . $apiKey;
    $signature    = hash_hmac('sha256', $stringToSign, $apiKey);
    $timestamp    = Date('YmdHis');

    $ch = curl_init($url);

    $headers = array(
        'Accept: application/json',
        'Content-Type: application/json',
        'va: ' . $va,
        'signature: ' . $signature,
        'timestamp: ' . $timestamp
    );

    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

    curl_setopt($ch, CURLOPT_POST, 0);
    curl_setopt($ch, CURLOPT_HTTPGET, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, null);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $err = curl_error($ch);
    $ret = curl_exec($ch);
    curl_close($ch);

    if ($err) {
        echo $err;
        exit;
    } else {
        $sqlData = "SELECT price, keterangan  FROM pricing where paket = ?";
        $get = $conn->prepare($sqlData);
        $get->bind_param("s", $paket);

        $get->execute();
        $result = $get->get_result();
        $get->close();
        if ($result->num_rows > 0) {
            $row = mysqli_fetch_assoc($result); // Ambil sekali saja
            $harga = ((int) $row['price'] * $qty);
            $keterangan = $row['keterangan'];
            $ppn = (($harga * 1) * 11) / 100;
            $response = [
                'status' => 'success',
                'message' => 'Berhasil Mengambil data',
                'data' => [
                    'payment' => [
                        [
                            'keterangan' => $keterangan,
                            'price' => (int) $row['price'],
                            'qty' => $qty,
                            'price_total' => $harga,
                        ],
                        [
                            'keterangan' => "PPN",
                            'price' => $ppn,
                            'qty' => "1",
                            'price_total' => $ppn
                        ]
                    ],
                    'total' => ($harga + $ppn),
                    'channel' => json_decode($ret)->Data
                ]
            ];
            echo json_encode($response, JSON_PRETTY_PRINT);
            exit();
        }
    }
} catch (Exception $e) {
    $status = false;
    $message = $e->getMessage();
}

$response = [
    "status" => $status,
    "message" => $message
];

echo json_encode($response, JSON_PRETTY_PRINT);
exit();
