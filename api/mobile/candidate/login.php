<?php
header("Content-Type: application/json");
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';

require_once(__DIR__ . '/../../../vendor/autoload.php');

use Firebase\JWT\JWT;
use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/../../..');
$dotenv->load();

// Security functions
function sanitizeOutput($data)
{
    if (is_array($data)) {
        $protected_fields = ['img', 'foto', 'avatar', 'logoURL', 'link', 'access_token', 'refresh_token'];
        $result = [];
        foreach ($data as $key => $value) {
            if (in_array($key, $protected_fields)) {
                // Protect S3 URLs and tokens from HTML encoding
                if (is_string($value) && (strpos($value, 'amazonaws.com') !== false ||
                    strpos($value, 'http') === 0 || filter_var($value, FILTER_VALIDATE_URL) ||
                    strpos($key, 'token') !== false)) {
                    $result[$key] = $value;
                } else {
                    $result[$key] = $value;
                }
            } else {
                $result[$key] = sanitizeOutput($value);
            }
        }
        return $result;
    } elseif (is_string($data)) {
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
    return $data;
}

function validateInput($input, $type, $options = [])
{
    switch ($type) {
        case 'email':
            $input = decryptEmail($input);
            $email = filter_var($input, FILTER_VALIDATE_EMAIL);
            return ($email && strlen($email) <= 255) ? $email : false;
        case 'password':
            return (strlen($input) >= 6 && strlen($input) <= 255) ? $input : false;
        case 'filename':
            return preg_match('/^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|gif|svg)$/i', $input) ? $input : false;
        case 'coordinate':
            return preg_match('/^-?\d+\.?\d*,-?\d+\.?\d*$/', $input) ? $input : false;
        case 'ip':
            return filter_var($input, FILTER_VALIDATE_IP) ? $input : false;
        case 'string':
            $maxLength = $options['max_length'] ?? 255;
            $cleaned = trim($input);
            return (strlen($cleaned) <= $maxLength) ? $cleaned : false;
        default:
            return false;
    }
}


function verifyPassword($inputPassword, $hashedPassword)
{

    $key = base64_decode('vewCDRQayYKYw3LCN/DQdK1FegvPTMaduED2M5ibdnw='); // SAMA dengan client
    $iv = 'e9256924d71fbcfa';
    $encrypted = base64_decode($inputPassword);
    $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);

    return password_verify($decrypted, $hashedPassword);
}

function decryptEmail($email)
{
    $key = base64_decode('vewCDRQayYKYw3LCN/DQdK1FegvPTMaduED2M5ibdnw='); // SAMA dengan client
    $iv = 'e9256924d71fbcfa';
    $encrypted = base64_decode($email);
    $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);

    return $decrypted;
}

function checkLoginAttempts($conn, $email)
{
    // Cek apakah ada record login attempts untuk email ini
    $sql = $conn->prepare("SELECT attempts, blocked_until FROM login_attempts WHERE email = ?");
    $sql->bind_param("s", $email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $attempts = $row['attempts'];
        $blocked_until = $row['blocked_until'];

        // Cek apakah masih dalam periode pemblokiran
        if ($blocked_until && strtotime($blocked_until) > time()) {
            $remaining_time = strtotime($blocked_until) - time();
            return [
                'blocked' => true,
                'attempts' => $attempts,
                'remaining_time' => $remaining_time
            ];
        }

        // Jika periode pemblokiran sudah lewat, reset attempts
        if ($blocked_until && strtotime($blocked_until) <= time()) {
            $reset_sql = $conn->prepare("UPDATE login_attempts SET attempts = 0, blocked_until = NULL WHERE email = ?");
            $reset_sql->bind_param("s", $email);
            $reset_sql->execute();
            $reset_sql->close();
            return ['blocked' => false, 'attempts' => 0];
        }

        return ['blocked' => false, 'attempts' => $attempts];
    }

    return ['blocked' => false, 'attempts' => 0];
}

function recordFailedLogin($conn, $email)
{
    // Cek apakah sudah ada record untuk email ini
    $check_sql = $conn->prepare("SELECT attempts FROM login_attempts WHERE email = ?");
    $check_sql->bind_param("s", $email);
    $check_sql->execute();
    $result = $check_sql->get_result();
    $check_sql->close();

    if ($result->num_rows > 0) {
        // Update attempts yang sudah ada
        $row = $result->fetch_assoc();
        $new_attempts = $row['attempts'] + 1;

        // Jika mencapai 5 attempts, blokir selama 1 menit
        if ($new_attempts >= 5) {
            $blocked_until = date('Y-m-d H:i:s', time() + 60); // 1 menit
            $update_sql = $conn->prepare("UPDATE login_attempts SET attempts = ?, blocked_until = ?, last_attempt = NOW() WHERE email = ?");
            $update_sql->bind_param("iss", $new_attempts, $blocked_until, $email);
        } else {
            $update_sql = $conn->prepare("UPDATE login_attempts SET attempts = ?, last_attempt = NOW() WHERE email = ?");
            $update_sql->bind_param("is", $new_attempts, $email);
        }
        $update_sql->execute();
        $update_sql->close();

        return $new_attempts;
    } else {
        // Insert record baru
        $insert_sql = $conn->prepare("INSERT INTO login_attempts (email, attempts, last_attempt) VALUES (?, 1, NOW())");
        $insert_sql->bind_param("s", $email);
        $insert_sql->execute();
        $insert_sql->close();

        return 1;
    }
}

function resetLoginAttempts($conn, $email)
{
    $reset_sql = $conn->prepare("DELETE FROM login_attempts WHERE email = ?");
    $reset_sql->bind_param("s", $email);
    $reset_sql->execute();
    $reset_sql->close();
}

function generateAccessToken($userData)
{
    $secretKey = $_ENV['ACCESS_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24); // 24 jam

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'nama' => $userData['nama'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}

function generateRefreshToken($userData)
{
    $secretKey = $_ENV['REFRESH_TOKEN_SECRET'];
    $issuer = "digitalcv";
    $audience = "candidate";
    $issuedAt = time();
    $expirationTime = $issuedAt + (60 * 60 * 24 * 30); // 30 hari

    $payload = [
        'iss' => $issuer,
        'aud' => $audience,
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'pin' => $userData['pin'],
            'email' => $userData['email'],
            'tipe' => 'kandidat'
        ]
    ];

    return JWT::encode($payload, $secretKey, 'HS256');
}

function formatTanggal($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = date_create($tanggal);
    if (!$tanggalObj) {
        return "Format tanggal tidak valid";
    }

    $tanggalFormat = date_format($tanggalObj, "j") . " " .
        $bulanIndo[date_format($tanggalObj, "n") - 1] . " " .
        date_format($tanggalObj, "Y");

    return $tanggalFormat;
}

// Validate and sanitize input parameters
$email = $_POST['email'] ?? '';
$inputPassword = $_POST['password'] ?? '';
$koordinat = $_POST['koordinat'] ?? '';
$device = $_POST['device'] ?? '';
$ip = $_POST['ip'] ?? '';

// Validate inputs
$clean_email = validateInput($email, 'email');
$clean_password = validateInput($inputPassword, 'password');
$clean_koordinat = !empty($koordinat) ? validateInput($koordinat, 'coordinate') : '';
$clean_device = !empty($device) ? validateInput($device, 'string', ['max_length' => 100]) : '';
$clean_ip = !empty($ip) ? validateInput($ip, 'ip') : '';

if (!$clean_email) {
    $responses = [
        "success" => false,
        "message" => "Format email tidak valid",
        "data" => [],
    ];
    echo json_encode($responses, JSON_PRETTY_PRINT);
    exit();
}

if (!$clean_password) {
    $responses = [
        "success" => false,
        "message" => "Password tidak valid",
        "data" => [],
    ];
    echo json_encode($responses, JSON_PRETTY_PRINT);
    exit();
}

$status = false;
$message = "Login gagal.";

$response['data'] = [];
try {
    // Mulai proses
    $conn->begin_transaction();


    // Cek pembatasan login attempts
    $loginCheck = checkLoginAttempts($conn, $clean_email);
    if ($loginCheck['blocked']) {
        $remaining_minutes = ceil($loginCheck['remaining_time'] / 60);
        throw new Exception("Akun Anda diblokir karena terlalu banyak percobaan login yang gagal. Silakan coba lagi dalam $remaining_minutes menit.");
    }

    // cek email user dengan secure query
    $sql = $conn->prepare("SELECT u.*, r.tempat_lahir, 
    r.tgl_lahir,  r.jenis_kelamin, 
    CONCAT(r.alamat, ' RT ', r.rt, ' RW ', r.rw, ' ', 
    r.kecamatan, ' ', r.kota, ' ', r.provinsi, '. ', r.kode_pos) as alamat
    FROM users_kandidat u LEFT JOIN rh r ON u.pin = r.id WHERE u.email = ?");
    $sql->bind_param("s", $clean_email);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $storedHash  = $row['password'];
        $pin = $row['pin'];
        $nama = $row['nama_lengkap'];
        $img = $row['foto'];
        $no_telp = $row['no_telp'];
        $tempat_lahir = $row['tempat_lahir'];
        $tgl_lahir = formatTanggal($row['tgl_lahir']);
        $jk = $row['jenis_kelamin'];
        $alamat = $row['alamat'];
        $fcmToken = $row['fcm_token'];

        // Secure S3 image handling
        $secure_img = "";
        if (!empty($img)) {
            // Sanitize filename to prevent path traversal
            $filename = basename($img);
            if (validateInput($filename, 'filename')) {
                $s3Key = 'kandidat/foto-profil/' . $filename;

                try {
                    if ($s3->doesObjectExist($bucket, $s3Key)) {
                        $cmd = $s3->getCommand('GetObject', [
                            'Bucket' => $bucket,
                            'Key'    => $s3Key
                        ]);
                        $request = $s3->createPresignedRequest($cmd, '+24 hours');
                        $secure_img = (string) $request->getUri();
                    }
                } catch (Exception $e) {
                    error_log("S3 error: " . $e->getMessage());
                    $secure_img = "";
                }
            }
        }

        // verifikasi password
        if (verifyPassword($clean_password, $storedHash)) {
            // Reset login attempts jika berhasil login
            resetLoginAttempts($conn, $clean_email);

            $dataUser = array('pin' => $pin, 'nama' => $nama, 'email' => $clean_email, 'img' => $secure_img, 'tipe' => 'kandidat');
            $_SESSION['users'] = $dataUser;
            unset($_SESSION['user_google']);

            // Generate JWT tokens
            $accessToken = generateAccessToken($dataUser);

            // Simpan refresh token ke database dengan secure query
            $refresh_token_expiry = date('Y-m-d H:i:s', time() + (60 * 60 * 24 * 30)); // 30 hari
            $update_token_sql = $conn->prepare("UPDATE users_kandidat SET refresh_token = ?, refresh_token_expiry = ? WHERE pin = ?");
            $update_token_sql->bind_param("sss", $accessToken, $refresh_token_expiry, $pin);
            $update_token_sql->execute();
            $update_token_sql->close();

            // simpan login history dengan secure query
            $id_log_hist = date("YmdHis");
            $created_at = date("Y-m-d H:i:s");

            $stmt = $conn->prepare("INSERT INTO login_history (`id`, `id_user`, `koordinat`, `device`, `ip`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssss", $id_log_hist, $pin, $clean_koordinat, $clean_device, $clean_ip, $created_at);
            $stmt->execute();

            // Log aktivitas berhasil login
            logActivity($conn, $row['pin'], "INFO", "User atas nama $nama telah login ke sistem.", "Web Digitalcv");

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = true;
            $message = "Login berhasil.";
        } else {
            // Record failed login attempt
            $attempts = recordFailedLogin($conn, $clean_email);
            $remaining_attempts = 5 - $attempts;

            if ($attempts >= 5) {
                throw new Exception("Terlalu banyak percobaan login yang gagal. Akun Anda diblokir selama 1 menit.");
            } else {
                throw new Exception("Password salah! Sisa percobaan: $remaining_attempts kali.");
            }
        }
        $response['data'][] = array(
            'pin' => $pin,
            'nama' => $nama,
            'email' => $clean_email,
            'img' => $secure_img,
            'no_telp' => $no_telp,
            'tempat_lahir' => $tempat_lahir,
            'tgl_lahir' => $tgl_lahir,
            'jenis_kelamin' => $jk,
            'alamat' => $alamat,
            'fcm_token' => $fcmToken,
            'access_token' => isset($accessToken) ? $accessToken : null
        );
    } else {
        // Record failed login attempt even for non-existent email
        $attempts = recordFailedLogin($conn, $clean_email);
        $remaining_attempts = 5 - $attempts;

        if ($attempts >= 5) {
            throw new Exception("Terlalu banyak percobaan login yang gagal. Akun Anda diblokir selama 1 menit.");
        } else {
            throw new Exception("Email belum terdaftar. Sisa percobaan: $remaining_attempts kali.");
        }
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = false;
    $message = $e->getMessage();
}

$responses = [
    "success" => $status,
    "message" => $message,
    "data" => $response['data'],
];

// Sanitize output untuk melindungi dari XSS dan corrupted URLs
$sanitized_responses = sanitizeOutput($responses);

$output = json_encode($sanitized_responses, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit();
