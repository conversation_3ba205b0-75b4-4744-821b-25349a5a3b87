const Toast = Swal.mixin({
  toast: true,
  position: 'top-end',
  showConfirmButton: false,
  timer: 3000
});

function getData() {
	$("#table-listRequest").DataTable({
        "ordering": false,
        "ajax" : {
            "url" : '../../controller/superadminController.php?func=getDataRequest',
            "dataSrc": "data",
            complete: function(data) { 
            console.log(data);  
        },
        },
        "lengthChange": false
    });

  $("#table-listIklan").DataTable({
        "ordering": false,
        "ajax" : {
            "url" : '../../controller/superadminController.php?func=getListIklan&data=Default&status=Pending',
            "dataSrc": "data",
            complete: function(data) { 
            console.log(data);  
            $('.loader2').fadeOut('slow');
            $('#listIklan').fadeIn('slow');
        },
        },
        "lengthChange": false
    });
}

function detailRequest(id) {
  $('.viewDetail').fadeOut('slow');
  $('.loader2').fadeIn('slow');
  $.get("../../controller/superadminController.php",{func: 'detailRequest', id: id},
      function(data) {
        var obj = JSON.parse(data);
        $('.loader2').fadeOut('slow');
        $('#tampil-detailRequest').html(obj[0]);
        $('.viewDetail').fadeIn('slow');
    });
}

function btnApprove(stat, id) {
  $.post("../../controller/superadminController.php?func=aksiApprove",{id: id, stat: stat},
      function(data) {
        $('#modal-DetailRequest').modal('toggle');

        if (data == 'Yes') {
          if (stat == 'Approve') {
            Toast.fire({
              type: 'success',
              title: 'Request berhasil diapprove!'
            })
          }else{
            Toast.fire({
              type: 'success',
              title: 'Request berhasil di-disapprove!'
            })
          }
        }else{
          Toast.fire({
              type: 'error',
              title: 'Request berhasil diapprove!'
            })
        }
        $("#table-listRequest").DataTable().ajax.reload();
    });
}

function filterIklan() {
  var data = $('#filterBulan').val();
      status = $('#filterStatus').val();


  $('#listIklan').fadeOut('slow');
  $('.loader2').fadeIn('slow');

  $("#table-listIklan").DataTable().destroy();
  $("#table-listIklan").DataTable({
        "ordering": false,
        "ajax" : {
            "url" : '../../controller/superadminController.php?func=getListIklan&data='+data+'&status='+status,
            "dataSrc": "data",
            complete: function(data) { 
            console.log(data);  
            $('.loader2').fadeOut('slow');
            $('#listIklan').fadeIn('slow');
        },
        },
        "lengthChange": false
    });


}

function aksiIklan(id_iklan, stat) {
  $.post("../../controller/superadminController.php?func=aksiApprove", { id: id_iklan, stat :stat},
    function(data) {

        if (data == 'Yes') {
          if (stat == 'Approve') {
            Toast.fire({
              type: 'success',
              title: 'Request berhasil diapprove!'
            })
          }else{
            Toast.fire({
              type: 'success',
              title: 'Request berhasil di-disapprove!'
            })
          }
        }else{
          Toast.fire({
              type: 'error',
              title: 'Request berhasil diapprove!'
            })
        }
        $("#table-listIklan").DataTable().ajax.reload();

    });
}



