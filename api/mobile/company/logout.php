<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../jwt_helper.php';

$response = [
    'success' => false,
    'message' => 'Logout gagal',
    'data' => []
];

// Middleware untuk autentikasi JWT
$userData = requireAuth();

try {
    // 1. Ambil token dari database untuk validasi
    $sql = $conn->prepare("SELECT refresh_token FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
    $sql->bind_param("ss", $userData->id, $userData->id_koordinator);
    $sql->execute();
    $result = $sql->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("Data pengguna tidak ditemukan");
    }

    $tokenData = $result->fetch_assoc();
    $sql->close();
    if (blacklistToken($tokenData['refresh_token'])) {
        // 2. Hapus refresh token dari database
        $sql = $conn->prepare("UPDATE koordinator_pic SET refresh_token = NULL, fcm_token = NULL WHERE id_pic = ? AND id_koordinator = ?");
        $sql->bind_param("ss", $userData->id, $userData->id_koordinator);

        if ($sql->execute()) {
            $sql->close();

            // Log aktivitas logout jika fungsi tersedia
            if (function_exists('logActivity')) {
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                logActivity(
                    $conn,
                    $userData->id,
                    "INFO",
                    "User atas nama {$userData->nama} telah logout dari sistem. Token di-blacklist. IP: {$ipAddress}",
                    "Mobile App"
                );
            }

            $response = [
                'success' => true,
                'message' => 'Logout berhasil. Token telah dinonaktifkan dan tidak dapat digunakan lagi.',
                'data' => [
                    'logged_out_at' => date('Y-m-d H:i:s'),
                    'user_name' => $userData->nama ?? 'Unknown',
                    'token_blacklisted' => true
                ]
            ];
        } else {
            throw new Exception("Gagal menghapus refresh token");
        }
    } else {
        $response = [
                'success' => false,
                'message' => 'Logout Gagal'
            ];
        exit;
    }
} catch (Exception $e) {
    // Log error untuk debugging jika fungsi tersedia
    if (function_exists('logActivity')) {
        logActivity(
            $conn,
            $userData->id ?? 'unknown',
            "ERROR",
            "Logout gagal: " . $e->getMessage(),
            "Mobile App"
        );
    }

    $response['message'] = 'Logout gagal: ' . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
