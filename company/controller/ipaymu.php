<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../model/database.php';
require '../../api/fcm_helper.php';
include '../../api/jwt_helper.php';
if (isset($_SESSION['lang_params'])) {
    $bahasa = $_SESSION['lang_params'];
} else {
    $_SESSION['lang_params'] = 'id';
    $bahasa = $_SESSION['lang_params'];
}

$va = '0000008117797779'; //get on iPaymu dashboard
$apiKey = 'SANDBOXEA517E38-52EA-42E5-88F4-1DDD3F7A6366'; //get on iPaymu dashboard

$url = 'https://sandbox.ipaymu.com/api/v2/payment'; // for development mode

$id_koordinator = 'null';
$user_id = 'null';
$email = 'null';
$status = "success";

$paket = 'null';
$is_mobile = false;

if (isset($_SESSION['users-pic']) && isset($_POST['paket'])) {
    $id_koordinator = $_SESSION['users-pic']['id_koordinator'];
    $user_id = addslashes($_SESSION['users-pic']['id']);
    $email = $_SESSION['users-pic']['email'];
    $paket = $_POST['paket'];
    $is_mobile = false;
}

if (empty($_SESSION['users-pic'])) {
    $userData = requireAuth();
    $id_koordinator = $userData->id_koordinator;
    $user_id = $userData->id;
    $email = $userData->email;
    $paket = $_POST['paket'];
    $is_mobile = true;
}

function payment($va, $apiKey, $url, $id_koordinator, $product, $price, $description, $buyerName, $email, $buyerPhone, $bahasa, $qty, $is_mobile)
{
    $body['product'][] = trim($product);
    $body['product'][] = trim('PPN');
    $body['qty'][] = $qty;
    $body['qty'][] = '1';
    $body['price'][] = $price;
    $body['price'][] = (($price * $qty) * 11) / 100;
    $body['description'][] = $description;
    $body['description'][] = 'PPN Indonesia';
    $body['imageUrl'][] = 'https://digitalcv.id/assets/images/logo/logoDcv2.png';
    $body['imageUrl'][] = 'https://digitalcv.id/assets/images/logo/logoDcv2.png';
    $body['referenceId'] = trim($id_koordinator);
    if ($is_mobile == true) {
        $body['returnUrl'] = trim('https://gestaltdev.digitalcv.id/company/pembayaran-berhasil-mobile');
        $body['cancelUrl'] = trim('https://gestaltdev.digitalcv.id/company/pembayaran-gagal-mobile');
    } else {
        $body['returnUrl'] = trim('http://localhost/digitalcv/company/pembayaran-berhasil');
        $body['cancelUrl'] = trim('http://localhost/digitalcv/company/pembayaran-gagal');
    }
    $body['notifyUrl'] = trim('https://gestaltdev.digitalcv.id/api/ipaymu/callback');

    $body['buyerName'] = $buyerName;
    $body['buyerEmail'] = trim($email);
    $body['buyerPhone'] = trim($buyerPhone);
    $body['expired'] = '24';
    $body['feeDirection'] = 'BUYER';
    $body['account'] = $va;
    $body['lang'] = $bahasa;

    $price_total = $price + ($price * 11 / 100);

    // Generate Signature
    // *Don't change this
    $jsonBody = json_encode($body, JSON_UNESCAPED_SLASHES);
    $requestBody = strtolower(hash('sha256', $jsonBody));
    $stringToSign = strtoupper('POST') . ':' . $va . ':' . $requestBody . ':' . $apiKey;
    $signature = hash_hmac('sha256', $stringToSign, $apiKey);
    $timestamp = Date('YmdHis');
    //End Generate Signature

    // Request header
    $headers = array(
        'Accept: application/json',
        'Content-Type: application/json',
        'va: ' . $va,
        'signature: ' . $signature,
        'timestamp: ' . $timestamp
    );

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    curl_setopt($ch, CURLOPT_POST, count($body));
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonBody);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $err = curl_error($ch);
    $response = curl_exec($ch);
    curl_close($ch);

    // Return the data as an associative array
    return [
        'response' => $response,
        'err' => $err,
        'price_total' => $price_total,
        'signature' => $signature,
    ];
}

$qty = 0;
if (isset($_POST['kategori']) && $_POST['kategori'] == 'paket akun company') {
    if ($paket == 'C1') {
        try {
            // Mulai proses
            $conn->begin_transaction();
            // update status lamaran user
            $update = $conn->prepare("UPDATE koordinator set paket='C1' where id_koordinator = ?");
            $update->bind_param("s", $id_koordinator);

            if ($update->execute()) {
                // Jika semua query berhasil, commit transaksi
                $conn->commit();
                $status = "success";
                $message = translate('Data berhasil disimpan.');
                $link = $is_mobile == true ? 'C1' : 'dashboard/beranda/index';
                $update->close();

                $data['status'] = $status;
                $data['message'] = $message;
                $data['link'] = $link;
                $output = json_encode($data, JSON_PRETTY_PRINT);
                echo $output;

                $conn->close();
                exit;
            } else {
                throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
            }
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();
            $status = "gagal";
            $link = 'package';
            $message = $e->getMessage();
        }
    } else {
        $sqlData = "SELECT * FROM pricing where keterangan LIKE 'Paket digitalcv%' and paket = ?";
        $get = $conn->prepare($sqlData);
        $get->bind_param("s", $paket);

        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $price = floatval($row['price']);
                $product = $row['keterangan'];
                $description = $row['keterangan'];
                $qty = 1;
            }
        } else {
            $status = "gagal";
            $message = 'Paket tidak terdaftar';
            $link = 'package';

            // simpan log aktivitas
            $messages = 'Paket tidak terdaftar.';
            $extra_info = "Pembelian Paket Gagal";
            $level = "ERROR";
            $path = $_SERVER['REQUEST_URI'];
            logActivity($conn, $user_id, $level, $messages, $extra_info);
        }
    }
} elseif (isset($_POST['kategori']) && $_POST['kategori'] == 'pendaftaran psikotes') {
    // validasi jumlah
    $input = isset($_POST['jml_kuota']) ? trim($_POST['jml_kuota']) : '';

    // jika kosong
    if ($input === '') {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak boleh kosong.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    // cek apakah numeric
    if (!is_numeric($input)) {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak sesuai. Silakan sesuaikan kembali.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    // ubah ke number (integer)
    $num = (int) $input;

    // jika < 1
    if ($num < 1) {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak sesuai. Silakan sesuaikan kembali.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    $qty = $num;

    $sqlData = "SELECT * FROM pricing where kategori = 'pendaftaran psikotes' and paket = ?";
    $get = $conn->prepare($sqlData);
    $get->bind_param("s", $paket);

    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_assoc($result);
        $price = floatval($row['price']);
        $product = $row['keterangan'];
        $description = $row['keterangan'];
    } else {
        $status = "gagal";
        $message = 'Paket tidak terdaftar';
        $link = '';

        // simpan log aktivitas
        $messages = 'Paket tidak terdaftar.';
        $extra_info = "Pembelian Psikotes Gagal";
        $level = "ERROR";
        $path = $_SERVER['REQUEST_URI'];
        logActivity($conn, $user_id, $level, $messages, $extra_info);
    }
} elseif (isset($_POST['kategori']) && $_POST['kategori'] == 'pencarian kandidat') {
    // validasi jumlah
    $input = isset($_POST['jml_kuota']) ? trim($_POST['jml_kuota']) : '';

    // jika kosong
    if ($input === '') {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak boleh kosong.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    // cek apakah numeric
    if (!is_numeric($input)) {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak sesuai. Silakan sesuaikan kembali.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    // ubah ke number (integer)
    $num = (int) $input;

    // jika < 1
    if ($num < 1) {
        $data['status'] = 'gagal';
        $data['message'] = translate('Jumlah kuota tidak sesuai. Silakan sesuaikan kembali.');
        $data['link'] = '';

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
        exit;
    }

    $qty = $num;

    $sqlData = "SELECT * FROM pricing where kategori = 'pencarian kandidat' and paket = ?";
    $get = $conn->prepare($sqlData);
    $get->bind_param("s", $paket);

    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_assoc($result);
        $price = floatval($row['price']);
        $product = $row['keterangan'];
        $description = $row['keterangan'];
    } else {
        $status = "gagal";
        $message = 'Paket tidak terdaftar';
        $link = '';

        // simpan log aktivitas
        $messages = 'Paket tidak terdaftar.';
        $extra_info = "Pembelian Pencarian Kandidat Gagal";
        $level = "ERROR";
        $path = $_SERVER['REQUEST_URI'];
        logActivity($conn, $user_id, $level, $messages, $extra_info);
    }
}

if ($status == 'success') {
    $get = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
    $get->bind_param("s", $id_koordinator);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_assoc($result);

        $buyerName = $row['label'];
        $expired = $row['expired'];
        $buyerPhone = $row['tlp'];

        $payment_result = payment($va, $apiKey, $url, $id_koordinator, $product, $price, $description, $buyerName, $email, $buyerPhone, $bahasa, $qty, $is_mobile);

        $err = $payment_result['err'];
        $response = $payment_result['response'];
        $price_total = $payment_result['price_total'];
        $signature = $payment_result['signature'];

        $data = array();

        if ($err) {
            $status = "gagal";
            $message = $err;
            $link = 'package';
        } else {
            $responseDecode = json_decode($response, true);
            $sessionid = $responseDecode['Data']['SessionID'];
            $link_ipaymu = $responseDecode['Data']['Url'];
            $created_at = date("Y-m-d H:i:s");


            try {
                // Mulai proses
                $conn->begin_transaction();
                // update status lamaran user
                $update = $conn->prepare("INSERT INTO `payment` (`SessionID`, `product`, `qty`, `price`, `description`, `referenceId`, `buyerName`, `buyerEmail`, `buyerPhone`, `status`, `paket`, `created_at`, `email`, `lang`,`signature`,`keterangan`,`expired_at`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?, ?, 'Pembelian/Upgrade Paket digitalcv.id', ?)");
                $update->bind_param("ssissssssssssss", $sessionid, $product, $qty, $price_total, $description, $id_koordinator, $buyerName, $email, $buyerPhone, $paket, $created_at, $email, $bahasa, $signature, $expired);

                if ($update->execute()) {

                    $conn->commit();
                    $status = "success";
                    $message = translate('Data berhasil disimpan.');
                    $link = $link_ipaymu;
                    $update->close();
                } else {
                    throw new Exception(translate('Tidak dapat menyimpan data. Silakan hubungi administrator.'));
                }
            } catch (Exception $e) {
                // Jika ada error, rollback proses
                $conn->rollback();
                $status = "gagal";
                $link = 'package';
                $message = $e->getMessage();
            }
        }
    } else {
        $status = "gagal";
        $message = 'Perusahaan tidak terdaftar.';
        $link = '';
    }
} else {
    $status = "gagal";
    $message = translate('Pembelian gagal. Silakan hubungi administrator.');
    $link = '';
}

$data['status'] = $status;
$data['message'] = $message;
$data['link'] = $link;

$output = json_encode($data, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit;
