<?php
require_once(__DIR__ . '/../vendor/autoload.php');

use Firebase\JWT\JWT;
use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

function verifyAccessToken($token)
{
    try {
        // Validasi input token - cek null, empty, atau hanya whitespace
        if (empty($token) || !is_string($token) || trim($token) === '') {
            return [
                'valid' => false,
                'message' => 'Token kosong atau tidak valid',
                'data' => null,
            ];
        }

        // Trim whitespace dari token
        $token = trim($token);

        // Validasi format JWT basic - harus ada minimal 2 titik
        if (substr_count($token, '.') < 2) {
            return [
                'valid' => false,
                'message' => 'Format token tidak valid',
                'data' => null,
            ];
        }

        // Validasi panjang token - JWT biasanya minimal 50 karakter
        if (strlen($token) < 50) {
            return [
                'valid' => false,
                'message' => 'Token terlalu pendek',
                'data' => null,
            ];
        }

        // Coba beberapa cara untuk mendapatkan secret key dengan prioritas
        $secretKey = null;

        // Prioritas 1: Environment variable dari $_ENV
        if (isset($_ENV['ACCESS_TOKEN_SECRET']) && !empty($_ENV['ACCESS_TOKEN_SECRET'])) {
            $secretKey = $_ENV['ACCESS_TOKEN_SECRET'];
        }
        // Prioritas 2: getenv()
        elseif (getenv('ACCESS_TOKEN_SECRET')) {
            $secretKey = getenv('ACCESS_TOKEN_SECRET');
        }
        // Prioritas 3: Coba dari file .env langsung
        elseif (file_exists(__DIR__ . '/../.env')) {
            $envContent = file_get_contents(__DIR__ . '/../.env');
            if (preg_match('/ACCESS_TOKEN_SECRET=(.*)/', $envContent, $matches)) {
                $secretKey = trim($matches[1]);
            }
        }
        // Prioritas 4: Default fallback (untuk development)
        else {
            // Log warning untuk production
            error_log('WARNING: ACCESS_TOKEN_SECRET tidak ditemukan, menggunakan default key');
            $secretKey = 'default_secret_key_for_development_only';
        }

        // Validasi secret key
        if (empty($secretKey) || !is_string($secretKey) || strlen($secretKey) < 8) {
            error_log('ERROR: Secret key tidak valid atau terlalu pendek');
            return [
                'valid' => false,
                'message' => 'Konfigurasi server tidak valid',
                'data' => null,
            ];
        }

        // Set timezone untuk konsistensi
        $originalTimezone = date_default_timezone_get();
        date_default_timezone_set('UTC');

        // Decode JWT token dengan error handling yang lebih baik
        // Gunakan format lama yang kompatibel dengan sebagian besar versi
        $decoded = JWT::decode($token, $secretKey, ['HS256']);

        // Restore timezone
        date_default_timezone_set($originalTimezone);

        // Validasi struktur token yang lebih ketat
        if (!is_object($decoded)) {
            return [
                'valid' => false,
                'message' => 'Token tidak dapat di-decode',
                'data' => null,
            ];
        }

        if (!isset($decoded->exp) || !isset($decoded->data)) {
            return [
                'valid' => false,
                'message' => 'Struktur token tidak valid',
                'data' => null,
            ];
        }

        // Validasi exp adalah integer
        if (!is_numeric($decoded->exp)) {
            return [
                'valid' => false,
                'message' => 'Waktu expired tidak valid',
                'data' => null,
            ];
        }

        // Cek apakah token sudah expired dengan toleransi waktu
        $currentTime = time();
        $toleranceSeconds = 30; // 30 detik toleransi untuk clock skew

        if ($decoded->exp < ($currentTime - $toleranceSeconds)) {
            return [
                'valid' => false,
                'message' => 'Token sudah expired',
                'data' => null,
            ];
        }

        // Validasi bahwa token tidak terlalu jauh di masa depan (anti-tampering)
        $maxFutureTime = $currentTime + (24 * 60 * 60); // 24 jam ke depan
        if ($decoded->exp > $maxFutureTime) {
            return [
                'valid' => false,
                'message' => 'Token memiliki waktu expired yang tidak valid',
                'data' => null,
            ];
        }

        // Validasi data user dalam token yang lebih ketat
        if (empty($decoded->data) || !is_object($decoded->data)) {
            return [
                'valid' => false,
                'message' => 'Data user dalam token tidak valid',
                'data' => null,
            ];
        }

        // // Cek field wajib dalam data
        // if (!isset($decoded->data->pin) || empty($decoded->data->pin)) {
        //     return [
        //         'valid' => false,
        //         'message' => 'PIN user tidak ditemukan dalam token',
        //         'data' => null,
        //         'error_code' => 'MISSING_PIN'
        //     ];
        // }

        // // Validasi PIN format (harus numerik)
        // if (!is_numeric($decoded->data->pin) && !ctype_digit($decoded->data->pin)) {
        //     return [
        //         'valid' => false,
        //         'message' => 'Format PIN tidak valid',
        //         'data' => null,
        //         'error_code' => 'INVALID_PIN_FORMAT'
        //     ];
        // }

        // Validasi field tambahan jika ada
        if (isset($decoded->data->email) && !empty($decoded->data->email)) {
            if (!filter_var($decoded->data->email, FILTER_VALIDATE_EMAIL)) {
                return [
                    'valid' => false,
                    'message' => 'Format email tidak valid',
                    'data' => null,
                ];
            }
        }

        // Tambahkan informasi tambahan untuk debugging
        $returnData = [
            'valid' => true,
            'message' => 'Token valid',
            'data' => $decoded->data,
            'token_info' => [
                'issued_at' => isset($decoded->iat) ? $decoded->iat : null,
                'expires_at' => $decoded->exp,
                'remaining_time' => $decoded->exp - $currentTime,
                'algorithm' => 'HS256'
            ]
        ];

        return $returnData;
    } catch (Firebase\JWT\ExpiredException $e) {
        return [
            'valid' => false,
            'message' => 'Token sudah expired',
            'data' => null,
        ];
    } catch (Firebase\JWT\SignatureInvalidException $e) {
        return [
            'valid' => false,
            'message' => 'Signature token tidak valid',
            'data' => null,
        ];
    } catch (Firebase\JWT\BeforeValidException $e) {
        return [
            'valid' => false,
            'message' => 'Token belum bisa digunakan',
            'data' => null,
        ];
    } catch (DomainException $e) {
        return [
            'valid' => false,
            'message' => 'Algoritma token tidak didukung',
            'data' => null,
        ];
    } catch (InvalidArgumentException $e) {
        return [
            'valid' => false,
            'message' => 'Parameter token tidak valid',
            'data' => null,
        ];
    } catch (UnexpectedValueException $e) {
        return [
            'valid' => false,
            'message' => 'Format token tidak valid',
            'data' => null,
        ];
    } catch (Exception $e) {
        // Log error untuk debugging dengan lebih detail
        error_log('JWT Verification Error: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine());
        return [
            'valid' => false,
            'message' => 'Token tidak valid',
            'data' => null,
        ];
    }
}

function verifyRefreshToken($token)
{
    try {
        // Validasi input token
        if (empty($token)) {
            return [
                'valid' => false,
                'message' => 'Refresh token kosong atau tidak valid',
                'data' => null
            ];
        }

        // Coba beberapa cara untuk mendapatkan secret key
        $secretKey = null;

        if (isset($_ENV['REFRESH_TOKEN_SECRET']) && !empty($_ENV['REFRESH_TOKEN_SECRET'])) {
            $secretKey = $_ENV['REFRESH_TOKEN_SECRET'];
        } elseif (getenv('REFRESH_TOKEN_SECRET')) {
            $secretKey = getenv('REFRESH_TOKEN_SECRET');
        } else {
            error_log('WARNING: REFRESH_TOKEN_SECRET tidak ditemukan, menggunakan default key');
            $secretKey = 'default_refresh_secret_key_for_development_only';
        }

        if (empty($secretKey)) {
            return [
                'valid' => false,
                'message' => 'Konfigurasi server tidak valid',
                'data' => null
            ];
        }

        $decoded = JWT::decode($token, $secretKey, ['HS256']);

        // Validasi struktur token
        if (!isset($decoded->exp) || !isset($decoded->data)) {
            return [
                'valid' => false,
                'message' => 'Struktur refresh token tidak valid',
                'data' => null
            ];
        }

        // Cek apakah token sudah expired dengan toleransi waktu
        $currentTime = time();
        $toleranceSeconds = 30;

        if ($decoded->exp < ($currentTime - $toleranceSeconds)) {
            return [
                'valid' => false,
                'message' => 'Refresh token sudah expired',
                'data' => null
            ];
        }

        return [
            'valid' => true,
            'message' => 'Refresh token valid',
            'data' => $decoded->data
        ];
    } catch (Firebase\JWT\ExpiredException $e) {
        return [
            'valid' => false,
            'message' => 'Refresh token sudah expired',
            'data' => null
        ];
    } catch (Exception $e) {
        error_log('Refresh JWT Verification Error: ' . $e->getMessage());
        return [
            'valid' => false,
            'message' => 'Refresh token tidak valid',
            'data' => null
        ];
    }
}

function generateNewAccessToken($userData)
{
    try {
        // Validasi input
        if (empty($userData)) {
            throw new Exception('Data user tidak boleh kosong');
        }

        // Coba beberapa cara untuk mendapatkan secret key
        $secretKey = null;

        if (isset($_ENV['ACCESS_TOKEN_SECRET']) && !empty($_ENV['ACCESS_TOKEN_SECRET'])) {
            $secretKey = $_ENV['ACCESS_TOKEN_SECRET'];
        } elseif (getenv('ACCESS_TOKEN_SECRET')) {
            $secretKey = getenv('ACCESS_TOKEN_SECRET');
        } else {
            error_log('WARNING: ACCESS_TOKEN_SECRET tidak ditemukan, menggunakan default key');
            $secretKey = 'default_secret_key_for_development_only';
        }

        if (empty($secretKey)) {
            throw new Exception('Konfigurasi server tidak valid');
        }

        $issuer = "digitalcv";
        $audience = "candidate";
        $issuedAt = time();
        $expirationTime = $issuedAt + (60 * 60 * 24); // 24 jam

        $payload = [
            'iss' => $issuer,
            'aud' => $audience,
            'iat' => $issuedAt,
            'exp' => $expirationTime,
            'data' => $userData
        ];

        return JWT::encode($payload, $secretKey, 'HS256');
    } catch (Exception $e) {
        error_log('JWT Generation Error: ' . $e->getMessage());
        throw new Exception('Gagal membuat access token');
    }
}

function getAuthorizationHeader()
{
    $headers = null;
    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }
    return $headers;
}

function getBearerToken()
{
    try {
        $headers = getAuthorizationHeader();

        if (empty($headers)) {
            return null;
        }

        // Trim whitespace dan cek apakah header mengandung Bearer
        $headers = trim($headers);

        if (empty($headers)) {
            return null;
        }

        // Pattern yang lebih ketat untuk Bearer token
        if (preg_match('/^Bearer\s+([a-zA-Z0-9._-]+)$/', $headers, $matches)) {
            return trim($matches[1]);
        }

        // Fallback untuk pattern yang kurang ketat
        if (preg_match('/Bearer\s+(\S+)/', $headers, $matches)) {
            return trim($matches[1]);
        }

        return null;
    } catch (Exception $e) {
        error_log('getBearerToken Error: ' . $e->getMessage());
        return null;
    }
}

function requireAuth()
{
    try {
        // Validasi metode HTTP untuk keamanan
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        $currentMethod = $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN';

        if (!in_array($currentMethod, $allowedMethods)) {
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Metode HTTP tidak diperbolehkan',
            ]);
            exit();
        }

        // Ambil token dari header
        $token = getBearerToken();

        if (!$token) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'Access token diperlukan. Harap sertakan token dalam header Authorization: Bearer <token>',

            ]);
            exit();
        }

        // // Cek apakah token sudah di-blacklist
        // if (isTokenBlacklisted($token)) {
        //     http_response_code(401);
        //     echo json_encode([
        //         'success' => false,
        //         'message' => 'Token sudah tidak valid. Silakan login ulang.',
        //     ]);
        //     exit();
        // }

        // Verifikasi token
        $verification = verifyAccessToken($token);

        if (!$verification['valid']) {
            $message = $verification['message'] ?? 'Token tidak valid';

            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => $message,
            ]);
            exit();
        }

        // Validasi tambahan untuk memastikan data user lengkap
        if (empty($verification['data']) || !is_object($verification['data'])) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'Data user dalam token kosong atau tidak valid',
            ]);
            exit();
        }

        // // Validasi field wajib
        // if (!isset($verification['data']->pin) || empty($verification['data']->pin)) {
        //     http_response_code(401);
        //     echo json_encode([
        //         'success' => false,
        //         'message' => 'PIN user tidak ditemukan dalam token',
        //         'error_code' => 'MISSING_PIN'
        //     ]);
        //     exit();
        // }

        // // Validasi format PIN
        // if (!is_numeric($verification['data']->pin) && !ctype_digit($verification['data']->pin)) {
        //     http_response_code(401);
        //     echo json_encode([
        //         'success' => false,
        //         'message' => 'Format PIN tidak valid',
        //         'error_code' => 'INVALID_PIN_FORMAT'
        //     ]);
        //     exit();
        // }

        // Validasi field tambahan (nama_lengkap dan email) jika tersedia
        if (isset($verification['data']->email) && !empty($verification['data']->email)) {
            if (!filter_var($verification['data']->email, FILTER_VALIDATE_EMAIL)) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => 'Format email dalam token tidak valid',
                ]);
                exit();
            }
        }

        // Log successful authentication untuk audit trail
        // error_log('Successful authentication for PIN: ' . $verification['data']->pin . ' from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

        return $verification['data'];
    } catch (Exception $e) {
        error_log('RequireAuth Error: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan sistem saat memverifikasi token',
        ]);
        exit();
    }
}

// Utility functions for JWT handling and validation

/**
 * Validate JWT structure without decoding
 * @param string $token
 * @return array
 */
function validateJWTStructure($token)
{
    try {
        if (empty($token) || !is_string($token)) {
            return [
                'valid' => false,
                'message' => 'Token kosong atau bukan string',
            ];
        }

        $token = trim($token);

        // JWT harus memiliki 3 bagian yang dipisahkan oleh titik
        $parts = explode('.', $token);

        if (count($parts) !== 3) {
            return [
                'valid' => false,
                'message' => 'JWT harus memiliki 3 bagian (header.payload.signature)',

            ];
        }

        // Validasi setiap bagian tidak kosong
        foreach ($parts as $index => $part) {
            if (empty($part)) {
                $partNames = ['header', 'payload', 'signature'];
                return [
                    'valid' => false,
                    'message' => "Bagian {$partNames[$index]} JWT kosong",

                ];
            }
        }

        // Validasi base64 encoding untuk header dan payload
        for ($i = 0; $i < 2; $i++) {
            $decoded = base64_decode($parts[$i], true);
            if ($decoded === false) {
                $partNames = ['header', 'payload'];
                return [
                    'valid' => false,
                    'message' => "Bagian {$partNames[$i]} tidak valid base64",

                ];
            }
        }

        return [
            'valid' => true,
            'message' => 'Struktur JWT valid',
            'parts' => $parts
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'message' => 'Error validating JWT structure: ' . $e->getMessage(),

        ];
    }
}

/**
 * Get JWT payload without verification (for debugging)
 * @param string $token
 * @return array
 */
function getJWTPayload($token)
{
    try {
        $structure = validateJWTStructure($token);

        if (!$structure['valid']) {
            return [
                'valid' => false,
                'message' => $structure['message'],
            ];
        }

        $payload = json_decode(base64_decode($structure['parts'][1]), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'valid' => false,
                'message' => 'Payload JSON tidak valid: ' . json_last_error_msg(),

            ];
        }

        return [
            'valid' => true,
            'payload' => $payload
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'message' => 'Error getting JWT payload: ' . $e->getMessage(),

        ];
    }
}

/**
 * Check if JWT is about to expire
 * @param string $token
 * @param int $warningMinutes
 * @return array
 */
function checkJWTExpiration($token, $warningMinutes = 5)
{
    try {
        $payload = getJWTPayload($token);

        if (!$payload['valid']) {
            return $payload;
        }

        $exp = $payload['payload']['exp'] ?? null;

        if (!$exp) {
            return [
                'valid' => false,
                'message' => 'Token tidak memiliki waktu expired',

            ];
        }

        $currentTime = time();
        $expirationTime = $exp;
        $warningTime = $expirationTime - ($warningMinutes * 60);

        if ($currentTime >= $expirationTime) {
            return [
                'valid' => false,
                'expired' => true,
                'message' => 'Token sudah expired',
            ];
        }

        if ($currentTime >= $warningTime) {
            return [
                'valid' => true,
                'expired' => false,
                'warning' => true,
                'message' => "Token akan expired dalam {$warningMinutes} menit",
                'remaining_seconds' => $expirationTime - $currentTime
            ];
        }

        return [
            'valid' => true,
            'expired' => false,
            'warning' => false,
            'message' => 'Token masih valid',
            'remaining_seconds' => $expirationTime - $currentTime
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'message' => 'Error checking JWT expiration: ' . $e->getMessage(),

        ];
    }
}

/**
 * Sanitize user data from JWT
 * @param object|null $userData
 * @return object
 */
function sanitizeJWTUserData($userData)
{
    try {
        if (!is_object($userData) || $userData === null) {
            return (object)[
                'pin' => '',
                'nama_lengkap' => '',
                'email' => ''
            ];
        }

        $sanitized = (object)[
            'pin' => isset($userData->pin) ? preg_replace('/[^0-9]/', '', $userData->pin) : '',
            'nama_lengkap' => isset($userData->nama_lengkap) ? trim(strip_tags($userData->nama_lengkap)) : '',
            'email' => isset($userData->email) ? filter_var($userData->email, FILTER_SANITIZE_EMAIL) : ''
        ];

        // Validasi email jika ada
        if (!empty($sanitized->email)) {
            if (!filter_var($sanitized->email, FILTER_VALIDATE_EMAIL)) {
                $sanitized->email = '';
            }
        }

        return $sanitized;
    } catch (Exception $e) {
        error_log('sanitizeJWTUserData Error: ' . $e->getMessage());
        return (object)[
            'pin' => '',
            'nama_lengkap' => '',
            'email' => ''
        ];
    }
}

/**
 * Enhanced requireAuth with additional validation options
 * @param bool $strictMode - Enable strict validation
 * @param bool $requireEmail - Require email in token
 * @return object
 */
function requireAuthEnhanced($strictMode = false, $requireEmail = false)
{
    try {
        // Use regular requireAuth but with additional validations
        $userData = requireAuth();

        if ($strictMode) {
            // Additional validations for strict mode
            if (empty($userData->nama_lengkap)) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nama lengkap diperlukan dalam token',
                ]);
                exit();
            }
        }

        if ($requireEmail) {
            if (empty($userData->email)) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'message' => 'Email diperlukan dalam token',
                ]);
                exit();
            }
        }

        // Sanitize data
        return sanitizeJWTUserData($userData);
    } catch (Exception $e) {
        error_log('requireAuthEnhanced Error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan sistem saat memverifikasi token',
        ]);
        exit();
    }
}

/**
 * Memeriksa apakah token sudah di-blacklist
 * @param string $token - JWT token yang akan dicek
 * @return bool - true jika token sudah di-blacklist, false jika belum
 */
function isTokenBlacklisted($token)
{
    try {
        $tokenHash = hash('sha256', $token);
        $blacklistFile = __DIR__ . '/cache/blacklisted_tokens.json';

        // Jika file blacklist tidak ada, token tidak di-blacklist
        if (!file_exists($blacklistFile)) {
            return false;
        }

        // Baca file blacklist
        $content = file_get_contents($blacklistFile);
        $blacklistedTokens = json_decode($content, true);

        if (!is_array($blacklistedTokens)) {
            return false;
        }

        // Cek apakah token ada di blacklist dan belum expired
        if (isset($blacklistedTokens[$tokenHash])) {
            $tokenData = $blacklistedTokens[$tokenHash];
            $expiryTime = strtotime($tokenData['expires_at']);

            // Jika token sudah expired, hapus dari blacklist
            if ($expiryTime < time()) {
                unset($blacklistedTokens[$tokenHash]);
                file_put_contents($blacklistFile, json_encode($blacklistedTokens, JSON_PRETTY_PRINT));
                return false;
            }

            return true; // Token masih di blacklist dan belum expired
        }

        return false; // Token tidak ada di blacklist
    } catch (Exception $e) {
        // Jika ada error, anggap token tidak blacklisted untuk menghindari blocking yang tidak perlu
        return false;
    }
}

/**
 * Menambahkan token ke blacklist
 * @param string $token - JWT token yang akan di-blacklist
 * @param string $reason - Alasan token di-blacklist (optional)
 * @return bool - true jika berhasil, false jika gagal
 */
function blacklistToken($token, $reason = 'logout')
{
    try {
        if (empty($token) || !is_string($token)) {
            return false;
        }

        // Hash token untuk keamanan (jangan simpan token asli)
        $tokenHash = hash('sha256', $token);

        
        
        // Tentukan lokasi file blacklist
        $blacklistFile = __DIR__ . '/cache/blacklisted_tokens.json';

        // Buat direktori cache jika belum ada
        $cacheDir = dirname($blacklistFile);
        if (!is_dir($cacheDir)) {
            if (!mkdir($cacheDir, 0755, true)) {
                error_log('Failed to create cache directory: ' . $cacheDir);
                return false;
            }
        }

        // Baca blacklist yang sudah ada atau buat array kosong
        $blacklistedTokens = [];
        if (file_exists($blacklistFile)) {
            $content = file_get_contents($blacklistFile);
            $decoded = json_decode($content, true);
            if (is_array($decoded)) {
                $blacklistedTokens = $decoded;
            }
        }

        // Ambil informasi ekspirasi dari token
        $payload = getJWTPayload($token);
        $expiresAt = date('Y-m-d H:i:s');
        
        if ($payload['valid'] && isset($payload['payload']['exp'])) {
            $expiresAt = date('Y-m-d H:i:s', $payload['payload']['exp']);
        }

        // Tambahkan token ke blacklist
        $blacklistedTokens[$tokenHash] = [
            'blacklisted_at' => date('Y-m-d H:i:s'),
            'expires_at' => $expiresAt,
            'reason' => $reason,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        // Simpan ke file
        $jsonData = json_encode($blacklistedTokens, JSON_PRETTY_PRINT);
        if (file_put_contents($blacklistFile, $jsonData) === false) {
            error_log('Failed to write blacklist file: ' . $blacklistFile);
            echo 'Failed to write blacklist file: ' . $blacklistFile;
            exit;
            return false;
        }

        // Set permission yang tepat untuk file
        chmod($blacklistFile, 0777);

        return true;
    } catch (Exception $e) {
        error_log('blacklistToken Error: ' . $e->getMessage());
        echo $e->getMessage();
        exit;
        return false;
    }
}

/**
 * Membersihkan token blacklist yang sudah expired
 * Fungsi ini sebaiknya dipanggil secara periodic melalui cron job
 */
function cleanupExpiredBlacklistedTokens()
{
    try {
        $blacklistFile = __DIR__ . '/cache/blacklisted_tokens.json';

        if (!file_exists($blacklistFile)) {
            return 0;
        }

        $content = file_get_contents($blacklistFile);
        $blacklistedTokens = json_decode($content, true);

        if (!is_array($blacklistedTokens)) {
            return 0;
        }

        $now = time();
        $deletedCount = 0;

        // Hapus token yang sudah expired
        foreach ($blacklistedTokens as $hash => $data) {
            if (strtotime($data['expires_at']) < $now) {
                unset($blacklistedTokens[$hash]);
                $deletedCount++;
            }
        }

        // Simpan kembali ke file jika ada perubahan
        if ($deletedCount > 0) {
            file_put_contents($blacklistFile, json_encode($blacklistedTokens, JSON_PRETTY_PRINT));
            chmod($blacklistFile, 0777); // Set permission yang tepat
        }

        return $deletedCount;
    } catch (Exception $e) {
        return 0;
    }
}

// End of utility functions
