# 📘 API Documentation

## 🔑 Register Candidate

Se<PERSON>a request harus menggunakan header berikut:

| Key          | Value                          | Required |
| ------------ | ------------------------------ | -------- |
| Content-Type | application/json               | ✅       |
| x-api-key    | xiAheLul2MLQb4MZKs0MLrG5FcKOtg | ✅       |

---

## 📑 Methods

Allow method hanya POST

---

## 📍 Endpoint

https://digitalcv.id/api/insertCandidate

---

## 📝 Parameter

Berikut parameter yang tersedia:
name => nama lengkap kandidat yang akan didaftarkan. tipe string required
email => email kandidat yang akan didaftarkan. tipe string required
password => password kandidat yang akan didaftarkan. tipe string required
phone => nomor telepon kandidat yang akan didaftarkan. tipe string required

---

#### Body (JSON)

```json
{
  "name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "1233Abcd*",
  "phone": "08123456789"
}
```

---

## ⚡ Example Request for PHP

$url = "https://digitalcv.id/api/insertCandidate";

$data = [
"name" => "Jhon Doe",
"email" => "<EMAIL>",
"password" => "1233Abcd*",
"phone" => "08123456789"
];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
"Content-Type: application/json",
"x-api-key: your-api-key"
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

echo $response;

---

## Response

✅ Success (201)
{
"status":"success",
"message":"Candidate successfully registered."
}

❌ Error (500)
{
"status":"error",
"message":"Database connection failed: ..."
}
Deskripsi: Error koneksi database

❌ Error (405)
{
"status":"error",
"message":"Method not allowed. Only POST is permitted."
}
Deskripsi: Metode yang di-ijinkan hanya POST

❌ Error (403)
{
"status":"error",
"message":"Email has been registered."
}
Deskripsi: Data kandidat sudah terdaftar

❌ Error (401)
{
"status":"error",
"message":"Unauthorized: Invalid API Key."
}
Deskripsi: x-api-key tidak valid

❌ Error (400)
{
"status":"error",
"message":"Missing required parameters.",
"missing" => [...]
}
Deskripsi: Parameter tidak lengkap. Pada element "missing" diperlihatkan parameter yang tidak ada

{
"status":"error",
"message":"Invalid email."
}
Deskripsi: Value email tidak valid. Email harus mengandung '@'

{
"status":"error",
"message":"Invalid phone number."
}
Deskripsi: Nomor telepon tidak valid. Jika pada nomor terdapat huruf maka error ini akan muncul

{
"status":"error",
"message":"Password must be at least 8 characters long."
}
Deskripsi: Panjang password minimal 8 karakter

{
"status":"error",
"message":"Passwords must contain uppercase letters."
}
Deskripsi: Password harus mengandung huruf bersar

{
"status":"error",
"message":"Passwords must contain lowercase letters."
}
Deskripsi: Password harus mengandung huruf kecil

{
"status":"error",
"message":"Passwords must contain numbers."
}
Deskripsi: Password harus mengandung angka

{
"status":"error",
"message":"Passwords must contain special characters."
}
Deskripsi: Password harus mengandung spesial karakter

{
"status":"error",
"message":"Candidate registration failed."
}
Deskripsi: Kandidat gagal disimpan

## Documentation from Postman

Insert Candidate API
This endpoint allows you to add a new candidate's information to the digital CV database. It is designed to accept candidate details through an HTTP POST request.
Request
Endpoint: http://localhost:8080/digitalcv/api/insertCandidate
Method: POST
Request Body Parameters (x-www-form-urlencoded)
name (text): The full name of the candidate.
email (text): The email address of the candidate.
password (text): The password for the candidate's account.
phone (text): The phone number of the candidate.

Response
Upon successful execution of the request, the API will return a response indicating the status of the operation. The structure of the response will typically include:
A success message confirming the addition of the candidate.
Any relevant error messages if the input parameters are invalid or if there are issues processing the request.

Usage Notes
Ensure that all required fields are provided in the request body.
Validate the format of the email and phone number before sending the request to avoid errors.
Handle responses appropriately to provide feedback to the user regarding the success or failure of the request.

This endpoint is essential for managing candidate data within the digital CV platform, enabling efficient onboarding of new candidates.
Body
name: Dian
email: <EMAIL>
password: Persib1933\*
phone: ************
