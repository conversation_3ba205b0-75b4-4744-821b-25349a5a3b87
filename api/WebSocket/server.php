<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

require __DIR__ . '/../../vendor/autoload.php';

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;

class NotificationServer implements MessageComponentInterface
{
    protected $clients;
    protected $userConnections;  // Array untuk menyimpan mapping resourceId dan userId

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->userConnections = [];  // Inisialisasi array untuk menyimpan hubungan resourceId dengan user
        echo "WebSocket server started...\n";
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $uri = $conn->httpRequest->getUri();
        $queryParams = parse_url($uri, PHP_URL_QUERY);  // Menangkap query string
        parse_str($queryParams, $params);  // Memparse query string menjadi array

        // Ambil userId dari query string
        $userId = isset($params['userId']) ? $params['userId'] : null;

        if ($userId) {
            // Menyimpan resourceId dengan userId
            $this->userConnections[$conn->resourceId] = $userId;
        }

        $this->clients->attach($conn);
        echo "New connection ({$conn->resourceId}) for user {$userId}\n";
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        echo "Received message";  // Menampilkan pesan yang diterima

        // Men-decode pesan JSON
        $data = json_decode($msg, true);
        $targetUserId = $data['to'];  // ID user yang ingin dikirimi pesan

        // Mencari resourceId untuk user tujuan
        foreach ($this->userConnections as $resourceId => $userId) {
            if ($userId == $targetUserId) {
                // Cari koneksi yang memiliki resourceId yang sesuai dengan target user
                foreach ($this->clients as $client) {
                    if ($client->resourceId == $resourceId) {
                        // Kirim pesan ke client yang sesuai
                        $client->send(json_encode($data));
                    }
                }
            }
        }
    }

    // Ketika koneksi ditutup
    public function onClose(ConnectionInterface $conn)
    {
        // Menghapus mapping jika koneksi ditutup
        unset($this->userConnections[$conn->resourceId]);
        $this->clients->detach($conn);
        echo "Connection {$conn->resourceId} has disconnected\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }
}

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new NotificationServer()
        )
    ),
    3000 // Ganti port kalau perlu
);

$server->run();
