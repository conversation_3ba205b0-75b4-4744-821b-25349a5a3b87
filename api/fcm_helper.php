<?php
require __DIR__ . '/../vendor/autoload.php';

use Google\Auth\OAuth2;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;

/**
 * Function to handle invalid/expired FCM tokens
 * @param string $pin User PIN
 * @param object $conn Database connection
 * @param string $table Table name (users_kandidat or koordinator_pic)
 * @param string $whereColumn Column to match (pin or id_koordinator)
 */
function cleanupInvalidFcmToken($pin, $conn, $table = 'users_kandidat', $whereColumn = 'pin')
{
    try {
        $sql = $conn->prepare("UPDATE {$table} SET fcm_token = NULL WHERE {$whereColumn} = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $sql->close();
        
        error_log("FCM: Cleaned up invalid token for {$whereColumn}: {$pin}");
        return true;
    } catch (Exception $e) {
        error_log("FCM: Failed to cleanup token - " . $e->getMessage());
        return false;
    }
}

/**
 * Function to parse FCM error response and determine if token is invalid
 * @param string $responseBody FCM error response body
 * @return array Contains status and error details
 */
function parseFcmError($responseBody)
{
    $errorData = json_decode($responseBody, true);
    
    if (isset($errorData['error'])) {
        $errorCode = $errorData['error']['code'] ?? 0;
        $errorStatus = $errorData['error']['status'] ?? '';
        $errorMessage = $errorData['error']['message'] ?? '';
        
        // FCM specific error codes for invalid tokens
        $invalidTokenErrors = ['NOT_FOUND', 'INVALID_ARGUMENT', 'UNREGISTERED'];
        $invalidTokenCodes = [404, 400];
        
        $isInvalidToken = in_array($errorStatus, $invalidTokenErrors) || 
                         in_array($errorCode, $invalidTokenCodes) ||
                         strpos($errorMessage, 'registration token') !== false;
        
        return [
            'is_invalid_token' => $isInvalidToken,
            'error_code' => $errorCode,
            'error_status' => $errorStatus,
            'error_message' => $errorMessage
        ];
    }
    
    return [
        'is_invalid_token' => false,
        'error_code' => 0,
        'error_status' => 'UNKNOWN',
        'error_message' => 'Unknown error format'
    ];
}

function sendNotifikasiFromCompany($pin, $title, $bodyMessage, $dataPayload = [], $conn)
{
    $sql = $conn->prepare("SELECT fcm_token FROM users_kandidat WHERE pin = ?");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $deviceToken = "";
    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $deviceToken  = $row['fcm_token'];
    }

    if ($deviceToken == "") {
        return [
            'status' => false,
            'message' => 'FCM registration token tidak ditemukan atau kosong'
        ];
    }

    // Path ke file service account
    $credentialsPath = __DIR__ . '/service-account.json';
    $credentials = json_decode(file_get_contents($credentialsPath), true);
    $projectId = $credentials['project_id'];

    try {
        // OAuth2 untuk access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);

        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Payload FCM
        $payload = [
            'message' => [
                'token' => $deviceToken,
                'notification' => [
                    'title' => $title,
                    'body' => $bodyMessage
                ],
                'data' => $dataPayload
            ]
        ];

        // Kirim ke FCM
        $client = new Client();
        $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ]);

        return [
            'status' => true,
            'message' => 'Notifikasi berhasil dikirim',
            'data' => json_decode($response->getBody(), true)
        ];

    } catch (ClientException $e) {
        $statusCode = $e->getResponse()->getStatusCode();
        $responseBody = (string) $e->getResponse()->getBody();
        
        error_log("FCM Client Error ({$statusCode}): {$responseBody}");
        
        // Parse error untuk menentukan apakah token invalid
        $errorInfo = parseFcmError($responseBody);
        
        if ($errorInfo['is_invalid_token']) {
            // Token expired/invalid, hapus dari database
            cleanupInvalidFcmToken($pin, $conn, 'users_kandidat', 'pin');
            
            return [
                'status' => false,
                'message' => 'FCM token tidak valid atau expired. Token telah dihapus dari database.',
                'error_type' => 'invalid_token',
                'error_details' => $errorInfo
            ];
        }
        
        return [
            'status' => false,
            'message' => 'Gagal mengirim notifikasi: ' . $errorInfo['error_message'],
            'error_type' => 'fcm_error',
            'error_details' => $errorInfo
        ];
        
    } catch (ServerException $e) {
        $responseBody = (string) $e->getResponse()->getBody();
        error_log("FCM Server Error: {$responseBody}");
        
        return [
            'status' => false,
            'message' => 'Server FCM mengalami masalah. Silakan coba lagi nanti.',
            'error_type' => 'server_error'
        ];
        
    } catch (Exception $e) {
        error_log("FCM General Error: " . $e->getMessage());
        
        return [
            'status' => false,
            'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage(),
            'error_type' => 'system_error'
        ];
    }
}

function sendNotifikasiAjukanPekerjaan($pin, $title, $bodyMessage, $dataPayload = [], $conn)
{
    $sql = $conn->prepare("SELECT fcm_token FROM users_kandidat WHERE pin = ?");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $deviceToken = "";
    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $deviceToken  = $row['fcm_token'];
    }

    // Cek jika deviceToken kosong
    if ($deviceToken == "") {
        return [
            'status' => false,
            'message' => 'FCM registration token tidak ditemukan atau kosong'
        ];
    }

    // Path ke file service account
    $credentialsPath = __DIR__ . '/service-account.json';
    $credentials = json_decode(file_get_contents($credentialsPath), true);
    $projectId = $credentials['project_id'];

    try {
        // OAuth2 untuk access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);

        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Payload FCM
        $payload = [
            'message' => [
                'token' => $deviceToken,
                'notification' => [
                    'title' => $title,
                    'body' => $bodyMessage
                ],
                'data' => $dataPayload
            ]
        ];

        // Kirim ke FCM
        $client = new Client();
        $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
            'headers' => [
                "Authorization" => "Bearer {$accessToken}",
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ]);
        
        return [
            'status' => true,
            'message' => 'Notifikasi berhasil dikirim',
            'data' => json_decode($response->getBody(), true)
        ];
        
    } catch (ClientException $e) {
        $statusCode = $e->getResponse()->getStatusCode();
        $responseBody = (string) $e->getResponse()->getBody();
        
        error_log("FCM Client Error ({$statusCode}): {$responseBody}");
        
        // Parse error untuk menentukan apakah token invalid
        $errorInfo = parseFcmError($responseBody);
        
        if ($errorInfo['is_invalid_token']) {
            // Token expired/invalid, hapus dari database
            cleanupInvalidFcmToken($pin, $conn, 'users_kandidat', 'pin');
            
            return [
                'status' => false,
                'message' => 'FCM token tidak valid atau expired. Token telah dihapus dari database.',
                'error_type' => 'invalid_token',
                'error_details' => $errorInfo
            ];
        }
        
        return [
            'status' => false,
            'message' => 'Gagal mengirim notifikasi: ' . $errorInfo['error_message'],
            'error_type' => 'fcm_error',
            'error_details' => $errorInfo
        ];
        
    } catch (ServerException $e) {
        $responseBody = (string) $e->getResponse()->getBody();
        error_log("FCM Server Error: {$responseBody}");
        
        return [
            'status' => false,
            'message' => 'Server FCM mengalami masalah. Silakan coba lagi nanti.',
            'error_type' => 'server_error'
        ];
        
    } catch (Exception $e) {
        error_log("FCM General Error: " . $e->getMessage());
        
        return [
            'status' => false,
            'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage(),
            'error_type' => 'system_error'
        ];
    }
}

function sendNotifikasiFromCandidate($idkoordinator, $title, $bodyMessage, $dataPayload = [], $conn)
{
    // Ambil semua fcm_token
    $sql = $conn->prepare("SELECT fcm_token FROM koordinator_pic WHERE id_koordinator = ?");
    $sql->bind_param("s", $idkoordinator);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $deviceTokens = [];
    while ($row = $result->fetch_assoc()) {
        if (!empty($row['fcm_token'])) {
            $deviceTokens[] = $row['fcm_token'];
        }
    }

    // Cek jika deviceToken kosong
    if (empty($deviceTokens)) {
        return ['status' => false, 'message' => 'Tidak ada token ditemukan'];
    }

    // Path ke file service account
    $credentialsPath = __DIR__ . '/service-account.json';
    $credentials = json_decode(file_get_contents($credentialsPath), true);
    $projectId = $credentials['project_id'];

    try {
        // Ambil access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);
        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Kirim notifikasi ke setiap token
        $client = new Client();
        $responses = [];
        $successCount = 0;
        $invalidTokens = [];
        
        foreach ($deviceTokens as $token) {
            $payload = [
                'message' => [
                    'token' => $token,
                    'notification' => [
                        'title' => $title,
                        'body' => $bodyMessage
                    ],
                    'data' => $dataPayload
                ]
            ];

            try {
                $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $payload
                ]);
                
                $successCount++;
                $responses[] = [
                    'token' => substr($token, 0, 20) . '...', // Partial token for security
                    'status' => 'success',
                    'data' => json_decode($response->getBody(), true)
                ];
                
            } catch (ClientException $e) {
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = (string) $e->getResponse()->getBody();
                
                error_log("FCM Client Error for token ({$statusCode}): {$responseBody}");
                
                // Parse error untuk menentukan apakah token invalid
                $errorInfo = parseFcmError($responseBody);
                
                if ($errorInfo['is_invalid_token']) {
                    $invalidTokens[] = $token;
                    $responses[] = [
                        'token' => substr($token, 0, 20) . '...',
                        'status' => 'invalid_token',
                        'error' => 'Token expired/invalid and will be cleaned up'
                    ];
                } else {
                    $responses[] = [
                        'token' => substr($token, 0, 20) . '...',
                        'status' => 'error',
                        'error' => $errorInfo['error_message']
                    ];
                }
                
            } catch (Exception $e) {
                $responses[] = [
                    'token' => substr($token, 0, 20) . '...',
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        // Cleanup invalid tokens
        if (!empty($invalidTokens)) {
            foreach ($invalidTokens as $invalidToken) {
                try {
                    $cleanupSql = $conn->prepare("UPDATE koordinator_pic SET fcm_token = NULL WHERE fcm_token = ?");
                    $cleanupSql->bind_param("s", $invalidToken);
                    $cleanupSql->execute();
                    $cleanupSql->close();
                    
                    error_log("FCM: Cleaned up invalid token for koordinator: {$idkoordinator}");
                } catch (Exception $e) {
                    error_log("FCM: Failed to cleanup token - " . $e->getMessage());
                }
            }
        }

        return [
            'status' => $successCount > 0,
            'message' => "Berhasil mengirim {$successCount} dari " . count($deviceTokens) . " notifikasi",
            'success_count' => $successCount,
            'total_tokens' => count($deviceTokens),
            'invalid_tokens_cleaned' => count($invalidTokens),
            'responses' => $responses
        ];
        
    } catch (Exception $e) {
        error_log("FCM General Error: " . $e->getMessage());
        
        return [
            'status' => false,
            'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage(),
            'error_type' => 'system_error'
        ];
    }
}

function broadcastForCandidate($title, $bodyMessage, $dataPayload = [])
{
    // Path ke file service account
    $credentialsPath = __DIR__ . '/service-account.json';
    $credentials = json_decode(file_get_contents($credentialsPath), true);
    $projectId = $credentials['project_id'];

    try {
        // OAuth2 untuk access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);

        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Payload FCM
        $payload = [
            'message' => [
                "topic" => "candidate",
                'notification' => [
                    'title' => $title,
                    'body' => $bodyMessage
                ],
                'data' => $dataPayload
            ]
        ];

        // Kirim ke FCM
        $client = new Client();
        $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ]);

        return [
            'status' => true,
            'message' => 'Broadcast notifikasi berhasil dikirim ke topic candidate',
            'data' => json_decode($response->getBody(), true)
        ];
        
    } catch (ClientException $e) {
        $statusCode = $e->getResponse()->getStatusCode();
        $responseBody = (string) $e->getResponse()->getBody();
        
        error_log("FCM Broadcast Client Error ({$statusCode}): {$responseBody}");
        
        return [
            'status' => false,
            'message' => 'Gagal mengirim broadcast notifikasi',
            'error_type' => 'fcm_error',
            'error_details' => $responseBody
        ];
        
    } catch (Exception $e) {
        error_log("FCM Broadcast Error: " . $e->getMessage());
        
        return [
            'status' => false,
            'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage(),
            'error_type' => 'system_error'
        ];
    }
}

function broadcastForCompany($title, $bodyMessage, $dataPayload = [])
{
    // Path ke file service account
    $credentialsPath = __DIR__ . '/service-account.json';
    $credentials = json_decode(file_get_contents($credentialsPath), true);
    $projectId = $credentials['project_id'];

    try {
        // OAuth2 untuk access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);

        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Payload FCM
        $payload = [
            'message' => [
                "topic" => "company",
                'notification' => [
                    'title' => $title,
                    'body' => $bodyMessage
                ],
                'data' => $dataPayload
            ]
        ];

        // Kirim ke FCM
        $client = new Client();
        $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ]);

        return [
            'status' => true,
            'message' => 'Broadcast notifikasi berhasil dikirim ke topic company',
            'data' => json_decode($response->getBody(), true)
        ];
        
    } catch (ClientException $e) {
        $statusCode = $e->getResponse()->getStatusCode();
        $responseBody = (string) $e->getResponse()->getBody();
        
        error_log("FCM Broadcast Client Error ({$statusCode}): {$responseBody}");
        
        return [
            'status' => false,
            'message' => 'Gagal mengirim broadcast notifikasi',
            'error_type' => 'fcm_error',
            'error_details' => $responseBody
        ];
        
    } catch (Exception $e) {
        error_log("FCM Broadcast Error: " . $e->getMessage());
        
        return [
            'status' => false,
            'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage(),
            'error_type' => 'system_error'
        ];
    }
}

/**
 * Function to validate and clean up all invalid FCM tokens in the database
 * This can be run periodically to maintain token hygiene
 * @param object $conn Database connection
 * @return array Cleanup results
 */
function validateAndCleanupAllTokens($conn)
{
    $cleanupResults = [
        'users_kandidat' => ['checked' => 0, 'cleaned' => 0],
        'koordinator_pic' => ['checked' => 0, 'cleaned' => 0],
        'errors' => []
    ];

    // Check users_kandidat tokens
    try {
        $sql = $conn->prepare("SELECT pin, fcm_token FROM users_kandidat WHERE fcm_token IS NOT NULL AND fcm_token != ''");
        $sql->execute();
        $result = $sql->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $cleanupResults['users_kandidat']['checked']++;
            
            // Test the token by sending a dry-run message
            $testResult = testFcmToken($row['fcm_token']);
            
            if (!$testResult['valid']) {
                // Clean up invalid token
                if (cleanupInvalidFcmToken($row['pin'], $conn, 'users_kandidat', 'pin')) {
                    $cleanupResults['users_kandidat']['cleaned']++;
                }
            }
        }
        $sql->close();
    } catch (Exception $e) {
        $cleanupResults['errors'][] = "Error checking users_kandidat: " . $e->getMessage();
    }

    // Check koordinator_pic tokens
    try {
        $sql = $conn->prepare("SELECT id_koordinator, fcm_token FROM koordinator_pic WHERE fcm_token IS NOT NULL AND fcm_token != ''");
        $sql->execute();
        $result = $sql->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $cleanupResults['koordinator_pic']['checked']++;
            
            // Test the token
            $testResult = testFcmToken($row['fcm_token']);
            
            if (!$testResult['valid']) {
                // Clean up invalid token
                try {
                    $cleanupSql = $conn->prepare("UPDATE koordinator_pic SET fcm_token = NULL WHERE id_koordinator = ? AND fcm_token = ?");
                    $cleanupSql->bind_param("ss", $row['id_koordinator'], $row['fcm_token']);
                    $cleanupSql->execute();
                    $cleanupSql->close();
                    $cleanupResults['koordinator_pic']['cleaned']++;
                } catch (Exception $e) {
                    $cleanupResults['errors'][] = "Error cleaning koordinator token: " . $e->getMessage();
                }
            }
        }
        $sql->close();
    } catch (Exception $e) {
        $cleanupResults['errors'][] = "Error checking koordinator_pic: " . $e->getMessage();
    }

    return $cleanupResults;
}

/**
 * Function to test if an FCM token is valid
 * @param string $token FCM token to test
 * @return array Test result
 */
function testFcmToken($token)
{
    if (empty($token)) {
        return ['valid' => false, 'reason' => 'Empty token'];
    }

    try {
        // Path ke file service account
        $credentialsPath = __DIR__ . '/service-account.json';
        $credentials = json_decode(file_get_contents($credentialsPath), true);
        $projectId = $credentials['project_id'];

        // OAuth2 untuk access token
        $oauth = new OAuth2([
            'audience' => 'https://oauth2.googleapis.com/token',
            'issuer' => $credentials['client_email'],
            'signingAlgorithm' => 'RS256',
            'signingKey' => $credentials['private_key'],
            'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
        ]);

        $accessToken = $oauth->fetchAuthToken()['access_token'];

        // Payload FCM dengan dry_run untuk testing
        $payload = [
            'message' => [
                'token' => $token,
                'notification' => [
                    'title' => 'Test',
                    'body' => 'Token validation test'
                ]
            ],
            'validate_only' => true // Dry run mode
        ];

        // Test ke FCM
        $client = new Client();
        $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => $payload
        ]);

        return ['valid' => true, 'reason' => 'Token is valid'];

    } catch (ClientException $e) {
        $statusCode = $e->getResponse()->getStatusCode();
        $responseBody = (string) $e->getResponse()->getBody();
        
        $errorInfo = parseFcmError($responseBody);
        
        return [
            'valid' => !$errorInfo['is_invalid_token'],
            'reason' => $errorInfo['error_message'],
            'status_code' => $statusCode
        ];

    } catch (Exception $e) {
        return ['valid' => false, 'reason' => 'System error: ' . $e->getMessage()];
    }
}

// function broadcastForCompany($title, $bodyMessage, $dataPayload = [], $conn)
// {
//     // Ambil semua fcm_token
//     $sql = $conn->prepare("SELECT fcm_token FROM koordinator_pic ");
//     $sql->execute();
//     $result = $sql->get_result();
//     $sql->close();

//     $deviceTokens = [];
//     while ($row = $result->fetch_assoc()) {
//         if (!empty($row['fcm_token'])) {
//             $deviceTokens[] = $row['fcm_token'];
//         }
//     }

//     if (empty($deviceTokens)) {
//         return ['status' => false, 'message' => 'Tidak ada token ditemukan'];
//     }

//     // Path ke file service account
//     $credentialsPath = __DIR__ . '/service-account.json';
//     $credentials = json_decode(file_get_contents($credentialsPath), true);
//     $projectId = $credentials['project_id'];

//     // Ambil access token
//     $oauth = new OAuth2([
//         'audience' => 'https://oauth2.googleapis.com/token',
//         'issuer' => $credentials['client_email'],
//         'signingAlgorithm' => 'RS256',
//         'signingKey' => $credentials['private_key'],
//         'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
//         'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
//     ]);
//     $accessToken = $oauth->fetchAuthToken()['access_token'];

//     // Kirim notifikasi ke setiap token
//     $client = new Client();
//     $responses = [];
//     foreach ($deviceTokens as $token) {
//         $payload = [
//             'message' => [
//                 'token' => $token,
//                 'notification' => [
//                     'title' => $title,
//                     'body' => $bodyMessage
//                 ],
//                 'data' => $dataPayload
//             ]
//         ];

//         try {
//             $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
//                 'headers' => [
//                     'Authorization' => 'Bearer ' . $accessToken,
//                     'Content-Type' => 'application/json',
//                 ],
//                 'json' => $payload
//             ]);
//             $responses[] = json_decode($response->getBody(), true);
//         } catch (Exception $e) {
//             $responses[] = ['token' => $token, 'error' => $e->getMessage()];
//         }
//     }

//     return $responses;
// }

// function broadcastForCandidate($title, $bodyMessage, $dataPayload = [], $conn)
// {
//     // Ambil semua fcm_token
//     $sql = $conn->prepare("SELECT fcm_token FROM users_kandidat ");
//     $sql->execute();
//     $result = $sql->get_result();
//     $sql->close();

//     $deviceTokens = [];
//     while ($row = $result->fetch_assoc()) {
//         if (!empty($row['fcm_token'])) {
//             $deviceTokens[] = $row['fcm_token'];
//         }
//     }

//     if (empty($deviceTokens)) {
//         return ['status' => false, 'message' => 'Tidak ada token ditemukan'];
//     }

//     // Path ke file service account
//     $credentialsPath = __DIR__ . '/service-account.json';
//     $credentials = json_decode(file_get_contents($credentialsPath), true);
//     $projectId = $credentials['project_id'];

//     // Ambil access token
//     $oauth = new OAuth2([
//         'audience' => 'https://oauth2.googleapis.com/token',
//         'issuer' => $credentials['client_email'],
//         'signingAlgorithm' => 'RS256',
//         'signingKey' => $credentials['private_key'],
//         'tokenCredentialUri' => 'https://oauth2.googleapis.com/token',
//         'scope' => 'https://www.googleapis.com/auth/firebase.messaging'
//     ]);
//     $accessToken = $oauth->fetchAuthToken()['access_token'];

//     // Kirim notifikasi ke setiap token
//     $client = new Client();
//     $responses = [];
//     foreach ($deviceTokens as $token) {
//         $payload = [
//             'message' => [
//                 'token' => $token,
//                 'notification' => [
//                     'title' => $title,
//                     'body' => $bodyMessage
//                 ],
//                 'data' => $dataPayload
//             ]
//         ];

//         try {
//             $response = $client->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", [
//                 'headers' => [
//                     'Authorization' => 'Bearer ' . $accessToken,
//                     'Content-Type' => 'application/json',
//                 ],
//                 'json' => $payload
//             ]);
//             $responses[] = json_decode($response->getBody(), true);
//         } catch (Exception $e) {
//             $responses[] = ['token' => $token, 'error' => $e->getMessage()];
//         }
//     }

//     return $responses;
// }
