<?php
header('Content-Type: application/json');
include '../../model/database.php';

// Cek koneksi
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $conn->connect_error
    ]);
    exit;
}

$data = array();

$status = "gagal";
$message = "Proses update kuota koordinator gagal.";
$dateNow = date("Y-m-d H:i:s");

try {
    // <PERSON><PERSON> proses
    $conn->begin_transaction();

    // Cek apakah ada kuota koordinator yang akan diupdate
    $get = $conn->query("SELECT
        qk.id_koordinator
    FROM
        `quotas_koordinator` qk
        JOIN quotas q ON qk.kategori = q.kategori
        JOIN koordinator k ON qk.id_koordinator = k.id_koordinator AND q.paket = k.paket
    WHERE
        DATE_ADD( qk.last_updated, INTERVAL 1 MONTH ) <= NOW()");
    if ($get->num_rows > 0) {
        // Update kuota koordinator jika last update sudah satu bulan
        $update = $conn->prepare("UPDATE
            `quotas_koordinator` qk
            JOIN quotas q ON qk.kategori = q.kategori
            JOIN koordinator k ON qk.id_koordinator = k.id_koordinator AND q.paket = k.paket
        SET
            qk.remaining_quota = q.default_quota, last_updated = ?
        WHERE
            DATE_ADD( qk.last_updated, INTERVAL 1 MONTH ) <= NOW()");
        $update->bind_param("s", $dateNow);
        if ($update->execute()) {
            $update->close();

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Proses update kuota koordinator berhasil.";
        } else {
            throw new Exception("Proses update kuota koordinator gagal.");
        }
    } else {
        $status = "success";
        $message = "Tidak ada kuota koordinator yang akan diupdate.";
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $status = "gagal";
    $message = $e->getMessage();
}

$data['status'] = $status;
$data['message'] = $message;

$output = array("data" => $data);
$output = json_encode($output, JSON_PRETTY_PRINT);
echo $output;
