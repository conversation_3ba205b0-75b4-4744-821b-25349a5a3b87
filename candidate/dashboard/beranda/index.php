<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../../model/database.php";

if (isset($_SESSION['users'])) {
    $tempAkses = "true";
    $pin = "-";
    $nama_user = "-";
    $email_user = "-";

    // get data user
    if (isset($_SESSION['users']['pin'])) {
        $pin = addslashes($_SESSION['users']['pin']);
    }

    if (isset($_SESSION['users']['nama'])) {
        $nama_user = $_SESSION['users']['nama'];
    }

    if (isset($_SESSION['users']['email'])) {
        $email_user = $_SESSION['users']['email'];
    }

    // simpan log aktivitas
    $messages = 'Menampilkan halaman beranda di sistem.';
    $extra_info = "Kandidat";
    $level = "INFO";
    $path = $_SERVER['REQUEST_URI'];
    logActivity($conn, $pin, $level, $messages, $extra_info);
} else {
    $tempAkses = "all";
    $pin = '';
}

if (isset($_GET['q'])) {
    $q = htmlspecialchars($_GET['q']);
} else {
    $q = '';
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><?= translate('Lowongan Tersedia') ?> - digitalcv</title>
    <meta
        content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
        name="viewport" />
    <?php include_once '../style.php'; ?>

</head>

<body>
    <div class="wrapper">
        <?php include '../sidebar.php' ?>
        <div class="main-panel">
            <?php include '../header.php' ?>
            <div class="container">
                <div class="page-inner">
                    <div class="row" style="background-color: #0D3B72;">
                        <div class="col-md-6">
                            <div class="input-group mt-3 mb-3" style="height: 35px;">
                                <input type="text" class="form-control" id="filter-cari" aria-label="<?= translate('Pencarian nama pekerjaan atau perusahaan') ?>" placeholder="<?= translate('Cari nama pekerjaan atau perusahaan') ?>" value="<?= $q ?>">
                                <button class="input-group-text" onclick="loadData(1)"><i class="bi bi-search"></i></button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2 align-self-center">
                            <select class="form-select" id="filter-lokasi" aria-label="Filter lokasi" style="position: relative !important; width: 100%;" onchange="loadData(1)">
                            </select>
                        </div>
                    </div>
                    <div class="row mt-1 mb-2">
                        <div class="col-md-3 mt-1">
                            <div class="dropdown" style="width: 100%;">
                                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="background-color: #0D3B72 !important; border: 0px; width: 100%;">
                                    <?= translate('Jenis Pekerjaan') ?>
                                </button>
                                <ul class="dropdown-menu" style="width:100%">
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Full-Time" name="jenis_pekerjaan[]" id="jenis_pekerjaan1" onchange="loadData(1)">
                                            <label class="form-check-label" for="jenis_pekerjaan1">
                                                <?= translate('Full-Time') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Part-Time" name="jenis_pekerjaan[]" id="jenis_pekerjaan2" onchange="loadData(1)">
                                            <label class="form-check-label" for="jenis_pekerjaan2">
                                                <?= translate('Part-Time') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Kontrak" name="jenis_pekerjaan[]" id="jenis_pekerjaan3" onchange="loadData(1)">
                                            <label class="form-check-label" for="jenis_pekerjaan3">
                                                <?= translate('Kontrak') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="Magang" name="jenis_pekerjaan[]" id="jenis_pekerjaan4" onchange="loadData(1)">
                                            <label class="form-check-label" for="jenis_pekerjaan4">
                                                <?= translate('Magang') ?>
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3 mt-1">
                            <div class="dropdown" style="width: 100%;">
                                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="background-color: #0D3B72 !important; border: 0px; width: 100%;">
                                    <?= translate('Spesialisasi') ?>
                                </button>
                                <ul class="dropdown-menu" style="max-height: 250px; overflow-y: auto;width:100%">
                                    <?php
                                    $arr_spesialisasi = array(
                                        'Akuntansi',
                                        'Administrasi & Dukungan Perkantoran',
                                        'Periklanan',
                                        'Seni & Media',
                                        'Pertanian',
                                        'Konstruksi',
                                        'Edukasi',
                                        'Energi & Sumber Daya',
                                        'Keuangan',
                                        'Kesehatan',
                                        'Teknologi Informasi',
                                        'Manufaktur',
                                        'Ritel & Perdagangan',
                                        'Transportasi & Logistik',
                                        'Pariwisata & Perhotelan',
                                        'Hukum & Konsultasi',
                                        'Telekomunikasi',
                                        'Keamanan',
                                        'Pemerintahan & LSM'
                                    );

                                    for ($i = 0; $i < count($arr_spesialisasi); $i++) {
                                        echo '<li>
                                                <div class="form-check" style="white-space: nowrap;">
                                                    <input class="form-check-input" type="checkbox" value="' . $arr_spesialisasi[$i] . '" name="spesialisasi[]" id="spesialisasi' . $i . '" onchange="loadData(1)">
                                                    <label class="form-check-label" style="white-space: normal;" for="spesialisasi' . $i . '">
                                                        ' . translate($arr_spesialisasi[$i]) . '
                                                    </label>
                                                </div>
                                            </li>';
                                    }
                                    ?>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3 mt-1">
                            <div class="dropdown" style="width: 100%;">
                                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="background-color: #0D3B72 !important; border: 0px; width: 100%;">
                                    <?= translate('Pendidikan') ?>
                                </button>
                                <ul class="dropdown-menu" style="max-height: 250px; overflow-y: auto;width:100%">
                                    <?php
                                    $arr_pendidikan = array(
                                        'SD',
                                        'SMP',
                                        'SMA',
                                        'SMK',
                                        'D1',
                                        'D2',
                                        'D3',
                                        'D4',
                                        'S1',
                                        'S2',
                                        'S3'
                                    );

                                    for ($i = 0; $i < count($arr_pendidikan); $i++) {
                                        echo '<li>
                                                <div class="form-check" style="white-space: nowrap;">
                                                    <input class="form-check-input" type="checkbox" value="' . $arr_pendidikan[$i] . '" name="pendidikan[]" id="pendidikan' . $i . '" onchange="loadData(1)">
                                                    <label class="form-check-label" for="pendidikan' . $i . '">
                                                        ' . translate($arr_pendidikan[$i]) . '
                                                    </label>
                                                </div>
                                            </li>';
                                    }
                                    ?>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-3 mt-1">
                            <div class="dropdown" style="width: 100%;">
                                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="background-color: #0D3B72 !important; border: 0px; width: 100%;">
                                    <?= translate('Waktu Posting') ?>
                                </button>
                                <ul class="dropdown-menu" style="width:100%">
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting1" value="Kapan Saja" onchange="loadData(1)" checked>
                                            <label class="form-check-label" for="waktu_posting1">
                                                <?= translate('Kapan Saja') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting2" value="Hari Ini" onchange="loadData(1)">
                                            <label class="form-check-label" for="waktu_posting2">
                                                <?= translate('Hari Ini') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting3" value="3 Hari Ini Terakhir" onchange="loadData(1)">
                                            <label class="form-check-label" for="waktu_posting3">
                                                <?= translate('3 Hari Ini Terakhir') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting4" value="7 Hari Ini Terakhir" onchange="loadData(1)">
                                            <label class="form-check-label" for="waktu_posting4">
                                                <?= translate('7 Hari Ini Terakhir') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting5" value="14 Hari Ini Terakhir" onchange="loadData(1)">
                                            <label class="form-check-label" for="waktu_posting5">
                                                <?= translate('14 Hari Ini Terakhir') ?>
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="waktu_posting[]" id="waktu_posting6" value="30 Hari Ini Terakhir" onchange="loadData(1)">
                                            <label class="form-check-label" for="waktu_posting6">
                                                <?= translate('30 Hari Ini Terakhir') ?>
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <br>

                    <?php
                    if (isset($_SESSION['users'])) {
                    ?>
                        <div class="row" id="konten-warning-progres-pengisian" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-warning" role="alert">
                                    <h6 class="alert-title"><b>Notice</b></h6>
                                    <?= translate('Silakan lengkapi data diri anda terlebih dahulu pada menu Data Diri sebelum melamar pekerjaan.') ?>
                                </div>
                            </div>
                        </div>
                    <?php
                    }
                    ?>

                    <div class="row" id="loading-pagination" style="display: none;">
                        <div class="col-md-12 d-flex justify-content-center">
                            <div class="loader"></div>
                        </div>
                    </div>

                    <div id="data-container"></div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../footer.php' ?>

    <script>
        function loadData(page) {
            $("#data-container").hide();
            $("#loading-pagination").show();

            var lokasi = $("#filter-lokasi").val();

            var pencarian = $("#filter-cari").val();
            let jenis_pekerjaan = $('input[name="jenis_pekerjaan[]"]:checked').map(function() {
                return $(this).val();
            }).get();
            let spesialisasi = $('input[name="spesialisasi[]"]:checked').map(function() {
                return $(this).val();
            }).get();
            let pendidikan = $('input[name="pendidikan[]"]:checked').map(function() {
                return $(this).val();
            }).get();
            let waktu_posting = $('input[name="waktu_posting[]"]:checked').map(function() {
                return $(this).val();
            }).get();

            if (lokasi === null) {
                lokasi = "";
            }

            $.ajax({
                url: "../../controller/controller.php?func=paginationListJob",
                type: "POST",
                data: {
                    page: page,
                    pencarian: pencarian,
                    lokasi: lokasi,
                    jenis_pekerjaan: jenis_pekerjaan,
                    spesialisasi: spesialisasi,
                    pendidikan: pendidikan,
                    waktu_posting: waktu_posting
                },
                success: function(response) {
                    var obj = JSON.parse(JSON.stringify(response));

                    $("#data-container").html(obj.html);
                    $("#loading-pagination").hide();
                    $("#data-container").show();
                }
            });
        }

        $(document).ready(function() {
            // Get progress pengisian
            var pin = '<?php echo $pin ?>';
            if (pin != "-") {
                $.ajax({
                    type: "post",
                    url: "../../controller/controller.php?func=getProgresPengisian",
                    success: function(result) {
                        var obj = JSON.parse(JSON.stringify(result));

                        if (obj.value == 100) {
                            $("#konten-warning-progres-pengisian").hide();
                        } else {
                            $("#konten-warning-progres-pengisian").show();
                        }
                    },
                });
            }
            // End Get progress pengisian

            $("#spesialisasi").select2();

            $('#filter-lokasi').select2({
                placeholder: "<?= translate('Lokasi') ?>",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getLokasi',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data Loaksi
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#filter-cari").on("keydown", function(event) {
                if (event.key === "Enter") {
                    loadData(1);
                }
            });

            loadData(1); // Load halaman pertama saat pertama kali dibuka

            $(document).on("click", ".page-link", function() {
                let page = $(this).data("page");
                loadData(page);
            });
        });

        function simpanFavLowongan(id) {
            grecaptcha.ready(function() {
                grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                    action: 'submit'
                }).then(function(token) {
                    if ($("#check-fav-" + id).is(':checked')) {
                        $("#check-fav-" + id).prop("checked", false);
                        $("#btn-fav-" + id).removeClass('bi bi-heart-fill').addClass('bi bi-heart');
                        var check = 'false';
                    } else {
                        $("#check-fav-" + id).prop("checked", true);
                        $("#btn-fav-" + id).removeClass('bi bi-heart').addClass('bi bi-heart-fill');
                        var check = 'true';
                    }

                    var q = $("#check-fav-" + id).val();

                    $.ajax({
                        type: "post",
                        url: "../../controller/controller.php?func=simpanFavLowongan",
                        data: {
                            q: q,
                            check: check,
                            recaptcha_response: token
                        },
                        success: function(result) {},
                    });
                });
            });
        }
    </script>
</body>

</html>