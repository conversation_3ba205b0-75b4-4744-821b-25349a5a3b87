//start function untuk mengambil angka saja
function hanyaAngka(evt) {
    var charCode = evt.which ? evt.which : event.keyCode;

    if (charCode < 48 || charCode > 57) return false;
    return true;
}
//end function untuk mengambil angka saja

//start funciton untuk menampilkan password saat melakukan input
function showPass(id, btn) {
    var input = document.getElementById(id);
    btn = document.getElementById(btn);

    if (input.type === "password") {
        input.type = "text";
        btn.innerHTML = '<i class="bi bi-eye"></i>';
    } else {
        input.type = "password";
        btn.innerHTML = '<i class="bi bi-eye-slash"></i>';
    }
}
//end funciton untuk menampilkan password saat melakukan input

//start fungsi submit daftar candidate
$("#form-daftar").on("submit", function (event) {
    event.preventDefault(); // Mencegah reload form
    event.stopPropagation(); // Menghentikan event bubbling

    if (this.checkValidity() === false) {
        $(this).addClass("was-validated");
        return;
    }

    var nama = $("[name=nama_lengkap]").val();
    let phoneNumber = $("[name=no_tlp]").val().replace(/^0+/, ''); // Hilangkan angka 0 di awal
    let countryCode = $('#country-code').val();
    let no_tlp = countryCode + phoneNumber;
    console.log(no_tlp);
    var password = $("[name=password]").val();
    var kom_pass = $("[name=kon_password]").val();

    // cek password dan konfirmasi password apakah sudah sama
    if (password != kom_pass) {
        toastr["warning"](window.appData.alert3);
    } else {
        $("#btn-daftar").html(window.appData.harapTunggu + "....");
        $("#btn-daftar").attr("disabled", true);
        $.ajax({
            type: "post",
            url: "../../controller/sign-up/registerCandidate",
            data: {
                nama: nama,
                no_tlp: no_tlp,
                password: password,
            },
            success: function (result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    Swal.fire({
                        title: window.appData.berhasil,
                        text: window.appData.textberhasil,
                        icon: "success",
                    }).then((result) => {
                        window.location.href = "../login";
                    });
                } else {
                    toastr["error"](obj.message);
                    $("#btn-daftar").html(window.appData.daftar);
                    $("#btn-daftar").prop("disabled", false);
                }
            },
        });
    }
});
//end fungsi submit daftar candidate
