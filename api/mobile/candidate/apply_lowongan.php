<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';
require '../../fcm_helper.php';
include '../../../api/ses.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

// Import AWS Exception jika tersedia
if (class_exists('Aws\Exception\AwsException')) {
    class_alias('Aws\Exception\AwsException', 'AwsException');
} else {
    // Fallback jika AWS SDK tidak tersedia
    class AwsException extends Exception {}
}

// Fungsi untuk validasi dan sanitasi input
function validateAndSanitizeInput($input, $type = 'string')
{
    if ($input === null || $input === '') {
        return false;
    }

    switch ($type) {
        case 'id':
            // Validasi ID hanya boleh alphanumeric, underscore, dan dash
            return preg_match('/^[a-zA-Z0-9_-]+$/', $input) ? $input : false;
        case 'location':
            // Sanitasi lokasi
            return filter_var(trim($input), FILTER_SANITIZE_STRING);
        case 'array':
            if (!is_array($input)) return false;
            return array_map(function ($item) {
                return htmlspecialchars(trim($item), ENT_QUOTES, 'UTF-8');
            }, $input);
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

// Verifikasi access token dan dapatkan data user
$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama_lengkap ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

function getIDLamar($length = 10)
{
    try {
        // Gunakan random_bytes untuk cryptographically secure random
        return bin2hex(random_bytes($length / 2));
    } catch (Exception $e) {
        // Fallback dengan random_int yang lebih aman dari rand()
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

if (!function_exists('getIDScreening')) {
    // function buat id screening
    function getIDScreening($length)
    {
        try {
            // Gunakan random_bytes untuk cryptographically secure random
            return bin2hex(random_bytes($length / 2));
        } catch (Exception $e) {
            // Fallback dengan random_int yang lebih aman dari rand()
            $data = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyz';
            $string = '';
            for ($i = 0; $i < $length; $i++) {
                $pos = random_int(0, strlen($data) - 1);
                $string .= $data[$pos];
            }
            return $string;
        }
    }
}

if (!function_exists('ProsesScreening')) {
    // function buat pincode
    function ProsesScreening($conn, $pin, $id_req)
    {
        $getRule = $conn->prepare("SELECT * FROM `list_request` where id_req = ?");
        $getRule->bind_param("s", $id_req);
        $getRule->execute();
        $queryRule = $getRule->get_result();
        $getRule->close();
        $rowRule = mysqli_fetch_assoc($queryRule);

        //get data from databse
        $id_koordinator           = $rowRule['id_koordinator'];
        $cekScreening             = explode("|", $rowRule['check_screening']);
        $lokasiKerjaRule          = explode(",", $rowRule['lokasi_kerja']);
        $usiaRule                 = explode("-", $rowRule['k_usia']);
        $minUsia                  = $usiaRule[0];
        $maxUsia                  = $usiaRule[1];
        $jkRule                   = $rowRule['k_jk'];
        $pengalamanTotalRule      = $rowRule['k_pengalaman'];
        $pengalamanSamaRule       = $rowRule['k_pengalaman_sama'];
        $statusRule               = $rowRule['k_status'];
        $pendidikanRule           = $rowRule['k_pendidikan'];
        $jurusanPendidikan        = explode("|", $rowRule['k_jurusan']);
        $sekolahRule              = explode("|", $rowRule['k_sekolah']);
        $simRule                  = explode(",", $rowRule['k_sim']);
        $sistem_kerja             = $rowRule['sistem_kerja'];
        $statusJurusan = '';

        $vPengalaman              = $rowRule['v_pengalaman'];
        $vMinPendidikan           = $rowRule['v_min_pendidikan'];
        $vUsiaLebih               = $rowRule['v_usia_lebih'];
        $vUsiaKurang              = $rowRule['v_usia_kurang'];
        $vJurusan                 = $rowRule['v_jurusan'];
        $vSekolah                 = $rowRule['v_sekolah'];

        $getRule2 = $conn->prepare("SELECT * FROM `list_kriteria` WHERE id_req = ?");
        $getRule2->bind_param("s", $id_req);
        $getRule2->execute();
        $queryRule2 = $getRule2->get_result();
        $rowRule2 = mysqli_fetch_assoc($queryRule2);
        $getRule2->close();

        //get data from databasae
        $kkRule       = explode(",", $rowRule2['kk']);
        $kbRule       = explode(",", $rowRule2['kb']);
        $kmRule       = $rowRule2['km'];
        $kbduRule     = $rowRule2['kbdu'];
        $rlpRule      = explode(',', $rowRule2['rlp']);

        $data = array();
        $statusPendidikan = '';
        $statusPengalaman = '';
        $isUmur = '';

        // cek apakah kandidat sudah perah screening
        $get = $conn->prepare("SELECT * FROM `hasil_screening` where id_gestalt = ? AND id_req = ?");
        $get->bind_param("ss", $pin, $id_req);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $rowHasilScreening = mysqli_fetch_array($result);
            $idScreening = $rowHasilScreening['id_screening'];
        } else {
            $sqlCek = "SELECT id_screening FROM hasil_screening";
            $queryCek = $conn->query($sqlCek);

            do {
                $idScreening = getIDScreening(10);
            } while ($idScreening == $queryCek);
        }

        // get data kandidat
        $get = $conn->prepare("SELECT
                                uk.pin as id_akun,
                                lr.id_req,
                                lr.posisi as posisi_lamar,
                                rh.nama as nama_lengkap,
                                rh.tempat_lahir,
                                rh.tgl_lahir,
                                rh.kota as kota_domisili,
                                rh.kecamatan as kec_domisili,
                                rh.pendidikan_terakhir,
                                IF(rp.jurusan IS NULL,'', GROUP_CONCAT(rp.jurusan)) as jurusan,
                                IF(rp.nama_sekolah IS NULL,'', GROUP_CONCAT(rp.nama_sekolah)) as nama_sekolah,
                                rh.status_pernikahan as status_marital,
                                rh.jenis_kelamin as gender,
                                rh.sim,
                                rh.lama_pengalaman_kerja as total_pengalaman,
                                rh.lama_posisi_kerja as total_pengalama_sama,
                                rh.minat_lokasi_kerja as penempatan,
                                rh.ilmu_komputerisasi as skill_komputer,
                                IF(pb.bahasa IS NULL,'',pb.bahasa) as skill_bahasa,
                                rh.memimpin_tim as skill_pimpin,
                                rh.kemampuan_presentasi as skill_public,
                                rh.lingkup_pekerjaan as skill_lingkup_pekerjaan,
                                rh.minat_lokasi_kerja as lokasi_kerja,
                                rh.perjalanan_dinas as kebutuhan_traveling
                            FROM
                                users_kandidat uk
                                JOIN users_lamar ul ON ul.id_gestalt = uk.pin
                                JOIN list_request lr ON ul.id_req = lr.id_req
                                JOIN rh ON rh.id = uk.pin
                                LEFT JOIN (SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')) as rp ON rp.id = uk.pin
                                LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE id = ? GROUP BY id) pb ON pb.id = uk.pin
                            WHERE
                                uk.pin = ?
                                AND ul.id_req = ?");
        $get->bind_param("ssss", $pin, $pin, $pin, $id_req);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        $poinScreening = 0;
        $poinValidasi = 0;

        while ($row = mysqli_fetch_array($result)) {
            $id_lamar           = $idScreening;
            $namaLengkap        = $row['nama_lengkap'];
            $tgl_lahir          = $row['tgl_lahir'];
            $kota_domisili      = $row['kota_domisili'];
            $pendidkan_terakhir = $row['pendidikan_terakhir'];
            $jurusan            = explode(",", $row['jurusan']);
            $nama_sekolah       = explode(",", $row['nama_sekolah']);
            $status_marital     = $row['status_marital'];
            $gender             = $row['gender'];
            $sim                = explode("|", $row['sim']);
            $lokasi             = explode("|", $row['penempatan']);
            $total_pengalaman   = $row['total_pengalaman'];
            $skill_komputer     = explode(",", $row['skill_komputer']);
            $skill_bahasa       = explode(",", $row['skill_bahasa']);
            $skill_pimpin       = $row['skill_pimpin'];
            $skill_public       = $row['skill_public'];
            $skill_lingkup      = explode(",", $row['skill_lingkup_pekerjaan']);
            $namaAkun           = $row['nama_lengkap'];
            $lokasi_kerja       = $row['lokasi_kerja'];
            $kebutuhan_traveling = $row['kebutuhan_traveling'];

            $data_riwayat_screening = array();

            for ($i = 0; $i < count($cekScreening); $i++) {
                $arrValidasi = $cekScreening[$i];
                $poin_validasi = 1;
                $poin_screening = 0;

                // cek screening untuk kebutuhan traveling
                if ($arrValidasi == 'Aktifitas Kerja') {
                    $poinValidasi++;

                    if ($kebutuhan_traveling == 'Ya') {
                        $poinScreening++;
                        $poin_screening = 1;
                    } elseif ($sistem_kerja == $kebutuhan_traveling) {
                        $poinScreening++;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Aktifitas Kerja', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk kebutuhan traveling

                // cek screening untuk lokasi kerja
                if ($arrValidasi == 'Lokasi Kerja') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($lokasi_kerja == 'DIMANA SAJA') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $resultLokasi = array();
                        //valdasi lokasi kerja
                        for ($k = 0; $k < count($lokasiKerjaRule); $k++) {
                            $arrLokasiKerja = $lokasiKerjaRule[$k];

                            for ($j = 0; $j < count($lokasi); $j++) {
                                $arrPenempatan = $lokasi[$j];

                                if ($arrLokasiKerja == $arrPenempatan) {
                                    $resultLokasi[] = "Yes";
                                } else {
                                    $resultLokasi[] = "No";
                                }
                            }
                        }

                        if (in_array("Yes", $resultLokasi)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Lokasi Kerja', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk lokasi kerja

                // cek screening untuk umur
                if ($arrValidasi == 'Umur Kandidat') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi Usia
                    $dateLahir = new DateTime($tgl_lahir);
                    $dateToday = new DateTime();
                    $diff = $dateToday->diff($dateLahir);
                    $umurKandidat = $diff->y;

                    if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                        $isUmur = 'true1';
                    } else if ($vUsiaLebih == 'Izinkan' && $vUsiaKurang == 'Tidak Izinkan') {
                        if ($minUsia <= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true2';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false2';
                        }
                    } else if ($vUsiaLebih == 'Tidak Izinkan' && $vUsiaKurang == 'Izinkan') {
                        if ($maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true3';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false3';
                        }
                    } else {
                        if ($minUsia <= $umurKandidat && $maxUsia >= $umurKandidat) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                            $isUmur = 'true4';
                        } else {
                            $poinScreening += 0;
                            $isUmur = 'false4';
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Umur Kandidat', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk umur

                // cek screening untuk jenis kelamin
                if ($arrValidasi == 'Jenis Kelamin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi gender
                    if ($jkRule == 'Keduanya' || $jkRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($jkRule == 'Laki-Laki') {
                        if ($gender == 'Laki-Laki') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($jkRule == 'Perempuan') {
                        if ($gender == 'Perempuan') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jenis Kelamin', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk jenis kelamin

                // cek screening untuk status pernikahan
                if ($arrValidasi == 'Status Pernikahan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi status
                    if ($statusRule == 'Tidak' || $statusRule == 'Tidak Perlu / Tidak Wajib') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else if ($statusRule == 'Belum Menikah') {
                        if ($status_marital == 'Belum Menikah') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    } else if ($statusRule == 'Menikah') {
                        if ($status_marital == 'Menikah' || $status_marital == 'Duda' || $status_marital == 'Janda') {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Status Pernikahan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk status pernikahan

                // cek screening untuk bahasa yang dikuasai
                if ($arrValidasi == 'Bahasa yang dikuasai') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi skill bahasa
                    $resultBahasa = array();
                    for ($h = 0; $h < count($kbRule); $h++) {
                        $arrKriteriaKb = $kbRule[$h];

                        for ($l = 0; $l < count($skill_bahasa); $l++) {
                            $arrskillBahasa = $skill_bahasa[$l];

                            if ($arrKriteriaKb == $arrskillBahasa) {
                                $resultBahasa[] = "Yes";
                            } else {
                                $resultBahasa[] = "No";
                            }
                        }
                    }

                    if (in_array("Yes", $resultBahasa)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Bahasa yang dikuasai', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk bahasa yang dikuasai

                // cek screening untuk sim
                if ($arrValidasi == 'SIM') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi SIM
                    $hasilSim = array();
                    for ($j = 0; $j < count($sim); $j++) {
                        $arrsim = $sim[$j];

                        if (in_array($arrsim, $simRule)) {
                            $hasilSim[] = "Yes";
                        } else {
                            $hasilSim[] = "No";
                        }
                    }

                    $countHasilSim = array_count_values($hasilSim);
                    if (in_array('No', $hasilSim)) {
                        $countyes = 0;
                    } else {
                        $countyes = $countHasilSim['Yes'];
                    }
                    if ($countyes == count($simRule)) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $poinScreening += 0;
                    }

                    $data_riwayat_screening[] = array('title' => 'SIM', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk sim

                // cek screening untuk pendidikan
                if ($arrValidasi == 'Pendidikan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi pendidikan
                    if ($vMinPendidikan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if ($pendidikanRule == 'SD') {
                            if ($pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMP') {
                            if ($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'SMA' || $pendidikanRule == 'SMK') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'Diploma') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S1') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S2') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        } else if ($pendidikanRule == 'S3') {
                            if (($pendidkan_terakhir != 'SD' && $pendidkan_terakhir != 'SMP' && $pendidkan_terakhir != 'SMA' && $pendidkan_terakhir != 'SMK' && $pendidkan_terakhir != 'Diploma' && $pendidkan_terakhir != 'S1' && $pendidkan_terakhir != 'S2') && $pendidkan_terakhir != '') {
                                $poinScreening += 1;
                                $poin_screening = 1;
                                $statusPendidikan = 'Lolos';
                            } else {
                                $poinScreening += 0;
                                $statusPendidikan = 'Tidak Lolos';
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Pendidikan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk pendidikan

                // cek screening untuk jurusan pendidikan
                if ($arrValidasi == 'Jurusan') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi jurusan
                    if ($vJurusan == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        if (in_array('Semua Jurusan', $jurusanPendidikan)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        } else {
                            $temp_arr_jurusan = array_intersect($jurusanPendidikan, $jurusan);

                            if (!empty($temp_arr_jurusan)) {
                                $poinScreening += 1;
                                $poin_screening = 1;
                            }
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Jurusan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk jurusan pendidikan

                // cek screening untuk nama sekolah
                if ($arrValidasi == 'Sekolah') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    //validasi sekolah
                    if ($vSekolah == 'Tidak Izinkan') {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    } else {
                        $temp_arr_sekolah = array_intersect($sekolahRule, $nama_sekolah);

                        if (!empty($temp_arr_sekolah)) {
                            $poinScreening += 1;
                            $poin_screening = 1;
                        }
                    }

                    $data_riwayat_screening[] = array('title' => 'Sekolah', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk nama sekolah

                // cek screening untuk pengalaman
                if ($arrValidasi == 'Pengalaman') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($total_pengalaman >= $pengalamanTotalRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Pengalaman', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk pengalaman

                // cek screening untuk Kemampuan Komputer
                if ($arrValidasi == 'Kemampuan Komputer' && count($kkRule) > 0) {
                    $poinValidasi += count($kkRule);
                    $poin_validasi = count($kkRule);
                    $poin_screening = 0;

                    //validasi skill komputer
                    $temp_arr_kk = array_intersect($kkRule, $skill_komputer);

                    if (count($temp_arr_kk) > 0) {
                        $poinScreening += count($temp_arr_kk);
                        $poin_screening += count($temp_arr_kk);
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Komputer', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Komputer

                // cek screening untuk Kemampuan Memimpin
                if ($arrValidasi == 'Kemampuan Memimpin') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_pimpin >= $kmRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan Memimpin', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Kemampuan Memimpin

                // cek screening untuk Kemampuan berbicara didepan umum
                if ($arrValidasi == 'Kemampuan berbicara didepan umum') {
                    $poinValidasi++;
                    $poin_validasi = 1;
                    $poin_screening = 0;

                    if ($skill_public >= $kbduRule) {
                        $poinScreening += 1;
                        $poin_screening = 1;
                    }

                    $data_riwayat_screening[] = array('title' => 'Kemampuan berbicara didepan umum', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // cek screening untuk Kemampuan berbicara didepan umum

                // cek screening untuk Ruang lingkup pekerjaan
                if ($arrValidasi == 'Ruang lingkup pekerjaan' && count($rlpRule) > 0) {
                    $poinValidasi += count($rlpRule);
                    $poin_validasi = count($rlpRule);
                    $poin_screening = 0;

                    //validasi skill komputer
                    $temp_arr_rlp = array_intersect($rlpRule, $skill_lingkup);

                    if (count($temp_arr_rlp) > 0) {
                        $poinScreening += count($temp_arr_rlp);
                        $poin_screening += count($temp_arr_rlp);
                    }

                    $data_riwayat_screening[] = array('title' => 'Ruang lingkup pekerjaan', 'poin' => $poin_screening . '/' . $poin_validasi);
                }
                // end cek screening untuk Ruang lingkup pekerjaan
            }
        }

        $presentase = round(($poinScreening / $poinValidasi) * 100);

        if ($presentase < 0) {
            $presentase = 0;
        }

        // hapus data screening sebelumnya
        $deleteHasil = $conn->prepare("DELETE FROM hasil_screening WHERE id_screening = ?");
        $deleteHasil->bind_param("s", $idScreening);
        $deleteHasil->execute();
        $deleteHasil->close();

        $deleteScreening = $conn->prepare("DELETE FROM screening_recruitment WHERE id_screening = ?");
        $deleteScreening->bind_param("s", $idScreening);
        $deleteScreening->execute();
        $deleteScreening->close();

        $deleteRiwayat = $conn->prepare("DELETE FROM riwayat_screening WHERE id_screening = ?");
        $deleteRiwayat->bind_param("s", $idScreening);
        $deleteRiwayat->execute();
        $deleteRiwayat->close();

        $created_at = date("Y-m-d H:i:s");

        // insert hasil screening
        $insert = $conn->prepare("INSERT INTO `hasil_screening`(`id_screening`, `id_gestalt`, `id_req`, `id_koordinator`, `percentase`, `tgl`) VALUES (?, ?, ?, ?, ?, ?)");
        $insert->bind_param("ssssss", $idScreening, $pin, $id_req, $id_koordinator, $presentase, $created_at);

        if ($insert->execute()) {
            $insert->close();

            // insert screening recruitment
            $insert = $conn->prepare("INSERT INTO `screening_recruitment`(`id_screening`, `id_akun`, `id_req`, `posisi_lamar`, `nama_lengkap`, `tempat_lahir`, `tgl_lahir`, `kota_domisili`, `kel_domisili`, `kec_domisili`, `pendidikan_terakhir`, `nama_sekolah`, `jurusan`, `tgl_lulus`, `status_marital`, `gender`, `sim`, `total_pengalaman`, `total_pengalama_sama`, `penempatan`, `skill_komputer`, `skill_bahasa`, `skill_pimpin`, `skill_public`, `skill_lingkup_pekerjaan`, `tgl`, `sekolah`)
            SELECT
                ? as id_screening,
                uk.pin as id_akun,
                lr.id_req,
                lr.posisi as posisi_lamar,
                rh.nama as nama_lengkap,
                rh.tempat_lahir,
                rh.tgl_lahir,
                rh.kota as kota_domisili,
                '' as kel_domisili,
                rh.kecamatan as kec_domisili,
                rh.pendidikan_terakhir,
                IF(rp.nama_sekolah IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.nama_sekolah <> '' THEN rp.nama_sekolah END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as nama_sekolah,
                IF(rp.jurusan IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.jurusan <> '' THEN rp.jurusan END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as jurusan,
                IF(rp.tahun_selesai IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.tahun_selesai <> '' THEN rp.tahun_selesai END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as tgl_lulus,
                rh.status_pernikahan as status_marital,
                rh.jenis_kelamin as gender,
                rh.sim,
                rh.lama_pengalaman_kerja as total_pengalaman,
                rh.lama_posisi_kerja as total_pengalama_sama,
                ul.lokasi as penempatan,
                rh.ilmu_komputerisasi as skill_komputer,
                IF(pb.bahasa IS NULL,'',pb.bahasa) as skill_bahasa,
                rh.memimpin_tim as skill_pimpin,
                rh.kemampuan_presentasi as skill_public,
                rh.lingkup_pekerjaan as skill_lingkup_pekerjaan,
                ? as tgl,
                IF(rp.nama_sekolah IS NULL,'', SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN rp.nama_sekolah <> '' THEN rp.nama_sekolah END ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3') SEPARATOR ','), ',', -1)) as sekolah
            FROM
                users_kandidat uk
                JOIN users_lamar ul ON ul.id_gestalt = uk.pin
                JOIN list_request lr ON ul.id_req = lr.id_req
                JOIN rh ON rh.id = uk.pin
                LEFT JOIN (SELECT * FROM riwayat_pendidikan WHERE id = ? ORDER BY FIELD(jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')) as rp ON rp.id = uk.pin
                LEFT JOIN (SELECT id, GROUP_CONCAT(bahasa) as bahasa FROM penguasaan_bahasa WHERE id = ? GROUP BY id) pb ON pb.id = uk.pin
            WHERE
                uk.pin = ?
                AND ul.id_req = ?");
            $insert->bind_param("ssssss", $idScreening, $created_at, $pin, $pin, $pin, $id_req);

            if ($insert->execute()) {
                $insert->close();

                // simpan riwayat screening
                $temp_cek = 'true';
                if (count($data_riwayat_screening) > 0) {
                    for ($i = 0; $i < count($data_riwayat_screening); $i++) {
                        $title = $data_riwayat_screening[$i]['title'];
                        $arr_poin = explode("/", $data_riwayat_screening[$i]['poin']);
                        $temp_poin_screening = $arr_poin[0];
                        $temp_poin_validasi = $arr_poin[1];

                        // simpan riwayat screening
                        $insert = $conn->prepare("INSERT `riwayat_screening` (`id_screening`, `id_gestalt`, `id_req`, `title`, `poin_screening`, `poin_validasi`, `created_at`) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $insert->bind_param("sssssss", $idScreening, $pin, $id_req, $title, $temp_poin_screening, $temp_poin_validasi, $created_at);

                        if ($insert->execute()) {
                            $insert->close();
                        } else {
                            $temp_cek = 'false';
                            break;
                        }
                    }
                }

                if ($temp_cek == 'true') {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}

function kirim_apply_lamaran($emailKandidat, $posisiLamar, $namaPerusahaan, $namaKandidat, $SesClient)
{
    $emailParams = [
        'Destination' => [
            'ToAddresses' => [$emailKandidat],
        ],
        'Message' => [
            'Body' => [
                'Html' => ['Data' => '
                                    <!DOCTYPE html
                                        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                                    <!--[if IE]><html xmlns="http://www.w3.org/1999/xhtml" class="ie"><![endif]--><!--[if !IE]><!-->
                                    <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml"><!--<![endif]-->

                                    <head>
                                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                                        <!--[if !mso]><!-->
                                        <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                        <!--<![endif]-->
                                        <meta name="viewport" content="width=device-width">
                                        <style type="text/css">
                                            @media only screen and (min-width: 620px) {
                                                .wrapper {
                                                    min-width: 600px !important
                                                }

                                                .wrapper h1 {}

                                                .wrapper h1 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper h2 {}

                                                .wrapper h2 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper h3 {}

                                                .column {}

                                                .wrapper .size-8 {
                                                    font-size: 8px !important;
                                                    line-height: 14px !important
                                                }

                                                .wrapper .size-9 {
                                                    font-size: 9px !important;
                                                    line-height: 16px !important
                                                }

                                                .wrapper .size-10 {
                                                    font-size: 10px !important;
                                                    line-height: 18px !important
                                                }

                                                .wrapper .size-11 {
                                                    font-size: 11px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-12 {
                                                    font-size: 12px !important;
                                                    line-height: 19px !important
                                                }

                                                .wrapper .size-13 {
                                                    font-size: 13px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-14 {
                                                    font-size: 14px !important;
                                                    line-height: 21px !important
                                                }

                                                .wrapper .size-15 {
                                                    font-size: 15px !important;
                                                    line-height: 23px !important
                                                }

                                                .wrapper .size-16 {
                                                    font-size: 16px !important;
                                                    line-height: 24px !important
                                                }

                                                .wrapper .size-17 {
                                                    font-size: 17px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-18 {
                                                    font-size: 18px !important;
                                                    line-height: 26px !important
                                                }

                                                .wrapper .size-20 {
                                                    font-size: 20px !important;
                                                    line-height: 28px !important
                                                }

                                                .wrapper .size-22 {
                                                    font-size: 22px !important;
                                                    line-height: 31px !important
                                                }

                                                .wrapper .size-24 {
                                                    font-size: 24px !important;
                                                    line-height: 32px !important
                                                }

                                                .wrapper .size-26 {
                                                    font-size: 26px !important;
                                                    line-height: 34px !important
                                                }

                                                .wrapper .size-28 {
                                                    font-size: 28px !important;
                                                    line-height: 36px !important
                                                }

                                                .wrapper .size-30 {
                                                    font-size: 30px !important;
                                                    line-height: 38px !important
                                                }

                                                .wrapper .size-32 {
                                                    font-size: 32px !important;
                                                    line-height: 40px !important
                                                }

                                                .wrapper .size-34 {
                                                    font-size: 34px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-36 {
                                                    font-size: 36px !important;
                                                    line-height: 43px !important
                                                }

                                                .wrapper .size-40 {
                                                    font-size: 40px !important;
                                                    line-height: 47px !important
                                                }

                                                .wrapper .size-44 {
                                                    font-size: 44px !important;
                                                    line-height: 50px !important
                                                }

                                                .wrapper .size-48 {
                                                    font-size: 48px !important;
                                                    line-height: 54px !important
                                                }

                                                .wrapper .size-56 {
                                                    font-size: 56px !important;
                                                    line-height: 60px !important
                                                }

                                                .wrapper .size-64 {
                                                    font-size: 64px !important;
                                                    line-height: 63px !important
                                                }
                                            }
                                        </style>
                                        <style type="text/css">
                                            body {
                                                margin: 0;
                                                padding: 0;
                                            }

                                            table {
                                                border-collapse: collapse;
                                                table-layout: fixed;
                                            }

                                            * {
                                                line-height: inherit;
                                            }

                                            [x-apple-data-detectors],
                                            [href^="tel"],
                                            [href^="sms"] {
                                                color: inherit !important;
                                                text-decoration: none !important;
                                            }

                                            .wrapper .footer__share-button a:hover,
                                            .wrapper .footer__share-button a:focus {
                                                color: #ffffff !important;
                                            }

                                            .btn a:hover,
                                            .btn a:focus,
                                            .footer__share-button a:hover,
                                            .footer__share-button a:focus,
                                            .email-footer__links a:hover,
                                            .email-footer__links a:focus {
                                                opacity: 0.8;
                                            }

                                            .preheader,
                                            .header,
                                            .layout,
                                            .column {
                                                transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                            }

                                            .preheader td {
                                                padding-bottom: 8px;
                                            }

                                            .layout,
                                            div.header {
                                                max-width: 400px !important;
                                                -fallback-width: 95% !important;
                                                width: calc(100% - 20px) !important;
                                            }

                                            div.preheader {
                                                max-width: 360px !important;
                                                -fallback-width: 90% !important;
                                                width: calc(100% - 60px) !important;
                                            }

                                            .snippet,
                                            .webversion {
                                                Float: none !important;
                                            }

                                            .column {
                                                max-width: 400px !important;
                                                width: 100% !important;
                                            }

                                            .fixed-width.has-border {
                                                max-width: 402px !important;
                                            }

                                            .fixed-width.has-border .layout__inner {
                                                box-sizing: border-box;
                                            }

                                            .snippet,
                                            .webversion {
                                                width: 50% !important;
                                            }

                                            .ie .btn {
                                                width: 100%;
                                            }

                                            [owa] .column div,
                                            [owa] .column button {
                                                display: block !important;
                                            }

                                            .ie .column,
                                            [owa] .column,
                                            .ie .gutter,
                                            [owa] .gutter {
                                                display: table-cell;
                                                float: none !important;
                                                vertical-align: top;
                                            }

                                            .ie div.preheader,
                                            [owa] div.preheader,
                                            .ie .email-footer,
                                            [owa] .email-footer {
                                                max-width: 560px !important;
                                                width: 560px !important;
                                            }

                                            .ie .snippet,
                                            [owa] .snippet,
                                            .ie .webversion,
                                            [owa] .webversion {
                                                width: 280px !important;
                                            }

                                            .ie div.header,
                                            [owa] div.header,
                                            .ie .layout,
                                            [owa] .layout,
                                            .ie .one-col .column,
                                            [owa] .one-col .column {
                                                max-width: 600px !important;
                                                width: 600px !important;
                                            }

                                            .ie .fixed-width.has-border,
                                            [owa] .fixed-width.has-border,
                                            .ie .has-gutter.has-border,
                                            [owa] .has-gutter.has-border {
                                                max-width: 602px !important;
                                                width: 602px !important;
                                            }

                                            .ie .two-col .column,
                                            [owa] .two-col .column {
                                                max-width: 300px !important;
                                                width: 300px !important;
                                            }

                                            .ie .three-col .column,
                                            [owa] .three-col .column,
                                            .ie .narrow,
                                            [owa] .narrow {
                                                max-width: 200px !important;
                                                width: 200px !important;
                                            }

                                            .ie .wide,
                                            [owa] .wide {
                                                width: 400px !important;
                                            }

                                            .ie .two-col.has-gutter .column,
                                            [owa] .two-col.x_has-gutter .column {
                                                max-width: 290px !important;
                                                width: 290px !important;
                                            }

                                            .ie .three-col.has-gutter .column,
                                            [owa] .three-col.x_has-gutter .column,
                                            .ie .has-gutter .narrow,
                                            [owa] .has-gutter .narrow {
                                                max-width: 188px !important;
                                                width: 188px !important;
                                            }

                                            .ie .has-gutter .wide,
                                            [owa] .has-gutter .wide {
                                                max-width: 394px !important;
                                                width: 394px !important;
                                            }

                                            .ie .two-col.has-gutter.has-border .column,
                                            [owa] .two-col.x_has-gutter.x_has-border .column {
                                                max-width: 292px !important;
                                                width: 292px !important;
                                            }

                                            .ie .three-col.has-gutter.has-border .column,
                                            [owa] .three-col.x_has-gutter.x_has-border .column,
                                            .ie .has-gutter.has-border .narrow,
                                            [owa] .has-gutter.x_has-border .narrow {
                                                max-width: 190px !important;
                                                width: 190px !important;
                                            }

                                            .ie .has-gutter.has-border .wide,
                                            [owa] .has-gutter.x_has-border .wide {
                                                max-width: 396px !important;
                                                width: 396px !important;
                                            }

                                            .ie .fixed-width .layout__inner {
                                                border-left: 0 none white !important;
                                                border-right: 0 none white !important;
                                            }

                                            .ie .layout__edges {
                                                display: none;
                                            }

                                            .mso .layout__edges {
                                                font-size: 0;
                                            }

                                            .layout-fixed-width,
                                            .mso .layout-full-width {
                                                background-color: #ffffff;
                                            }

                                            @media only screen and (min-width: 620px) {

                                                .column,
                                                .gutter {
                                                    display: table-cell;
                                                    Float: none !important;
                                                    vertical-align: top;
                                                }

                                                div.preheader,
                                                .email-footer {
                                                    max-width: 560px !important;
                                                    width: 560px !important;
                                                }

                                                .snippet,
                                                .webversion {
                                                    width: 280px !important;
                                                }

                                                div.header,
                                                .layout,
                                                .one-col .column {
                                                    max-width: 600px !important;
                                                    width: 600px !important;
                                                }

                                                .fixed-width.has-border,
                                                .fixed-width.ecxhas-border,
                                                .has-gutter.has-border,
                                                .has-gutter.ecxhas-border {
                                                    max-width: 602px !important;
                                                    width: 602px !important;
                                                }

                                                .two-col .column {
                                                    max-width: 300px !important;
                                                    width: 300px !important;
                                                }

                                                .three-col .column,
                                                .column.narrow {
                                                    max-width: 200px !important;
                                                    width: 200px !important;
                                                }

                                                .column.wide {
                                                    width: 400px !important;
                                                }

                                                .two-col.has-gutter .column,
                                                .two-col.ecxhas-gutter .column {
                                                    max-width: 290px !important;
                                                    width: 290px !important;
                                                }

                                                .three-col.has-gutter .column,
                                                .three-col.ecxhas-gutter .column,
                                                .has-gutter .narrow {
                                                    max-width: 188px !important;
                                                    width: 188px !important;
                                                }

                                                .has-gutter .wide {
                                                    max-width: 394px !important;
                                                    width: 394px !important;
                                                }

                                                .two-col.has-gutter.has-border .column,
                                                .two-col.ecxhas-gutter.ecxhas-border .column {
                                                    max-width: 292px !important;
                                                    width: 292px !important;
                                                }

                                                .three-col.has-gutter.has-border .column,
                                                .three-col.ecxhas-gutter.ecxhas-border .column,
                                                .has-gutter.has-border .narrow,
                                                .has-gutter.ecxhas-border .narrow {
                                                    max-width: 190px !important;
                                                    width: 190px !important;
                                                }

                                                .has-gutter.has-border .wide,
                                                .has-gutter.ecxhas-border .wide {
                                                    max-width: 396px !important;
                                                    width: 396px !important;
                                                }
                                            }

                                            @media only screen and (-webkit-min-device-pixel-ratio: 2),
                                            only screen and (min--moz-device-pixel-ratio: 2),
                                            only screen and (-o-min-device-pixel-ratio: 2/1),
                                            only screen and (min-device-pixel-ratio: 2),
                                            only screen and (min-resolution: 192dpi),
                                            only screen and (min-resolution: 2dppx) {
                                                .fblike {
                                                    background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .tweet {
                                                    background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .linkedinshare {
                                                    background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }

                                                .forwardtoafriend {
                                                    background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                                }
                                            }

                                            @media (max-width: 321px) {
                                                .fixed-width.has-border .layout__inner {
                                                    border-width: 1px 0 !important;
                                                }

                                                .layout,
                                                .column {
                                                    min-width: 320px !important;
                                                    width: 320px !important;
                                                }

                                                .border {
                                                    display: none;
                                                }
                                            }

                                            .mso div {
                                                border: 0 none white !important;
                                            }

                                            .mso .w560 .divider {
                                                Margin-left: 260px !important;
                                                Margin-right: 260px !important;
                                            }

                                            .mso .w360 .divider {
                                                Margin-left: 160px !important;
                                                Margin-right: 160px !important;
                                            }

                                            .mso .w260 .divider {
                                                Margin-left: 110px !important;
                                                Margin-right: 110px !important;
                                            }

                                            .mso .w160 .divider {
                                                Margin-left: 60px !important;
                                                Margin-right: 60px !important;
                                            }

                                            .mso .w354 .divider {
                                                Margin-left: 157px !important;
                                                Margin-right: 157px !important;
                                            }

                                            .mso .w250 .divider {
                                                Margin-left: 105px !important;
                                                Margin-right: 105px !important;
                                            }

                                            .mso .w148 .divider {
                                                Margin-left: 54px !important;
                                                Margin-right: 54px !important;
                                            }

                                            .mso .size-8,
                                            .ie .size-8 {
                                                font-size: 8px !important;
                                                line-height: 14px !important;
                                            }

                                            .mso .size-9,
                                            .ie .size-9 {
                                                font-size: 9px !important;
                                                line-height: 16px !important;
                                            }

                                            .mso .size-10,
                                            .ie .size-10 {
                                                font-size: 10px !important;
                                                line-height: 18px !important;
                                            }

                                            .mso .size-11,
                                            .ie .size-11 {
                                                font-size: 11px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-12,
                                            .ie .size-12 {
                                                font-size: 12px !important;
                                                line-height: 19px !important;
                                            }

                                            .mso .size-13,
                                            .ie .size-13 {
                                                font-size: 13px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-14,
                                            .ie .size-14 {
                                                font-size: 14px !important;
                                                line-height: 21px !important;
                                            }

                                            .mso .size-15,
                                            .ie .size-15 {
                                                font-size: 15px !important;
                                                line-height: 23px !important;
                                            }

                                            .mso .size-16,
                                            .ie .size-16 {
                                                font-size: 16px !important;
                                                line-height: 24px !important;
                                            }

                                            .mso .size-17,
                                            .ie .size-17 {
                                                font-size: 17px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-18,
                                            .ie .size-18 {
                                                font-size: 18px !important;
                                                line-height: 26px !important;
                                            }

                                            .mso .size-20,
                                            .ie .size-20 {
                                                font-size: 20px !important;
                                                line-height: 28px !important;
                                            }

                                            .mso .size-22,
                                            .ie .size-22 {
                                                font-size: 22px !important;
                                                line-height: 31px !important;
                                            }

                                            .mso .size-24,
                                            .ie .size-24 {
                                                font-size: 24px !important;
                                                line-height: 32px !important;
                                            }

                                            .mso .size-26,
                                            .ie .size-26 {
                                                font-size: 26px !important;
                                                line-height: 34px !important;
                                            }

                                            .mso .size-28,
                                            .ie .size-28 {
                                                font-size: 28px !important;
                                                line-height: 36px !important;
                                            }

                                            .mso .size-30,
                                            .ie .size-30 {
                                                font-size: 30px !important;
                                                line-height: 38px !important;
                                            }

                                            .mso .size-32,
                                            .ie .size-32 {
                                                font-size: 32px !important;
                                                line-height: 40px !important;
                                            }

                                            .mso .size-34,
                                            .ie .size-34 {
                                                font-size: 34px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-36,
                                            .ie .size-36 {
                                                font-size: 36px !important;
                                                line-height: 43px !important;
                                            }

                                            .mso .size-40,
                                            .ie .size-40 {
                                                font-size: 40px !important;
                                                line-height: 47px !important;
                                            }

                                            .mso .size-44,
                                            .ie .size-44 {
                                                font-size: 44px !important;
                                                line-height: 50px !important;
                                            }

                                            .mso .size-48,
                                            .ie .size-48 {
                                                font-size: 48px !important;
                                                line-height: 54px !important;
                                            }

                                            .mso .size-56,
                                            .ie .size-56 {
                                                font-size: 56px !important;
                                                line-height: 60px !important;
                                            }

                                            .mso .size-64,
                                            .ie .size-64 {
                                                font-size: 64px !important;
                                                line-height: 63px !important;
                                            }
                                        </style>

                                        <!--[if !mso]><!-->
                                        <style type="text/css">
                                            @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                                        </style>
                                        <style type="text/css">
                                            body {
                                                background-color: #fff
                                            }

                                            .logo a:hover,
                                            .logo a:focus {
                                                color: #1e2e3b !important
                                            }

                                            .mso .layout-has-border {
                                                border-top: 1px solid #ccc;
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .layout-has-bottom-border {
                                                border-bottom: 1px solid #ccc
                                            }

                                            .mso .border,
                                            .ie .border {
                                                background-color: #ccc
                                            }

                                            .mso h1,
                                            .ie h1 {}

                                            .mso h1,
                                            .ie h1 {
                                                font-size: 26px !important;
                                                line-height: 34px !important
                                            }

                                            .mso h2,
                                            .ie h2 {}

                                            .mso h2,
                                            .ie h2 {
                                                font-size: 20px !important;
                                                line-height: 28px !important
                                            }

                                            .mso h3,
                                            .ie h3 {}

                                            .mso .layout__inner,
                                            .ie .layout__inner {}

                                            .mso .footer__share-button p {}

                                            .mso .footer__share-button p {
                                                font-family: Cabin, Avenir, sans-serif
                                            }
                                        </style>
                                        <meta name="robots" content="noindex,nofollow">
                                        </meta>
                                        <meta property="og:title" content="Mail v.01">
                                        </meta>
                                    </head>

                                    <body
                                        style="width:100%;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0;">
                                        <div class="es-wrapper-color" style="background-color:#F6F6F6;">
                                            <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;">
                                                <tr style="border-collapse:collapse;">
                                                    <td class="st-br" valign="top" style="padding:0;Margin:0;">
                                                        <table cellpadding="0" cellspacing="0" class="es-header" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-color:transparent;background-position:center bottom;background-repeat:no-repeat;"
                                                                    bgcolor="transparent">
                                                                    <div>
                                                                        <table bgcolor="transparent" class="es-header-body" align="center" cellpadding="0"
                                                                            cellspacing="0" width="600"
                                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                            <tr style="border-collapse:collapse;">
                                                                                <td align="left"
                                                                                    style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px;">
                                                                                    <table cellpadding="0" cellspacing="0" width="100%"
                                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                        <tr style="border-collapse:collapse;">
                                                                                            <td width="560" align="center" valign="top"
                                                                                                style="padding:0;Margin:0;">
                                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" style="padding:0;Margin:0;">
                                                                                                            <a target="_blank" href="https://digitalcv.id"
                                                                                                                style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;font-size:14px;text-decoration:underline;color:#1376C8;">
                                                                                                                <img src="https://digitalcv.id/assets/images/logo/logoDcv2.png"
                                                                                                                    alt
                                                                                                                    style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;width:126px;height:131px;"
                                                                                                                    height="131">
                                                                                                            </a>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                    <tr style="border-collapse:collapse;">
                                                                                                        <td align="center" height="42"
                                                                                                            style="padding:0;Margin:0;"></td>
                                                                                                    </tr>
                                                                                                </table>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-content" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;">
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center" bgcolor="transparent"
                                                                    style="padding:0;Margin:0;background-color:transparent;">
                                                                    <table bgcolor="transparent" class="es-content-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-bottom:15px;padding-top:30px;padding-left:30px;padding-right:30px;border-radius:10px 10px 0px 0px;background-color:#FFFFFF;background-position:left bottom;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-position:left bottom;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="left" style="padding:0;Margin:0;">
                                                                                                    </td>
                                                                                                </tr>
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center"
                                                                                                        style="padding:0;Margin:0;padding-top:20px;">
                                                                                                        <p
                                                                                                            style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;">
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:30px;padding-right:30px;border-radius:0px 0px 10px 10px;background-position:left top;background-color:#FFFFFF;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" style="padding:0;Margin:0;">
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Hai, ' . $namaKandidat . '
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Lamaran Anda untuk posisi <b style="color:#fbb116">' . $posisiLamar . '</b> sudah berhasil dikirimkan ke perusahaan <b style="color:#fbb116">' . $namaPerusahaan . '</b>. Setiap perusahaan memiliki serangkaian prosesnya sendiri, jadi mungkin perusahaan akan memberikan tanggapan atau tidak kepada Anda.
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Ikuti dan pantau terus pekerjaan yang telah dilamar serta cari dan temukan lebih banyak lagi peluang pekerjaan serupa.
                                                                                                        </p>
                                                                                                        <br>
                                                                                                        <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-size:16px;font-family:roboto, helvetica neue, helvetica, arial, sans-serif;line-height:24px;color:#131313;text-align: justify;">
                                                                                                            Recruitment Team
                                                                                                            <br>
                                                                                                            <b style="color:#fbb116">' . $namaPerusahaan . '</b>
                                                                                                        </p>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-footer" align="center"
                                                            style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:#F6F6F6;background-repeat:repeat;background-position:center top;">

                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center"
                                                                    style="padding:0;Margin:0;background-image:url(https://ultrajaya.digitalcv.id/style/images/93961578369120938.png);background-position:left bottom;background-repeat:no-repeat;"
                                                                    background="https://ultrajaya.digitalcv.id/style/images/93961578369120938.png">
                                                                    <table bgcolor="#31cb4b" class="es-footer-body" align="center" cellpadding="0"
                                                                        cellspacing="0" width="600"
                                                                        style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;">
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left"
                                                                                style="Margin:0;padding-top:20px;padding-bottom:20px;padding-left:30px;padding-right:30px;background-position:left top;background-color:#FFFFFF;border-radius:10px 10px 0px 0px;"
                                                                                bgcolor="#ffffff">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="540" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="left" style="padding:0;Margin:0;">
                                                                                                        <hr>
                                                                                                        <div>digitalcv<br>
                                                                                                            Jalan Sarijadi Raya No. 62 Sarijadi Bandung Jawa
                                                                                                            Barat<br>
                                                                                                            Telepon: 022-32097159<br>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse;">
                                                                            <td align="left" style="padding:0;Margin:0;background-position:left top;">
                                                                                <table cellpadding="0" cellspacing="0" width="100%"
                                                                                    style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                    <tr style="border-collapse:collapse;">
                                                                                        <td width="600" align="center" valign="top"
                                                                                            style="padding:0;Margin:0;">
                                                                                            <table cellpadding="0" cellspacing="0" width="100%"
                                                                                                style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;">
                                                                                                <tr style="border-collapse:collapse;">
                                                                                                    <td align="center" height="40"
                                                                                                        style="padding:0;Margin:0;"></td>
                                                                                                </tr>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </body>
                                    </table>
                                    <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;"
                                        src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                                    </body>

                                    </html>'],
            ],
            'Subject' => ['Data' => 'Lamaran Posisi ' . $posisiLamar . ' di ' . $namaPerusahaan . '| digitalcv'],
        ],
        'Source' => 'digitalcv <<EMAIL>>',
    ];

    try {
        $result = $SesClient->sendEmail($emailParams);
        return true;
    } catch (AwsException $e) {
        return false;
    }
}

$data = array();

$status = "gagal";
$message = "Proses lamaran gagal dilakukan.";
$created_at = date("Y-m-d H:i:s");

try {
    // Mulai proses
    $conn->begin_transaction();

    // cek apakah user terdaftar atau tidak
    $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows == 0) {
        throw new Exception("Proses lamaran gagal dilakukan. Silakan untuk login kembali.");
    }

    $row = $result->fetch_assoc();
    $nama_pelamar = $row['nama_lengkap'];

    $id_req = "-";
    $lokasi = "-";
    $pertanyaan_khusus = "";
    $where_lokasi = "";

    // cek parameter dengan validasi yang ketat
    if (isset($_POST['id_req']) && isset($_POST['lokasi'])) {
        // Validasi id_req
        $id_req = validateAndSanitizeInput($_POST['id_req'], 'id');
        if (!$id_req) {
            throw new Exception("Parameter id_req tidak valid.");
        }

        // Validasi lokasi
        $lokasi_raw = validateAndSanitizeInput($_POST['lokasi'], 'location');
        if (!$lokasi_raw) {
            throw new Exception("Parameter lokasi tidak valid.");
        }
        $lokasi = strtoupper(str_replace("KAB. ", "KABUPATEN ", $lokasi_raw));
        $where_lokasi = "%" . $lokasi . "%";

        // Validasi pertanyaan khusus jika ada
        if (isset($_POST['pertanyaan_khusus'])) {
            $pertanyaan_validated = validateAndSanitizeInput($_POST['pertanyaan_khusus'], 'array');
            if ($pertanyaan_validated && !empty($pertanyaan_validated)) {
                $pertanyaan_khusus = implode("|", $pertanyaan_validated);
            }
        }
    } else {
        throw new Exception("Parameter yang diperlukan tidak lengkap.");
    }

    // cek apakah lowongan ada
    $get = $conn->prepare("SELECT id_koordinator, perusahaan, posisi, `status` FROM list_request WHERE id_req = ? AND lokasi_kerja LIKE ?");
    $get->bind_param("ss", $id_req, $where_lokasi);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    $id_koordinator = "-";
    $perusahaan = "-";
    $posisi = "-";
    if ($result->num_rows > 0) {
        $rowData = mysqli_fetch_array($result);
        if ($rowData['status'] == 'On Proccess') {
            $id_koordinator = $rowData['id_koordinator'];
            $perusahaan = $rowData['perusahaan'];
            $posisi = $rowData['posisi'];
        } else {
            throw new Exception("Lowongan sudah tidak tersedia.");
        }
    } else {
        throw new Exception("Proses lamaran gagal dilakukan.");
    }

    // cek apakah user sudah melakukan proses lamaran pada lowongan
    $get = $conn->prepare("SELECT id_koordinator FROM users_lamar WHERE id_gestalt = ? AND id_req = ? AND lokasi = ?");
    $get->bind_param("sss", $pin, $id_req, $lokasi);
    echo $get->error;
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $status = "exist";
        throw new Exception("Anda sudah melamar lowongan ini.");
    }

    // Generate id lamar
    $sqlCek = "SELECT id_lamar FROM users_lamar";
    $queryCek = $conn->query($sqlCek);

    do {
        $id_lamar = getIDLamar();
    } while ($id_lamar == $queryCek);

    // insert proses lamar
    $insert = $conn->prepare("INSERT INTO `users_lamar`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`, `lokasi`, `jawaban_k_khusus`) 
        VALUES (?, ?, ?, ?, 'On Proccess Digitalcv', ?, ?, ?)");
    $insert->bind_param("sssssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at, $lokasi, $pertanyaan_khusus);

    if ($insert->execute()) {
        $insert->close();
    } else {
        throw new Exception("Proses lamaran gagal dilakukan. Silakan hubungi administrator");
    }

    // insert riwayat lamar
    $insert = $conn->prepare("INSERT INTO `users_lamar_history`(`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
        VALUES (?, ?, ?, ?, 'Lamaran Dikirim', ?)");
    $insert->bind_param("sssss", $id_lamar, $pin, $id_req, $id_koordinator, $created_at);

    if ($insert->execute()) {
        $insert->close();
    } else {
        throw new Exception("Proses lamaran gagal dilakukan. Silakan hubungi administrator");
    }

    // lakukan proses screening
    $hasil_screening = ProsesScreening($conn, $pin, $id_req);
    if ($hasil_screening) {
        if (canSendEmail($conn, $email_user, 'Apply Lamaran')) {
            if (kirim_apply_lamaran($email_user, $posisi, $perusahaan, $nama_pelamar, $SesClient)) {
                // simpan log aktivitas
                $messages = 'Melamar lowongan di ' . $perusahaan . ' dengan posisi ' . $posisi . ' [' . $id_req . '].';
                $extra_info = "Kandidat";
                $level = "INFO";
                logActivity($conn, $pin, $level, $messages, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = "Proses lamaran berhasil dilakukan.";

                $dataPayload = [
                    'tipe' => 'ajukan',
                    'id_req' => $id_req,
                    'id_lamar' => $id_lamar,
                    'posisi' => $posisi,
                    'perusahaan' => $perusahaan,
                    'nama_pelamar' => $nama_pelamar,
                    'pin' => $pin,
                ];

                // kirim notifikasi
                $pesan_notif = $nama_pelamar . " melamar pekerjaan diposisi " . $posisi . ".";
                $id_referensi = base64_encode($id_req);
                $kirim_notif = $conn->prepare("INSERT INTO `notif_summary`(`pengirim`, `penerima`, `judul`, `isi`, `id_referensi`, `status`, `link`, `create_at`) VALUES (?, ?, 'Melamar Pekerjaan', ?, ?, 'Dikirim', '', ?)");
                $kirim_notif->bind_param("sssss", $pin, $id_koordinator, $pesan_notif, $id_referensi, $created_at);
                if ($kirim_notif->execute()) {
                    $kirim_notif->close();

                    $dataPayload = [
                        'tipe' => 'ajukan',
                        'id_req' => $id_req,
                        'id_lamar' => $id_lamar,
                        'posisi' => $posisi,
                        'perusahaan' => $perusahaan,
                        'nama_pelamar' => $nama_pelamar,
                        'pin' => $pin,
                    ];

                    try {
                        $result = sendNotifikasiFromCandidate($id_koordinator, "Lamar Pekerjaan", $nama_pelamar . " melamar pekerjaan Anda untuk posisi " . $posisi . ".", $dataPayload, $conn);
                    } catch (Exception $e) {
                        $message = $message . " " . $e->getMessage();
                    }

                    $apiKeyWS = '57pOr213C&^XM701%(*U4';
                    $urlWS = $baseURL . 'api/WebSocket/send.php';

                    $dataWS = [
                        'to' => urlencode($id_koordinator),
                        'message' => $pesan_notif
                    ];

                    $optionsWS = [
                        'http' => [
                            'method'  => 'POST',
                            'header'  => "Content-type: application/x-www-form-urlencoded\r\nX-Api-Key: $apiKeyWS\r\n",
                            'content' => http_build_query($dataWS)
                        ],
                        'ssl' => [
                            'verify_peer' => false,
                            'verify_peer_name' => false
                        ]
                    ];

                    $contextWS = stream_context_create($optionsWS);
                    file_get_contents($urlWS, false, $contextWS);
                }

                $context = stream_context_create([
                    "ssl" => [
                        "verify_peer" => false,
                        "verify_peer_name" => false,
                    ]
                ]);
                $url_notif = $baseURL . "api/WebSocket/send.php?to=" . urlencode($id_koordinator) . "&message=" . urlencode($pesan_notif);
                file_get_contents($url_notif, false, $context);

                $status = "success";
                $message = translate('Proses lamaran berhasil dilakukan.');
            } else {
                throw new Exception(translate('Proses lamaran gagal dilakukan. Silakan hubungi administrator'));
            }
        } else {
            http_response_code(429);
            $status = "error";
            $message = "Terlalu banyak permintaan. Silakan coba lagi nanti.";
            $data['status'] = $status;
            $data['message'] = $message;
            $data['data'] = [];
            echo json_encode($data, JSON_PRETTY_PRINT);
            exit();
        }
    } else {
        throw new Exception("Proses lamaran gagal dilakukan. Silakan hubungi administrator");
    }
} catch (Exception $e) {
    // Jika ada error, rollback proses
    $conn->rollback();

    $message = $e->getMessage();
}

$data['status'] = $status;
$data['message'] = $message;

$output = json_encode($data, JSON_PRETTY_PRINT);
echo $output;

$conn->close();
exit;
