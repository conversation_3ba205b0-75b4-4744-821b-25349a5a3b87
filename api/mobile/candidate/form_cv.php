<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Verifikasi access token dan dapatkan data user
$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama_lengkap ?? "-";
$email_user = $userData->email ?? "-";

// Validasi parameter func
$func = $_GET['func'] ?? '';
if (empty($func)) {
    $data['status'] = "gagal";
    $data['message'] = "Parameter func tidak valid.";
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit();
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}


if ($func == 'getProgresPengisian') {
    $data = array();

    // get progress pengisian data diri
    $sql = $conn->prepare("SELECT
                            ROUND(((p_1 + p_2 + p_3 + p_4 + p_5 + p_6 + p_7) / 7) * 100) as persentase
                        FROM
                        (
                        SELECT
                            IF(ktp = '',0,1) as p_1,
                            IF(pendidikan_terakhir = '',0,1) as p_2,
                            IF(kursus = '',0,1) as p_3,
                            IF(pengalaman_kerja = '',0,1) as p_4,
                            IF(minat_gaji = '',0,1) as p_5,
                            IF(kelebihan = '',0,1) as p_6,
                            IF(organisasi = '',0,1) as p_7
                        FROM
                            `rh`
                        WHERE
                            id = ?
                        ) h");
    $sql->bind_param("s", $pin);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    $persentase_progres = 0;
    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        if ($row['persentase'] > 100) {
            $persentase_progres = 100;
        } else {
            $persentase_progres = $row['persentase'];
        }
    }

    $status = "success";
    $value = $persentase_progres;

    $data['status'] = $status;
    $data['value'] = $value;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanIdentitasDiri') {
    $data = array();

    $status = "gagal";
    $message = "Identitas diri gagal disimpan.";

    try {
        // Validasi input dengan ketat
        $nama = $_POST['nama'] ?? '';
        $tempat_lahir = $_POST['tempat_lahir'] ?? '';
        $tgl_lahir = $_POST['tgl_lahir'] ?? '';
        $jk = $_POST['jk'] ?? '';
        $status_pernikahan = $_POST['status_pernikahan'] ?? '';
        $ktp = $_POST['ktp'] ?? '';
        $no_telepon = $_POST['no_telepon'] ?? '';
        $email = $_POST['email'] ?? '';

        // Validasi array SIM
        $sim_array = $_POST['sim'] ?? [];
        $sim = is_array($sim_array) ? implode(",", $sim_array) : '';

        $propinsi = $_POST['provinsi'] ?? '';
        $kota_tinggal = $_POST['kota_tinggal'] ?? '';
        $kec_tinggal = $_POST['kec_tinggal'] ?? '';
        $rt_tinggal = $_POST['rt_tinggal'] ?? '';
        $rw_tinggal = $_POST['rw_tinggal'] ?? '';
        $alamat_tinggal = $_POST['alamat_tinggal'] ?? '';
        $pos_tinggal = $_POST['pos_tinggal'] ?? '';

        // Validasi field yang wajib diisi
        if (empty($nama)) {
            throw new Exception("Nama harus diisi.");
        }
        if (empty($tempat_lahir)) {
            throw new Exception("Tempat lahir harus diisi.");
        }
        if (empty($tgl_lahir)) {
            throw new Exception("Tanggal lahir harus diisi.");
        }
        if (empty($email)) {
            throw new Exception("Email harus diisi.");
        }

        $created_at = date("Y-m-d H:i:s");

        try {
            // Mulai proses
            $conn->begin_transaction();

            // get data provinsi
            $get = $conn->prepare("SELECT `name` as nama, 'provinsi' as ket FROM `provinces` WHERE id = ?
                            UNION
                            SELECT `name` as nama, 'kota' as ket FROM `regencies` WHERE id = ?
                            UNION
                            SELECT `name` as nama, 'kecamatan' as ket FROM `districts` WHERE id = ?");
            $get->bind_param("sss", $propinsi, $kota_tinggal, $kec_tinggal);
            $get->execute();
            $result = $get->get_result();
            $get->close();

            // $propinsi = "-";
            // $kota_tinggal = "-";
            // $kec_tinggal = "-";

            while ($row = mysqli_fetch_array($result)) {
                if ($row['ket'] == 'provinsi') {
                    $propinsi = $row['nama'];
                } elseif ($row['ket'] == 'kota') {
                    $kota_tinggal = $row['nama'];
                } elseif ($row['ket'] == 'kecamatan') {
                    $kec_tinggal = $row['nama'];
                }
            }

            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data identitas diri
                $update = $conn->prepare("UPDATE `rh` SET `nama` = ?, `tempat_lahir` = ?, `tgl_lahir` = ?, `jenis_kelamin` = ?, `status_pernikahan` = ?, `ktp` = ?, `no_telepon` = ?, `email` = ?, `sim` = ?, `provinsi` = ?, `kota` = ?, `kecamatan` = ?, `rt` = ?, `rw` = ?, `alamat` = ?, `kode_pos` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("ssssssssssssssssss", $nama, $tempat_lahir, $tgl_lahir, $jk, $status_pernikahan, $ktp, $no_telepon, $email, $sim, $propinsi, $kota_tinggal, $kec_tinggal, $rt_tinggal, $rw_tinggal, $alamat_tinggal, $pos_tinggal, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan update data identitas diri.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = "Data identitas diri berhasil disimpan.";
                } else {
                    throw new Exception("Data identitas diri gagal disimpan. Silakan hubungi administrator");
                }
            } else {
                // insert data identitas diri
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `nama`, `tempat_lahir`, `tgl_lahir`, `jenis_kelamin`, `status_pernikahan`, `ktp`, `no_telepon`, `email`, `sim`, `provinsi`, `kota`, `kecamatan`, `rt`, `rw`, `alamat`, `kode_pos`, `created_at`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("ssssssssssssssssss", $pin, $nama, $tempat_lahir, $tgl_lahir, $jk, $status_pernikahan, $ktp, $no_telepon, $email, $sim, $propinsi, $kota_tinggal, $kec_tinggal, $rt_tinggal, $rw_tinggal, $alamat_tinggal, $pos_tinggal, $created_at);

                if ($insert->execute()) {
                    $insert->close();

                    // simpan log aktivitas
                    $messages = 'Melakukan input data identitas diri.';
                    $extra_info = "Kandidat";
                    $level = "INFO";
                    logActivity($conn, $pin, $level, $messages, $extra_info);

                    // Jika semua query berhasil, commit transaksi
                    $conn->commit();

                    $status = "success";
                    $message = "Data identitas diri berhasil disimpan.";
                } else {
                    throw new Exception("Data identitas diri gagal disimpan. Silakan hubungi administrator");
                }
            }
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = "gagal";
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } catch (Exception $e) {
        $data['status'] = "gagal";
        $data['message'] = $e->getMessage();
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit();
    }
}

if ($func == 'simpanRiwayatPendidikan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pendidikan gagal disimpan.";

    try {
        // Validasi input dengan ketat
        $pendidikan_terakhir = $_POST['pendidikan_tinggi'] ?? '';

        if ($pendidikan_terakhir == 'S1' || $pendidikan_terakhir == 'S2' || $pendidikan_terakhir == 'S3') {
            $diploma = $_POST['cek_diploma'] ?? '';
        } elseif ($pendidikan_terakhir == 'Diploma') {
            $diploma = "Ya";
        } else {
            $diploma = "Tidak";
        }

        $jenjang_pendidikan = $_POST['jenjang_pendidikan'] ?? '';
        $nama_sekolah_pendidikan = $_POST['nama_sekolah_pendidikan'] ?? '';
        $jurusan_pendidikan = $_POST['jurusan_pendidikan'] ?? '';
        $tahun_mulai_pendidikan = $_POST['tahun_mulai_pendidikan'] ?? '';
        $tahun_selesai_pendidikan = $_POST['tahun_selesai_pendidikan'] ?? '';
        $ket_pendidikan = $_POST['ket_pendidikan'] ?? '';

        // Validasi field yang wajib diisi
        if (empty($jenjang_pendidikan)) {
            throw new Exception("Jenjang pendidikan harus diisi.");
        }
        if (empty($nama_sekolah_pendidikan)) {
            throw new Exception("Nama sekolah harus diisi.");
        }

        $created_at = date("Y-m-d H:i:s");

        try {
            // Mulai proses
            $conn->begin_transaction();

            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
            }

            // cek jika data kosong
            if ($jenjang_pendidikan == "" && $nama_sekolah_pendidikan == "" && $jurusan_pendidikan == "" && $tahun_mulai_pendidikan == "" && $tahun_selesai_pendidikan == "" && $ket_pendidikan == "") {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pendidikan
                $update = $conn->prepare("UPDATE `rh` SET `pendidikan_terakhir` = ?, `diploma` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("ssss",  $pendidikan_terakhir, $diploma, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception("Riwayat pendidikan gagal disimpan. Silakan hubungi administrator");
                }
            } else {
                // insert data pendidikan
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pendidikan_terakhir`, `diploma`, `created_at`) 
            VALUES (?, ?, ?, ?)");
                $insert->bind_param("ssss", $pin, $pendidikan_terakhir, $diploma, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Riwayat pendidikan gagal disimpan. Silakan hubungi administrator");
                }
            }

            $temp_jenjang = array();
            // simpan detail riwayat pendidikan
            // cek apakah jenjang pendidikan sudah ada
            $sql = $conn->prepare("SELECT id FROM riwayat_pendidikan WHERE id = ? AND jenjang = ?");
            $sql->bind_param("ss", $pin, $jenjang_pendidikan);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            // insert detail riwayat pendidikan
            $insert = $conn->prepare("INSERT INTO `riwayat_pendidikan`(`id`, `jenjang`, `nama_sekolah`, `tahun_mulai`, `tahun_selesai`, `jurusan`, `ket`, `created_at`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("ssssssss", $pin, $jenjang_pendidikan, $nama_sekolah_pendidikan, $tahun_mulai_pendidikan, $tahun_selesai_pendidikan, $jurusan_pendidikan, $ket_pendidikan, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pendidikan gagal disimpan. Silakan hubungi administrator");
            }


            // simpan log aktivitas
            $messages = 'Melakukan input riwyat pendidikan.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Riwayat pendidikan berhasil disimpan.";
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = "gagal";
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } catch (Exception $e) {
        $data['status'] = "gagal";
        $data['message'] = $e->getMessage();
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit();
    }
}

if ($func == 'updateRiwayatPendidikan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pendidikan gagal diperbarui.";

    $pendidikan_terakhir = $_POST['pendidikan_tinggi'];
    if ($pendidikan_terakhir == 'S1' || $pendidikan_terakhir == 'S2' || $pendidikan_terakhir == 'S3') {
        $diploma = $_POST['cek_diploma'];
    } elseif ($pendidikan_terakhir == 'Diploma') {
        $diploma = "Ya";
    } else {
        $diploma = "Tidak";
    }
    $id_riwayat = $_POST['id_riwayat'];
    $jenjang_pendidikan = $_POST['jenjang_pendidikan'];
    $nama_sekolah_pendidikan = $_POST['nama_sekolah_pendidikan'];
    $jurusan_pendidikan = $_POST['jurusan_pendidikan'];
    $tahun_mulai_pendidikan = $_POST['tahun_mulai_pendidikan'];
    $tahun_selesai_pendidikan = $_POST['tahun_selesai_pendidikan'];
    $ket_pendidikan = $_POST['ket_pendidikan'];

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat mengubah data. Silakan untuk login kembali.");
        }

        // cek jika data kosong
        if ($jenjang_pendidikan == "" && $nama_sekolah_pendidikan == "" && $jurusan_pendidikan == "" && $tahun_mulai_pendidikan == "" && $tahun_selesai_pendidikan == "" && $ket_pendidikan == "") {
            throw new Exception("Tidak dapat mengubah data. Silakan hubungi administrator.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data pendidikan
            $update = $conn->prepare("UPDATE `rh` SET `pendidikan_terakhir` = ?, `diploma` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("ssss",  $pendidikan_terakhir, $diploma, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat pendidikan gagal diubah. Silakan hubungi administrator");
            }
        } else {
            // insert data pendidikan
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pendidikan_terakhir`, `diploma`, `created_at`) 
            VALUES (?, ?, ?, ?)");
            $insert->bind_param("ssss", $pin, $pendidikan_terakhir, $diploma, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pendidikan gagal diubah. Silakan hubungi administrator");
            }
        }

        // update detail riwayat pendidikan
        $update = $conn->prepare("UPDATE `riwayat_pendidikan` SET `nama_sekolah` = ?, `tahun_mulai` = ?, `tahun_selesai` = ?, `jurusan` = ?, `ket` = ?, `updated_at` = ? WHERE `id` = ? AND no = ?");
        $update->bind_param("ssssssss",  $nama_sekolah_pendidikan, $tahun_mulai_pendidikan, $tahun_selesai_pendidikan, $jurusan_pendidikan, $ket_pendidikan, $created_at, $pin, $id_riwayat);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Riwayat pendidikan gagal diubah. Silakan hubungi administrator");
        }



        // simpan log aktivitas
        $messages = 'Melakukan update riwayat pendidikan.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pendidikan berhasil diubah.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'deleteRiwayatPendidikan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pendidikan gagal diperbarui.";

    $id_riwayat = $_POST['id_riwayat'];

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menghapus data. Silakan untuk login kembali.");
        }

        // update detail riwayat pendidikan
        $update = $conn->prepare("DELETE FROM `riwayat_pendidikan`  WHERE `id` = ? AND no = ?");
        $update->bind_param("ss",  $pin, $id_riwayat);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Riwayat pendidikan gagal dihapus. Silakan hubungi administrator");
        }

        $sql = $conn->prepare("SELECT id FROM riwayat_pendidikan WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows < 1) {
            // update data pendidikan
            $update = $conn->prepare("UPDATE `rh` SET `pendidikan_terakhir` = '', `diploma` = '', `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("ss", $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat pendidikan gagal dihapus. Silakan hubungi administrator");
            }
        }

        // simpan log aktivitas
        $messages = 'Melakukan delete riwayat pendidikan.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pendidikan berhasil dihapus.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatKursus') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pelatihan/kursus gagal disimpan.";

    try {
        // Validasi input dengan ketat
        $kursus = $_POST['kursus'] ?? '';

        // Validasi field yang wajib diisi
        if (empty($kursus)) {
            throw new Exception("Data kursus harus diisi.");
        }

        $created_at = date("Y-m-d H:i:s");

        try {
            // Mulai proses
            $conn->begin_transaction();

            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pelatihan/kursus
                $update = $conn->prepare("UPDATE `rh` SET `kursus` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sss",  $kursus, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception("Riwayat pelatihan/kursus gagal disimpan. Silakan hubungi administrator");
                }
            } else {
                // insert data pelatihan/kursus
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `kursus`, `created_at`) 
            VALUES (?, ?, ?)");
                $insert->bind_param("sss", $pin, $kursus, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Riwayat pelatihan/kursus gagal disimpan. Silakan hubungi administrator");
                }
            }

            // cek keterangan kursus
            if ($kursus == 'Ya') {
                $nama_kursus = $_POST['nama_kursus'];
                $sertifikat_kursus = $_POST['sertifikat_kursus'];
                $tempat_kursus = $_POST['tempat_kursus'];
                $tgl_mulai_kursus = $_POST['tgl_mulai_kursus'];
                $tgl_selesai_kursus = $_POST['tgl_selesai_kursus'];

                // cek jika data kosong
                if ($nama_kursus == "" && $sertifikat_kursus == "" && $tempat_kursus == "" && $tgl_mulai_kursus == "" && $tgl_selesai_kursus == "") {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }

                // insert detail riwayat pendidikan
                $insert = $conn->prepare("INSERT INTO `riwayat_kursus`(`id`, `nama`, `tempat`, `sertifikat`, `tgl_mulai`, `tgl_selesai`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssss", $pin, $nama_kursus, $tempat_kursus, $sertifikat_kursus, $tgl_mulai_kursus, $tgl_selesai_kursus, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Riwayat pelatihan/kursus gagal disimpan. Silakan hubungi administrator");
                }
                // end simpan detail riwayat pelatihan/kursus
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwayat pelatihan/kursus.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Riwayat pelatihan/kursus berhasil disimpan.";
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = "gagal";
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } catch (Exception $e) {
        $data['status'] = "gagal";
        $data['message'] = $e->getMessage();
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit();
    }
}

if ($func == 'updateRiwayatKursus') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pelatihan/kursus gagal disimpan.";

    $kursus = $_POST['kursus'];

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat mengubah data. Silakan untuk login kembali.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data pelatihan/kursus
            $update = $conn->prepare("UPDATE `rh` SET `kursus` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sss",  $kursus, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat pelatihan/kursus gagal diubah. Silakan hubungi administrator");
            }
        } else {
            // insert data pelatihan/kursus
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `kursus`, `created_at`) 
            VALUES (?, ?, ?)");
            $insert->bind_param("sss", $pin, $kursus, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pelatihan/kursus gagal diubah. Silakan hubungi administrator");
            }
        }

        // cek keterangan kursus
        if ($kursus == 'Ya') {
            $id_kursus = $_POST['id_kursus'];
            $nama_kursus = $_POST['nama_kursus'];
            $sertifikat_kursus = $_POST['sertifikat_kursus'];
            $tempat_kursus = $_POST['tempat_kursus'];
            $tgl_mulai_kursus = $_POST['tgl_mulai_kursus'];
            $tgl_selesai_kursus = $_POST['tgl_selesai_kursus'];

            $created_at = date('Y-m-d H:i:s'); // Misalnya
            // $pin = $_POST['pin']; // Misalnya user id login

            // cek jika data kosong
            if (
                $nama_kursus == "" &&
                $sertifikat_kursus == "" &&
                $tempat_kursus == "" &&
                $tgl_mulai_kursus == "" &&
                $tgl_selesai_kursus == ""
            ) {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }

            // QUERY diperbaiki: penulisan kolom dan parameter
            $insert = $conn->prepare("UPDATE `riwayat_kursus` 
                                            SET `nama` = ?, `tempat` = ?, `sertifikat` = ?, `tgl_mulai` = ?, `tgl_selesai` = ?, `created_at` = ? 
                                            WHERE `id` = ? AND `no` = ?");
            $insert->bind_param("ssssssss", $nama_kursus, $tempat_kursus, $sertifikat_kursus, $tgl_mulai_kursus, $tgl_selesai_kursus, $created_at, $pin, $id_kursus);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pelatihan/kursus gagal diubah. Silakan hubungi administrator");
            }
            // end simpan detail riwayat pelatihan/kursus
        }

        // simpan log aktivitas
        $messages = 'Melakukan input riwayat pelatihan/kursus.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pelatihan/kursus berhasil disimpan.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'deleteRiwayatKursus') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat kursus gagal diperbarui.";

    $id_kursus = $_POST['id_kursus'];

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menghapus data. Silakan untuk login kembali.");
        }

        // update detail riwayat pendidikan
        $update = $conn->prepare("DELETE FROM `riwayat_kursus`  WHERE `id` = ? AND `no` = ?");
        $update->bind_param("ss",  $pin, $id_kursus);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Riwayat kursus gagal dihapus. Silakan hubungi administrator");
        }

        $sql = $conn->prepare("SELECT id FROM riwayat_kursus WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows < 1) {
            // update data pendidikan
            $update = $conn->prepare("UPDATE `rh` SET `kursus` = '', `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("ss", $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat kursus gagal dihapus. Silakan hubungi administrator");
            }
        }

        // simpan log aktivitas
        $messages = 'Melakukan delete riwayat kursus.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pendidikan berhasil dihapus.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanRiwayatPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pekerjaan gagal disimpan.";

    try {
        // Validasi input dengan ketat
        $pengalaman_kerja = $_POST['pengalaman_kerja'] ?? '';
        $total_pengalaman_kerja = $_POST['total_pengalaman_kerja'] ?? '';
        $pengalaman_posisi_sama = $_POST['pengalaman_posisi_sama'] ?? '';

        if ($pengalaman_kerja != "Ya") {
            $total_pengalaman_kerja = "";
            $pengalaman_posisi_sama = "";
            $nama = "";
            $jabatan = "";
            $status = "";
            $gaji = "";
            $tahun_mulai = "";
            $tahun_selesai = "";
            $alasan = "";
        } else {
            // Validasi data pekerjaan jika ada pengalaman kerja
            $nama = $_POST['nama'] ?? '';
            $jabatan = $_POST['jabatan'] ?? '';
            $status = $_POST['status'] ?? '';
            $gaji = $_POST['gaji'] ?? '';
            $tahun_mulai = $_POST['tahun_mulai'] ?? '';
            $tahun_selesai = $_POST['tahun_selesai'] ?? '';
            $alasan = $_POST['alasan'] ?? '';
        }

        $created_at = date("Y-m-d H:i:s");

        try {
            // Mulai proses
            $conn->begin_transaction();

            // cek apakah user terdaftar atau tidak
            $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows == 0) {
                throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
            }

            // cek apakah data rh sudah ada atau tidak
            $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // update data pekerjaan
                $update = $conn->prepare("UPDATE `rh` SET `pengalaman_kerja` = ?, `lama_pengalaman_kerja` = ?, `lama_posisi_kerja` = ?, `updated_at` = ? WHERE `id` = ?");
                $update->bind_param("sssss",  $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception("Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator");
                }
            } else {
                // insert data pekerjaan
                $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pengalaman_kerja`, `lama_pengalaman_kerja`, `lama_posisi_kerja`, `created_at`) 
            VALUES (?, ?, ?, ?, ?)");
                $insert->bind_param("sssss", $pin, $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator");
                }
            }

            $nama = "";
            if (isset($_POST['nama'])) {
                $nama = $_POST['nama'];
            }

            // cek keterangan pengalaman kerja
            if ($pengalaman_kerja == 'Ya' && $nama != "") {
                $jabatan = $_POST['jabatan'];
                $status = $_POST['status'];
                $gaji = $_POST['gaji'];
                $tahun_mulai = $_POST['tahun_mulai'];
                $tahun_selesai = $_POST['tahun_selesai'];
                $alasan = $_POST['alasan'];

                // cek jika data kosong
                if ($nama == "" && $jabatan == "" && $status == "" && $gaji == "" && $tahun_mulai == "" && $tahun_selesai == "" && $alasan == "") {
                    throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
                }

                // insert detail riwayat pekerjaan
                $insert = $conn->prepare("INSERT INTO `riwayat_pekerjaan`(`id`, `nama_perusahaan`, `jabatan`, `status_kerja`, `gaji`, `tahun_mulai`, `tahun_selesai`, `alasan_berhenti`, `created_at`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssssss", $pin, $nama, $jabatan, $status, $gaji, $tahun_mulai, $tahun_selesai, $alasan, $created_at);

                if ($insert->execute()) {
                    $insert->close();
                } else {
                    throw new Exception("Riwayat pekerjaan gagal disimpan. Silakan hubungi administrator");
                }
                // end simpan detail riwayat pekerjaan
            }

            // simpan log aktivitas
            $messages = 'Melakukan input riwayat pekerjaan.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Riwayat pekerjaan berhasil disimpan.";
        } catch (Exception $e) {
            // Jika ada error, rollback proses
            $conn->rollback();

            $status = "gagal";
            $message = $e->getMessage();
        }

        $data['status'] = $status;
        $data['message'] = $message;

        $output = json_encode($data, JSON_PRETTY_PRINT);
        echo $output;
    } catch (Exception $e) {
        $data['status'] = "gagal";
        $data['message'] = $e->getMessage();
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit();
    }
}

if ($func == 'updateRiwayatPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pekerjaan gagal diubah.";

    $pengalaman_kerja = $_POST['pengalaman_kerja'];
    $total_pengalaman_kerja = $_POST['total_pengalaman_kerja'];
    $pengalaman_posisi_sama = isset($_POST['pengalaman_posisi_sama']) ? $_POST['pengalaman_posisi_sama'] : "";

    if ($pengalaman_kerja != "Ya") {
        $total_pengalaman_kerja = "";
        $pengalaman_posisi_sama = "";
        $nama = "";
        $jabatan = "";
        $status = "";
        $gaji = "";
        $tahun_mulai = "";
        $tahun_selesai = "";
        $alasan = "";
    }

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat mengubah data. Silakan untuk login kembali.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data pekerjaan
            $update = $conn->prepare("UPDATE `rh` SET `pengalaman_kerja` = ?, `lama_pengalaman_kerja` = ?, `lama_posisi_kerja` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sssss",  $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat pekerjaan gagal diubah. Silakan hubungi administrator");
            }
        } else {
            // insert data pekerjaan
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `pengalaman_kerja`, `lama_pengalaman_kerja`, `lama_posisi_kerja`, `created_at`) 
            VALUES (?, ?, ?, ?, ?)");
            $insert->bind_param("sssss", $pin, $pengalaman_kerja, $total_pengalaman_kerja, $pengalaman_posisi_sama, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pekerjaan gagal diubah. Silakan hubungi administrator");
            }
        }

        $nama = "";
        if (isset($_POST['nama'])) {
            $nama = $_POST['nama'];
        }

        // cek keterangan pengalaman kerja
        if ($pengalaman_kerja == 'Ya' && $nama != "") {
            $id_pekerjaan = $_POST['id_pekerjaan'];
            $jabatan = $_POST['jabatan'];
            $status = $_POST['status'];
            $gaji = $_POST['gaji'];
            $tahun_mulai = $_POST['tahun_mulai'];
            $tahun_selesai = $_POST['tahun_selesai'];
            $alasan = $_POST['alasan'];

            // cek jika data kosong
            if ($nama == "" && $jabatan == "" && $status == "" && $gaji == "" && $tahun_mulai == "" && $tahun_selesai == "" && $alasan == "") {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }

            // insert detail riwayat pekerjaan
            $insert = $conn->prepare("UPDATE `riwayat_pekerjaan` SET `nama_perusahaan` = ?, `jabatan` = ?, `status_kerja` = ?, `gaji` = ?, `tahun_mulai` = ?, `tahun_selesai` = ?, `alasan_berhenti` = ?, `created_at`  = ? WHERE `id` = ? AND `no` = ? ");

            $insert->bind_param("ssssssssss",  $nama, $jabatan, $status, $gaji, $tahun_mulai, $tahun_selesai, $alasan, $created_at, $pin, $id_pekerjaan);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat pekerjaan gagal diubah. Silakan hubungi administrator");
            }
            // end simpan detail riwayat pekerjaan
        }

        // simpan log aktivitas
        $messages = 'Melakukan update riwayat pekerjaan.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pekerjaan berhasil diubah.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'deleteRiwayatPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pekerjaan gagal diperbarui.";

    $id_riwayat = $_POST['id_pekerjaan'];
    $created_at = date("Y-m-d H:i:s");
    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menghapus data. Silakan untuk login kembali.");
        }

        // update detail riwayat pendidikan
        $update = $conn->prepare("DELETE FROM `riwayat_pekerjaan`  WHERE `id` = ? AND `no` = ?");
        $update->bind_param("ss",  $pin, $id_riwayat);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Riwayat pekerjaan gagal dihapus. Silakan hubungi administrator");
        }

        $sql = $conn->prepare("SELECT id FROM riwayat_pekerjaan WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows < 1) {
            // update data pendidikan
            $update = $conn->prepare("UPDATE `rh` SET `pengalaman_kerja` = '', `lama_pengalaman_kerja` = '', `lama_posisi_kerja` = '', `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("ss", $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat pekerjaan gagal dihapus. Silakan hubungi administrator");
            }
        }

        // simpan log aktivitas
        $messages = 'Melakukan delete riwayat pekerjaan.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pekerjaan berhasil dihapus.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'simpanInformasiPekerjaan') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat pekerjaan gagal disimpan.";

    $perjalanan_dinas = $_POST['perjalanan_dinas'];
    $minat_lokasi_kerja = $_POST['minat_lokasi_kerja'];
    $temp_gaji = $_POST['temp_gaji'];

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data informasi pekerjaan
            $update = $conn->prepare("UPDATE `rh` SET `perjalanan_dinas` = ?, `minat_lokasi_kerja` = ?, `minat_gaji` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sssss",  $perjalanan_dinas, $minat_lokasi_kerja, $temp_gaji, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat informasi pekerjaan gagal disimpan. Silakan hubungi administrator");
            }
        } else {
            // insert data informasi pekerjaan
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `perjalanan_dinas`, `minat_lokasi_kerja`, `minat_gaji`, `created_at`) 
            VALUES (?, ?, ?, ?, ?)");
            $insert->bind_param("sssss", $pin, $perjalanan_dinas, $minat_lokasi_kerja, $temp_gaji, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Riwayat informasi pekerjaan gagal disimpan. Silakan hubungi administrator");
            }
        }

        // simpan log aktivitas
        $messages = 'Melakukan input informasi pekerjaan.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat informasi pekerjaan berhasil disimpan.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

// if ($func == 'simpanMinatKonsep') {
//     $data = array();

//     $status = "gagal";
//     $message = "Minat dan konsep pribadi gagal disimpan.";

//     $penguasaan_bhs = $_POST['penguasaan_bhs'];
//     if (isset($_POST['bahasa']) && isset($_POST['membaca_bhs']) && isset($_POST['menulis_bhs']) && isset($_POST['mendengar_bhs']) && isset($_POST['berbicara_bhs'])) {
//         $bahasa = $_POST['bahasa'];
//         $membaca_bhs = $_POST['membaca_bhs'];
//         $menulis_bhs = $_POST['menulis_bhs'];
//         $mendengar_bhs = $_POST['mendengar_bhs'];
//         $berbicara_bhs = $_POST['berbicara_bhs'];
//     } else {
//         $bahasa = array();
//         $membaca_bhs = array();
//         $menulis_bhs = array();
//         $mendengar_bhs = array();
//         $berbicara_bhs = array();
//     }
//     $kelebihan = implode(",", $_POST['kelebihan']);
//     $kekurangan = implode(",", $_POST['kekurangan']);
//     $kk = implode(",", $_POST['kk']);
//     $pimpin_tim = $_POST['pimpin_tim'];
//     $kemampuan_persentasi = $_POST['kemampuan_persentasi'];
//     $rlp = implode(",", $_POST['rlp']);

//     $created_at = date("Y-m-d H:i:s");

//     try {
//         // Mulai proses
//         $conn->begin_transaction();

//         // cek apakah user terdaftar atau tidak
//         $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
//         $sql->bind_param("s", $pin);
//         $sql->execute();
//         $result = $sql->get_result();
//         $sql->close();

//         if ($result->num_rows == 0) {
//             throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
//         }

//         // hapus data penguasaan bahasa yang sudah ada
//         $del = $conn->prepare("DELETE FROM `penguasaan_bahasa` WHERE id = ?");
//         $del->bind_param("s", $pin);
//         if ($del->execute()) {
//             $del->close();
//         } else {
//             throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
//         }

//         // cek keterangan penguasaan bahasa
//         if ($penguasaan_bhs == 'Ya' && count($bahasa) > 0) {
//             // cek jika data kosong
//             if (count($bahasa) == 0 && count($membaca_bhs) == 0 && count($menulis_bhs) == 0 && count($mendengar_bhs) == 0 && count($berbicara_bhs) == 0) {
//                 throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
//             }

//             // simpan detail penguasaan bahasa
//             for ($i = 0; $i < count($bahasa); $i++) {
//                 $temp_bahasa = $bahasa[$i];
//                 $temp_membaca_bhs = $membaca_bhs[$i];
//                 $temp_menulis_bhs = $menulis_bhs[$i];
//                 $temp_mendengar_bhs = $mendengar_bhs[$i];
//                 $temp_berbicara_bhs = $berbicara_bhs[$i];

//                 // insert detail penguasaan bahasa
//                 $insert = $conn->prepare("INSERT INTO `penguasaan_bahasa`(`id`, `bahasa`, `membaca`, `menulis`, `mendengar`, `berbicara`, `created_at`) 
//                 VALUES (?, ?, ?, ?, ?, ?, ?)");
//                 $insert->bind_param("sssssss", $pin, $temp_bahasa, $temp_membaca_bhs, $temp_menulis_bhs, $temp_mendengar_bhs, $temp_berbicara_bhs, $created_at);

//                 if ($insert->execute()) {
//                     $insert->close();
//                 } else {
//                     throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
//                 }
//             }
//             // end simpan detail penguasaan bahasa
//         }

//         // cek apakah data rh sudah ada atau tidak
//         $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
//         $sql->bind_param("s", $pin);
//         $sql->execute();
//         $result = $sql->get_result();
//         $sql->close();

//         if ($result->num_rows > 0) {
//             // update data minat dan konsep pribadi
//             $update = $conn->prepare("UPDATE `rh` SET `bahasa_asing` = ?, `kelebihan` = ?, `kekurangan` = ?, `ilmu_komputerisasi` = ?, `memimpin_tim` = ?, `kemampuan_presentasi` = ?, `lingkup_pekerjaan` = ?, `updated_at` = ? WHERE `id` = ?");
//             $update->bind_param("sssssssss",  $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at, $pin);

//             if ($update->execute()) {
//                 $update->close();
//             } else {
//                 throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
//             }
//         } else {
//             // insert data minat dan konsep pribadi
//             $insert = $conn->prepare("INSERT INTO `rh`(`id`, `bahasa_asing`, `kelebihan`, `kekurangan`, `ilmu_komputerisasi`, `memimpin_tim`, `kemampuan_presentasi`, `lingkup_pekerjaan`, `created_at`) 
//             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
//             $insert->bind_param("sssssssss", $pin, $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at);

//             if ($insert->execute()) {
//                 $insert->close();
//             } else {
//                 throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
//             }
//         }

//         // simpan log aktivitas
//         $messages = 'Melakukan input minat dan konsep.';
//         $extra_info = "Kandidat";
//         $level = "INFO";
//         logActivity($conn, $pin, $level, $messages, $extra_info);

//         // Jika semua query berhasil, commit transaksi
//         $conn->commit();

//         $status = "success";
//         $message = "Minat dan konsep pribadi berhasil disimpan.";
//     } catch (Exception $e) {
//         // Jika ada error, rollback proses
//         $conn->rollback();

//         $status = "gagal";
//         $message = $e->getMessage();
//     }

//     $data['status'] = $status;
//     $data['message'] = $message;

//     $output = json_encode($data, JSON_PRETTY_PRINT);
//     echo $output;
// }

if ($func == 'simpanMinatKonsep') {
    $data = array();
    $status = "gagal";
    $message = "Minat dan konsep pribadi gagal disimpan.";

    // Ambil input JSON
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    // $pin = isset($input['pin']) ? $input['pin'] : '';

    $penguasaan_bhs = isset($input['penguasaan_bahasa']) ? $input['penguasaan_bahasa'] : 'Tidak';
    $bahasa_asing = isset($input['bahasa_asing']) ? $input['bahasa_asing'] : [];

    $kelebihan = isset($input['kelebihan']) ? $input['kelebihan'] : '';
    $kekurangan = isset($input['kekurangan']) ? $input['kekurangan'] : '';
    $kk = isset($input['komputerisasi']) ? $input['komputerisasi'] : '';
    $pimpin_tim = isset($input['memimpin_tim']) ? $input['memimpin_tim'] : '';
    $kemampuan_persentasi = isset($input['presentasi']) ? $input['presentasi'] : '';
    $rlp = isset($input['rlp']) ? $input['rlp'] : '';

    $created_at = date("Y-m-d H:i:s");

    try {
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // hapus data penguasaan bahasa yang sudah ada
        $del = $conn->prepare("DELETE FROM `penguasaan_bahasa` WHERE id = ?");
        $del->bind_param("s", $pin);
        if (!$del->execute()) {
            throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
        }
        $del->close();

        // simpan detail penguasaan bahasa
        if ($penguasaan_bhs == 'Ya' && count($bahasa_asing) > 0) {
            foreach ($bahasa_asing as $item) {
                $temp_bahasa = isset($item['bahasa']) ? $item['bahasa'] : '';
                $temp_membaca_bhs = isset($item['membaca']) ? $item['membaca'] : '';
                $temp_menulis_bhs = isset($item['menulis']) ? $item['menulis'] : '';
                $temp_mendengar_bhs = isset($item['mendengar']) ? $item['mendengar'] : '';
                $temp_berbicara_bhs = isset($item['berbicara']) ? $item['berbicara'] : '';

                $insert = $conn->prepare("INSERT INTO `penguasaan_bahasa`(`id`, `bahasa`, `membaca`, `menulis`, `mendengar`, `berbicara`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?, ?)");
                $insert->bind_param("sssssss", $pin, $temp_bahasa, $temp_membaca_bhs, $temp_menulis_bhs, $temp_mendengar_bhs, $temp_berbicara_bhs, $created_at);

                if (!$insert->execute()) {
                    throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
                }
                $insert->close();
            }
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data minat dan konsep pribadi
            $update = $conn->prepare("UPDATE `rh` SET `bahasa_asing` = ?, `kelebihan` = ?, `kekurangan` = ?, `ilmu_komputerisasi` = ?, `memimpin_tim` = ?, `kemampuan_presentasi` = ?, `lingkup_pekerjaan` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sssssssss",  $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at, $pin);

            if (!$update->execute()) {
                throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
            }
            $update->close();
        } else {
            // insert data minat dan konsep pribadi
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `bahasa_asing`, `kelebihan`, `kekurangan`, `ilmu_komputerisasi`, `memimpin_tim`, `kemampuan_presentasi`, `lingkup_pekerjaan`, `created_at`) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssssssss", $pin, $penguasaan_bhs, $kelebihan, $kekurangan, $kk, $pimpin_tim, $kemampuan_persentasi, $rlp, $created_at);

            if (!$insert->execute()) {
                throw new Exception("Minat dan konsep pribadi gagal disimpan. Silakan hubungi administrator");
            }
            $insert->close();
        }

        // simpan log aktivitas
        $messages = 'Melakukan input minat dan konsep.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Commit jika semua berhasil
        $conn->commit();

        $status = "success";
        $message = "Minat dan konsep pribadi berhasil disimpan.";
    } catch (Exception $e) {
        $conn->rollback();
        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    echo json_encode($data, JSON_PRETTY_PRINT);
}


if ($func == 'simpanRiwayatOrganisasi') {
    $data = array();

    $status = "gagal";
    $message = "Pengalaman rganisasi gagal disimpan.";

    $organisasi = $_POST['organisasi'];

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data organisasi
            $update = $conn->prepare("UPDATE `rh` SET `organisasi` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sss",  $organisasi, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Pengalaman Organisasi gagal disimpan. Silakan hubungi administrator");
            }
        } else {
            // insert data organisasi
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `organisasi`, `created_at`) 
            VALUES (?, ?, ?)");
            $insert->bind_param("sss", $pin, $organisasi, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Pengalaman Organisasi gagal disimpan. Silakan hubungi administrator");
            }
        }

        $nama = "";
        if (isset($_POST['nama'])) {
            $nama = $_POST['nama'];
        }

        // cek keterangan kursus
        if ($organisasi == 'Ya' && $nama != "") {
            $nama = $_POST['nama'];
            $jabatan = $_POST['jabatan'];
            $tahun = $_POST['tahun'];
            $tempat = $_POST['tempat'];

            // cek jika data kosong
            if ($nama == "" && $jabatan == "" && $tahun == "" && $tempat == "") {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }

            // insert detail riwayat organisasi
            $insert = $conn->prepare("INSERT INTO `riwayat_organisasi`(`id`, `nama`, `jabatan`, `tempat`, `tahun`, `created_at`) 
                VALUES (?, ?, ?, ?, ?, ?)");
            $insert->bind_param("ssssss", $pin, $nama, $jabatan, $tempat, $tahun, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Pengalaman organisasi gagal disimpan. Silakan hubungi administrator");
            }
            // end simpan detail riwayat organisasi
        }

        // simpan log aktivitas
        $messages = 'Melakukan input riwayat organisasi.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Pengalaman organisasi berhasil disimpan.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateRiwayatOrganisasi') {
    $data = array();

    $status = "gagal";
    $message = "Pengalaman rganisasi gagal disimpan.";

    $organisasi = $_POST['organisasi'];

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat mengubah data. Silakan untuk login kembali.");
        }

        // cek apakah data rh sudah ada atau tidak
        $sql = $conn->prepare("SELECT id FROM rh WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // update data organisasi
            $update = $conn->prepare("UPDATE `rh` SET `organisasi` = ?, `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("sss",  $organisasi, $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Pengalaman Organisasi gagal diubah. Silakan hubungi administrator");
            }
        } else {
            // insert data organisasi
            $insert = $conn->prepare("INSERT INTO `rh`(`id`, `organisasi`, `created_at`) 
            VALUES (?, ?, ?)");
            $insert->bind_param("sss", $pin, $organisasi, $created_at);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Pengalaman Organisasi gagal diubah. Silakan hubungi administrator");
            }
        }

        $nama = "";
        if (isset($_POST['nama'])) {
            $nama = $_POST['nama'];
        }

        // cek keterangan kursus
        if ($organisasi == 'Ya' && $nama != "") {
            $id_organisasi = $_POST['id_organisasi'];
            $nama = $_POST['nama'];
            $jabatan = $_POST['jabatan'];
            $tahun = $_POST['tahun'];
            $tempat = $_POST['tempat'];

            // cek jika data kosong
            if ($nama == "" && $jabatan == "" && $tahun == "" && $tempat == "") {
                throw new Exception("Tidak dapat mengubah data. Silakan hubungi administrator.");
            }

            // insert detail riwayat organisasi
            $insert = $conn->prepare("UPDATE `riwayat_organisasi` SET `nama` = ?, `jabatan` = ?, `tempat` = ?, `tahun` = ?, `created_at` = ? WHERE `id` = ? AND `no` = ? ");
            $insert->bind_param("sssssss", $nama, $jabatan, $tempat, $tahun, $created_at, $pin, $id_organisasi);

            if ($insert->execute()) {
                $insert->close();
            } else {
                throw new Exception("Pengalaman organisasi gagal diubah. Silakan hubungi administrator");
            }
            // end simpan detail riwayat organisasi
        }

        // simpan log aktivitas
        $messages = 'Melakukan update riwayat organisasi.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Pengalaman organisasi berhasil diubah.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'deleteRiwayatOrganisasi') {
    $data = array();

    $status = "gagal";
    $message = "Riwayat organisasi gagal diperbarui.";

    $id_riwayat = $_POST['id_organisasi'];

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT pin FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menghapus data. Silakan untuk login kembali.");
        }

        // update detail riwayat pendidikan
        $update = $conn->prepare("DELETE FROM `riwayat_organisasi`  WHERE `id` = ? AND `no` = ?");
        $update->bind_param("ss",  $pin, $id_riwayat);

        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Riwayat organisasi gagal dihapus. Silakan hubungi administrator");
        }

        $sql = $conn->prepare("SELECT id FROM riwayat_organisasi WHERE id = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows < 1) {
            // update data pendidikan
            $update = $conn->prepare("UPDATE `rh` SET `organisasi` = '', `updated_at` = ? WHERE `id` = ?");
            $update->bind_param("ss", $created_at, $pin);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Riwayat organisasi gagal dihapus. Silakan hubungi administrator");
            }
        }

        // simpan log aktivitas
        $messages = 'Melakukan delete riwayat organisasi.';
        $extra_info = "Kandidat";
        $level = "INFO";
        logActivity($conn, $pin, $level, $messages, $extra_info);

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Riwayat pekerjaan berhasil dihapus.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

$conn->close();
exit;
