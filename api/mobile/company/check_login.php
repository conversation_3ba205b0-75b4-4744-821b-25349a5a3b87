<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../jwt_helper.php';

$userData = requireAuth();

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

if (!function_exists('random_bytes')) {
    function random_bytes($length = 36)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $characters_length = strlen($characters);
        $output = '';
        for ($i = 0; $i < $length; $i++)
            $output .= $characters[rand(0, $characters_length - 1)];
        return $output;
    }
}

$email = $userData->email ?? '';
$id_koordinator = $userData->id_koordinator ?? '';
$id_pic = $userData->id ?? '';

$status = false;
$message = "Check login gagal.";
$link = "beranda/index";

$response['data'] = [];
try {
    // Cek email user
    $sql = $conn->prepare("SELECT pic.*, k.label, k.paket  
                           FROM koordinator_pic pic 
                           JOIN koordinator k ON pic.id_koordinator = k.id_koordinator 
                           WHERE pic.email = ? AND pic.id_koordinator = ? AND pic.id_pic = ?");
    $sql->bind_param("sss", $email, $id_koordinator, $id_pic);
    $sql->execute();
    $result = $sql->get_result();
    $sql->close();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        $storedHash = $row['password'];
        $id_pic = $row['id_pic'];
        $id_koordinator = $row['id_koordinator'];
        $divisi = $row['posisi'];
        $nama = $row['nama'];
        $img = $row['img'];
        $label = $row['label'];
        $role = $row['role'];
        $paket = $row['paket'];
        $noHp = $row['no_hp'];
        $session_token = $row['refresh_token'];

        $fitur_ids = explode("|", $row['fitur']);
        $fitur_user = [];

        if (!empty($fitur_ids)) {
            // Buat string ID untuk digunakan dalam query SQL (pakai prepared statement lebih aman)
            // Tapi karena array dinamis, kita akan bangun query secara dinamis
            $placeholders = implode(',', array_fill(0, count($fitur_ids), '?'));
            $types = str_repeat('s', count($fitur_ids)); // tipe parameter

            $stmt = $conn->prepare("SELECT sub_menu FROM list_fitur WHERE id_fitur IN ($placeholders)");
            $stmt->bind_param($types, ...$fitur_ids);
            $stmt->execute();
            $resultFitur = $stmt->get_result();

            while ($rowFitur = $resultFitur->fetch_assoc()) {
                $fitur_user[] = strtolower($rowFitur['sub_menu']);
            }

            $stmt->close();
        }

        $response['data'][] = array(
            'id' => $id_pic,
            'id_koordinator' => $id_koordinator,
            'divisi' => $divisi,
            'nama' => $nama,
            'email' => $email,
            'img' => $img,
            'label' => $label,
            'tipe' => 'company',
            'role' => $role,
            'paket' => $paket,
            'no_hp' => $noHp,
            'fitur_user' => $fitur_user,
            'token' => $session_token,
        );


        $status = true;
        $message = "Check login berhasil.";
    } else {
        throw new Exception("Email tidak terdaftar.");
    }
} catch (Exception $e) {
    $status = false;
    $message = $e->getMessage();
}

$response = [
    'status' => $status,
    'message' => $message,
    'data' => $response['data'],
];

echo json_encode($response, JSON_PRETTY_PRINT);
$conn->close();
exit();
