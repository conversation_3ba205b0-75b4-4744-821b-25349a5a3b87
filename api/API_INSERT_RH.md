# 📘 API Documentation

## 🔑 Insert RH

Semua request harus menggunakan header berikut:

| Key          | Value                          | Required |
| ------------ | ------------------------------ | -------- |
| Content-Type | application/json               | ✅       |
| x-api-key    | xiAheLul2MLQb4MZKs0MLrG5FcKOtg | ✅       |

---

## 📑 Methods

Allow method hanya POST

---

## 📍 Endpoint

https://digitalcv.id/api/insertRH

---

## 📝 Parameter

Berikut parameter yang tersedia:
email => Email. tipe string required
nama => Nama Lengkap. tipe string required
tempat_lahir => Tempat lahir. tipe string required
tgl_lahir => Tanggal lahir. tipe string required
jk => Jenis kelamin. tipe string required
status_pernikahan => Status Pernikahan. tipe string required
no_telepon => Nomor telepon. tipe string required
sim => Surat Ijin Mengemudi. tipe array required
propinsi => Propinsi tempat tinggal sekarang. tipe string required
kota_tinggal => Kota/Kab tempat tinggal sekarang. tipe string required
kec_tinggal => Kecamatan tempat tinggal sekarang. tipe string required
rt_tinggal => Nomor rukun tetangga tempat tinggal sekarang. tipe string required
rw_tinggal => Nomor rukun warga tempat tinggal sekarang. tipe string required
alamat_tinggal => Alamat tempat tinggal sekarang. tipe string required
pos_tinggal => Kode pos tempat tinggal sekarang. tipe string required
cek_diploma => Informasi jika pendidikan melewati diploma atau tidak. tipe string required
pendidikan_tinggi => Pendidikan. tipe string required
jenjang_pendidikan => Jenjang riwayat pendidikan. tipe array required
nama_sekolah_pendidikan => Nama sekolah riwayat pendidikan. tipe array required
jurusan_pendidikan => Jurusan riwayat pendidikan. tipe array required
tahun_mulai_pendidikan => Tahun mulai riwayat pendidikan. tipe array required
tahun_selesai_pendidikan => Tahun selesai riwayat pendidikan. tipe array required
ket_pendidikan => Keterangan riwayat pendidikan. tipe array required
kursus => Informasi apakah kandidat memiliki kursus/pelatihan. tipe string required
nama_kursus => Nama riwayat kursus/pelatihan. tipe array required
sertifikat_kursus => Sertifikat riwayat kursus/pelatihan. tipe array required
tempat_kursus => Tempat riwayat kursus/pelatihan. tipe array required
tgl_mulai_kursus => Tanggal mulai riwayat kursus/pelatihan. tipe array required
tgl_selesai_kursus => Tanggal selesai riwayat kursus/pelatihan. tipe array required
pengalaman_kerja => Informasi apakah kandidat memiliki pengalaman kerja. tipe string required
total_pengalaman_kerja => Informasi total pengalaman pengalaman kerja. tipe number required
rp_nama_perusahaan => Nama perusahaan riwayat pengalaman kerja. tipe array required
rp_jabatan => Jabatan riwayat pengalaman kerja. tipe array required
rp_status => Status riwayat pengalaman kerja. tipe array required
rp_gaji => Gaji riwayat pengalaman kerja. tipe array required
rp_tahun_mulai => Tahun mulai riwayat pengalaman kerja. tipe array required
rp_tahun_selesai => Tahun selesai riwayat pengalaman kerja. tipe array required
rp_alasan => Alasan berhenti riwayat pengalaman kerja. tipe array required
perjalanan_dinas => Informasi apakah kandidat bersedia melakukan perjalanan dinas. tipe string required
minat_lokasi_kerja => Informasi minat lokasi kerja kandidat. tipe string required
minat_gaji => Informasi gaji yang di-inginkan kandidat. tipe string required
penguasaan_bhs => Informasi apakah kandidat memiliki kemampuan bahasa asing. tipe string required
bahasa => Bahasa yang dikuasai. tipe array required
membaca_bhs => Kemampuan membaca dari bahasa yang dikuasai. tipe array required
menulis_bhs => Kemampuan menulis dari bahasa yang dikuasai. tipe array required
mendengar_bhs => Kemampuan mendengar dari bahasa yang dikuasai. tipe array required
berbicara_bhs => Kemampuan berbicara dari bahasa yang dikuasai. tipe array required
kelebihan => Kelebihan yang dimiliki kandidat. tipe array required
kekurangan => Kekurangan yang dimiliki kandidat. tipe array required
kk => Kemampuan komputerisasi yang dimiliki kandidat. tipe number required
pimpin_tim => Kemampuan kepemimpinan yang dimiliki kandidat. tipe number required
kemampuan_persentasi => Kemampuan berbicara dihadapan umum yang dimiliki kandidat. tipe number required
rlp => Ruang lingkup pekerjaan yang disukai kandidat. tipe array required
organisasi => Informasi apakah kandidat memiliki riwayat mengikuti organisasi. tipe string required
po_nama => Nama organisasi yang di-ikuti. tipe array required
po_jabatan => Jabatan organisasi yang di-ikuti. tipe array required
po_tahun => Tahun mengikuti organisasi. tipe array required
po_tempat => Lokasi organisasi yang di-ikuti. tipe array required

---

#### Body (JSON)

```json
{
  "email": "<EMAIL>",
  "nama": "Jhon Doe",
  "tempat_lahir": "Kota Bandung",
  "tgl_lahir": "1970-01-01",
  "jk": "Laki-Laki",
  "status_pernikahan": "Belum Menikah",
  "no_telepon": "08123456789",
  "sim": ["A", "C"],
  "propinsi": "Jawa Barat",
  "kota_tinggal": "Kota Bandung",
  "kec_tinggal": "Sarijadi",
  "rt_tinggal": "03",
  "rw_tinggal": "11",
  "alamat_tinggal": "Jl. Sarijadi",
  "pos_tinggal": "14045",
  "cek_diploma": "Tidak",
  "pendidikan_tinggi": "S1",
  "jenjang_pendidikan": ["SD", "SMP", "SMA", "S1"],
  "nama_sekolah_pendidikan": [
    "SDN CONTOH",
    "SMPN CONTOH",
    "SMK CONTOH",
    "UNIVERSITAS CONTOH"
  ],
  "jurusan_pendidikan": ["", "", "Teknik Otomotif", "Teknik Informatika"],
  "tahun_mulai_pendidikan": ["2002", "2008", "2011", "2014"],
  "tahun_selesai_pendidikan": ["2008", "2011", "2014", "2018"],
  "ket_pendidikan": [
    "Lulus Berijazah",
    "Lulus Berijazah",
    "Lulus Berijazah",
    "Lulus Berijazah"
  ],
  "kursus": "Ya",
  "nama_kursus": ["Kursus 1", "Kursus 2"],
  "sertifikat_kursus": ["Ada", "Tidak Ada"],
  "tempat_kursus": ["Kota Bandung", "Kota Bandung"],
  "tgl_mulai_kursus": ["2018-01-01", "2018-02-01"],
  "tgl_selesai_kursus": ["2018-03-01", "2018-04-01"],
  "pengalaman_kerja": "Ya",
  "total_pengalaman_kerja": "7",
  "rp_nama_perusahaan": ["PT. Perusahaan 1", "PT. Perusahaan 2"],
  "rp_jabatan": ["Staff 1", "Staff 2"],
  "rp_status": ["(Magang)", "(Full Time)"],
  "rp_gaji": ["500.000", "5.000.000"],
  "rp_tahun_mulai": ["2017-06-01", "2019-02-01"],
  "rp_tahun_selesai": ["2017-11-31", "2025-08-31"],
  "rp_alasan": ["Alasan 1", "Alasan 2"],
  "perjalanan_dinas": "Ya",
  "minat_lokasi_kerja": "Kota Bandung",
  "minat_gaji": "12.000.000",
  "penguasaan_bhs": "Ya",
  "bahasa": ["Inggris", "Arabic"],
  "membaca_bhs": ["B", "C"],
  "menulis_bhs": ["B", "C"],
  "mendengar_bhs": ["C", "C"],
  "berbicara_bhs": ["C", "K"],
  "kelebihan": ["Kelebihan 1", "Kelebihan 2"],
  "kekurangan": ["Kekurangan 1", "Kekurangan 2"],
  "kk": "4",
  "pimpin_tim": "2",
  "kemampuan_persentasi": "2",
  "rlp": ["1", "2", "3", "4"],
  "organisasi": "Ya",
  "po_nama": ["Organisasi 1", "Organisasi 2"],
  "po_jabatan": ["Anggota 1", "Anggota 2"],
  "po_tahun": ["2018", "2019"],
  "po_tempat": ["Kota Bandung", "Kota Bandung"]
}
```

---

## ⚡ Example Request for PHP

$url = "https://digitalcv.id/api/insertRH";

$data = [
"email" => "<EMAIL>",
"nama" => "Jhon Doe",
"tempat_lahir" => "Kota Bandung",
"tgl_lahir" => "1970-01-01",
"jk" => "Laki-Laki",
"status_pernikahan" => "Belum Menikah",
"no_telepon" => "08123456789",
"sim" => ["A", "C"],
"propinsi" => "Jawa Barat",
"kota_tinggal" => "Kota Bandung",
"kec_tinggal" => "Sarijadi",
"rt_tinggal" => "03",
"rw_tinggal" => "11",
"alamat_tinggal" => "Jl. Sarijadi",
"pos_tinggal" => "14045",
"cek_diploma" => "Tidak",
"pendidikan_tinggi" => "S1",
"jenjang_pendidikan" => ["SD", "SMP", "SMA", "S1"],
"nama_sekolah_pendidikan" => ["SDN CONTOH", "SMPN CONTOH", "SMK CONTOH", "UNIVERSITAS CONTOH"],
"jurusan_pendidikan" => ["", "", "Teknik Otomotif", "Teknik Informatika"],
"tahun_mulai_pendidikan" => ["2002", "2008", "2011", "2014"],
"tahun_selesai_pendidikan" => ["2008", "2011", "2014", "2018"],
"ket_pendidikan" => ["Lulus Berijazah", "Lulus Berijazah", "Lulus Berijazah", "Lulus Berijazah"],
"kursus" => "Ya",
"nama_kursus" => ["Kursus 1", "Kursus 2"],
"sertifikat_kursus" => ["Ada", "Tidak Ada"],
"tempat_kursus" => ["Kota Bandung", "Kota Bandung"],
"tgl_mulai_kursus" => ["2018-01-01", "2018-02-01"],
"tgl_selesai_kursus" => ["2018-03-01", "2018-04-01"],
"pengalaman_kerja" => "Ya",
"total_pengalaman_kerja" => "7",
"rp_nama_perusahaan" => ["PT. Perusahaan 1", "PT. Perusahaan 2"],
"rp_jabatan" => ["Staff 1", "Staff 2"],
"rp_status" => ["(Magang)", "(Full Time)"],
"rp_gaji" => ["500.000", "5.000.000"],
"rp_tahun_mulai" => ["2017-06-01", "2019-02-01"],
"rp_tahun_selesai" => ["2017-11-31", "2025-08-31"],
"rp_alasan" => ["Alasan 1", "Alasan 2"],
"perjalanan_dinas" => "Ya",
"minat_lokasi_kerja" => "Kota Bandung",
"minat_gaji" => "12.000.000",
"penguasaan_bhs" => "Ya",
"bahasa" => ["Inggris", "Arabic"],
"membaca_bhs" => ["B", "C"],
"menulis_bhs" => ["B", "C"],
"mendengar_bhs" => ["C", "C"],
"berbicara_bhs" => ["C", "K"],
"kelebihan" => ["Kelebihan 1", "Kelebihan 2"],
"kekurangan" => ["Kekurangan 1", "Kekurangan 2"],
"kk" => "4",
"pimpin_tim" => "2",
"kemampuan_persentasi" => "2",
"rlp" => ["1", "2", "3", "4"],
"organisasi" => "Ya",
"po_nama" => ["Organisasi 1", "Organisasi 2"],
"po_jabatan" => ["Anggota 1", "Anggota 2"],
"po_tahun" => ["2018", "2019"],
"po_tempat" => ["Kota Bandung", "Kota Bandung"]
];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
"Content-Type: application/json",
"x-api-key: your-api-key"
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

echo $response;

---

## Response

✅ Success (201)
{
"status":"success",
"message":"Personal data has been successfully saved."
}

❌ Error (500)
{
"status":"error",
"message":"Database connection failed: ..."
}
Deskripsi: Error koneksi database

❌ Error (405)
{
"status":"error",
"message":"Method not allowed. Only POST is permitted."
}
Deskripsi: Metode yang di-ijinkan hanya POST

❌ Error (403)
{
"status":"error",
"message":"Unregistered candidate."
}
Deskripsi: Kandidat tidak terdaftar

{
"status":"error",
"message":"Personal data is already available."
}
Deskripsi: Data RH sudah ada

❌ Error (401)
{
"status":"error",
"message":"Unauthorized: Invalid API Key."
}
Deskripsi: x-api-key tidak valid

❌ Error (400)
{
"status":"error",
"message":"Missing required parameters.",
"missing" => [...]
}
Deskripsi: Parameter tidak lengkap. Pada element "missing" diperlihatkan parameter yang tidak ada

{
"status":"error",
"message":"Invalid email."
}
Deskripsi: Value email tidak valid. Email harus mengandung '@'

{
"status":"error",
"message":"Invalid phone number."
}
Deskripsi: Nomor telepon tidak valid. Jika pada nomor terdapat huruf maka error ini akan muncul

{
"status":"error",
"message":"Invalid date of birth format."
}
Deskripsi: Format tanggal harus Y-m-d

{
"status":"error",
"message":"Invalid gender."
}
Deskripsi: Value jenis kelamin harus 'Laki-Laki' atau 'Perempuan'

{
"status":"error",
"message":"Invalid gender."
}
Deskripsi: Value jenis kelamin harus 'Laki-Laki' atau 'Perempuan'

{
"status":"error",
"message":"Invalid postal code."
}
Deskripsi: Kode pos tidak valid. Jika pada kode pos terdapat huruf maka error ini akan muncul

{
"status":"error",
"message":"Personal identity data failed to be saved."
}
Deskripsi: Data diri gagal disimpan

{
"status":"error",
"message":"The value 'cek_diploma' is not valid."
}
Deskripsi: Value cek_diploma harus 'Ya' atau 'Tidak'

{
"status":"error",
"message":"Education history failed to save."
}
Deskripsi: Data riwayat pendidikan gagal disimpan

{
"status":"error",
"message":"The education history field cannot be empty."
}
Deskripsi: Data riwayat pendidikan tidak boleh kosong

{
"status":"error",
"message":"The start date or end date of education is invalid."
}
Deskripsi: Format tahun mulai dan tahun selesai riwayat pendidikan harus Y-m-d

{
"status":"error",
"message":"The start date of the education cannot be later than the end date."
}
Deskripsi: Tahun mulai riwayat pendidikan tidak boleh lebih besar dari tahun selesai

{
"status":"error",
"message":"Education history failed to save."
}
Deskripsi: Riwayat pendidikan gagal disimpan

{
"status":"error",
"message":"The value 'kursus' is not valid."
}
Deskripsi: Value kursus harus 'Ya' atau 'Tidak'

{
"status":"error",
"message":"Course/training history failed to save."
}
Deskripsi: Riwayat kursus/pelatihan gagal disimpan

{
"status":"error",
"message":"Course/training history data cannot be empty."
}
Deskripsi: Data riwayat kursus/pelatihan tidak boleh kosong

{
"status":"error",
"message":"The start date or end date of the course are invalid."
}
Deskripsi: Format tahun mulai dan tahun selesai riwayat kursus/pelatihan harus Y-m-d

{
"status":"error",
"message":"The start date of the course cannot be later than the end date."
}
Deskripsi: Tahun mulai riwayat kursus/pelatihan tidak boleh lebih besar dari tahun selesai

{
"status":"error",
"message":"The value 'pengalaman_kerja' is not valid."
}
Deskripsi: Value pengalaman_kerja harus 'Ya', 'Tidak' atau 'Fresh Graduate'

{
"status":"error",
"message":"Length of work experience is invalid."
}
Deskripsi: Lama pengalaman tidak valid. Value harus angka

{
"status":"error",
"message":"Work experience failed to save."
}
Deskripsi: Pengalaman kerja gagal disimpan

{
"status":"error",
"message":"Work history data cannot be left blank."
}
Deskripsi: Data riwayat pengalaman kerja tidak boleh kosong

{
"status":"error",
"message":"Work history data cannot be left blank."
}
Deskripsi: Data riwayat pengalaman kerja tidak boleh kosong

{
"status":"error",
"message":"The value 'rp_tahun_mulai' is not valid."
}
Deskripsi: Format tahun mulai riwayat pengalaman kerja harus Y-m atau Y-m-d

{
"status":"error",
"message":"The value 'rp_tahun_selesai' is not valid."
}
Deskripsi: Format tahun selesai riwayat pengalaman kerja harus Y-m atau Y-m-d

{
"status":"error",
"message":"The start date of the work experience cannot be later than the end date."
}
Deskripsi: Tahun mulai riwayat pengalaman kerja tidak boleh lebih besar dari tahun selesai

{
"status":"error",
"message":"Salary history does not match."
}
Deskripsi: Riwayat gaji pengalaman kerja tidak sesuai. Gaji harus lebih sama dengan 0

{
"status":"error",
"message":"The value 'perjalanan_dinas' is not valid."
}
Deskripsi: Value perjalanan_dinas harus 'Ya' atau 'Tidak'

{
"status":"error",
"message":"The value 'minat_gaji' is not valid."
}
Deskripsi: Gaji yang di-inginkan tidak sesuai. Gaji harus lebih sama dengan 0

{
"status":"error",
"message":"Job information failed to be saved."
}
Deskripsi: Informasi pekerjaan gagal disimpan

{
"status":"error",
"message":"The value 'penguasaan_bhs' is not valid."
}
Deskripsi: Value penguasaan_bhs harus 'Ya' atau 'Tidak'

{
"status":"error",
"message":"The value 'kk' is not valid."
}
Deskripsi: Value kk harus berupa angka

{
"status":"error",
"message":"The value 'pimpin_tim' is not valid."
}
Deskripsi: Value pimpin_tim harus berupa angka

{
"status":"error",
"message":"The value 'kemampuan_persentasi' is not valid."
}
Deskripsi: Value kemampuan_persentasi harus berupa angka

{
"status":"error",
"message":"Language proficiency data must not be empty."
}
Deskripsi: Data penguasaan bahasa tidak boleh kosong

{
"status":"error",
"message":"The value 'membaca_bhs' is not valid."
}
Deskripsi: Value membaca_bhs harus 'BS', 'B', 'C', 'K' atau 'KS'

{
"status":"error",
"message":"The value 'menulis_bhs' is not valid."
}
Deskripsi: Value menulis_bhs harus 'BS', 'B', 'C', 'K' atau 'KS'

{
"status":"error",
"message":"The value 'mendengar_bhs' is not valid."
}
Deskripsi: Value mendengar_bhs harus 'BS', 'B', 'C', 'K' atau 'KS'

{
"status":"error",
"message":"The value 'berbicara_bhs' is not valid."
}
Deskripsi: Value berbicara_bhs harus 'BS', 'B', 'C', 'K' atau 'KS'

{
"status":"error",
"message":"Personal interests and concepts failed to be saved."
}
Deskripsi: Minat dan konsep pribadi gagal disimpan

{
"status":"error",
"message":"The value 'organisasi' is not valid."
}
Deskripsi: Value organisasi harus 'Ya' atau 'Tidak'

{
"status":"error",
"message":"Organizational experience was not retained."
}
Deskripsi: Data organisasi gagal disimpan

{
"status":"error",
"message":"Organization history data cannot be empty."
}
Deskripsi: Data riwayat organisasi tidak boleh kosong

## Documentation from Postman

Insert RH API Endpoint
This endpoint allows users to submit a comprehensive digital CV by sending personal and professional details via an HTTP POST request. The data provided will be processed to create or update a record in the system.
Request Parameters
The request body must be formatted as x-www-form-urlencoded and should include the following parameters:
email: User's email address (text)
nama: Full name of the user (text)
tempat_lahir: Place of birth (text)
tgl_lahir: Date of birth (text)
jk: Gender (text)
status_pernikahan: Marital status (text)
no_telepon: Phone number (text)
sim[]: List of driving licenses (text)
propinsi: Province of residence (text)
kota_tinggal: City of residence (text)
kec_tinggal: Sub-district of residence (text)
rt_tinggal: RT (neighborhood unit) of residence (text)
rw_tinggal: RW (community unit) of residence (text)
alamat_tinggal: Address of residence (text)
pos_tinggal: Postal code of residence (text)
cek_diploma: Diploma verification status (text)
pendidikan_tinggi: Highest education level (text)
jenjang_pendidikan[]: Educational levels attained (text)
nama_sekolah_pendidikan[]: Names of educational institutions (text)
jurusan_pendidikan[]: Fields of study (text)
tahun_mulai_pendidikan[]: Start years of education (text)
tahun_selesai_pendidikan[]: End years of education (text)
ket_pendidikan[]: Additional notes on education (text)
kursus: Course details (text)
nama_kursus[]: Names of courses taken (text)
sertifikat_kursus[]: Course certificates (text)
tempat_kursus[]: Locations of courses (text)
tgl_mulai_kursus[]: Start dates of courses (text)
tgl_selesai_kursus[]: End dates of courses (text)
pengalaman_kerja: Work experience summary (text)
total_pengalaman_kerja: Total years of work experience (text)
rp_nama_perusahaan[]: Names of previous employers (text)
rp_jabatan[]: Job titles held (text)
rp_status[]: Employment status (text)
rp_gaji[]: Salary details (text)
rp_tahun_mulai[]: Start years of employment (text)
rp_tahun_selesai[]: End years of employment (text)
rp_alasan[]: Reasons for leaving previous jobs (text)
perjalanan_dinas: Business travel experience (text)
minat_lokasi_kerja: Preferred work locations (text)
minat_gaji: Salary expectations (text)
penguasaan_bhs: Language proficiency (text)
bahasa[]: Languages spoken (text)
membaca_bhs[]: Reading proficiency in languages (text)
menulis_bhs[]: Writing proficiency in languages (text)
mendengar_bhs[]: Listening proficiency in languages (text)
berbicara_bhs[]: Speaking proficiency in languages (text)
kelebihan[]: Strengths (text)
kekurangan[]: Weaknesses (text)
kk: Family card information (text)
pimpin_tim: Team leadership experience (text)
kemampuan_persentasi: Presentation skills (text)
rlp[]: Relevant links or portfolios (text)
organisasi: Organizational involvement (text)
po_nama[]: Names of organizations (text)
po_jabatan[]: Positions held in organizations (text)
po_tahun[]: Years of involvement (text)
po_tempat[]: Locations of organizations (text)

Expected Response
Upon successful submission, the API will return a response indicating the success or failure of the operation, along with any relevant messages or identifiers for the newly created or updated CV record.
Notes
Ensure that all fields are filled out accurately to avoid validation errors.
The use of arrays (e.g., sim[], jenjang_pendidikan[], etc.) allows for multiple entries for those parameters.
This endpoint is crucial for users looking to maintain a digital CV for job applications or professional networking.
