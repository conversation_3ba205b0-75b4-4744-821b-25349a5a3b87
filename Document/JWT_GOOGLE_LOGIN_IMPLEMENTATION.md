# Implementasi JWT pada Google Login

## Deskripsi

Implementasi JWT (JSON Web Token) pada endpoint login Google untuk kandidat. Menambahkan autentikasi berbasis token yang konsisten dengan login regular.

## Fitur yang Ditambahkan

### 1. JWT Token Generation

- **Access Token**: Token dengan masa berlaku 24 jam untuk akses API
- **Refresh Token**: Token dengan masa berlaku 30 hari untuk memperbarui access token

### 2. Token Storage

- Refresh token disimpan di database dengan expiry time
- Session tetap dipertahankan untuk kompatibilitas

### 3. Enhanced Response

- Response login sekarang mencakup access_token dan refresh_token
- Data pengguna tetap lengkap seperti sebelumnya

## Perubahan yang Dilakukan

### 1. Import dan Dependencies

```php
require_once(__DIR__ . '/../../../vendor/autoload.php');

use Firebase\JWT\JWT;
use Dotenv\Dotenv;

$dotenv = Dotenv::createImmutable(__DIR__ . '/../../..');
$dotenv->load();
```

### 2. Fungsi JWT

- `generateAccessToken($userData)` - Generate access token
- `generateRefreshToken($userData)` - Generate refresh token

### 3. Database Update

- Update tabel `users_kandidat` dengan refresh token dan expiry

### 4. Enhanced Logging

- Menambahkan log aktivitas untuk login via Google
- Commit transaction setelah semua operasi berhasil

## JWT Payload Structure

### Access Token

```json
{
  "iss": "digitalcv",
  "aud": "candidate",
  "iat": timestamp,
  "exp": timestamp + 24h,
  "data": {
    "pin": "user_pin",
    "nama": "user_name",
    "email": "user_email",
    "tipe": "kandidat"
  }
}
```

### Refresh Token

```json
{
  "iss": "digitalcv",
  "aud": "candidate",
  "iat": timestamp,
  "exp": timestamp + 30d,
  "data": {
    "pin": "user_pin",
    "email": "user_email",
    "tipe": "kandidat"
  }
}
```

## Response Format

### Successful Login

```json
{
  "success": true,
  "message": "Login berhasil.",
  "data": [
    {
      "pin": "123456",
      "nama": "John Doe",
      "email": "<EMAIL>",
      "img": "profile_image_url",
      "no_telp": "08123456789",
      "tempat_lahir": "Jakarta",
      "tgl_lahir": "1 Januari 1990",
      "jenis_kelamin": "Laki-laki",
      "alamat": "Jl. Example No. 1",
      "fcm_token": "fcm_token_here",
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
  ]
}
```

### Failed Login

```json
{
  "success": false,
  "message": "Email belum terdaftar.",
  "data": []
}
```

## Environment Variables Required

Pastikan file `.env` berisi:

```env
ACCESS_TOKEN_SECRET=your_access_token_secret_key
REFRESH_TOKEN_SECRET=your_refresh_token_secret_key
```

## Database Schema Requirements

Tabel `users_kandidat` harus memiliki kolom:

- `refresh_token` - TEXT/VARCHAR untuk menyimpan refresh token
- `refresh_token_expiry` - DATETIME untuk masa berlaku refresh token

## Security Features

1. **Secret Key Separation**: Access dan refresh token menggunakan secret key yang berbeda
2. **Token Expiry**: Access token 24 jam, refresh token 30 hari
3. **Database Storage**: Refresh token disimpan untuk validasi
4. **Activity Logging**: Semua login via Google dicatat

## Usage Notes

1. **Client Implementation**: Client harus menyimpan access_token untuk API calls
2. **Token Refresh**: Gunakan refresh_token untuk mendapatkan access_token baru
3. **Session Fallback**: Session tetap aktif untuk kompatibilitas dengan kode lama
4. **Error Handling**: Rollback transaction jika ada error

## Compatibility

- Kompatibel dengan sistem login regular yang sudah ada
- Response format diperluas tanpa breaking changes
- Session mechanism tetap berfungsi
- Dapat digunakan bersama dengan rate limiting yang sudah ada

## Testing

Untuk testing, gunakan endpoint dengan data:

```
POST /api/mobile/candidate/login_google.php
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>
koordinat=-6.2000,106.8000
device=Test Device
ip=***********
```
