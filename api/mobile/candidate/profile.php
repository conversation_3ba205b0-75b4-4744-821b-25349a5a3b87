<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

$func = $_GET['func'];

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('Y'); // Ambil tahun empat digit

    return "$tgl $bln $thn";
}

function getIDLamar($length = 10)
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

// fungsi untuk memverifikasi password
function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

if ($func == 'getFotoProfil') {
    $response['data'] = [];

    // get tahap lamaran
    $get = $conn->prepare("SELECT foto
    FROM users_kandidat WHERE pin = ?");
    $get->bind_param("s", $pin);
    echo $get->error;
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $img = $row['foto'];

            if ($img != "") {
                if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $img)) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'kandidat/foto-profil/' . $img
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+24 hours');
                    $img = (string) $request->getUri();
                }
            }
            $row['foto'] = $img;
            $response['data'][] = $row;
        }
    }

    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data']
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'uploadFotoProfil') {
    $data = array();

    $status = "gagal";
    $message = "Upload foto profil gagal dilakukan.";
    $urlFoto = "";
    $created_at = date("Y-m-d H:i:s");

    if (isset($_FILES['file']['name'])) {
        if (file_exists($_FILES['file']['tmp_name']) || is_uploaded_file($_FILES['file']['tmp_name'])) {
            try {
                // Mulai proses
                $conn->begin_transaction();

                // cek apakah user terdaftar atau tidak
                $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
                $sql->bind_param("s", $pin);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows == 0) {
                    throw new Exception("Proses gagal dilakukan. Silakan untuk login kembali.");
                }

                // get file profil yang lama
                $row = mysqli_fetch_array($result);
                $img = $row['foto'];

                if ($img != "") {
                    if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $img)) {
                        // hapus file profil yang lama
                        $result = $s3->deleteObject([
                            'Bucket' => $bucket,
                            'Key'    => 'kandidat/foto-profil/' . $img
                        ]);
                    }
                }

                // upload file
                $file_tmp = $_FILES['file']['tmp_name'];
                $fileName = $_FILES['file']['name'];
                $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                $nama_file = 'FP-' . $pin . '.' . $ext;

                $s3->putObject([
                    'Bucket' => $bucket,
                    'Key'    => 'kandidat/foto-profil/' . $nama_file,
                    'SourceFile' => $file_tmp
                ]);

                // cek apakah file sudah ke upload atau belum
                if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $nama_file)) {
                    // update nama file di table kandidat
                    $update = $conn->prepare("UPDATE `users_kandidat` SET `foto` = ?, `update_at` = ? WHERE `pin` = ?");
                    $update->bind_param("sss",  $nama_file, $created_at, $pin);

                    if ($update->execute()) {
                        $update->close();

                        // buat link untuk foto profile
                        $cmd = $s3->getCommand('GetObject', [
                            'Bucket' => $bucket,
                            'Key'    => 'kandidat/foto-profil/' . $nama_file
                        ]);

                        $request = $s3->createPresignedRequest($cmd, '+24 hours');
                        $urlFoto = (string) $request->getUri();

                        // Mengubah nilai img pada session
                        $_SESSION['users']['img'] = $urlFoto;

                        // simpan log aktivitas
                        $messages = 'Melakukan update foto profil.';
                        $extra_info = "Kandidat";
                        $level = "INFO";
                        logActivity($conn, $pin, $level, $messages, $extra_info);

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = "Foto profil berhasil disimpan.";
                    } else {
                        throw new Exception("Upload foto profil gagal dilakukan.");
                    }
                } else {
                    throw new Exception("Upload foto profil gagal dilakukan.");
                }
            } catch (Exception $e) {
                // Jika ada error, rollback proses
                $conn->rollback();

                $message = $e->getMessage();
            }
        }
    }

    $data = array(
        "status" => $status === "success",
        "message" => $message,
        "data" => array(
            array("urlFoto" => $urlFoto)
        )
    );

    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == 'updateProfile') {
    $data = array();

    $status = "gagal";
    $message = "Update profil gagal dilakukan.";
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Proses gagal dilakukan. Silakan untuk login kembali.");
        }

        // cek keterangan update
        if (!isset($_POST['ket'])) {
            throw new Exception("Update profil gagal dilakukan.");
        }

        if ($_POST['ket'] == 'updateNoTelepon') {
            if (isset($_POST['no_telepon'])) {
                $no_telepon = $_POST['no_telepon'];

                // cek apakah no telepon sudah terdaftar atau belum
                $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE no_telp = ?");
                $sql->bind_param("s", $no_telepon);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    throw new Exception("Nomor telepon sudah terdaftar.");
                }

                // update no telepon di table users kandidat
                $update = $conn->prepare("UPDATE `users_kandidat` SET `no_telp` = ?, `update_at` = ? WHERE `pin` = ?");
                $update->bind_param("sss",  $no_telepon, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();

                    // update no telepon di table rh
                    $update = $conn->prepare("UPDATE `rh` SET `no_telepon` = ?, `updated_at` = ? WHERE `id` = ?");
                    $update->bind_param("sss",  $no_telepon, $created_at, $pin);

                    if ($update->execute()) {
                        $update->close();

                        // simpan log aktivitas
                        $messages = 'Melakukan update nomor telepon.';
                        $extra_info = "Kandidat";
                        $level = "INFO";
                        logActivity($conn, $pin, $level, $messages, $extra_info);

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = "Nomor telepon berhasil disimpan.";
                    } else {
                        throw new Exception("Update nomor telepon gagal dilakukan.");
                    }
                } else {
                    throw new Exception("Update nomor telepon gagal dilakukan.");
                }
            } else {
                throw new Exception("Update profil gagal dilakukan.");
            }
        } elseif ($_POST['ket'] == 'updateEmail') {
            if (isset($_POST['email']) && isset($_POST['otp'])) {
                $email = $_POST['email'];
                $otp = $_POST['otp'];

                // cek apakah email sudah terdaftar atau belum
                $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE email = ?");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    throw new Exception("Email sudah terdaftar.");
                }

                // cek otp
                $timezone = "SET time_zone = '+07:00'";
                $conn->query($timezone);

                $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
                $sql->bind_param("ss", $otp, $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    // Update Status OTP
                    $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                    $update->bind_param("ss", $otp, $email);

                    if ($update->execute()) {
                        $update->close();
                    } else {
                        throw new Exception("Update profil gagal dilakukan.");
                    }
                } else {
                    throw new Exception("OTP Salah.");
                }

                // update email di table users kandidat
                $update = $conn->prepare("UPDATE `users_kandidat` SET `email` = ?, `update_at` = ? WHERE `pin` = ?");
                $update->bind_param("sss",  $email, $created_at, $pin);

                if ($update->execute()) {
                    $update->close();

                    // update email di table rh
                    $update = $conn->prepare("UPDATE `rh` SET `email` = ?, `updated_at` = ? WHERE `id` = ?");
                    $update->bind_param("sss",  $email, $created_at, $pin);

                    if ($update->execute()) {
                        $update->close();

                        // update session users email
                        $_SESSION['users']['email'] = $email;

                        // simpan log aktivitas
                        $messages = 'Melakukan update email.';
                        $extra_info = "Kandidat";
                        $level = "INFO";
                        logActivity($conn, $pin, $level, $messages, $extra_info);

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = "Email berhasil disimpan.";
                    } else {
                        throw new Exception("Update email gagal dilakukan.");
                    }
                } else {
                    throw new Exception("Update email gagal dilakukan.");
                }
            } else {
                throw new Exception("Update profil gagal dilakukan.");
            }
        } else {
            throw new Exception("Update profil gagal dilakukan.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'sendOTPUpdateEmail') {
    // get from post data users
    $email = $_POST['email'];
    $no_hp_pic = $_POST['no_hp_pic'];

    if ($_SERVER["REQUEST_METHOD"] === "POST") {
        $recaptcha_secret = "6LdFFMMrAAAAAMB1_18jWoyNc4VlSyqfMj-YhXQH"; // Replace with your secret key
        $recaptcha_response = $_POST['recaptcha_response'];

        // Verify token with Google
        $verify_url = "https://www.google.com/recaptcha/api/siteverify";
        $response = file_get_contents("$verify_url?secret=$recaptcha_secret&response=$recaptcha_response");
        $response_data = json_decode($response);

        if ($response_data->success && $response_data->score >= 0.5) {
            // tambahan
            $tgl = date('Y-m-d H:i:s');
            $warna    = '#fbb116';

            // function buat kode verifikasi
            function create_kodever($length)
            {
                $data = '1234567890';
                $string = '';
                for ($i = 0; $i < $length; $i++) {
                    $pos = rand(0, strlen($data) - 1);
                    $string .= $data[$pos];
                }
                return $string;
            }

            $status = "";
            $message = "";

            try {

                $sql = "DELETE FROM kode_verifikasi where email='$email'";
                $result = $conn->query($sql);
                // Mulai proses
                $conn->begin_transaction();

                // Cek nomor otp
                $sqlCode = "SELECT code FROM kode_verifikasi";
                $queryCode = $conn->query($sqlCode);

                // Generate nomor otp
                do {
                    $code = create_kodever(5);
                } while ($code == $queryCode);

                // cek email apakah sudah aktif
                $sql = $conn->prepare("SELECT * FROM `users_kandidat` WHERE email = ? AND `status` = 'Active'");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows > 0) {
                    throw new Exception("Email sudah terdaftar.");
                }

                // cek email apakah sudah terdaftar
                $sql = $conn->prepare("SELECT * FROM `kode_verifikasi` WHERE email = ? AND `status` = 'Pending'");
                $sql->bind_param("s", $email);
                $sql->execute();
                $result = $sql->get_result();
                $sql->close();

                if ($result->num_rows == 0) {
                    $insert = $conn->prepare("INSERT INTO `kode_verifikasi` (`code`,`email`,`status`,`date`) VALUES (?, ?, 'Pending', ?)");
                    $insert->bind_param("sss", $code, $email, $tgl);

                    if ($insert->execute()) {
                        $insert->close();

                        // Jika semua query berhasil, commit transaksi
                        $conn->commit();

                        $status = "success";
                        $message = "Periksa email Anda untuk melihat kode verifikasi akun.";
                    } else {
                        throw new Exception("Kode OTP gagal dikirim.");
                    }
                } else {
                    $row = mysqli_fetch_array($result);
                    $code = $row['code'];
                }

                // Kirim Email
                $mailContent = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                        <html xmlns="http://www.w3.org/1999/xhtml" class="ie">
                        <html style="margin: 0;padding: 0;" xmlns="http://www.w3.org/1999/xhtml">
                        <head>
                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                            <!--[if !mso]><!-->
                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                            <!--<![endif]-->
                            <meta name="viewport" content="width=device-width">
                            <style type="text/css">
                                @media only screen and (min-width: 620px){.wrapper{min-width:600px !important}.wrapper h1{}.wrapper h1{font-size:26px !important;line-height:34px !important}.wrapper h2{}.wrapper h2{font-size:20px !important;line-height:28px !important}.wrapper h3{}.column{}.wrapper .size-8{font-size:8px !important;line-height:14px !important}.wrapper .size-9{font-size:9px !important;line-height:16px !important}.wrapper .size-10{font-size:10px !important;line-height:18px !important}.wrapper .size-11{font-size:11px !important;line-height:19px !important}.wrapper .size-12{font-size:12px !important;line-height:19px !important}.wrapper .size-13{font-size:13px !important;line-height:21px !important}.wrapper .size-14{font-size:14px !important;line-height:21px !important}.wrapper .size-15{font-size:15px !important;line-height:23px !important}.wrapper .size-16{font-size:16px !important;line-height:24px
                                !important}.wrapper .size-17{font-size:17px !important;line-height:26px !important}.wrapper .size-18{font-size:18px !important;line-height:26px !important}.wrapper .size-20{font-size:20px !important;line-height:28px !important}.wrapper .size-22{font-size:22px !important;line-height:31px !important}.wrapper .size-24{font-size:24px !important;line-height:32px !important}.wrapper .size-26{font-size:26px !important;line-height:34px !important}.wrapper .size-28{font-size:28px !important;line-height:36px !important}.wrapper .size-30{font-size:30px !important;line-height:38px !important}.wrapper .size-32{font-size:32px !important;line-height:40px !important}.wrapper .size-34{font-size:34px !important;line-height:43px !important}.wrapper .size-36{font-size:36px !important;line-height:43px !important}.wrapper .size-40{font-size:40px !important;line-height:47px !important}.wrapper
                                .size-44{font-size:44px !important;line-height:50px !important}.wrapper .size-48{font-size:48px !important;line-height:54px !important}.wrapper .size-56{font-size:56px !important;line-height:60px !important}.wrapper .size-64{font-size:64px !important;line-height:63px !important}}
                                </style>
                            <style type="text/css">
                                body {
                                    margin: 0;
                                    padding: 0;
                                }
                                table {
                                    border-collapse: collapse;
                                    table-layout: fixed;
                                }
                                * {
                                    line-height: inherit;
                                }
                                [x-apple-data-detectors],
                                [href^="tel"],
                                [href^="sms"] {
                                    color: inherit !important;
                                    text-decoration: none !important;
                                }
                                .wrapper .footer__share-button a:hover,
                                .wrapper .footer__share-button a:focus {
                                    color: #ffffff !important;
                                }
                                .btn a:hover,
                                .btn a:focus,
                                .footer__share-button a:hover,
                                .footer__share-button a:focus,
                                .email-footer__links a:hover,
                                .email-footer__links a:focus {
                                    opacity: 0.8;
                                }
                                .preheader,
                                .header,
                                .layout,
                                .column {
                                    transition: width 0.25s ease-in-out, max-width 0.25s ease-in-out;
                                }
                                .preheader td {
                                    padding-bottom: 8px;
                                }
                                .layout,
                                div.header {
                                    max-width: 400px !important;
                                    -fallback-width: 95% !important;
                                    width: calc(100% - 20px) !important;
                                }
                                div.preheader {
                                    max-width: 360px !important;
                                    -fallback-width: 90% !important;
                                    width: calc(100% - 60px) !important;
                                }
                                .snippet,
                                .webversion {
                                    Float: none !important;
                                }
                                .column {
                                    max-width: 400px !important;
                                    width: 100% !important;
                                }
                                .fixed-width.has-border {
                                    max-width: 402px !important;
                                }
                                .fixed-width.has-border .layout__inner {
                                    box-sizing: border-box;
                                }
                                .snippet,
                                .webversion {
                                    width: 50% !important;
                                }
                                .ie .btn {
                                    width: 100%;
                                }
                                [owa] .column div,
                                [owa] .column button {
                                    display: block !important;
                                }
                                .ie .column,
                                [owa] .column,
                                .ie .gutter,
                                [owa] .gutter {
                                    display: table-cell;
                                    float: none !important;
                                    vertical-align: top;
                                }
                                .ie div.preheader,
                                [owa] div.preheader,
                                .ie .email-footer,
                                [owa] .email-footer {
                                    max-width: 560px !important;
                                    width: 560px !important;
                                }
                                .ie .snippet,
                                [owa] .snippet,
                                .ie .webversion,
                                [owa] .webversion {
                                    width: 280px !important;
                                }
                                .ie div.header,
                                [owa] div.header,
                                .ie .layout,
                                [owa] .layout,
                                .ie .one-col .column,
                                [owa] .one-col .column {
                                    max-width: 600px !important;
                                    width: 600px !important;
                                }
                                .ie .fixed-width.has-border,
                                [owa] .fixed-width.has-border,
                                .ie .has-gutter.has-border,
                                [owa] .has-gutter.has-border {
                                    max-width: 602px !important;
                                    width: 602px !important;
                                }
                                .ie .two-col .column,
                                [owa] .two-col .column {
                                    max-width: 300px !important;
                                    width: 300px !important;
                                }
                                .ie .three-col .column,
                                [owa] .three-col .column,
                                .ie .narrow,
                                [owa] .narrow {
                                    max-width: 200px !important;
                                    width: 200px !important;
                                }
                                .ie .wide,
                                [owa] .wide {
                                    width: 400px !important;
                                }
                                .ie .two-col.has-gutter .column,
                                [owa] .two-col.x_has-gutter .column {
                                    max-width: 290px !important;
                                    width: 290px !important;
                                }
                                .ie .three-col.has-gutter .column,
                                [owa] .three-col.x_has-gutter .column,
                                .ie .has-gutter .narrow,
                                [owa] .has-gutter .narrow {
                                    max-width: 188px !important;
                                    width: 188px !important;
                                }
                                .ie .has-gutter .wide,
                                [owa] .has-gutter .wide {
                                    max-width: 394px !important;
                                    width: 394px !important;
                                }
                                .ie .two-col.has-gutter.has-border .column,
                                [owa] .two-col.x_has-gutter.x_has-border .column {
                                    max-width: 292px !important;
                                    width: 292px !important;
                                }
                                .ie .three-col.has-gutter.has-border .column,
                                [owa] .three-col.x_has-gutter.x_has-border .column,
                                .ie .has-gutter.has-border .narrow,
                                [owa] .has-gutter.x_has-border .narrow {
                                    max-width: 190px !important;
                                    width: 190px !important;
                                }
                                .ie .has-gutter.has-border .wide,
                                [owa] .has-gutter.x_has-border .wide {
                                    max-width: 396px !important;
                                    width: 396px !important;
                                }
                                .ie .fixed-width .layout__inner {
                                    border-left: 0 none white !important;
                                    border-right: 0 none white !important;
                                }
                                .ie .layout__edges {
                                    display: none;
                                }
                                .mso .layout__edges {
                                    font-size: 0;
                                }
                                .layout-fixed-width,
                                .mso .layout-full-width {
                                    background-color: #ffffff;
                                }
                                @media only screen and (min-width: 620px) {
                                    .column,
                                    .gutter {
                                        display: table-cell;
                                        Float: none !important;
                                        vertical-align: top;
                                    }
                                    div.preheader,
                                    .email-footer {
                                        max-width: 560px !important;
                                        width: 560px !important;
                                    }
                                    .snippet,
                                    .webversion {
                                        width: 280px !important;
                                    }
                                    div.header,
                                    .layout,
                                    .one-col .column {
                                        max-width: 600px !important;
                                        width: 600px !important;
                                    }
                                    .fixed-width.has-border,
                                    .fixed-width.ecxhas-border,
                                    .has-gutter.has-border,
                                    .has-gutter.ecxhas-border {
                                        max-width: 602px !important;
                                        width: 602px !important;
                                    }
                                    .two-col .column {
                                        max-width: 300px !important;
                                        width: 300px !important;
                                    }
                                    .three-col .column,
                                    .column.narrow {
                                        max-width: 200px !important;
                                        width: 200px !important;
                                    }
                                    .column.wide {
                                        width: 400px !important;
                                    }
                                    .two-col.has-gutter .column,
                                    .two-col.ecxhas-gutter .column {
                                        max-width: 290px !important;
                                        width: 290px !important;
                                    }
                                    .three-col.has-gutter .column,
                                    .three-col.ecxhas-gutter .column,
                                    .has-gutter .narrow {
                                        max-width: 188px !important;
                                        width: 188px !important;
                                    }
                                    .has-gutter .wide {
                                        max-width: 394px !important;
                                        width: 394px !important;
                                    }
                                    .two-col.has-gutter.has-border .column,
                                    .two-col.ecxhas-gutter.ecxhas-border .column {
                                        max-width: 292px !important;
                                        width: 292px !important;
                                    }
                                    .three-col.has-gutter.has-border .column,
                                    .three-col.ecxhas-gutter.ecxhas-border .column,
                                    .has-gutter.has-border .narrow,
                                    .has-gutter.ecxhas-border .narrow {
                                        max-width: 190px !important;
                                        width: 190px !important;
                                    }
                                    .has-gutter.has-border .wide,
                                    .has-gutter.ecxhas-border .wide {
                                        max-width: 396px !important;
                                        width: 396px !important;
                                    }
                                }
                                @media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
                                    .fblike {
                                        background-image: url(https://i7.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                    }
                                    .tweet {
                                        background-image: url(https://i8.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                    }
                                    .linkedinshare {
                                        background-image: url(https://i10.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                    }
                                    .forwardtoafriend {
                                        background-image: url(https://i9.createsend1.com/static/eb/master/13-the-blueprint-3/images/<EMAIL>) !important;
                                    }
                                }
                                @media (max-width: 321px) {
                                    .fixed-width.has-border .layout__inner {
                                        border-width: 1px 0 !important;
                                    }
                                    .layout,
                                    .column {
                                        min-width: 320px !important;
                                        width: 320px !important;
                                    }
                                    .border {
                                        display: none;
                                    }
                                }
                                .mso div {
                                    border: 0 none white !important;
                                }
                                .mso .w560 .divider {
                                    Margin-left: 260px !important;
                                    Margin-right: 260px !important;
                                }
                                .mso .w360 .divider {
                                    Margin-left: 160px !important;
                                    Margin-right: 160px !important;
                                }
                                .mso .w260 .divider {
                                    Margin-left: 110px !important;
                                    Margin-right: 110px !important;
                                }
                                .mso .w160 .divider {
                                    Margin-left: 60px !important;
                                    Margin-right: 60px !important;
                                }
                                .mso .w354 .divider {
                                    Margin-left: 157px !important;
                                    Margin-right: 157px !important;
                                }
                                .mso .w250 .divider {
                                    Margin-left: 105px !important;
                                    Margin-right: 105px !important;
                                }
                                .mso .w148 .divider {
                                    Margin-left: 54px !important;
                                    Margin-right: 54px !important;
                                }
                                .mso .size-8,
                                .ie .size-8 {
                                    font-size: 8px !important;
                                    line-height: 14px !important;
                                }
                                .mso .size-9,
                                .ie .size-9 {
                                    font-size: 9px !important;
                                    line-height: 16px !important;
                                }
                                .mso .size-10,
                                .ie .size-10 {
                                    font-size: 10px !important;
                                    line-height: 18px !important;
                                }
                                .mso .size-11,
                                .ie .size-11 {
                                    font-size: 11px !important;
                                    line-height: 19px !important;
                                }
                                .mso .size-12,
                                .ie .size-12 {
                                    font-size: 12px !important;
                                    line-height: 19px !important;
                                }
                                .mso .size-13,
                                .ie .size-13 {
                                    font-size: 13px !important;
                                    line-height: 21px !important;
                                }
                                .mso .size-14,
                                .ie .size-14 {
                                    font-size: 14px !important;
                                    line-height: 21px !important;
                                }
                                .mso .size-15,
                                .ie .size-15 {
                                    font-size: 15px !important;
                                    line-height: 23px !important;
                                }
                                .mso .size-16,
                                .ie .size-16 {
                                    font-size: 16px !important;
                                    line-height: 24px !important;
                                }
                                .mso .size-17,
                                .ie .size-17 {
                                    font-size: 17px !important;
                                    line-height: 26px !important;
                                }
                                .mso .size-18,
                                .ie .size-18 {
                                    font-size: 18px !important;
                                    line-height: 26px !important;
                                }
                                .mso .size-20,
                                .ie .size-20 {
                                    font-size: 20px !important;
                                    line-height: 28px !important;
                                }
                                .mso .size-22,
                                .ie .size-22 {
                                    font-size: 22px !important;
                                    line-height: 31px !important;
                                }
                                .mso .size-24,
                                .ie .size-24 {
                                    font-size: 24px !important;
                                    line-height: 32px !important;
                                }
                                .mso .size-26,
                                .ie .size-26 {
                                    font-size: 26px !important;
                                    line-height: 34px !important;
                                }
                                .mso .size-28,
                                .ie .size-28 {
                                    font-size: 28px !important;
                                    line-height: 36px !important;
                                }
                                .mso .size-30,
                                .ie .size-30 {
                                    font-size: 30px !important;
                                    line-height: 38px !important;
                                }
                                .mso .size-32,
                                .ie .size-32 {
                                    font-size: 32px !important;
                                    line-height: 40px !important;
                                }
                                .mso .size-34,
                                .ie .size-34 {
                                    font-size: 34px !important;
                                    line-height: 43px !important;
                                }
                                .mso .size-36,
                                .ie .size-36 {
                                    font-size: 36px !important;
                                    line-height: 43px !important;
                                }
                                .mso .size-40,
                                .ie .size-40 {
                                    font-size: 40px !important;
                                    line-height: 47px !important;
                                }
                                .mso .size-44,
                                .ie .size-44 {
                                    font-size: 44px !important;
                                    line-height: 50px !important;
                                }
                                .mso .size-48,
                                .ie .size-48 {
                                    font-size: 48px !important;
                                    line-height: 54px !important;
                                }
                                .mso .size-56,
                                .ie .size-56 {
                                    font-size: 56px !important;
                                    line-height: 60px !important;
                                }
                                .mso .size-64,
                                .ie .size-64 {
                                    font-size: 64px !important;
                                    line-height: 63px !important;
                                }
                            </style>
                            <style type="text/css">
                                @import url(https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400);
                            </style>
                            <link href="https://fonts.googleapis.com/css?family=Cabin:400,700,400italic,700italic|Open+Sans:400italic,700italic,700,400" rel="stylesheet" type="text/css">
                            <style type="text/css">
                                body{
                                    background-color:#fff
                                }
                                .logo a:hover,
                                .logo a:focus{
                                    color:#1e2e3b !important
                                }
                                .mso .layout-has-border{
                                    border-top:1px solid #ccc;
                                    border-bottom:1px solid #ccc
                                }
                                .mso .layout-has-bottom-border{
                                    border-bottom:1px solid #ccc
                                }
                                .mso .border,
                                .ie .border{
                                    background-color:#ccc
                                }
                                .mso h1,
                                .ie h1{
    
                                }
                                .mso h1,
                                .ie h1{
                                    font-size:26px !important;
                                    line-height:34px !important
                                }
                                .mso h2,
                                .ie h2{
    
                                }
                                .mso h2,
                                .ie h2{
                                    font-size:20px !important;
                                    line-height:28px !important
                                }
                                .mso h3,
                                .ie h3{
    
                                }
                                .mso .layout__inner,
                                .ie .layout__inner{
    
                                }
                                .mso .footer__share-button p{
    
                                }
                                .mso .footer__share-button p{
                                    font-family:Cabin,Avenir,sans-serif
                                }
                            </style>
                            <meta name="robots" content="noindex,nofollow"></meta>
                            <meta property="og:title" content="Mail v.01"></meta>
                        </head>
                        <body class="full-padding" style="margin: 0;padding: 0;-webkit-text-size-adjust: 100%;">
                            <table class="wrapper" style="border-collapse: collapse;table-layout: fixed;min-width: 320px;width: 100%;background-color: #fff;" cellpadding="0" cellspacing="0" role="presentation">
                                <tbody>
                                    <tr>
                                        <td>
                                            <div role="banner">
                                                <div class="preheader" style="Margin: 0 auto;max-width: 560px;min-width: 280px; width: 280px;width: calc(28000% - 167440px);">
                                                    <div style="border-collapse: collapse;display: table;width: 100%;">
                                                        <div class="snippet" style="display: table-cell;Float: left;font-size: 12px;line-height: 19px;max-width: 280px;min-width: 140px; width: 140px;width: calc(14000% - 78120px);padding: 10px 0 5px 0;color: #93a6ad;font-family: Cabin,Avenir,sans-serif;">
                                                        </div>
                                                        <div class="webversion" style="display: table-cell;Float: left;font-size: 12px;line-height: 19px;max-width: 280px;min-width: 139px; width: 139px;width: calc(14100% - 78680px);padding: 10px 0 5px 0;text-align: right;color: #93a6ad;font-family: Cabin,Avenir,sans-serif;">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="header" style="Margin: 0 auto;max-width: 600px;min-width: 320px; width: 320px;width: calc(28000% - 167400px);" id="emb-email-header-container">
                                                    <div class="logo emb-logo-margin-box" style="font-size: 26px;line-height: 32px;Margin-top: 6px;Margin-bottom: 20px;color: #41637e;font-family: Avenir,sans-serif;Margin-left: 20px;Margin-right: 20px;" align="center">
                                                        <div class="logo-center" align="center" id="emb-email-header">
                                                            <a style="text-decoration: none;transition: opacity 0.1s ease-in;color: #41637e;" href="https://digitalcv.id/">
                                                                <img style="display: block;height: auto;width: 100%;border: 0;max-width: 128px;" src="https://digitalcv.id/id/assets/images/logo/logoDcv2.png" alt="" width="128">
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div role="section">
                                                <div class="layout one-col fixed-width has-border" style="Margin: 0 auto;max-width: 602px;min-width: 322px; width: 322px;width: calc(28000% - 167398px);overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;">
                                                    <div class="layout__inner" style="border-collapse: collapse;width: 100%;background-color: #ffffff;max-width: 601px;border-top: 1px solid #ccc;border-right: 1px solid #ccc;border-bottom: 1px solid #ccc;border-left: 1px solid #ccc; border-radius: 25px;">
                                                        <div class="column" style="text-align: left;color: #8e8e8e;font-size: 14px;line-height: 21px;font-family: Cabin,Avenir,sans-serif; min-width: 320px;">
                                                            <div style="Margin-left: 20px;Margin-right: 20px;Margin-bottom: 24px;">
                                                                <div style="mso-line-height-rule: exactly;mso-text-raise: 4px;">
                                                                    <br>
                                                                    <p class="size-14" style="Margin-top: 0;Margin-bottom: 0;font-size: 12px;line-height: 19px;text-align: left;" lang="x-size-12">
                                                                        <span style="color:#8e8e8e">
                                                                            <span style="font-weight:700;color:' . $warna . '">
                                                                                Selamat datang,
                                                                            </span>
                                                                            <br/>
                                                                            Terima kasih telah mendaftarkan email Anda pada digitalcv.id. Untuk mengaktifkan email, silahkan masukkan kode verifikasi berikut, yang berlaku selama 5 menit :
                                                                        </span>
                                                                    </p>
                                                                    <p class="size-24" style="margin-left: auto; margin-right: auto; text-align: center;">
                                                                        <span style="color:#8e8e8e">
                                                                            <span style="font-weight:700;color:#410031">
                                                                                "' . $code . '"
                                                                            </span>
                                                                        </span>
                                                                    </p>
                                                                    <p class="size-14" style="Margin-top: 0;Margin-bottom: 0;font-size: 12px;line-height: 19px;text-align: left;" lang="x-size-12">
                                                                        <span style="color:#8e8e8e">
                                                                            *JANGAN BERITAHUKAN KODE VERIFIKASI INI KESIAPAPUN*
                                                                            <br>
                                                                            Jika kamu tidak sedang mendaftarkan email baru akun digitalcv.id segera hubungi 022-86065353
                                                                        </span>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <div style="mso-line-height-rule: exactly;line-height: 20px;font-size: 20px;">
                                                &nbsp;
                                            </div>
                                            <div style="mso-line-height-rule: exactly;" role="contentinfo">
                                                <div class="layout email-footer" style="Margin: 0 auto;max-width: 600px;min-width: 320px; width: 320px;width: calc(28000% - 167400px);overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;">
                                                    <div class="layout__inner" style="border-collapse: collapse;width: 100%; border-radius:25px;">
                                                        <div class="column wide" style="text-align: left;font-size: 12px;line-height: 19px;color: #93a6ad;font-family: Cabin,Avenir,sans-serif;Float: left;max-width: 400px;min-width: 320px; width: 320px;width: calc(8000% - 47600px);">
                                                            <div style="Margin-left: 20px;Margin-right: 20px;Margin-top: 10px;Margin-bottom: 10px;">
                                                                <div style="font-size: 12px;line-height: 19px;">
                                                                    <div>DigitalCV.id<br>
                                                                        Istana Pasteur Regency, Kompleks Ruko Kav. CRB 91 B Sukaraja Cicendo Sukaraja Bandung Jawa Barat ID 40175<br>
                                                                        Email:<EMAIL>
                                                                    </div>
                                                                </div>
                                                                <div style="font-size: 12px;line-height: 19px;Margin-top: 18px;">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="column narrow" style="text-align: left;font-size: 12px;line-height: 19px;color: #93a6ad;font-family: Cabin,Avenir,sans-serif;Float: left;max-width: 320px;min-width: 200px; width: 320px;width: calc(72200px - 12000%);">
                                                            <div style="Margin-left: 20px;Margin-right: 20px;Margin-bottom: 10px;">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="mso-line-height-rule: exactly;line-height: 40px;font-size: 40px;">
                                                &nbsp;
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <img style="overflow: hidden;position: fixed;visibility: hidden !important;display: block !important;height: 1px !important;width: 1px !important;border: 0 !important;margin: 0 !important;padding: 0 !important;" src="https://gestaltcenter.createsend1.com/t/j-o-oiuklhd-l/o.gif" width="1" height="1" border="0" alt="">
                        </body>
                        </html>';

                // Replace <EMAIL> with your "From" address.
                // This address must be verified with Amazon SES.
                $sender = '<EMAIL>';
                $senderName = 'Digital CV';

                // Replace <EMAIL> with a "To" address. If your account
                // is still in the sandbox, this address must be verified.
                $recipient = $email;

                // Replace smtp_username with your Amazon SES SMTP user name.
                $usernameSmtp = '<EMAIL>';

                // Replace smtp_password with your Amazon SES SMTP password.
                $passwordSmtp = '123123Asd!';

                // If you're using Amazon SES in a region other than US West (Oregon),
                // replace email-smtp.us-west-2.amazonaws.com with the Amazon SES SMTP
                // endpoint in the appropriate region.
                $host = 'srv80.niagahoster.com';
                $port = 465;

                // The subject line of the email
                $subject = 'Kode Verifikasi Registrasi';

                // Amazon SES SMTP interface using the PHPMailer class.";
                $mail = new PHPMailer(true);

                try {
                    // Specify the SMTP settings.
                    $mail->isSMTP();
                    $mail->setFrom($sender, $senderName);
                    $mail->Username   = $usernameSmtp;
                    $mail->Password   = $passwordSmtp;
                    $mail->Host       = $host;
                    $mail->Port       = $port;
                    $mail->SMTPAuth   = true;
                    $mail->SMTPSecure = 'ssl';

                    // Specify the message recipients.
                    $mail->addAddress($recipient);
                    // You can also add CC, BCC, and additional To recipients here.

                    // Specify the content of the message.
                    $mail->isHTML(true);
                    $mail->Subject    = $subject;
                    $mail->Body       = $mailContent;
                    $mail->Send();

                    $status = "success";
                    $message = "Periksa email Anda untuk melihat kode verifikasi akun.";
                } catch (Exception $e) {
                    $status = "gagal";
                    $message = "Kode OTP gagal dikirim."; //Catch errors from PHPMailer or Amazon SES.
                }

                //send otp via wa
                // $url = "https://api.barantum.com/api/v1/send-message-template-custom";

                // $data = [
                //     "company_uuid" => "50f3086041a90d7edad22ea3c5bd733c73905a14006a695144db5dcf092ed404",
                //     "agent_id" => "",
                //     "contacts" => [
                //         [
                //             "user_name" => $email,
                //             "number" => $no_hp_pic,
                //             "variabel" => [
                //                 "{{1}}" => "(otp)" . $code
                //             ]
                //         ]
                //     ],
                //     "template_uuid" => "b985089c-c850-4b03-b3a2-198dc927d655",
                //     "chat_bot_uuid" => "51ac1ccc-2ea1-4d0e-951d-0ca4e30cad31"
                // ];

                // $payload = json_encode($data);

                // $ch = curl_init($url);
                // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                // curl_setopt($ch, CURLOPT_HTTPHEADER, [
                //     "Content-Type: application/json"
                // ]);
                // curl_setopt($ch, CURLOPT_POST, true);
                // curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

                // $response = curl_exec($ch);
                // $error = curl_error($ch);
                // curl_close($ch);

                // if ($error) {
                //     $status = "cURL Error: " . $error;
                // }
            } catch (Exception $e) {
                // Jika ada error, rollback proses
                $conn->rollback();

                $status = "gagal";
                $message = $e->getMessage();
            }
        } else {
            // Failed reCAPTCHA verification
            $status = "error";
            $message = "Verifikasi reCAPTCHA gagal, silakan coba kembali nanti";
        }
    } else {
        http_response_code(403);
        $status = "error";
        $status = "Invalid request.";
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateKataSandi') {

    $response['data'] = [];
    $data = array();

    $status = "gagal";
    $message = "Update kata sandi gagal dilakukan.";
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            $response = [
                "success" => "gagal",
                "message" => "Proses gagal dilakukan. Silakan untuk login kembali.",
                "data" => [],
            ];
            header('Content-Type: application/json');
            echo json_encode($response, JSON_PRETTY_PRINT);
            $conn->close();
            exit;
        }

        if (!isset($_POST['password_lama']) || !isset($_POST['password']) || !isset($_POST['kon_password'])) {
            $response = [
                "success" => "gagal",
                "message" => "Update kata sandi gagal dilakukan.",
                "data" => [],
            ];
            header('Content-Type: application/json');
            echo json_encode($response, JSON_PRETTY_PRINT);
            $conn->close();
            exit;
        }

        $password_lama = $_POST['password_lama'];
        $password = hashPassword($_POST['password']);
        $kon_password = $_POST['kon_password'];
        $otp = $_POST['otp'];

        // cek otp
        $timezone = "SET time_zone = '+07:00'";
        $conn->query($timezone);

        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
        $sql->bind_param("ss", $otp, $email_user);
        $sql->execute();
        $resultOtp = $sql->get_result();
        $sql->close();

        if ($resultOtp->num_rows > 0) {
            $sql_cek = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? AND status = 'Finish'");
            $sql_cek->bind_param("ss", $otp, $email_user);
            $sql_cek->execute();
            $result_cek = $sql_cek->get_result();
            $sql_cek->close();
            
            if ($result_cek->num_rows == 0) {
                // Update Status OTP
                $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                $update->bind_param("ss", $otp, $email_user);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Update kata sandi gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('OTP sudah pernah digunakan.'));
            }
            
        } else {
            throw new Exception(translate('OTP Salah.'));
        }

        // cek password lama
        $row = mysqli_fetch_array($result);
        $storedHash = $row['password'];
        // verifikasi password
        if (verifyPassword($password_lama, $storedHash)) {
            // update password
            $update = $conn->prepare("UPDATE `users_kandidat` SET `password` = ?, `update_at` = ? WHERE `pin` = ?");
            $update->bind_param("sss",  $password, $created_at, $pin);
            if ($update->execute()) {
                $update->close();

                // simpan log aktivitas
                $messages = 'Melakukan update kata sandi.';
                $extra_info = "Kandidat";
                $level = "INFO";
                logActivity($conn, $pin, $level, $messages, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = "Kata sandi berhasil disimpan.";
            } else {

                // Format JSON response
                $response = [
                    "success" => "gagal",
                    "message" => "Update kata sandi gagal!",
                    "data" => [],
                ];
                header('Content-Type: application/json');
                echo json_encode($response, JSON_PRETTY_PRINT);
                $conn->close();
                exit;
            }
        } else {
            // Format JSON response
            $response = [
                "success" => "gagal",
                "message" => "Password lama salah. Silakan sesuaikan kembali.",
                "data" => $row,
            ];
            header('Content-Type: application/json');
            echo json_encode($response, JSON_PRETTY_PRINT);
            $conn->close();
            exit;
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }


    // Format JSON response
    $response = [
        "success" => $status,
        "message" => $message,
        "data" => [],
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
}

if ($func == 'updateVisibilitas') {
    $data = array();

    $status = "gagal";
    $message = translate('Tidak dapat menyimpan data. Silakan untuk login kembali.');
    $created_at = date("Y-m-d H:i:s");
    $visibilitas = '';

    try {
        // Mulai proses
        $conn->begin_transaction();
        // cek value
        if (!isset($_POST['status'])) {
            throw new Exception(translate('Update data gagal dilakukan.'));
        }

        $visibilitas = $_POST['status'];

        // update password
        $update = $conn->prepare("UPDATE `users_kandidat` SET `visibilitas` = ? WHERE `pin` = ?");
        $update->bind_param("ss",  $visibilitas, $pin);
        if ($update->execute()) {
            $update->close();

            // simpan log aktivitas
            $messages = 'Melakukan update visibilitas.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Data berhasil disimpan.');
        } else {
            throw new Exception(translate('Update data gagal dilakukan.'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['data'] = [
        'visibilitas' => $visibilitas
    ];


    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'tutupAkun') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses hapus akun gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();
        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
        }

        if (!isset($_POST['password'])) {
            throw new Exception(translate('Proses hapus akun gagal dilakukan.'));
        }

        $password = $_POST['password'];

        // cek password lama
        $row = mysqli_fetch_array($result);
        $storedHash = $row['password'];

        // verifikasi password
        if (verifyPassword($password, $storedHash)) {
            $deleted_at = date("Y-m-d", strtotime($created_at . ' +7 days')) . " 00:00:00";

            // cek apakah akun sudah dalam proses penghapusan
            $sql = $conn->prepare("SELECT * FROM list_hapus_akun WHERE id_akun = ? AND `status` = 'pending'");
            $sql->bind_param("s", $pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                throw new Exception(translate('Akun Anda sudah dalam proses penghapusan.'));
            }

            // insert ke daftar hapus akun
            $insert = $conn->prepare("INSERT INTO `list_hapus_akun` (`id_akun`,`status`,`deleted_at`,`created_at`) VALUES (?, 'pending', ?, ?)");
            $insert->bind_param("sss", $pin, $deleted_at, $created_at);
            if ($insert->execute()) {
                $insert->close();

                // simpan log aktivitas
                $messages = 'Melakukan proses penghapusan akun.';
                $extra_info = "Kandidat";
                $level = "INFO";
                logActivity($conn, $pin, $level, $messages, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = translate('Proses hapus akun berhasil dilakukan.');
            } else {
                throw new Exception(translate('Proses hapus akun gagal dilakukan.'));
            }
        } else {
            throw new Exception(translate('Password salah. Silakan sesuaikan kembali.'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;
    $data['data'] = [
        'deleted_at' => formatTanggalIndonesia($deleted_at)
    ];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'batalTutupAkun') {
    $data = array();

    $status = "gagal";
    $message = translate('Proses pembatalan hapus akun gagal dilakukan.');
    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();
        // cek apakah user terdaftar atau tidak
        $sql = $conn->prepare("SELECT * FROM users_kandidat WHERE pin = ?");
        $sql->bind_param("s", $pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception(translate('Proses gagal dilakukan. Silakan untuk login kembali.'));
        }

        // hapus data pembatalan akun
        $delete = $conn->prepare("DELETE FROM list_hapus_akun WHERE id_akun = ? AND `status` = 'pending'");
        $delete->bind_param("s", $pin);
        if ($delete->execute()) {
            $delete->close();

            // simpan log aktivitas
            $messages = 'Melakukan proses pembatalan penghapusan akun.';
            $extra_info = "Kandidat";
            $level = "INFO";
            logActivity($conn, $pin, $level, $messages, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = translate('Proses pembatalan hapus akun berhasil dilakukan.');
        } else {
            throw new Exception(translate('Proses pembatalan hapus akun gagal dilakukan.'));
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();
        $message = $e->getMessage();
    }

    $data['status'] = $status;
    $data['message'] = $message;

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

$conn->close();
exit;
