<?php
header('Content-Type: application/json');
include '../model/database.php';

// Cek koneksi
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $conn->connect_error
    ]);
    exit;
}

// Set API Key valid
$valid_api_key = "123456789abcdef";

// Ambil API Key dari request header
$headers = apache_request_headers();
$api_key = isset($headers['key']) ? $headers['key'] : null;

// Alternatif kalau apache_request_headers tidak ada
if (!$api_key && isset($_SERVER['HTTP_X_API_KEY'])) {
    $api_key = $_SERVER['HTTP_X_API_KEY'];
}

// Cek API Key
if ($api_key !== $valid_api_key) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized: Invalid API Key'
    ]);
    exit;
} else {


    $input = file_get_contents('php://input');
    $cek_data = json_decode($input, true);
    if ($cek_data['func'] == 'getData') {
        if (!empty($cek_data['id'])) {
            if ($cek_data['id'] != '') {
                $id = $cek_data['id'];
                $q_id = "and ul.id_gestalt IN ($id)";
            } else {
                $q_id = '';
            }
        } else {
            $q_id = '';
        }

        $sql = "SELECT
            GROUP_CONCAT( ul.id_lamar ) AS id_lamar,
            rh.id,
            rh.nama,
            GROUP_CONCAT( DISTINCT lr.posisi ) AS posisi_lamar,
            rh.email,
        IF
            ( rh.ktp = '', rh.id, rh.ktp ) AS nik,
            ul.tgl,
            k.label as nama_perusahaan,
			rh.no_telepon,
			ul.id_gestalt,
            uk.foto
        FROM
            users_lamar ul
            JOIN list_request lr ON lr.id_req = ul.id_req
            JOIN rh ON ul.id_gestalt = rh.id 
            JOIN koordinator k on ul.id_koordinator = k.id_koordinator
            JOIN users_kandidat uk on ul.id_gestalt=uk.pin
        WHERE
            ul.`status` = 'On Going Psikotes' $q_id
        GROUP BY
            ul.id_gestalt 
        ORDER BY
            rh.nama";
        $result = $conn->query($sql);
        $data = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'data' => $data
        ]);

        $conn->close();
    } elseif ($cek_data['func'] == 'prosesPsikotes') {
        if (!empty($cek_data['id'])) {
            if ($cek_data['id'] != '') {
                $id = $cek_data['id'];
                $q_id = "and ul.id_gestalt IN ($id)";

                $sql2 = "SELECT
                    GROUP_CONCAT( ul.id_lamar ) AS id_lamar,
                    rh.id,
                    rh.nama,
                    GROUP_CONCAT( DISTINCT lr.posisi ) AS posisi_lamar,
                    rh.email,
                IF
                    ( rh.ktp = '', rh.id, rh.ktp ) AS nik,
                    ul.tgl,
                    k.label as nama_perusahaan,
                    rh.no_telepon,
                    uk.foto
                FROM
                    users_lamar ul
                    JOIN list_request lr ON lr.id_req = ul.id_req
                    JOIN rh ON ul.id_gestalt = rh.id 
                    JOIN koordinator k on ul.id_koordinator = k.id_koordinator
                    JOIN users_kandidat uk on ul.id_gestalt=uk.pin
                WHERE
                    ul.`status` = 'On Going Psikotes' $q_id
                GROUP BY
                    ul.id_gestalt 
                ORDER BY
                    rh.nama";
                $result = $conn->query($sql2);
                $data = [];
                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $data[] = $row;
                    }

                    $sql = "UPDATE users_lamar ul set ul.`status` = 'Psikotes Gestalt' where ul.`status` = 'On Going Psikotes' $q_id";
                    $result = $conn->query($sql);

                    $tgl = date("Y-m-d H:i:s");
                    $insert = "INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`)
                    SELECT id_lamar, id_gestalt, id_req, id_koordinator, `status`, '$tgl' as tgl FROM users_lamar ul WHERE ul.`status` = 'Psikotes Gestalt' $q_id";
                    $conn->query($insert);

                    header('Content-Type: application/json');
                    echo json_encode([
                        'status' => 'success',
                        'data' => $data
                    ]);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'Data Kosong'
                    ]);
                }
            }
        } else {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'data' => 0
            ]);
        }

        $conn->close();
    } elseif ($cek_data['func'] == 'selesaiPsikotes') {
        if (!empty($cek_data['id'])) {
            if ($cek_data['id'] != '') {
                $id = $cek_data['id'];
                $q_id = "and ul.id_gestalt IN ($id)";

                $sql = "UPDATE users_lamar ul set ul.`status` = 'Selesai Psikotes' where (ul.`status` = 'On Going Psikotes' or ul.`status`='Psikotes Gestalt') $q_id";
                $result = $conn->query($sql);

                $tgl = date("Y-m-d H:i:s");
                $insert = "INSERT INTO users_lamar_history (`id_lamar`,`id_gestalt`,`id_req`,`id_koordinator`,`status`,`tgl`)
                SELECT id_lamar, id_gestalt, id_req, id_koordinator, `status`, '$tgl' as tgl FROM users_lamar ul WHERE ul.`status` = 'Selesai Psikotes' $q_id";
                $conn->query($insert);

                header('Content-Type: application/json');
                echo json_encode([
                    'status' => 'success',
                    'data' => 1
                ]);
            }
        } else {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'data' => 0
            ]);
        }

        $conn->close();
    }
}
