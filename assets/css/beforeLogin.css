:root {
  --black: #000;
  --primary: #0d3b72;
  --orange: #ff9800;
  --black: #000000;

  //Font SIZE
  --big-font-size: 2rem;
  --h1-font-size: 1.5rem;
  --h2-font-size: 1.25rem;
  --h3-font-size: 1.125rem;
  --normal-font-size: 0.928rem;
  --small-font-size: 0.85rem;
  --smaller-font-size: 0.75rem;
}

* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
  scroll-behavior: smooth;
}

body {
  background-color: #ffffff;
  font-family: "Poppins", sans-serif;
  background-image: url(../images/content/bg1.png);
  /* background-repeat: no-repeat; */
  background-size: 100%;
}

.body-sign {
  background-color: #ffffff;
  font-family: "Poppins", sans-serif;
  background-image: url(../images/content/bg-sign.png) !important;
  background-repeat: repeat !important;
  background-size: 100% !important;
}

a {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  color: var(--black);
}

.main-wrap {
  padding-top: 150px;
  padding-bottom: 100px;
}

.main {
  width: 50%;
  margin: 50px auto;
}

/*==========================================NAVBAR==================================*/

.navbar {
  transition: all 0.4s;
}
.acf-required {
  color: #ff0000;
}

.navbar .nav-link {
  color: #000;
}

.text-btn {
  font-size: 20px;
}

.navbar .nav-link:hover,
.navbar .nav-link:focus {
  color: #000;
  text-decoration: none;
}

.navbar .navbar-brand {
  color: #000;
}

/* Change navbar styling on scroll */
.navbar.active {
  background: #fff;
  box-shadow: 1px 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.active .nav-link {
  color: #000;
}

.navbar.active .nav-link:hover,
.navbar.active .nav-link:focus {
  color: #000;
  text-decoration: none;
}

.navbar.active .navbar-brand {
  color: #000;
}

.bi-translate {
  color: #1967d2;
}

.dropdown-toggle {
  border: none;
}
.dropdown-toggle i {
  font-size: 20px;
}

.navbar-toggler {
  border: none;
  box-shadow: none;
}

.text-small {
  font-size: 0.9rem !important;
}
.nav-link:active,
.nav-link:focus,
.nav-link:hover {
  background-color: transparent;
  color: #000;
}

.active-nav {
  margin-left: 0;
  font-weight: 700;
}

.nav-text {
  font-weight: 500;
}

.nav-link:hover {
  background-color: #aee2ff;
  border-radius: 10px;
}

/*==========================================End Navbar==================================*/

/*==========================================Index File Kandidat==================================*/
.index-container-hero {
  padding-top: 150px;
  padding-bottom: 50px;
}

.text-index-kandidat {
  font-size: 20px;
  text-align: center;
}

.form-control:focus {
  box-shadow: none;
}

.heading-hero h1 {
  font-weight: 800;
  font-size: 60px;
  line-height: 90px;
}

.jobsPage input {
  border: none !important;
}

.index-content-hero {
  padding: 25px;
  margin-top: 100px;
}

.index-header-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--black);
}

.index-candidat-section {
  justify-content: center;
  margin-top: 80px;
  margin-bottom: 150px;
}

.index-candidat-section img {
  z-index: -2;
}

.index-candidat-content {
  padding: 10px;
}

.text-candidat {
  margin-top: 50px;
}

/*Text effect*/
.tracking-in-expand {
  -webkit-animation: tracking-in-expand 1s cubic-bezier(0.215, 0.61, 0.355, 1)
    0.5s both;
  animation: tracking-in-expand 1s cubic-bezier(0.215, 0.61, 0.355, 1) 0.5s both;
}

@-webkit-keyframes tracking-in-expand {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }
  40% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
@keyframes tracking-in-expand {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }
  40% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* images effect  */
.tracking-in-expand-img {
  -webkit-animation: tracking-in-expand 1s ease-in 0.5s both;
  animation: tracking-in-expand 1s ease-in 0.5s both;
}
@-webkit-keyframes tracking-in-expand-img {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }
  40% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
@keyframes tracking-in-expand-img {
  0% {
    letter-spacing: -0.5em;
    opacity: 0;
  }
  40% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.popular_courses {
  padding-bottom: 100px;
}

.popular_courses .owl-item {
  opacity: 0;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  padding: 0px 10px;
  padding-bottom: 30px;
}

.popular_courses .owl-item.active {
  opacity: 1;
}

.popular_courses .owl-nav {
  display: flex !important;
  justify-content: space-between;
  position: absolute;
  top: 30%;
  width: 108%;
  /* transform: translateY(-50%); */
  left: -5%;
  color: var(--orange);
}

.popular_courses .owl-nav .owl-prev i,
.popular_courses .owl-nav .owl-next i {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.popular_courses .owl-nav .owl-prev i:hover,
.popular_courses .owl-nav .owl-next i:hover {
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  filter: grayscale(0%);
}

.card-text {
  font-size: 14px;
}

/*=================End Index File======================*/

/* =============================Index Company Page================================= */
.index-hero-company {
  padding: 65px 30px;
}

.wrap-hero-company {
  background: #ffffff;
  box-shadow: 0px 0px 8px rgba(185, 185, 185, 0.25);
  border-radius: 40px;
}

.sub-hero-company {
  margin-top: 80px;
}

.img-index-company {
  background-image: url(../images/content/bg-img.png);
  background-repeat: no-repeat;
  background-size: 100px;
}
.text_hero {
  font-weight: 800;
  line-height: 1.5em;
  color: #000000;
}

.text-p-company {
  font-size: 20px;
}

.divider:after,
.divider:before {
  content: "";
  flex: 1;
  height: 1px;
  background: #eee;
}

/* =============================Button Style================================= */

.btn-login_google {
  border: 2px solid var(--primary);
  background-color: white;
  color: var(--black);
  font-size: 16px;
  cursor: pointer;
}

.btn-login_google:hover {
  background-color: var(--orange);
  color: #fff;
}

.btn-signUp {
  border: 2px solid var(--primary);
  background-color: white;
  color: var(--black);
  font-size: 16px;
  cursor: pointer;
}
.btn-signUp:hover {
  background-color: var(--primary);
  color: white;
}

.btn-signIn {
  background-color: var(--primary);
  color: #ffffff;
}

.btn-signIn:hover {
  background-color: var(--orange);
  color: #ffffff;
}

.btn-signIn:disabled {
  background-color: var(--primary);
  color: #ffffff;
}

.btn-signIn {
  background-color: var(--primary);
  color: #ffffff;
}

.btn-signIn:hover {
  background-color: var(--orange);
  color: #ffffff;
}

.btn-signIn:disabled {
  background-color: var(--primary);
  color: #ffffff;
}

.btn-signIn-google {
  background-color: #ffffff; /* White background */
  color: var(--primary); /* Primary color text */
  border: 1px solid var(--primary); /* Optional: Add border */
}

.btn-signIn-google:hover {
  background-color: #e7e7e7;
  color: #000000;
  border: 1px solid var(--primary); /* Optional: Add border */

}

.btn-signIn-google:disabled {
  background-color: #ffffff;
  color: var(--primary);
  opacity: 0.6; /* Optional: Make disabled button slightly transparent */
}

.btn-index_company {
  background-color: var(--primary);
  border-radius: 30px;
}
.btn-index_company:hover {
  background-color: var(--orange);
}
.btn-white {
  background: #ffffff;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
}

.btn-white:hover {
  background: var(--primary);
  color: #fff;
}

.btn-search {
  font-size: var(--smaller-font-size);
}

.btn-otp {
  background: var(--orange);
  color: #ffffff;
}
.btn-otp:hover {
  background: var(--primary);
  color: #ffffff;
}
.btn-otp:disabled {
  background: var(--orange);
  color: #ffffff;
}
.btn-search {
  border-radius: 30px;
}

.btn-lamar {
  background-color: var(--orange);
}
button.btn.btn-md.btn-lamar.text-white:hover {
  background-color: var(--black);
}
.btn-cari {
  background-color: var(--black);
}

.btn-save {
  border: 2px solid var(--orange);
}

.btn-save:hover {
  background-color: var(--orange);
  color: #fff;
}

.wrap h2,
button {
  color: var(--black);
}

.btn-orange {
  color: var(--orange);
}

.bi-favorit {
  color: var(--black);
}

.bi-favorit.pressed {
  color: var(--orange);
}

.nav-link {
  color: var(--black);
}

/*-------------------------------End Button Style---------------------------------*/

/*=============================Swipper=======================================*/
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  font-size: 18px;
  height: auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
}

/*-------------------------------Sign Up------------------------------------------*/
.card-signUp {
  padding: 30px;
  margin: 10px;
}

.header-form {
  margin-bottom: 50px;
}

/*-----------------------------End Sign Up Page-----------------------------------*/

/*-----------------------------CONTENT-----------------------------------*/

@media (max-width: 991px) {
  .navbar {
    background: #fff;
  }

  .navbar .navbar-brand,
  .navbar .nav-link {
    color: #000;
  }

  .popular_courses {
    padding-bottom: 40px;
  }

  .popular_courses .owl-nav {
    display: none !important;
  }

  .text_hero {
    font-size: 30px;
  }

  .form-hero {
    margin-top: 50px;
  }
}

.single_course .course_head {
  position: relative;
  overflow: hidden;
}

.single_course .price {
  position: absolute;
  top: -34px;
  z-index: 2;
  color: #002347;
  display: inline-block;
  line-height: 65px;
  text-align: center;
  border-radius: 50px;
  background: #fdc632;
  font-weight: 500;
  font-size: 20px;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single_course .course_content {
  background: #f9f9ff;
  position: relative;
  -webkit-transition: all 0.3s ease 0s;
  -moz-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.single_course .course_content .tag {
  padding: 2px 21px;
  font-size: 13px;
  color: #fff;
  background: #002347;
  text-transform: uppercase;
}

.single_course .course_content h4 {
  font-size: 20px;
  font-weight: 500;
}

.single_course .course_content h4 a {
  color: #002347;
}

.single_course .course_content p {
  margin: 0;
}

.single_course .course_content .course_meta {
  margin-top: 25px;
}

.single_course .course_content .course_meta .meta_info a {
  color: #002347;
}

.single_course .authr_meta img {
  width: 45px !important;
  height: auto;
  display: inline-block !important;
}

.single_course .authr_meta span {
  color: #002347;
  font-weight: 500;
}

.single_course:hover .course_head img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.single_course:hover .course_content {
  background: #fff;
}

.single_course:hover .price {
  background: #002347;
  color: #fdc632;
}

.single_course:hover h4 a {
  color: #fdc632;
}

/*-------------------------------SIGN IN PAGE---------------------------------*/

/*-----------------------------FOOTER-----------------------------------*/
footer {
  background-color: #fff;
}

footer img {
  margin-left: 10px;
  margin-right: 15px;
  float: left;
}

.footer-text {
  margin-top: 10px;
  margin-right: 15px;
}
footer a {
  color: var(--black);
  text-decoration: none;
}
/*-----------------------------RESPONSIVE-----------------------------------*/

@media screen and (max-width: 870px) {
  .heading-hero h1 {
    font-size: 1.8rem;
  }

  .cattegory-hero {
    margin-right: auto;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 15px;
  }

  .image-content {
    width: 80px;
    margin-right: 15px;
    float: left;
  }

  .header-content h2 {
    font-size: var(--h1-font-size);
    color: var(--black);
  }

  .header-content p {
    font-size: var(--normal-font-size);
    color: var(--black);
  }

  .card-findJob {
    width: 10rem;
    float: inherit;
    margin-left: auto;
    margin-right: auto;
    margin-top: 20px;
  }

  .card-findJob img {
    width: 80px;
  }

}

@media screen and (max-width: 678px) {
  .heading-hero h1 {
    font-size: var(--h1-font-size);
  }

  .text-p-company {
    font-size: 16px;
  }

  .img-hero {
    margin-top: 30px;
  }

  .candidat-section img {
    z-index: -2;
  }

  .content-hero p {
    font-size: var(--smaller-font-size);
  }

  .header-content h2 {
    font-size: var(--h1-font-size);
    color: var(--black);
  }

  .card-findJob {
    width: 13rem;
    margin-left: auto;
    margin-right: auto;
    margin-top: 20px;
  }

}

/* =====================SMALL DEVICE============================================*/

@media screen and (max-width: 400px) {
  .text-index-kandidat {
    font-size: 14px;
    text-align: left;
  }

  .card-hero h2 {
    font-size: 22px;
  }

  .heading-hero h1 {
    font-size: 1.8rem;
  }

  .btn-index_company {
    margin-left: 50px;
  }

  .index-container-hero {
    padding: 130px 20px 10px 20px;
  }

  .text_hero {
    text-align: center;
  }

  .text-p-company {
    font-size: 14px;
  }

  .img-hero img {
    width: 400px;
  }

  .image-content {
    width: 80px;
    margin-right: 15px;
    float: left;
  }

  .content-hero p {
    font-size: var(--small-font-size);
  }

  .header-content h2 {
    font-size: 2rem;
    color: var(--black);
  }

  .candidat-content {
    padding-left: 10px;
    margin-left: 50px;
  }
}

/*==========================Card=================================*/
.card-hero {
  background-color: #fff;
  border-radius: 50px;
  padding: 50px 55px;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 4px 8px 0 rgba(0, 0, 0, 0.19);
}

.card-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--black);
}

.card-findJob {
  width: 12rem;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
}

.grecaptcha-badge { 
  visibility: hidden !important;
}