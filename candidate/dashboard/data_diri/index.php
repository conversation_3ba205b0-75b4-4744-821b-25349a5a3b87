<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../../model/database.php";

$tempAkses = "true";
if (isset($_SESSION['users'])) {
    $pin = "-";
    $nama_user = "-";
    $email_user = "-";

    // get data user
    if (isset($_SESSION['users']['pin'])) {
        $pin = addslashes($_SESSION['users']['pin']);
    }

    if (isset($_SESSION['users']['nama'])) {
        $nama_user = $_SESSION['users']['nama'];
    }

    if (isset($_SESSION['users']['email'])) {
        $email_user = $_SESSION['users']['email'];
    }

    // simpan log aktivitas
    $messages = 'Menampilkan halaman input data diri di sistem.';
    $extra_info = "Kandidat";
    $level = "INFO";
    $path = $_SERVER['REQUEST_URI'];
    logActivity($conn, $pin, $level, $messages, $extra_info);
} else {
    $tempAkses = "false";
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><?= translate('Data Diri') ?> - digitalcv</title>
    <meta
        content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
        name="viewport" />
    <link rel="icon" href="../../../assets/images/logo/logoDcv2.png" />
    <?php include_once '../style.php'; ?>
    <style>
        input.error,
        select.error {
            border: 2px solid #ff5858 !important;
        }

        .was-validated .form-check-input:invalid+.select2 .select2-selection {
            border-color: #dc3545;
            padding-right: calc(1.5em + .75rem);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(1em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem)
        }

        .form-control.is-valid:focus,
        .was-validated :valid.form-control {
            border: 2px solid #aaa !important;
            border-color: #ebedf2 !important;
            background-image: inherit !important;
            box-shadow: inherit !important;
        }

        .form-select.is-valid:focus,
        .was-validated :valid.form-select {
            border: 2px solid #aaa !important;
            border-color: #ebedf2 !important;
            background-image: inherit !important;
            box-shadow: inherit !important;
        }

        *:focus {
            outline: 0px;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <?php include '../sidebar.php' ?>
        <div class="main-panel">
            <?php include '../header.php' ?>
            <div class="container">
                <div class="page-inner">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row" id="loading-form" style="display: none;">
                                        <div class="col-md-12 d-flex justify-content-center">
                                            <div class="loader"></div>
                                        </div>
                                    </div>
                                    <div id="form-data-diri"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script src="../assets/js/jquery.mask.js"></script>
        </div>
    </div>
    <?php include '../footer.php' ?>

    <script>
        function hanyaAngka(evt) {
            var charCode = evt.which ? evt.which : event.keyCode;

            if (charCode < 48 || charCode > 57) return false;
            return true;
        }
        $(document).ready(function() {
            loadPage('form-identitas-diri.php')
        });

        function loadPage(link) {
            $("#form-data-diri").hide();
            $("#loading-form").show();

            $.ajax({
                url: link,
                type: "GET",
                success: function(response) {
                    $("#loading-form").hide();
                    $("#form-data-diri").html(response);
                    $("#form-data-diri").show();
                },
                error: function(xhr, status, error) {
                    console.error("<?= translate('Terjadi kesalahan') ?>: " + error);
                }
            });
        }
    </script>
</body>

</html>