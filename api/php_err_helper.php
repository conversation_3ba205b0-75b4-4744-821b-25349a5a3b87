<?php

// 1. Custom error handler
set_error_handler(function ($severity, $message, $file, $line) {
    $log_conn = new mysqli(DB_SERVER, DB_USER, DB_PASSWORD, DB_DATABASE);

    // Dapatkan info tambahan
    $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
    $agent = isset($_SERVER['HTTP_USER_AGENT']) ?  addslashes($_SERVER['HTTP_USER_AGENT']) : '';
    $err_msg = $message . " in " . $file . ":" . $line;
    $created_at = date("Y-m-d H:i:s");

    $log = "INSERT INTO `log_aktifitas` (`id`, `timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES ('', ?, 'ERROR', ?, 'PHP', 'SISTEM', ?, ?, '')";
    $log_stmt = $log_conn->prepare($log);
    if ($log_stmt) {
        $bind = $log_stmt->bind_param("ssss", $created_at, $err_msg, $ip, $agent);
        $log_stmt->execute();
        $log_stmt->close();
    } else {
        // Optional: Tulis ke file jika logging ke DB juga gagal
        error_log("Gagal menyimpan log ke database: [PHP] $err_msg");
    }

    // Simulasikan lemparan error
    throw new Exception("Proses gagal. Silakan hubungi administrator");

    $log_conn->close();
    exit();
});

// 2. Tangani fatal error saat shutdown
register_shutdown_function(function () {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        $log_conn = new mysqli(DB_SERVER, DB_USER, DB_PASSWORD, DB_DATABASE);

        // Dapatkan info tambahan
        $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
        $agent = isset($_SERVER['HTTP_USER_AGENT']) ?  addslashes($_SERVER['HTTP_USER_AGENT']) : '';
        $err_msg = $error['message'] . " in " . $error['file'] . ":" . $error['line'];
        $created_at = date("Y-m-d H:i:s");

        $log = "INSERT INTO `log_aktifitas` (`id`, `timestamp`, `level`, `message`, `extra_info`, `user_id`, `ip_address`, `user_agent`, `path`) 
            VALUES ('', ?, 'ERROR', ?, 'PHP', 'SISTEM', ?, ?, '')";
        $log_stmt = $log_conn->prepare($log);
        if ($log_stmt) {
            $bind = $log_stmt->bind_param("ssss", $created_at, $err_msg, $ip, $agent);
            $log_stmt->execute();
            $log_stmt->close();
        } else {
            // Optional: Tulis ke file jika logging ke DB juga gagal
            error_log("Gagal menyimpan log ke database: [PHP] $err_msg");
        }

        // Simulasikan lemparan error
        throw new Exception("Proses gagal. Silakan hubungi administrator");

        $log_conn->close();
        exit();
    }
});
