<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

// $userData = requireAuth();
// $pin = $userData->pin ?? "-";
// $nama_user = $userData->nama ?? "-";
// $email_user = $userData->email ?? "-";

// Validasi dan whitelist function parameter
$allowedFunctions = [
    'getLokasi',
    'getProvinsi',
    'getKota',
    'getKecamatan'
];

$func = isset($_GET['func']) ? trim($_GET['func']) : '';

// Validasi function parameter
if (empty($func) || !in_array($func, $allowedFunctions)) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Function tidak valid atau tidak diizinkan',
        'allowed_functions' => $allowedFunctions
    ], JSON_PRETTY_PRINT);
    exit;
}

function sanitizeOutput($data)
{
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeOutput($value);
        }
    } elseif (is_string($data)) {
        return $data;
    }
    return $data;
}

function executeSecureQuery($conn, $query, $params = [], $types = "")
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Failed to prepare statement");
        }

        if (!empty($params) && !empty($types)) {
            if (!$stmt->bind_param($types, ...$params)) {
                throw new Exception("Failed to bind parameters");
            }
        }

        if (!$stmt->execute()) {
            throw new Exception("Failed to execute query");
        }

        $result = $stmt->get_result();
        if (!$result) {
            throw new Exception("Failed to get result");
        }

        $stmt->close();
        return $result;
    } catch (Exception $e) {
        if (isset($stmt)) {
            $stmt->close();
        }
        return false;
    }
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

if ($func == 'getLokasi') {
    // Validasi dan sanitasi input parameters
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = isset($_GET['page_size']) ? min(100, max(1, (int)$_GET['page_size'])) : 5;

    // Validasi search parameter - hanya boleh huruf, angka, spasi, dan beberapa karakter khusus
    if (!empty($search) && !preg_match('/^[a-zA-Z0-9\s\-_\.]+$/', $search)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Format pencarian tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi page untuk mencegah overflow
    if ($page > 10000) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Halaman tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $offset = ($page - 1) * $perPage;

    $sql = "SELECT id, `name` FROM regencies";
    $countQuery = "SELECT COUNT(*) as total FROM regencies";
    $params = [];
    $types = "";

    // Tambahkan kondisi WHERE jika ada pencarian (berdasarkan ID atau nama)
    if (!empty($search)) {
        // Cek apakah search adalah angka (ID) atau teks (nama)
        if (is_numeric($search)) {
            // Pencarian berdasarkan ID
            $sql .= " WHERE id = ?";
            $countQuery .= " WHERE id = ?";
            $params[] = (int)$search;
            $types .= "i";
        } else {
            // Pencarian berdasarkan nama
            $sql .= " WHERE `name` LIKE ?";
            $countQuery .= " WHERE `name` LIKE ?";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $types .= "s";
        }
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii";

    // Execute main query
    $result = executeSecureQuery($conn, $sql, $params, $types);

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam mengambil data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => (string)$row['id'], "nama" => $row['name']];
    }

    // Execute count query
    $count_params = [];
    $count_types = "";

    if (!empty($search)) {
        if (is_numeric($search)) {
            $count_params[] = (int)$search;
            $count_types = "i";
        } else {
            $count_params[] = $searchTerm;
            $count_types = "s";
        }
    }

    $countResult = executeSecureQuery($conn, $countQuery, $count_params, $count_types);

    if ($countResult === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam menghitung data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $countData = $countResult->fetch_assoc();
    $totalRows = isset($countData['total']) ? (int)$countData['total'] : 0;

    // Format response JSON dengan sanitasi
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => sanitizeOutput($lokasi),
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}


if ($func == 'getProvinsi') {
    // Validasi dan sanitasi input parameters
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = isset($_GET['page_size']) ? min(100, max(1, (int)$_GET['page_size'])) : 5;

    // Validasi search parameter - hanya boleh huruf, angka, spasi, dan beberapa karakter khusus
    if (!empty($search) && !preg_match('/^[a-zA-Z0-9\s\-_\.]+$/', $search)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Format pencarian tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi page untuk mencegah overflow
    if ($page > 10000) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Halaman tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $offset = ($page - 1) * $perPage;

    $sql = "SELECT id, `name` FROM provinces";
    $countQuery = "SELECT COUNT(*) as total FROM provinces";
    $params = [];
    $types = "";

    // Tambahkan kondisi WHERE jika ada pencarian (berdasarkan ID atau nama)
    if (!empty($search)) {
        // Cek apakah search adalah angka (ID) atau teks (nama)
        if (is_numeric($search)) {
            // Pencarian berdasarkan ID
            $sql .= " WHERE id = ?";
            $countQuery .= " WHERE id = ?";
            $params[] = (int)$search;
            $types .= "i";
        } else {
            // Pencarian berdasarkan nama
            $sql .= " WHERE `name` LIKE ?";
            $countQuery .= " WHERE `name` LIKE ?";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $types .= "s";
        }
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii";

    // Execute main query
    $result = executeSecureQuery($conn, $sql, $params, $types);

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam mengambil data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => (string)$row['id'], "nama" => $row['name']];
    }

    // Execute count query
    $count_params = [];
    $count_types = "";

    if (!empty($search)) {
        if (is_numeric($search)) {
            $count_params[] = (int)$search;
            $count_types = "i";
        } else {
            $count_params[] = $searchTerm;
            $count_types = "s";
        }
    }

    $countResult = executeSecureQuery($conn, $countQuery, $count_params, $count_types);

    if ($countResult === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam menghitung data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $countData = $countResult->fetch_assoc();
    $totalRows = isset($countData['total']) ? (int)$countData['total'] : 0;

    // Format response JSON dengan sanitasi
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => sanitizeOutput($lokasi),
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getKota') {
    // Validasi dan sanitasi input parameters
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = isset($_GET['page_size']) ? min(100, max(1, (int)$_GET['page_size'])) : 5;
    $provinceId = isset($_GET['province_id']) ? (int)$_GET['province_id'] : null;

    // Validasi search parameter
    if (!empty($search) && !preg_match('/^[a-zA-Z0-9\s\-_\.]+$/', $search)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Format pencarian tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi province_id
    if ($provinceId !== null && ($provinceId <= 0 || $provinceId > 99999)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "ID provinsi tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi page untuk mencegah overflow
    if ($page > 10000) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Halaman tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $offset = ($page - 1) * $perPage;

    // Query dasar
    $sql = "SELECT id, `name` FROM regencies";
    $countQuery = "SELECT COUNT(*) as total FROM regencies";
    $params = [];
    $types = "";

    // Tambahkan filter berdasarkan province_id jika ada
    if ($provinceId) {
        $sql .= " WHERE province_id = ?";
        $countQuery .= " WHERE province_id = ?";
        $params[] = $provinceId;
        $types .= "i";
    }

    // Tambahkan filter berdasarkan search jika ada
    if (!empty($search)) {
        // Cek apakah search adalah angka (ID) atau teks (nama)
        if (is_numeric($search)) {
            // Pencarian berdasarkan ID
            if (strpos($sql, "WHERE") !== false) {
                $sql .= " AND id = ?";
                $countQuery .= " AND id = ?";
            } else {
                $sql .= " WHERE id = ?";
                $countQuery .= " WHERE id = ?";
            }
            $params[] = (int)$search;
            $types .= "i";
        } else {
            // Pencarian berdasarkan nama
            if (strpos($sql, "WHERE") !== false) {
                $sql .= " AND `name` LIKE ?";
                $countQuery .= " AND `name` LIKE ?";
            } else {
                $sql .= " WHERE `name` LIKE ?";
                $countQuery .= " WHERE `name` LIKE ?";
            }
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $types .= "s";
        }
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $count_params = $params; // Copy params for count query
    $count_types = $types;   // Copy types for count query
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii";

    // Execute main query
    $result = executeSecureQuery($conn, $sql, $params, $types);

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam mengambil data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => (string)$row['id'], "nama" => $row['name']];
    }

    // Execute count query
    $countResult = executeSecureQuery($conn, $countQuery, $count_params, $count_types);

    if ($countResult === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam menghitung data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $countData = $countResult->fetch_assoc();
    $totalRows = isset($countData['total']) ? (int)$countData['total'] : 0;

    // Format response JSON dengan sanitasi
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => sanitizeOutput($lokasi),
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

if ($func == 'getKecamatan') {
    // Validasi dan sanitasi input parameters
    $search = isset($_GET['q']) ? trim($_GET['q']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = isset($_GET['page_size']) ? min(100, max(1, (int)$_GET['page_size'])) : 5;
    $regencyId = isset($_GET['regency_id']) ? (int)$_GET['regency_id'] : null;

    // Validasi search parameter
    if (!empty($search) && !preg_match('/^[a-zA-Z0-9\s\-_\.]+$/', $search)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Format pencarian tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi regency_id
    if ($regencyId !== null && ($regencyId <= 0 || $regencyId > 99999)) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "ID kabupaten/kota tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    // Validasi page untuk mencegah overflow
    if ($page > 10000) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Halaman tidak valid",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $offset = ($page - 1) * $perPage;

    // Query dasar
    $sql = "SELECT id, `name` FROM districts";
    $countQuery = "SELECT COUNT(*) as total FROM districts";
    $params = [];
    $types = "";

    // Tambahkan filter berdasarkan regency_id jika ada
    if ($regencyId) {
        $sql .= " WHERE regency_id = ?";
        $countQuery .= " WHERE regency_id = ?";
        $params[] = $regencyId;
        $types .= "i";
    }

    // Tambahkan filter berdasarkan search jika ada
    if (!empty($search)) {
        // Cek apakah search adalah angka (ID) atau teks (nama)
        if (is_numeric($search)) {
            // Pencarian berdasarkan ID
            if (strpos($sql, "WHERE") !== false) {
                $sql .= " AND id = ?";
                $countQuery .= " AND id = ?";
            } else {
                $sql .= " WHERE id = ?";
                $countQuery .= " WHERE id = ?";
            }
            $params[] = (int)$search;
            $types .= "i";
        } else {
            // Pencarian berdasarkan nama
            if (strpos($sql, "WHERE") !== false) {
                $sql .= " AND `name` LIKE ?";
                $countQuery .= " AND `name` LIKE ?";
            } else {
                $sql .= " WHERE `name` LIKE ?";
                $countQuery .= " WHERE `name` LIKE ?";
            }
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $types .= "s";
        }
    }

    // Tambahkan limit dan offset untuk pagination
    $sql .= " LIMIT ?, ?";
    $count_params = $params; // Copy params for count query
    $count_types = $types;   // Copy types for count query
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii";

    // Execute main query
    $result = executeSecureQuery($conn, $sql, $params, $types);

    if ($result === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam mengambil data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $lokasi = [];
    while ($row = $result->fetch_assoc()) {
        $lokasi[] = ["id" => (string)$row['id'], "nama" => $row['name']];
    }

    // Execute count query
    $countResult = executeSecureQuery($conn, $countQuery, $count_params, $count_types);

    if ($countResult === false) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Terjadi kesalahan dalam menghitung data",
            "data" => [],
            "page" => $page,
            "page_size" => $perPage,
            "total_data" => 0
        ], JSON_PRETTY_PRINT);
        exit;
    }

    $countData = $countResult->fetch_assoc();
    $totalRows = isset($countData['total']) ? (int)$countData['total'] : 0;

    // Format response JSON dengan sanitasi
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => sanitizeOutput($lokasi),
        "page" => $page,
        "page_size" => $perPage,
        "total_data" => $totalRows
    ];

    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

// Jika sampai sini berarti function tidak valid
header('Content-Type: application/json');
echo json_encode([
    'success' => false,
    'message' => 'Function tidak ditemukan'
], JSON_PRETTY_PRINT);

$conn->close();
exit;
