# JWT Implementation - Company Login API

## Overview

Implementasi JWT (JSON Web Token) pada sistem login company yang menggantikan session token tradisional dengan token yang lebih aman dan scalable.

## Perubahan dari Session Token ke JWT

### Sebelum (Session Token)

```json
{
  "status": true,
  "message": "Login berhasil.",
  "data": [
    {
      "id": "user_id",
      "nama": "User Name",
      "email": "<EMAIL>",
      "token": "64_character_session_token"
      // ... other fields
    }
  ]
}
```

### Sesudah (JWT Token)

```json
{
  "status": true,
  "message": "Login berhasil.",
  "data": [
    {
      "id": "user_id",
      "nama": "User Name",
      "email": "<EMAIL>",
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "token_type": "Bearer",
      "expires_in": 86400
      // ... other fields
    }
  ]
}
```

## Fitur JWT Implementation

### 1. Access Token

- **Duration**: 24 hours
- **Algorithm**: HS256
- **Purpose**: Autentikasi API requests
- **Storage**: Client-side memory/secure storage

### 2. Refresh Token

- **Duration**: 7 days
- **Algorithm**: HS256
- **Purpose**: Generate new access token
- **Storage**: Database + Client-side secure storage

### 3. Token Payload Structure

```json
{
  "iss": "digitalcv",
  "aud": "company",
  "iat": 1641024000,
  "exp": 1641110400,
  "data": {
    "id": "user_id",
    "id_koordinator": "koordinator_id",
    "nama": "User Name",
    "email": "<EMAIL>",
    "divisi": "IT",
    "label": "Company Name",
    "tipe": "company",
    "role": "admin",
    "paket": "premium",
    "no_hp": "081234567890",
    "fitur_user": ["dashboard", "reports"],
    "fcm_token": "fcm_token_here"
  }
}
```

## API Endpoints

### 1. Login

**Endpoint**: `POST /api/mobile/company/login.php`

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "koordinat": "-6.200000,106.816666",
  "device": "mobile",
  "ip": "***********"
}
```

**Success Response**:

```json
{
  "status": true,
  "message": "Login berhasil.",
  "data": [
    {
      "id": "user_id",
      "id_koordinator": "koordinator_id",
      "divisi": "IT",
      "nama": "User Name",
      "email": "<EMAIL>",
      "img": "profile.jpg",
      "label": "Company Name",
      "tipe": "company",
      "role": "admin",
      "paket": "premium",
      "no_hp": "081234567890",
      "fitur_user": ["dashboard", "reports"],
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "token_type": "Bearer",
      "expires_in": 86400,
      "fcm_token": "fcm_token_here"
    }
  ]
}
```

### 2. Refresh Token

**Endpoint**: `POST /api/mobile/company/refresh-token.php`

**Request Body**:

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response**:

```json
{
  "status": true,
  "message": "Token berhasil direfresh",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 86400
  }
}
```

### 3. Logout

**Endpoint**: `POST /api/mobile/company/logout.php`

**Headers**:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Success Response**:

```json
{
  "status": true,
  "message": "Logout berhasil",
  "data": []
}
```

### 4. Verify Token

**Endpoint**: `GET /api/mobile/company/verify-token.php`

**Headers**:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Success Response**:

```json
{
  "status": true,
  "message": "Token valid",
  "data": {
    "id": "user_id",
    "nama": "User Name",
    "email": "<EMAIL>"
    // ... other user data
  }
}
```

## Client-Side Implementation

### 1. Login dan Simpan Token

```javascript
// Login request
const loginResponse = await fetch("/api/mobile/company/login.php", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    email: "<EMAIL>",
    password: "password123",
    koordinat: "-6.200000,106.816666",
    device: "mobile",
    ip: "***********",
  }),
});

const data = await loginResponse.json();

if (data.status) {
  // Simpan tokens
  localStorage.setItem("access_token", data.data[0].access_token);
  localStorage.setItem("refresh_token", data.data[0].refresh_token);

  // Simpan user data
  localStorage.setItem("user_data", JSON.stringify(data.data[0]));
}
```

### 2. Gunakan Access Token untuk API Calls

```javascript
// API request with JWT
const apiResponse = await fetch("/api/mobile/company/some-endpoint.php", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${localStorage.getItem("access_token")}`,
    "Content-Type": "application/json",
  },
});
```

### 3. Auto Refresh Token

```javascript
async function refreshAccessToken() {
  const refreshToken = localStorage.getItem("refresh_token");

  const response = await fetch("/api/mobile/company/refresh-token.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      refresh_token: refreshToken,
    }),
  });

  const data = await response.json();

  if (data.status) {
    localStorage.setItem("access_token", data.data.access_token);
    return data.data.access_token;
  } else {
    // Refresh token expired, redirect to login
    window.location.href = "/login";
  }
}

// Interceptor untuk auto refresh
async function apiCall(url, options = {}) {
  let response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      Authorization: `Bearer ${localStorage.getItem("access_token")}`,
    },
  });

  if (response.status === 401) {
    // Token expired, try refresh
    const newToken = await refreshAccessToken();

    // Retry with new token
    response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${newToken}`,
      },
    });
  }

  return response;
}
```

## Database Changes

### Kolom Baru di tabel `koordinator_pic`

```sql
ALTER TABLE koordinator_pic
ADD COLUMN refresh_token TEXT NULL;

-- Index untuk performa
CREATE INDEX idx_refresh_token
ON koordinator_pic (refresh_token(255));
```

## Security Features

### 1. Token Validation

- Signature verification dengan secret key
- Expiration time checking
- Issuer dan audience validation
- Payload structure validation

### 2. Environment Variables

```env
ACCESS_TOKEN_SECRET=your_64_character_secret_key_here
REFRESH_TOKEN_SECRET=your_64_character_refresh_secret_key_here
```

### 3. Error Handling

- Comprehensive error codes
- Proper HTTP status codes
- Audit logging untuk security events

## Migration Guide

### 1. Update Existing API Endpoints

Ganti authentication dari session token ke JWT:

```php
// Sebelum
if (isset($_POST['id_koordinator'])) {
    $id_koordinator = $_POST['id_koordinator'];
}

// Sesudah
include '../../jwt_helper.php';
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
```

### 2. Update Client Applications

- Ganti `token` field dengan `access_token`
- Implementasikan refresh token mechanism
- Update header authorization format

### 3. Backward Compatibility

Untuk transisi smooth, bisa support kedua format:

```php
// Support both session token and JWT
$userData = null;

// Try JWT first
try {
    $userData = requireAuth();
} catch (Exception $e) {
    // Fallback to session token
    if (isset($_POST['token'])) {
        // Validate session token logic
    }
}
```

## Testing

### Manual Testing

```bash
# Test login
curl -X POST http://localhost/api/mobile/company/login.php \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl -X GET http://localhost/api/mobile/company/verify-token.php \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Test refresh token
curl -X POST http://localhost/api/mobile/company/refresh-token.php \
  -H "Content-Type: application/json" \
  -d '{"refresh_token":"YOUR_REFRESH_TOKEN"}'
```

### Automated Testing

```bash
php test_jwt_login.php
```

## Best Practices

### 1. Token Storage

- **Access Token**: Memory atau sessionStorage
- **Refresh Token**: httpOnly cookie atau secure storage
- **Never**: Plain localStorage untuk production

### 2. Token Rotation

- Rotate refresh tokens pada setiap penggunaan
- Implement refresh token blacklisting
- Monitor suspicious refresh patterns

### 3. Security Headers

```php
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000');
```

## Troubleshooting

### Common Issues

1. **"Token tidak valid"**: Periksa secret key di .env
2. **"Token expired"**: Implementasikan auto refresh
3. **"Signature invalid"**: Pastikan secret key konsisten
4. **Database error**: Pastikan kolom refresh_token ada

### Debug Mode

```php
// Enable untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log JWT errors
error_log('JWT Error: ' . $exception->getMessage());
```

Implementasi JWT untuk company login telah selesai dan siap digunakan!
