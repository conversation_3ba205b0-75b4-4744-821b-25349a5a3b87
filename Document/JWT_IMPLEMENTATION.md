# Implementasi JWT Authentication untuk DigitalCV

## Overview

Implementasi JWT (JSON Web Token) authentication telah ditambahkan ke sistem DigitalCV untuk memberikan keamanan yang lebih baik pada API mobile kandidat.

## Files yang Dibuat/Dimodifikasi

### 1. Modified Files

- `api/mobile/candidate/login.php` - Ditambahkan JWT token generation
- `.env` - Berisi JWT secret keys

### 2. New Files

- `api/jwt_helper.php` - Helper functions untuk JWT
- `api/mobile/candidate/refresh-token.php` - Endpoint untuk refresh access token
- `api/mobile/candidate/get-profile.php` - Contoh endpoint yang dilindungi JWT
- `api/mobile/candidate/logout.php` - Endpoint untuk logout dan invalidate token
- `database_update.sql` - SQL untuk update database schema

## Database Changes

Tambahkan kolom berikut ke tabel `users_kandidat`:

```sql
ALTER TABLE users_kandidat
ADD COLUMN refresh_token TEXT NULL,
ADD COLUMN refresh_token_expiry DATETIME NULL;
```

## JWT Configuration

File `.env` berisi:

```
ACCESS_TOKEN_SECRET=dxxp483NevIj6kXobemgQq9Dbg1dnSlof3il4UkBhwCXFcjhBJ
REFRESH_TOKEN_SECRET=vf8iPYU5q6fpKJcv14sonnlzII8XXclYUc69AXwkH958Yc5xJC
```

## API Endpoints

### 1. Login

**POST** `/api/mobile/candidate/login.php`

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "koordinat": "optional",
  "device": "optional",
  "ip": "optional"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Login berhasil.",
  "data": [
    {
      "pin": "12345",
      "nama": "John Doe",
      "email": "<EMAIL>",
      "img": "profile_url",
      "no_telp": "08123456789",
      "tempat_lahir": "Jakarta",
      "tgl_lahir": "1 Januari 1990",
      "jenis_kelamin": "L",
      "alamat": "Jl. Contoh No. 1",
      "fcm_token": "fcm_token",
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
  ]
}
```

### 2. Refresh Token

**POST** `/api/mobile/candidate/refresh-token.php`

**Request Body:**

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response:**

```json
{
  "success": true,
  "message": "Access token berhasil diperbarui",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 86400
  }
}
```

### 3. Protected Endpoint (Get Profile)

**GET** `/api/mobile/candidate/get-profile.php`

**Headers:**

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**

```json
{
  "success": true,
  "message": "Data profil berhasil diambil",
  "data": {
    "pin": "12345",
    "nama": "John Doe",
    "email": "<EMAIL>",
    "no_telp": "08123456789",
    "img": "profile_url",
    "tempat_lahir": "Jakarta",
    "tgl_lahir": "1 Januari 1990",
    "jenis_kelamin": "L",
    "alamat": "Jl. Contoh No. 1",
    "fcm_token": "fcm_token"
  }
}
```

### 4. Logout

**POST** `/api/mobile/candidate/logout.php`

**Headers:**

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**

```json
{
  "success": true,
  "message": "Logout berhasil",
  "data": []
}
```

## JWT Helper Functions

### 1. verifyAccessToken($token)

Memverifikasi access token dan mengembalikan data user jika valid.

### 2. verifyRefreshToken($token)

Memverifikasi refresh token dan mengembalikan data user jika valid.

### 3. generateNewAccessToken($userData)

Generate access token baru dengan masa berlaku 24 jam.

### 4. requireAuth()

Middleware function untuk melindungi endpoint. Akan return data user jika token valid, atau mengirim response 401 jika tidak valid.

### 5. getBearerToken()

Mengambil token dari Authorization header.

## Token Expiration

- **Access Token**: 24 jam
- **Refresh Token**: 30 hari

## Security Features

1. Access token dan refresh token menggunakan secret key yang berbeda
2. Refresh token disimpan di database dan dapat di-invalidate saat logout
3. Token expiration checking
4. Authorization header validation
5. Proper error handling

## Usage in Mobile App

1. Login untuk mendapatkan access_token dan refresh_token
2. Gunakan access_token di Authorization header untuk semua API calls
3. Jika access_token expired, gunakan refresh_token untuk mendapatkan access_token baru
4. Jika refresh_token juga expired, user harus login ulang
5. Saat logout, refresh_token akan di-invalidate

## Error Responses

```json
{
  "success": false,
  "message": "Token tidak valid: Token signature could not be verified."
}
```

```json
{
  "success": false,
  "message": "Access token diperlukan"
}
```

```json
{
  "success": false,
  "message": "Token sudah expired"
}
```

## Important Notes

1. **Database Update Required**: Pastikan menjalankan SQL script di `database_update.sql` untuk menambahkan kolom refresh_token
2. **Path Issues**: Beberapa file mungkin perlu penyesuaian path untuk autoload dan dotenv
3. **JWT Version**: Menggunakan Firebase JWT versi 5.0 dengan syntax yang sesuai
4. **Error Handling**: Implementasi ini sudah mencakup proper error handling dan logging
5. **Security**: Token menggunakan HS256 algorithm dengan secret key yang kuat

## Testing

Untuk testing implementasi JWT:

1. **Test Login**:

   ```bash
   curl -X POST http://localhost/api/mobile/candidate/login.php \
   -H "Content-Type: application/json" \
   -d '{"email": "<EMAIL>", "password": "password123"}'
   ```

2. **Test Protected Endpoint**:

   ```bash
   curl -X GET http://localhost/api/mobile/candidate/get-profile.php \
   -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
   ```

3. **Test Refresh Token**:
   ```bash
   curl -X POST http://localhost/api/mobile/candidate/refresh-token.php \
   -H "Content-Type: application/json" \
   -d '{"refresh_token": "YOUR_REFRESH_TOKEN"}'
   ```

## Next Steps

1. Jalankan SQL update untuk database
2. Test semua endpoint
3. Implementasikan JWT middleware di endpoint lain yang memerlukan autentikasi
4. Sesuaikan error handling sesuai kebutuhan aplikasi
