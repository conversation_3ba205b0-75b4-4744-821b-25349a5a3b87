<?php
ini_set('max_execution_time', 300);
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../../model/database.php";
include '../../../api/s3.php';
include '../../jwt_helper.php';

// Require authentication and get user data from JWT
$userData = requireAuth();

function decrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $data = strtr($data, '-_,', '+/=');
    $decoded = base64_decode($data);
    $iv = substr($decoded, 0, 16);
    $encrypted = substr($decoded, 16);
    return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
}

function tgl_indo($tanggal)
{
    $bulan = array(
        1 => '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        'April',
        '<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'September',
        '<PERSON>tober',
        'November',
        'Desember'
    );

    $pecah = explode('-', $tanggal);
    $tahun = $pecah[0];
    $bulan = $bulan[(int)$pecah[1]];
    $hari = ltrim($pecah[2], '0'); // hapus angka 0 di depan (misal 01 jadi 1)

    return $hari . ' ' . $bulan . ' ' . $tahun;
}

$q = 'false';
$tempAkses = "true";

// Check if 'q' parameter exists in GET request (encrypted candidate ID)
if (isset($_GET['q'])) {
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
    $q = decrypt_url($_GET['q'], $key);

    if ($q != "") {
        // cek apakah q terdaftar
        $get = $conn->prepare("SELECT
                                uk.nama_lengkap as nama,
                                uk.foto,
                                rh.jenis_kelamin,
                                rh.tempat_lahir,
                                rh.tgl_lahir,
                                rh.ktp,
                                rh.no_telepon,
                                rh.sim,
                                rh.email,
                                rh.status_pernikahan,
                                rh.alamat,
                                rh.rt,
                                rh.rw,
                                rh.kecamatan,
                                rh.kota,
                                rh.kode_pos,
                                rh.pengalaman_kerja,
                                rh.minat_gaji,
                                rh.minat_lokasi_kerja,
                                rh.perjalanan_dinas,
                                rh.bahasa_asing,
                                rh.kelebihan,
                                rh.kekurangan,
                                rh.ilmu_komputerisasi,
                                rh.memimpin_tim,
                                rh.kemampuan_presentasi,
                                rh.lingkup_pekerjaan,
                                rh.organisasi
                            FROM
                                users_kandidat	uk
                                JOIN rh ON uk.pin = rh.id
                            WHERE
                                rh.id = ?");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $row = mysqli_fetch_array($result);
            $nama = strtoupper($row['nama']);
            if ($row['foto'] != '') {
                if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $row['foto'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'kandidat/foto-profil/' . $row['foto']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+24 hours');
                    $img = (string) $request->getUri();

                    $pas_foto = '<img width="70" height="80" src="' . $img . '" class="user-image" alt=".">';
                } else {
                    $pas_foto = '<img width="70" height="80" src="' . $row['foto'] . '" referrerpolicy="no-referrer" onerror="this.src=\'../../../assets/images/content/img-avatar-default.png\';" class="user-image" alt=".">';
                }
            } else {
                $pas_foto = '<img width="70" height="80" src="../../../assets/images/content/img-avatar-default.png" class="user-image" alt=".">';
            }

            $jenis_kelamin = $row['jenis_kelamin'];
            $tempat_lahir = $row['tempat_lahir'];
            $tgl_lahir = tgl_indo($row['tgl_lahir']);
            $ktp = $row['ktp'];
            $no_telepon = $row['no_telepon'];
            $sim = str_replace(",", ", ", $row['sim']);
            $email = $row['email'];
            $status_pernikahan = $row['status_pernikahan'];
            $alamat = $row['alamat'];
            $rt_rw = $row['rt'] . "/" . $row['rw'];
            $kecamatan  = ucwords(strtolower($row['kecamatan']));
            $kota = ucwords(strtolower($row['kota']));
            $kode_pos = $row['kode_pos'];
            $pengalaman_kerja = $row['pengalaman_kerja'];
            if ($row['minat_gaji'] > 0) {
                $minat_gaji = number_format($row['minat_gaji'], 0, ".", ".");
            } else {
                $minat_gaji = 0;
            }
            $minat_lokasi_kerja = ucwords(strtolower($row['minat_lokasi_kerja']));
            $perjalanan_dinas = $row['perjalanan_dinas'];
            $bahasa_asing = $row['bahasa_asing'];
            $kelebihan = str_replace(",", ", ", $row['kelebihan']);
            $kekurangan = str_replace(",", ", ", $row['kekurangan']);
            $ilmu_komputerisasi = explode(",", $row['ilmu_komputerisasi']);
            $arr_memimpin = array("1" => 'Tidak Pernah', "2" => '1 - 3 Orang', "3" => '4 - 10 Orang', "4" => '11 - 50 Orang', "5" => 'Lebih dari 50 Orang');
            $memimpin_tim = $arr_memimpin[$row['memimpin_tim']];
            if ($row['kemampuan_presentasi'] == '1') {
                $kemampuan_presentasi = 'Kurang';
            } else if ($row['kemampuan_presentasi'] == '2') {
                $kemampuan_presentasi = 'Cukup';
            } else if ($row['kemampuan_presentasi'] == '3') {
                $kemampuan_presentasi = 'Sangat Baik';
            } else {
                $kemampuan_presentasi = '-';
            }
            $lingkup_pekerjaan = $row['lingkup_pekerjaan'];
            $organisasi = $row['organisasi'];

            // Get user data from JWT
            $id_koordinator = $userData->id_koordinator;
            $divisi = $userData->divisi;
            $user_id = $userData->id;

            // simpan log aktivitas
            $messages = 'Menampilkan digital cv kandidat ' . $q . ' atas nama ' . $nama . '.';
            $extra_info = "HRD";
            $level = "INFO";
            $path = $_SERVER['REQUEST_URI'];
            logActivity($conn, $user_id, $level, $messages, $extra_info);
        } else {
            $tempAkses = "false";
        }
    } else {
        $tempAkses = "false";
    }
} else {
    $tempAkses = "false";
}

if ($tempAkses == 'false') {
    echo '<script>alert("Anda tidak dapat mengakses halaman ini."); window.close();</script>';
}

$paket = "-";
if ($extra_info == "HRD") {
    $id_koordinator = $userData->id_koordinator;
    // cek apakah q terdaftar
    $get = $conn->prepare("SELECT paket
                            FROM
                                koordinator
                            WHERE
                                id_koordinator = ? ");
    $get->bind_param("s", $id_koordinator);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $paket = $row['paket'];
    }
}
?>

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?= $nama ?></title>
    <!-- Icon -->
    <link rel="icon" type="image/png" href="../../assets/images/logo/logo.png" />
    <style>
        .row {
            display: flex;
            flex-wrap: wrap;
            align-items: baseline;
            max-width: 1000px;
        }

        .long-text {
            width: 50%;
            text-align: justify;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
        }

        .col-0 {
            width: 1%;
        }

        .col-1 {
            width: 8.333333%;
        }

        .col-2 {
            width: 16.666667%;
        }

        .col-3 {
            width: 25%;
        }

        .col-4 {
            width: 33.333333%;
        }

        .col-5 {
            width: 41.666667%;
        }

        .col-6 {
            width: 50%;
        }

        .col-7 {
            width: 58.333333%;
        }

        .col-8 {
            width: 66.666667%;
        }

        .col-9 {
            width: 75%;
        }

        .col-10 {
            width: 83.333333%;
        }

        .col-11 {
            width: 91.666667%;
        }

        .col-12 {
            width: 100%;
            max-width: 1000px;
        }

        .table-container {
            overflow: visible;
            word-wrap: break-word;
        }

        .page_break {
            page-break-before: always !important;
            page-break-after: always !important;

        }

        .page_break_avoid {
            page-break-before: avoid;
        }

        .data-table {
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            -moz-border-collapse: collapse;
            box-sizing: border-box;
            background: white !important;
        }

        .long-text {
            text-align: justify;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .data-table thead tr {
            background-color: #2c3e50 !important;
            color: #ffffff !important;
        }

        .no-right-border {
            border-right: none;
        }

        .p-5 {
            padding: 4px;
        }

        .p-2 {
            padding: 2px;
        }

        .data-table th,
        .data-table td {
            font-family: Times New Roman, sans-serif;
            text-transform: capitalize;
            word-wrap: break-word;
        }

        .data-table th {
            font-size: 16px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .data-table tbody tr {
            border-bottom: 0px solid #000;
        }

        .border-right {
            border-right: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-right {
            border-top: 1px #000000 solid !important;
            border-left: 1px #000000 solid !important;
            border-bottom: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-left {
            border-top: 1px #000000 solid !important;
            border-right: 1px #000000 solid !important;
            border-bottom: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-bottom {
            border-bottom: 0px !important;
            box-sizing: border-box;
        }


        .border {
            box-sizing: border-box;
            border: 1px #000000 solid !important;
            -moz-box-sizing: border-box;
        }

        .no-border {
            border: 0px solid;
        }

        .qa-item {
            display: grid;
            grid-template-columns: 15px 1fr;
            width: 100%;
            /* Full width */
            page-break-inside: avoid;
        }

        .qa-content {
            display: flex;
            flex-direction: column;
        }

        @media print {
            .footer {
                position: absolute;
                right: 0;
                font-size: 12px;
                background-color: #DBDBDB;
                color: black;
                padding: 5px 10px;
                border-radius: 5px;
            }


            div {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
                page-break-before: auto !important;
            }


            * {
                @include box-shadow(none);
                @include text-shadow(none);
                clear: both;
            }


            @page {
                size: A4 portrait;
                margin: 7mm;
                font-size: 10px;

                @bottom-right {

                    font-weight: bold;
                    font-size: 10px;
                    content: "Halaman " counter(page) " dari " counter(pages);
                    position: relative;
                }

            }



            div {
                page-break-inside: avoid;
                page-break-after: auto;
                page-break-before: auto;
            }

            body {
                margin: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                background: white !important;
            }

            .avoid-break {
                break-inside: avoid;
                /* Prevents breaking inside an element */
            }

            .page_break {
                page-break-before: always !important;
                page-break-after: always !important;
                break-before: always !important;
                /* Modern alternative */
                break-after: always !important;
                /* Modern alternative */
            }

            .page_break_avoid {
                page-break-before: avoid;
                break-inside: avoid !important;
                /* Modern alternative */
                page-break-after: avoid;
            }

            .table-container {
                overflow: visible;
                border-collapse: collapse;
                -moz-border-collapse: collapse;
            }

            .data-table {
                width: 100% !important;
            }

            .border-no-right {
                -moz-box-sizing: border-box;
                border-top: 1px #000000 solid !important;
                border-left: 1px #000000 solid !important;
                border-bottom: 1px #000000 solid !important;
                box-sizing: border-box !important;

            }

            .border-no-left {
                -moz-box-sizing: border-box;
                border-top: 1px #000000 solid !important;
                border-right: 1px #000000 solid !important;
                border-bottom: 1px #000000 solid !important;
                box-sizing: border-box !important;
            }

            .data-table th {
                text-transform: capitalize;
            }

            .data-table th tr td {
                vertical-align: middle;
                overflow-wrap: break-word !important;
                white-space: normal;
                color: black !important;
            }

            .number {
                float: left;
                margin-right: 10px;
            }

            .question-text {
                margin-left: 25px;
            }

            .answer {
                margin-left: 25px;
                font-style: italic;
                color: #444;
            }

            .spacer {
                height: 10px;
                page-break-after: auto;
            }


            .col-0 {
                width: 1%;
            }

            .col-1 {
                width: 8.333333%;
            }

            .col-2 {
                width: 16.666667%;
            }

            .col-3 {
                width: 25%;
            }

            .col-4 {
                width: 33.333333%;
            }

            .col-5 {
                width: 41.666667%;
            }

            .col-6 {
                width: 50%;
            }

            .col-7 {
                width: 58.333333%;
            }

            .col-8 {
                width: 66.666667%;
            }

            .col-9 {
                width: 75%;
            }

            .col-10 {
                width: 83.333333%;
            }

            .col-11 {
                width: 91.666667%;
            }

            .col-12 {
                width: 100%;
            }

            .p-5 {
                padding: 4px;
            }

            .p-2 {
                padding: 2px;
            }


            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }

            border {
                -moz-box-sizing: border-box !important;
                page-break-before: always !important;
                box-sizing: border-box !important;
            }

            td {
                page-break-inside: avoid;
                word-wrap: break-word;
            }
        }


        .blurry-wrapper {
            position: relative;
            display: inline-block;
            cursor: help;
            padding: 2px 0;
        }

        .blurry-text {
            filter: blur(4px);
            transition: filter 0.3s ease-out;
            color: #000;
        }

        .info-text {
            position: absolute;
            top: 100%;
            left: 0;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
            background-color: rgba(0, 0, 0, 0.85);
            color: #fff;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            z-index: 10;
            pointer-events: none;
            transform: translateY(5px);
        }

        .blurry-wrapper:hover .info-text {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .link-example .blurry-text {
            color: #007bff;
            font-weight: bold;
        }

        .link-example .info-text {
            background-color: #007bff;
        }

        .blurry-wrapper .info-text.info-top {
            top: auto;
            bottom: 100%;
            transform: translateY(-5px);
        }

        .blurry-wrapper:hover .info-text.info-top {
            transform: translateY(0);
        }

        a.blurry-wrapper {
            text-decoration: none;
        }
    </style>
</head>

<body>
    <div class="table-container">
        <div class="row" style="margin-bottom:10px;">
            <div class="col-9 border-no-right">
                <div class="row" style="align-items :center; padding-top: 5px; padding-bottom:5px;">
                    <div class="col-6">
                        <img width="80" height="80" src="../../assets/images/logo/logoDcv2.png" class="user-image" alt="." style="margin-left: 20px; text-align:center;">
                    </div>
                    <div class="col-6">
                        <h3 style="margin: 0;">DIGITAL CV</h3>
                    </div>
                </div>
            </div>
            <div class="col-3 border">
                <div class="row" style="text-align :center; padding-top: 5px; padding-bottom:5px;">
                    <div class="col-12"><?= $pas_foto ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-container">
        <table class="data-table">
            <tr>
                <p class="col-12 p-2" style="background-color: #0d3b72;color:white;padding:5px;"><strong>I. <?= strtoupper(translate('Identitas')) ?></strong></p>
            </tr>
            <tr>
                <div class="row border " style="border-bottom:0px !important">
                    <div class="col-6 border-right ">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Nama Lengkap') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($nama) ?></div>
                        </div>
                    </div>
                    <div class="col-6 ">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Jenis Kelamin') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($jenis_kelamin) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-bottom:0px !important">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Tempat Lahir') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($tempat_lahir) ?></div>
                        </div>
                    </div>
                    <div class="col-6 ">
                        <div class="row  p-2">
                            <div class="col-5"><?= translate('Tanggal Lahir') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($tgl_lahir) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-bottom:0px !important">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('SIM') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($sim) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Nomor Handphone') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <?php
                            if ($paket == 'C1') {
                                echo '<span class="blurry-wrapper">
                                        <span class="blurry-text">
                                            ????????????????
                                        </span>
                                        <span class="info-text info-top">
                                            ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                        </span>
                                    </span>';
                            } else {
                                echo '<div class="col-6">' . htmlspecialchars($no_telepon) . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Status Pernikahan') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($status_pernikahan) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Alamat Email') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <?php
                            if ($paket == 'C1') {
                                echo '<span class="blurry-wrapper">
                                        <span class="blurry-text">
                                            ????????????????
                                        </span>
                                        <span class="info-text info-top">
                                            ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                        </span>
                                    </span>';
                            } else {
                                echo '<div class="col-6">' . htmlspecialchars($email) . '</div>';
                            }
                            ?>

                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-top: 0px !important;border-bottom: 0px !important;">
                    <div class="col-12">
                        <div class="row p-2">
                            <div class="col-11"><?= translate('Alamat Tinggal (Alamat Surat)') ?></div>
                            <div class="col-1"></div>
                        </div>
                    </div>
                </div>
            <tr>
                <div class="row border" style="border-top: 0px !important;border-bottom: 0px !important;">
                    <div class="col-12">
                        <div class="row p-2" style="margin-top:5px;">
                            <div class="col-12"><?= htmlspecialchars($alamat) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-top: 0px !important;">
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5" style="margin-top: 10px;"><?= translate('RT / RW') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6" style="margin-top: 10px;"><?= htmlspecialchars($rt_rw) ?></div>
                            <div class="col-5"><?= translate('Kec.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kecamatan) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Kota / Kab.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kota) ?></div>
                            <div class="col-5"><?= translate('Kode Pos.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kode_pos) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <td class="p-5"></td>
            </tr>
        </table>
    </div>

    <?php
    // cek apakah ada data pendidikan formal
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_pendidikan rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
    ?>
        <div class="table-container">
            <div>
                <p class="col-12 p-2" style="background-color: #0d3b72;color:white;padding:5px;"><strong>II. PENDIDIKAN</strong></p>
            </div>

            <div>
                <td class=" p-2" colspan="9" style="height: 10%;">1. Pendidikan Formal</td>
            </div>
            <table class="data-table">
                <tr>
                    <th class="border" style="width: 10%;">Jenjang</th>
                    <th class="border" style="width: 25%;">Nama & Tempat (Kota)</th>
                    <th class="border" style="width: 20%;">Prog. Studi / Jurusan</th>
                    <th class="border" style="width: 20%;">Tahun</th>
                    <th class="border" style="width: 10%;">Keterangan</th>
                </tr>
                <?php
                while ($row = mysqli_fetch_array($result)) {
                    echo '<tr>
                            <td class="border" align="center">' . $row['jenjang'] . '</td>
                            <td class="border" align="center">' . $row['nama_sekolah'] . '</td>
                            <td class="border" align="center">' . $row['jurusan'] . '</td>
                            <td class="border" align="center">' . $row['tahun_mulai'] . ' s/d ' . $row['tahun_selesai'] . '</td>
                            <td class="border" align="center">' . $row['ket'] . '</td>
                        </tr>';
                }
                ?>
                <div>
                    <td class=" p-2" colspan="9" style="height: 10%;"></td>
                </div>
            </table>
        </div>
    <?php
    }

    // cek apakah ada data pendidikan formal
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_kursus rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY rp.tgl_mulai");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    echo '<div class="table-container">
            <table  class="data-table">';
    if ($result->num_rows > 0) {
        echo '<tr>
                <td class="p-5" colspan="9" style="height: 10%;"></td>
            </tr>
            <tr>
                <td class="p-2" colspan="9">2. Pendidikan Non Formal (Pelatihan / Training)</td>
            </tr>
            <tr>
                <th class="border p-2" width="30%">Pelatihan / Training</th>
                <th class="border p-2" width="20%">Tahun</th>
                <th class="border p-2" width="20%">Kota</th>
                <th class="border p-2" width="10%" >Sertifikat</th>
            </tr>';
        while ($row = mysqli_fetch_array($result)) {
            echo '<tr>
                    <td class="border" align="center" width="30%">' . $row['nama'] . '</td>
                    <td class="border" align="center" width="20%" >' . date('m-Y', strtotime($row['tgl_mulai'])) . ' / ' . date('m-Y', strtotime($row['tgl_selesai'])) . '</td>
                    <td class="border" align="center" width="20%">' . $row['tempat'] . '</td>
                    <td class="border" align="center" width="10%" >' . $row['sertifikat'] . '</td>
                </tr>';
        }
    } else {
        echo '<tr>
                <td class="p-2" colspan="9" style="height: 10%;">2. Pendidikan Non Formal (Pelatihan / Training) : <strong>Tidak ada</strong></td>
            </tr>';
    }
    echo '</table>
        </div>';

    // cek apakah riwayat pekerjaan ada
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_pekerjaan rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY CONCAT(SUBSTRING(rp.tahun_mulai, 4, 4), '-', SUBSTRING(rp.tahun_mulai, 1, 2)) ASC");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($pengalaman_kerja == 'Tidak') {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. RIWAYAT PEKERJAAN DAN AKTIVITAS PEKERJAAN</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. Riwayat Pekerjaan?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Tidak Ada</i></strong></div>
            </div>';
    } elseif ($pengalaman_kerja == 'Fresh Graduate') {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. RIWAYAT PEKERJAAN DAN AKTIVITAS PEKERJAAN</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. Riwayat Pekerjaan?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Fresh Graduate</i></strong></div>
            </div>';
    } elseif ($pengalaman_kerja == 'Ya' && $result->num_rows > 0) {
        echo ' <div style="margin-top: 10px;">
                    <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. RIWAYAT PEKERJAAN DAN AKTIVITAS PEKERJAAN</strong></p>
                </div>

                <div style="margin-top: 10px;">
                    <p class="p-2">1. Riwayat Pekerjaan </p>
                </div>';
        $temp = 1;
        $arr_riwayat_pekerjaan = array();
        $last_row = $result->num_rows;
        $no = 1;
        while ($row = mysqli_fetch_array($result)) {
            if ($row['gaji'] > 0) {
                $temp_gaji = number_format($row['gaji'], 0, ".", ".");
            } else {
                $temp_gaji = 0;
            }

            if ($temp == 1) {
                $temp_array = array();
                $temp_array[] = array('nama_perusahaan' => $row['nama_perusahaan'], 'jabatan' => $row['jabatan'], 'gaji' => $temp_gaji, 'tahun' => $row['tahun_mulai'] . ' s/d ' . $row['tahun_selesai'], 'alasan_berhenti' => $row['alasan_berhenti']);
                $temp = 2;
            } else {
                $temp_array[] = array('nama_perusahaan' => $row['nama_perusahaan'], 'jabatan' => $row['jabatan'], 'gaji' => $temp_gaji, 'tahun' => $row['tahun_mulai'] . ' s/d ' . $row['tahun_selesai'], 'alasan_berhenti' => $row['alasan_berhenti']);
                $arr_riwayat_pekerjaan[] = $temp_array;
                $temp = 1;
            }

            if ($last_row == $no && $last_row % 2 != 0) {
                $arr_riwayat_pekerjaan[] = $temp_array;
            }

            $no++;
        }

        if (count($arr_riwayat_pekerjaan) > 0) {
            for ($i = 0; $i < count($arr_riwayat_pekerjaan); $i++) {
                $arr = $arr_riwayat_pekerjaan[$i];
                if (isset($arr[0])) {
                    if (isset($arr[1])) {
                        $nama_perusahaan2 = '<td style="width: 50%;  margin-top:20px;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                        <div class="col-5">Nama Perusahaan</div>
                                                            <div class="col-7">  : ' . $arr[1]['nama_perusahaan'] . '</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                        $jabatan2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                <div class="col-5">Jabatan</div>
                                                    <div class="col-7">  : ' . $arr[1]['jabatan'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>';
                        $gaji2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5">Gaji Net</div>
                                                <div class="col-7">  : Rp. ' . $arr[1]['gaji'] . '</div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $tahun2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5">Tahun (..<span style="text-transform: lowercase !important">s/d</span> ..)</div>
                                                <div class="col-7">  : ' . $arr[1]['tahun'] . '</div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $alasan_berhenti2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-12">Alasan Berhenti </div>
                                                            <div class="col-12">' . $arr[1]['alasan_berhenti'] . '</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                    } else {
                        $nama_perusahaan2 = '<td style="width: 50%;  margin-top:20px;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                        <div class="col-5"></div>
                                                            <div class="col-7"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                        $jabatan2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                <div class="col-5"></div>
                                                    <div class="col-7"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>';
                        $gaji2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5"></div>
                                                <div class="col-7"></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $tahun2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5"></div>
                                                <div class="col-7"></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $alasan_berhenti2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-12"></div>
                                                            <div class="col-12"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                    }

                    echo '<div class="table-container" style="margin-top:20px;">
                            <table  class="data-table">
                                <tr>
                                    <td style="width: 50%; " class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">Nama Perusahaan</div>
                                                    <div class="col-7">  : ' . $arr[0]['nama_perusahaan'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $nama_perusahaan2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">Jabatan</div>
                                                    <div class="col-7">  : ' . $arr[0]['jabatan'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $jabatan2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">Gaji Net</div>
                                                    <div class="col-7">  : Rp. ' . $arr[0]['gaji'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $gaji2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">Tahun (..<span style="text-transform: lowercase !important">s/d</span> ..)</div>
                                                    <div class="col-7">  : ' . $arr[0]['tahun'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $tahun2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-12">Alasan Berhenti </div>
                                                    <div class="col-12">' . $arr[0]['alasan_berhenti'] . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $alasan_berhenti2 . '
                                </tr>
                            </table>
                        </div>';
                }
            }
        } else {
            echo '<div style="margin-top: 10px;">
                    <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. RIWAYAT PEKERJAAN DAN AKTIVITAS PEKERJAAN</strong></p>
                </div>
                <div style="margin-top: 10px;">
                    <p class="p-2">1. Riwayat Pekerjaan : <strong><i>-</i></strong></p>
                </div>';
        }
    } else {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. RIWAYAT PEKERJAAN DAN AKTIVITAS PEKERJAAN</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. Riwayat Pekerjaan?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Tidak Ada</i></strong></div>
            </div>';
    }
    ?>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">2. Sebutkan gaji yang Anda inginkan?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Rp. <?= $minat_gaji ?></i></strong></div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">3. Lokasi kerja yang diminati?<br> &nbsp;&nbsp;&nbsp;&nbsp;<strong><i><?= $minat_lokasi_kerja ?></i></strong></div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">4. Apakah Anda bersedia melakukan perjalanan dinas?<br> &nbsp;&nbsp;&nbsp;&nbsp;<strong><i><?= $perjalanan_dinas ?></i></strong></div>
    </div>

    <div classs="" style="margin-top: 20px;">
        <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>IV. MINAT DAN KONSEP PRIBADI</strong></p>
    </div>

    <?php
    if ($bahasa_asing == 'Ya') {
        // cek apakah riwayat pekerjaan ada
        $get = $conn->prepare("SELECT
            rp.*
        FROM
            users_kandidat uk
            JOIN penguasaan_bahasa rp ON rp.id = uk.pin
        WHERE
            uk.pin = ?
        ORDER BY bahasa ASC");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            echo '<div class="row "  style="margin-top: 10px;">
                    <div class="col-12">1. Penguasaan Bahasa Asing </div>
                </div>
                <div class="table-container" style="margin-top: 10px;">
                    <table  class="data-table">
                    <tr>
                        <th class="border p-2" align="center" >Bahasa Asing yang dikuasai</th>
                        <th class="border p-2" align="center" >Membaca</th>
                        <th class="border p-2" align="center" >Menulis</th>
                        <th class="border p-2" align="center" >Mendengar</th>
                        <th class="border p-2" align="center" >Berbicara</th>
                    </tr>';
            while ($row = mysqli_fetch_array($result)) {
                echo '<tr>
                        <td class="border p-2" align="center" >' . $row['bahasa'] . '</td>
                        <td class="border p-2" align="center" >' . $row['membaca'] . '</td>
                        <td class="border p-2" align="center" >' . $row['menulis'] . '</td>
                        <td class="border p-2" align="center" >' . $row['mendengar'] . '</td>
                        <td class="border p-2" align="center" >' . $row['berbicara'] . '</td>
                    </tr>';
            }

            echo '</table>
                </div>
                <div">
                    <div class="col-12">Pilih sesuai kemampuan Anda : BS (Baik Sekali), B (Baik), C (Cukup), K (Kurang), KS (Kurang sekali)</div>
                </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="col-12">1. Penguasaan Bahasa Asing :<br>  &nbsp;&nbsp;&nbsp;<strong><i>Tidak Menguasai Bahasa Asing</i></strong></div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. Penguasaan Bahasa Asing :<br>  &nbsp;&nbsp;&nbsp;<strong><i>Tidak Menguasai Bahasa Asing</i></strong></div>
            </div>';
    }
    ?>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>2.</span>
            <div class="qa-content">
                <div class="qa-question">Menurut Anda, apa kelebihan yang Anda miliki? </div>
                <div class="qa-question"><strong><i><?= $kelebihan ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>3.</span>
            <div class="qa-content">
                <div class="qa-question">Menurut Anda, apa yang berpotensi menjadi hambatan/kekurangan Anda? </div>
                <div class="qa-question"><strong><i><?= $kekurangan ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>4.</span>
            <div class="qa-content">
                <div class="qa-question">Bagaimana pengetahuan Anda dibidang komputerisasi? </div>
                <?php
                if (count($ilmu_komputerisasi) > 0) {
                    $arr_kk = array(
                        '1' => 'Mengetahui dasar dan pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)',
                        '2' => 'Mengerti dan Menguasai pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)',
                        '3' => 'Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya',
                        '4' => 'Menguasai Pemograman dan Analist Pemograman',
                        '5' => 'Spesialis Komputer Sains',
                        '6' => 'Tidak menguasai'
                    );

                    for ($i = 0; $i < count($ilmu_komputerisasi); $i++) {
                        $temp = $ilmu_komputerisasi[$i];
                        echo '<div class="qa-question"><strong><i> - ' . $arr_kk[$temp] . '</i></strong> </div>';
                    }
                } else {
                    echo '<div class="qa-question"><strong><i>-</i></strong> </div>';
                }
                ?>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>5.</span>
            <div class="qa-content">
                <div class="qa-question">Berapa orang yang pernah dipimpin dalam tim kerja Anda? </div>
                <div class="qa-question"><strong><i><?= $memimpin_tim ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>6.</span>
            <div class="qa-content">
                <div class="qa-question">Berikan penilaian terhadap kemampuan Anda dalam presentasi/berbicara di depan umum? </div>
                <div class="qa-question"><strong><i><?= $kemampuan_presentasi ?></i></strong> </div>
            </div>
        </div>
    </div>
    <?php
    // cek apakah ruang lingkup pekerjaan ada
    if ($lingkup_pekerjaan != "") {
        $get = $conn->query("SELECT
            *
        FROM
            ruang_lingkup_pekerjaan
        WHERE
            id IN ($lingkup_pekerjaan)
        ORDER BY id ASC");

        if ($get->num_rows > 0) {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="qa-item">
                        <span>7.</span>
                        <div class="qa-content">
                            <div class="qa-question">Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai? </div>';
            while ($row = mysqli_fetch_array($get)) {
                echo '<div class="qa-question"><strong><i> - ' . $row['name'] . '</i></strong> </div>';
            }
            echo '</div>
                </div>
            </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="qa-item">
                        <span>7.</span>
                        <div class="qa-content">
                            <div class="qa-question">Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai? </div>
                            <div class="qa-question"><strong><i>-</i></strong> </div>
                        </div>
                    </div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="qa-item">
                    <span>7.</span>
                    <div class="qa-content">
                        <div class="qa-question">Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai? </div>
                        <div class="qa-question"><strong><i>-</i></strong> </div>
                    </div>
                </div>
            </div>';
    }
    ?>
    <div style="margin-top: 10px;">
        <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>V. AKTIVITAS SOSIAL</strong></p>
    </div>
    <?php
    if ($organisasi == 'Ya') {
        // cek apakah riwayat organisasi ada
        $get = $conn->prepare("SELECT
            rp.*
        FROM
            users_kandidat uk
            JOIN riwayat_organisasi rp ON rp.id = uk.pin
        WHERE
            uk.pin = ?
        ORDER BY tahun ASC");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            echo '<div class="row" style="margin-top: 10px;">
                    <div class="col-12">1. Sebutkan pengalaman organisasi Anda! (Organisasi Sosial, Politik, Kemasyarakatan, Serikat Pekerja) </div>
                </div>
                <div class="table-container" style="margin-top: 10px;">
                    <table  class="data-table">
                        <tr>
                            <th class="border p-2" width="25%">Nama Organisasi</th>
                            <th class="border p-2"  width="25%">Tempat</th>
                            <th class="border p-2"  width="25%">Jabatan</th>
                            <th class="border p-2"  width="25%">Tahun</th>
                        </tr>';
            while ($row = mysqli_fetch_array($result)) {
                echo '<tr>
                        <td class="border p-2" align="center">' . $row['nama'] . '</td>
                        <td class="border p-2" align="center">' . $row['tempat'] . '</td>
                        <td class="border p-2" align="center">' . $row['jabatan'] . '</td>
                        <td class="border p-2" align="center">' . $row['tahun'] . '</td>
                    </tr>';
            }

            echo '</table>
                </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="col-12">1. Sebutkan pengalaman organisasi Anda  :  &nbsp;&nbsp;&nbsp;<strong><i>Tidak mengikuti Organisasi</i></strong></div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. Sebutkan pengalaman organisasi Anda  :  &nbsp;&nbsp;&nbsp;<strong><i>Tidak mengikuti Organisasi</i></strong></div>
            </div>';
    }
    ?>
</body>
<!-- <script>
    window.onload = function() {
        window.print();
    };
</script> -->

</html>