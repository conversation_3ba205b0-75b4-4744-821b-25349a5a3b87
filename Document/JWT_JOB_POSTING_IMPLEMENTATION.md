# JWT Job Posting Implementation Documentation

## Overview

This document describes the implementation of JWT authentication for the job posting endpoints in the recruitment system. The migration replaces parameter-based authentication with secure JWT token-based user validation.

## Files Modified

- `api/mobile/company/job_posting.php`

## JWT Implementation Details

### Authentication Changes

1. **JWT Helper Integration**

   - Added `include '../../jwt_helper.php'`
   - Added `requireAuth()` call to validate JWT tokens
   - Removed session-based authentication

2. **User Data Extraction**
   - Replaced `$_POST['id_koordinator']` with `$userData->id_koordinator`
   - Replaced `$_POST['divisi']` with `$userData->divisi`
   - Replaced `$_POST['id']` with JWT-derived values
   - Added `$company = $userData->company` for compatibility

### Endpoint Functions

#### 1. submitJobPosting

- **Purpose**: Create new job posting
- **JWT Data Used**:
  - `id_koordinator`: Company/coordinator ID
  - `divisi`: Division information
- **Changes**:
  - Removed parameter extraction for user identification
  - Uses JWT data for all company validation and job creation
  - Activity logging with JWT user ID

#### 2. getListRequest

- **Purpose**: Get paginated list of job postings
- **JWT Data Used**:
  - `id_koordinator`: Filter job postings by company
- **Changes**:
  - Added activity logging
  - Uses JWT company ID for data filtering

#### 3. detailLowongan

- **Purpose**: Get detailed job posting information
- **JWT Data Used**:
  - `id_koordinator`: Access control (implicit)
- **Changes**:
  - Added activity logging with job ID

#### 4. extendJob

- **Purpose**: Extend job posting expiration date
- **JWT Data Used**:
  - `id_koordinator`: User verification and ownership validation
- **Changes**:
  - Updated user verification to use JWT data
  - Activity logging with JWT user ID

#### 5. tutupLowongan

- **Purpose**: Close/end job posting
- **JWT Data Used**:
  - `id_koordinator`: User verification and ownership validation
- **Changes**:
  - Updated user verification to use JWT data
  - Activity logging with JWT user ID

#### 6. getJurusan, getPosisi, getSekolah

- **Purpose**: Get reference data (majors, positions, schools)
- **JWT Data Used**: None (read-only operations)
- **Changes**: No changes needed - these are utility functions

### Security Improvements

#### Before JWT

```php
// Insecure parameter-based authentication
if (isset($_POST['id_koordinator'])) {
    $id_koordinator = addslashes($_POST['id_koordinator']);
}
```

#### After JWT

```php
// Secure JWT-based authentication
$userData = requireAuth();
$id_koordinator = $userData->id_koordinator;
```

### Activity Logging

All major operations now log user activities:

- Job posting creation
- Accessing job lists
- Viewing job details
- Extending job expiration
- Closing job postings

### Database Queries Updated

All SQL queries now use JWT-derived user data:

```sql
-- Example: Job posting ownership validation
SELECT * FROM list_request WHERE id_koordinator = '$id_koordinator'

-- Example: User verification
SELECT * FROM koordinator_pic WHERE id_pic = '$id_koordinator'
```

## Client-Side Usage

### Authentication Required

All endpoints now require a valid JWT token in the Authorization header:

```javascript
const headers = {
  Authorization: `Bearer ${jwtToken}`,
  "Content-Type": "application/json",
};
```

### Example API Calls

#### Create Job Posting

```javascript
fetch("/api/mobile/company/job_posting.php?func=submitJobPosting", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  body: new URLSearchParams({
    posisi: "Software Developer",
    jmlh_kandidat: "2",
    lokasi_k: "Jakarta",
    // ... other job posting data
    // Note: id_koordinator, divisi, id are now extracted from JWT
  }),
});
```

#### Get Job List

```javascript
fetch(
  "/api/mobile/company/job_posting.php?func=getListRequest&page=1&page_size=10",
  {
    method: "GET",
    headers: {
      Authorization: `Bearer ${jwtToken}`,
    },
  }
);
```

#### Get Job Detail

```javascript
fetch("/api/mobile/company/job_posting.php?func=detailLowongan&id_req=REQ001", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
  },
});
```

#### Extend Job

```javascript
fetch("/api/mobile/company/job_posting.php?func=extendJob", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  body: new URLSearchParams({
    id_req: "REQ001",
    expired_date: "30", // days
  }),
});
```

#### Close Job

```javascript
fetch("/api/mobile/company/job_posting.php?func=tutupLowongan", {
  method: "POST",
  headers: {
    Authorization: `Bearer ${jwtToken}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
  body: new URLSearchParams({
    id_req: "REQ001",
  }),
});
```

## Error Handling

### JWT Validation Errors

- **401 Unauthorized**: Invalid or expired JWT token
- **403 Forbidden**: Valid JWT but insufficient permissions

### Business Logic Errors

- **400 Bad Request**: Invalid input data
- **404 Not Found**: Job posting not found
- **409 Conflict**: Duplicate job posting

## Migration Impact

### Backward Compatibility

- **Breaking Change**: All endpoints now require JWT authentication
- **Parameters Removed**: id_koordinator, divisi, id from POST data
- **Client Updates Required**: Must include Authorization header

### Data Integrity

- All user identification now comes from validated JWT tokens
- Improved security through token-based authentication
- Enhanced activity logging for audit trails

## Testing

Use the test script `test_jwt_job_posting.php` to verify all endpoint functionality with JWT authentication.

## Notes

- All endpoints maintain the same response format
- Activity logging provides comprehensive audit trails
- JWT tokens must be refreshed before expiration
- All database queries use prepared statements for security
