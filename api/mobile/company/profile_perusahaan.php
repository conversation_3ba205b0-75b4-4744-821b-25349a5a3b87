<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';

// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;
$id_pegawai = $userData->id;

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function get_email_from_lamar($id_lamar, $conn)
{
    $sql = "SELECT
                uk.email
            FROM
                `users_lamar`  ul
                JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
            WHERE
                id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $email = $row['email'];
    } else {
        $email = null;
    }
    return $email;
}

function get_posisi($id_req, $conn)
{
    $sql = "SELECT posisi FROM list_request WHERE id_req = '$id_req'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $posisi = $row['posisi'];
    } else {
        $posisi = null;
    }
    return $posisi;
}

function get_nama_kandidat($id_lamar, $conn)
{
    $sql = "SELECT
        uk.nama_lengkap
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $nama_lengkap = $row['nama_lengkap'];
    } else {
        $nama_lengkap = null;
    }
    return $nama_lengkap;
}

function update_proses_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "On Proccess Digitalcv";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "On Proccess Interview";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "On Proccess Psikotes";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "On Proccess Validasi";
    } elseif ($text == 'VALIDASI') {
        $status_update = "On Proccess Offering";
    } elseif ($text == 'OFFERING') {
        $status_update = "On Proccess Medical";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Sudah Diterima";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_close_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_terima_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Terima Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Terima Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Terima Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Terima Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Terima Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Terima Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Terima Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Terima Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_tolak_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Tolak Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function get_pin($id_lamar, $conn)
{
    $sql = "SELECT
        uk.pin
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $pin = $row['pin'];
    } else {
        $pin = null;
    }
    return $pin;
}

function get_company($company, $conn)
{
    $data = [];
    $sql = "SELECT * FROM koordinator WHERE id_koordinator = '$company'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $data['nama_perusahaan'] = $row['label'];
        $data['img'] = $row['img'];
        $data['alamat'] = $row['alamat'];
    } else {
        $data = null;
    }
    return $data;
}


function cek_feedback_hrd($conn, $id_lamar)
{
    //cek review = HRD
    $sql = "SELECT approval FROM feedback WHERE id_lamar = '$id_lamar' AND review = 'HRD'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['approval'];
}

function getInitials($name)
{
    $words = explode(' ', strtoupper($name));
    $initials = '';
    foreach ($words as $w) {
        if ($w !== '') {
            $initials .= $w[0];
        }
    }
    return substr($initials, 0, 2); // maksimal 2 huruf
}

function selisihTahunBulan($tanggal_awal, $tanggal_akhir)
{
    $tanggal_awal = DateTime::createFromFormat('m-Y', $tanggal_awal);
    $tanggal_awal = $tanggal_awal->format('Y-m-d');

    $tanggal_akhir = DateTime::createFromFormat('m-Y', $tanggal_akhir);
    $tanggal_akhir = $tanggal_akhir->format('Y-m-d');

    $awal  = new DateTime($tanggal_awal);
    $akhir = new DateTime($tanggal_akhir);

    if ($awal > $akhir) {
        // Tukar jika tanggal awal lebih besar dari akhir
        $temp = $awal;
        $awal = $akhir;
        $akhir = $temp;
    }

    $selisih = $awal->diff($akhir);

    $tahun = $selisih->y;
    $bulan = $selisih->m;

    $output = "";

    if ($tahun > 0) {
        $output .= $tahun . " Tahun";
    }

    if ($bulan > 0) {
        if ($output !== "") {
            $output .= " ";
        }
        $output .= $bulan . " Bulan";
    }

    return $output === "" ? "0 Bulan" : $output;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

// Get function from URL parameter
$func = $_GET['func'] ?? '';

if ($func == 'getProfilPerusahaan') {


    header('Content-Type: application/json');

    $response = [
        "status" => false,
        "message" => "Data tidak ditemukan.",
        "data" => [],
        "page" => 1,
        "page_size" => 10,
        "total_page" => 0,
        "total_data" => 0
    ];

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $page_size = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
    $offset = ($page - 1) * $page_size;

    // Ambil data koordinator
    $get = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ? LIMIT ?, ?");
    $get->bind_param("sii", $id_koordinator, $offset, $page_size);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    // Hitung total data untuk pagination
    $count = $conn->prepare("SELECT COUNT(*) as total FROM koordinator WHERE id_koordinator = ?");
    $count->bind_param("s", $id_koordinator);
    $count->execute();
    $total_result = $count->get_result()->fetch_assoc();
    $total_data = (int) $total_result['total'];
    $total_page = ceil($total_data / $page_size);
    $count->close();

    if ($result->num_rows > 0) {
        $data = [];

        while ($row = $result->fetch_assoc()) {
            $logoURL = '';
            $bannerURL = '';

            // Ambil logo jika ada
            if (!empty($row['img']) && $s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $row['img']
                ]);
                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();
            }

            // Ambil banner jika ada
            if (!empty($row['img_banner']) && $s3->doesObjectExist($bucket, 'perusahaan/banner/' . $row['img_banner'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/banner/' . $row['img_banner']
                ]);
                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $bannerURL = (string) $request->getUri();
            }

            $data[] = [
                "nama_perusahaan" => isset($row['label']) ? $row['label'] : "-",
                "alamat" => isset($row['alamat']) ? $row['alamat'] : "-",
                "tipe" => isset($row['tipe']) ? $row['tipe'] : "-",
                "link_perusahaan" => isset($row['link_perusahaan']) ? $row['link_perusahaan'] : "-",
                "tentang_perusahaan" => isset($row['deskripsi']) ? $row['deskripsi'] : "-",
                "tlp" => isset($row['tlp']) ? $row['tlp'] : "-",
                "email" => isset($row['email']) ? $row['email'] : "-",
                "logo_perusahaan_url" => $logoURL,
                "banner_perusahaan_url" => $bannerURL
            ];
        }

        $response['status'] = true;
        $response['message'] = "Data perusahaan berhasil ditemukan.";
        $response['data'] = $data;
        $response['page'] = $page;
        $response['page_size'] = $page_size;
        $response['total_page'] = $total_page;
        $response['total_data'] = $total_data;
    }

    echo json_encode($response, JSON_PRETTY_PRINT);
}


if ($func == 'updateProfilPerusahaan') {

    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        // cek apakah data yang dikirim ada
        if (!isset($_POST['nama_perusahaan']) && !isset($_POST['no_tlp_perusahaan']) && !isset($_POST['email_perusahaan']) && !isset($_POST['alamat_perusahaan']) && !isset($_POST['website_perusahaan']) && !isset($_POST['industri_perusahaan']) && !isset($_POST['deskripsi_perusahaan'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
                                    kp.id_pic,
                                    k.img,
                                    k.img_banner
                                FROM
                                    koordinator k
                                    JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
                                WHERE
                                    kp.id_pic = ?
                                    AND k.id_koordinator = ?
                                GROUP BY
                                    k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $row = mysqli_fetch_array($result);
        $logo_lama = $row['img'];
        $banner_lama = $row['img_banner'];

        $nama_perusahaan = $_POST['nama_perusahaan'];
        $no_tlp_perusahaan = $_POST['no_tlp_perusahaan'];
        $email_perusahaan = $_POST['email_perusahaan'];
        $alamat_perusahaan = $_POST['alamat_perusahaan'];
        $website_perusahaan = $_POST['website_perusahaan'];
        $industri_perusahaan = $_POST['industri_perusahaan'];
        $deskripsi_perusahaan = $_POST['deskripsi_perusahaan'];

        // update data perusahaan
        $update = $conn->prepare("UPDATE
                                    koordinator
                                SET
                                    label = ?,
                                    alias = ?,
                                    alamat = ?,
                                    tipe = ?,
                                    link_perusahaan = ?,
                                    deskripsi = ?,
                                    tlp = ?,
                                    email = ?,
                                    updated_at = ?
                                WHERE
                                    id_koordinator = ?");
        $update->bind_param("ssssssssss", $nama_perusahaan, $nama_perusahaan, $alamat_perusahaan, $industri_perusahaan, $website_perusahaan, $deskripsi_perusahaan, $no_tlp_perusahaan, $email_perusahaan, $created_at, $id_koordinator);
        if ($update->execute()) {
            $update->close();
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }

        // cek apakah logo akan diupdate
        if (isset($_FILES['logo'])) {
            if (file_exists($_FILES['logo']['tmp_name']) || is_uploaded_file($_FILES['logo']['tmp_name'])) {
                // hapus logo lama
                if ($logo_lama != "") {
                    if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $logo_lama)) {
                        // hapus file profil yang lama
                        $result = $s3->deleteObject([
                            'Bucket' => $bucket,
                            'Key' => 'perusahaan/logo/' . $logo_lama
                        ]);
                    }
                }

                // upload logo baru
                $file_tmp = $_FILES['logo']['tmp_name'];
                $fileName = $_FILES['logo']['name'];
                $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                $nama_file = 'LOGO-' . $id_koordinator . '.' . $ext;

                $s3->putObject([
                    'Bucket' => $bucket,
                    'Key' => 'perusahaan/logo/' . $nama_file,
                    'SourceFile' => $file_tmp
                ]);

                // cek apakah file sudah ke upload atau belum
                if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $nama_file)) {
                    // update nama file di table koordinator
                    $update = $conn->prepare("UPDATE `koordinator` SET `img` = ? WHERE `id_koordinator` = ?");
                    $update->bind_param("ss", $nama_file, $id_koordinator);

                    if ($update->execute()) {
                        $update->close();
                    } else {
                        throw new Exception("Upload logo perusahaan gagal dilakukan.");
                    }
                } else {
                    throw new Exception("Upload logo perusahaan gagal dilakukan.");
                }
            }
        }

        // cek apakah banner akan diupdate
        if (isset($_FILES['banner'])) {
            if (file_exists($_FILES['banner']['tmp_name']) || is_uploaded_file($_FILES['banner']['tmp_name'])) {
                // hapus banner lama
                if ($banner_lama != "") {
                    if ($s3->doesObjectExist($bucket, 'perusahaan/banner/' . $banner_lama)) {
                        // hapus file profil yang lama
                        $result = $s3->deleteObject([
                            'Bucket' => $bucket,
                            'Key' => 'perusahaan/banner/' . $banner_lama
                        ]);
                    }
                }

                // upload banner baru
                $file_tmp = $_FILES['banner']['tmp_name'];
                $fileName = $_FILES['banner']['name'];
                $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                $nama_file = 'BANNER-' . $id_koordinator . '.' . $ext;

                $s3->putObject([
                    'Bucket' => $bucket,
                    'Key' => 'perusahaan/banner/' . $nama_file,
                    'SourceFile' => $file_tmp
                ]);

                // cek apakah file sudah ke upload atau belum
                if ($s3->doesObjectExist($bucket, 'perusahaan/banner/' . $nama_file)) {
                    // update nama file di table koordinator
                    $update = $conn->prepare("UPDATE `koordinator` SET `img_banner` = ? WHERE `id_koordinator` = ?");
                    $update->bind_param("ss", $nama_file, $id_koordinator);

                    if ($update->execute()) {
                        $update->close();
                    } else {
                        throw new Exception("Upload banner perusahaan gagal dilakukan.");
                    }
                } else {
                    throw new Exception("Upload banner perusahaan gagal dilakukan.");
                }
            }
        }

        // simpan log aktivitas sudah dilakukan di awal function

        // Jika semua query berhasil, commit transaksi
        $conn->commit();

        $status = "success";
        $message = "Data perusahaan berhasil diupdate.";
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}


$conn->close();
exit;
