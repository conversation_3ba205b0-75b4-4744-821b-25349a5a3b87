function active_course() {
  if ($(".active_course").length) {
    $(".active_course").owlCarousel({
      loop: true,
      margin: 20,
      items: 3,
      nav: true,
      autoplay: 2500,
      smartSpeed: 1500,
      dots: false,
      responsiveClass: true,
      thumbs: true,
      thumbsPrerendered: true,
      navText: [
        "<i class='bi bi-arrow-left-circle' style='font-size: 50px;'></i>",
        "<i class='bi bi-arrow-right-circle' style='font-size: 50px;'></i>",
      ],
      responsive: {
        0: {
          items: 1,
          margin: 0,
        },
        991: {
          items: 2,
          margin: 30,
        },
        1200: {
          items: 3,
          margin: 30,
        },
      },
    });
  }
}
active_course();

function myFunction() {
  let x = document.getElementById("myInput");
  if (x.type === "password") {
    x.type = "text";
  } else {
    x.type = "password";
  }
}

function myFunction1() {
  let i = document.getElementById("myInput1");
  if (i.type === "password") {
    i.type = "text";
  } else {
    i.type = "password";
  }
}

/* Validation Form register */
(function () {
  "use strict";
  let forms = document.querySelectorAll(".needs-validation");

  Array.from(forms).forEach(function (form) {
    form.addEventListener(
      "submit",
      function (event) {
        if (!form.checkValidity()) {
          event.preventDefault();
          event.stopPropagation();
        }
        form.classList.add("was-validated");
      },
      false
    );
  });
})();

/* Card active onClick */
const cards = document.getElementsByClassName("card-title");
for (let i = 0; i < cards.length; i++) {
  cards[i].addEventListener("click", function () {
    this.classList.toggle("actived");
  });
}

/* NAVBAR */
if (window.parent && window.parent.parent) {
  window.parent.parent.postMessage(
    [
      "resultsFrame",
      {
        height: document.body.getBoundingClientRect().height,
        slug: "ravpqxok",
      },
    ],
    "*"
  );
}

window.name = "result";

const elements = document.getElementsByClassName("card");
for (let i = 0; i < elements.length; i++) {
  elements[i].addEventListener("click", function () {
    this.classList.toggle("pressed");
  });
}

document.addEventListener("DOMContentLoaded", function (event) {
  const showNavbar = (toggleId, navId, bodyId, headerId) => {
    const toggle = document.getElementById(toggleId),
      nav = document.getElementById(navId),
      bodypd = document.getElementById(bodyId),
      headerpd = document.getElementById(headerId);

    // Validate that all variables exist
    if (toggle && nav && bodypd && headerpd) {
      toggle.addEventListener("click", () => {
        // show navbar
        nav.classList.toggle("show-sidebar");
        // change icon
        toggle.classList.toggle("bx-x");
        // add padding to body
        bodypd.classList.toggle("body-pd");
        // add padding to header
        headerpd.classList.toggle("body-pd");
      });
    }
  };

  showNavbar("header-toggle", "nav-bar", "body-pd", "header");

  /*===== LINK active =====*/
  const linkColor = document.querySelectorAll(".nav_link");

  function colorLink() {
    if (linkColor) {
      linkColor.forEach((l) => l.classList.remove("aktif"));
      this.classList.add("aktif");
    }
  }
  linkColor.forEach((l) => l.addEventListener("click", colorLink));
});

function acceptInvitation(params) {
  $("#loader").show();
  $("#konten").hide();

  if (params != "") {
    $.ajax({
      type: "post",
      url: "../candidate/controller/controller.php?func=terimaPengajuanPekerjaan",
      data: {
        q: params,
      },
      success: function (result) {
        var obj = JSON.parse(JSON.stringify(result));
        $("#konten").html(obj.konten);

        $("#loader").hide();
        $("#konten").show();
      },
    });
  }
}
