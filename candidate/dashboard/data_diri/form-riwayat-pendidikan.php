<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data identitas diri jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$pendidikan_tinggi = "";
$cek_diploma = "";

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $pendidikan_tinggi = htmlspecialchars($row['pendidikan_terakhir']);
    $cek_diploma = htmlspecialchars($row['diploma']);
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Riwayat Pendidikan') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-md-4 mt-2">
            <div class="mb-3">
                <label for="pendidikan_tinggi" class="form-label"><?= translate('Jenjang pendidikan terakhir') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" name="pendidikan_tinggi" id="pendidikan_tinggi" aria-label="Default select example" required>
                    <option value="" disabled selected><?= translate('Silakan pilih pendidikan terakhir') ?></option>
                    <option value="SD"><?= translate('SD') ?></option>
                    <option value="SMP"><?= translate('SMP') ?></option>
                    <option value="SMA"><?= translate('SMA/Sederajat') ?></option>
                    <option value="Diploma">Diploma</option>
                    <option value="S1">S1</option>
                    <option value="S2">S2</option>
                    <option value="S3">S3</option>
                </select>
            </div>
        </div>
        <div class="col-md-4 mt-2">
            <div class="mb-3" id="cekdiplomas" style="display: none;">
                <label for="exampleInputEmail1" class="form-label"><?= translate('Melalui Diploma') ?>? <a style="color: red;">(*)</a></label>
                <br>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="cek_diploma" id="ydiploma" value="Ya">
                    <label class="form-check-label" for="ydiploma">
                        <?= translate('Ya') ?>
                    </label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" name="cek_diploma" id="tdiploma" value="Tidak">
                    <label class="form-check-label" for="tdiploma">
                        <?= translate('Tidak') ?>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-4 mt-2">
            <div class="mb-3">
                <label for="temp_jenjang" class="form-label"><?= translate('Jenjang Pendidikan') ?></label>
                <select class="form-select" name="temp_jenjang" id="temp_jenjang" aria-label="Default select example" style="width: 100%;">
                    <option value="" disabled selected><?= translate('Silakan pilih pendidikan terakhir') ?></option>
                    <option value="SD"><?= translate('SD') ?></option>
                    <option value="SMP"><?= translate('SMP') ?></option>
                    <option value="SMA"><?= translate('SMA/Sederajat') ?></option>
                    <option value="Diploma">Diploma</option>
                    <option value="S1">S1</option>
                    <option value="S2">S2</option>
                    <option value="S3">S3</option>
                </select>
            </div>
        </div>
        <div class="col-md-4 mt-2">
            <div class="mb-3">
                <label for="temp_nama_sekolah" class="form-label"><?= translate('Nama Sekolah') ?></label><br>
                <select class="custom-select" name="temp_nama_sekolah" id="temp_nama_sekolah" style="width: 100%;"></select>
            </div>
        </div>
        <div class="col-md-4 mt-2" id="konten-jurusan-sekolah" style="display: none;">
            <div class="mb-3">
                <label for="temp_jurusan_sekolah" class="form-label"><?= translate('Jurusan') ?></label><br>
                <select class="custom-select" name="temp_jurusan_sekolah" id="temp_jurusan_sekolah" style="width: 100%;"></select>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3 mt-2">
            <label for="tahun_mulai_sekolah" class="form-label"><?= translate('Tahun Masuk') ?></label>
            <select class="form-select" id="tahun_mulai_sekolah" aria-label="Default select example" style="width: 100%;">
                <option value="" disabled selected><?= translate('Silakan pilih tahun') ?></option>
                <?php
                $tahunSekarang = date("Y"); // Ambil tahun saat ini
                $tahunMulai = 1900; // Tahun paling awal
                for ($i = $tahunSekarang; $i >= $tahunMulai; $i--) {
                    echo "<option value='$i'>$i</option>";
                }
                ?>
            </select>
        </div>
        <div class="col-md-3 mt-2">
            <label for="tahun_selesai_sekolah" class="form-label"><?= translate('Tahun Selesai') ?></label>
            <select class="form-select" id="tahun_selesai_sekolah" aria-label="Default select example" style="width: 100%;">
                <option value="" disabled selected><?= translate('Silakan pilih tahun') ?></option>
                <?php
                $tahunSekarang = date("Y"); // Ambil tahun saat ini
                $tahunMulai = 1900; // Tahun paling awal
                for ($i = $tahunSekarang; $i >= $tahunMulai; $i--) {
                    echo "<option value='$i'>$i</option>";
                }
                ?>
            </select>
        </div>
        <div class="col-md-3 mt-2">
            <div class="mb-3">
                <label for="ket_sekolah" class="form-label"><?= translate('Keterangan') ?></label>
                <select class="form-control" id="ket_sekolah" style="width: 100%;">
                    <option value="" selected disabled><?= translate('Silakan pilih keterangan') ?></option>
                    <option value="Lulus Berijazah"><?= translate('Lulus Berijazah') ?></option>
                    <option value="Lulus Tidak Berijazah"><?= translate('Lulus Tidak Berijazah') ?></option>
                    <option value="Belum Lulus"><?= translate('Belum Lulus') ?></option>
                    <option value="Berhenti"><?= translate('Berhenti') ?></option>
                    <option value="Cuti"><?= translate('Cuti') ?></option>
                </select>
            </div>
        </div>
        <div class="col-md-3 mt-2">
            <label class="form-label" style="visibility: hidden;">#</label><br>
            <button type="button" class="btn btn-primary btn-sm" onclick="tambahRiwayatPendidikan()"><i class="fas fa-plus-square"></i> <?= translate('Simpan') ?></button>
        </div>
    </div>

    <div class="row mt-2">
        <span><?= translate('Detail Riwayat Pendidikan') ?></span>
        <?php
        // get riwayat pendidikan
        $sql = $conn->prepare("SELECT * FROM riwayat_pendidikan WHERE id = ?");
        $sql->bind_param("s", $temp_pin);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            echo '<div class="col-md-12" id="konten-info-detail-pendidikan" style="display: none;">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i> ' . translate('Riwayat pendidikan belum ditambahkan') . '.
                    </div>
                </div>
                <div class="row mt-2" id="konten-detail-pendidikan">';
            $no = 0;
            while ($row = mysqli_fetch_array($result)) {
                echo '<div class="col-md-4" id="detailPendidikan-' . $no . '">
                        <div class="card">
                            <div class="card-body">
                                <span class="card-title" style="font-size: 14px;">' . htmlspecialchars($row['nama_sekolah']) . '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailPendidikan(' . $no . ')"></i></span>
                                <p class="card-text mt-2" style="font-size: 12px;">
                                    ' . translate('Tahun') . ' : ' . htmlspecialchars($row['tahun_mulai']) . ' - ' . htmlspecialchars($row['tahun_selesai']) . '<br>
                                    ' . translate('Jurusan') . ' : ' . htmlspecialchars($row['jurusan']) . '<br>
                                    ' . translate('Keterangan') . ' : ' . translate($row['ket']) . '
                                </p>
                                <input type="hidden" name="jenjang_pendidikan[]" value="' . htmlspecialchars($row['jenjang']) . '">
                                <input type="hidden" name="nama_sekolah_pendidikan[]" value="' . htmlspecialchars($row['nama_sekolah']) . '">
                                <input type="hidden" name="jurusan_pendidikan[]" value="' . htmlspecialchars($row['jurusan']) . '">
                                <input type="hidden" name="tahun_mulai_pendidikan[]" value="' . htmlspecialchars($row['tahun_mulai']) . '">
                                <input type="hidden" name="tahun_selesai_pendidikan[]" value="' . htmlspecialchars($row['tahun_selesai']) . '">
                                <input type="hidden" name="ket_pendidikan[]" value="' . htmlspecialchars($row['ket']) . '">
                            </div>
                        </div>
                    </div>';
                $no++;
            }
            echo '</div>';
        } else {
            echo '<div class="col-md-12" id="konten-info-detail-pendidikan">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i> ' . translate('Riwayat pendidikan belum ditambahkan') . '.
                    </div>
                </div>
                <div class="row mt-2" id="konten-detail-pendidikan">
                </div>';
        }
        ?>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-identitas-diri.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        /* validasi di form data pendidikan */
        $('[name=pendidikan_tinggi]').on('change', function() {
            var text = $(this).val();
            $("input[name='cek_diploma']").prop("checked", false);

            if (text == 'S1' || text == 'S2' || text == 'S3') {
                $('#cekdiplomas').show();
                $("input[name='cek_diploma']").attr("required", true);
            } else {
                $('#cekdiplomas').hide();
                $("input[name='cek_diploma']").attr("required", false);
            }
        });

        $('[name=temp_jenjang]').on('change', function() {
            var text = $(this).val();

            if (text == 'SD' || text == 'SMP' || text == '' || text === null) {
                $("#konten-jurusan-sekolah").hide();
            } else {
                $("#konten-jurusan-sekolah").show();
            }
        });

        $('#temp_nama_sekolah').select2({
            placeholder: "<?= translate('Cari Sekolah') ?>...",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getSekolah',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data sekolah
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 4, // Mulai mencari setelah mengetik minimal 4 huruf
            tags: true
        });

        $('#temp_jurusan_sekolah').select2({
            placeholder: "<?= translate('Cari Jurusan') ?>...",
            allowClear: true,
            tags: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getJurusan',
                        pendidikan: $('#temp_jenjang').val(), // Ambil jenjang pendidikan yang dipilih
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data sekolah
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
        });

        $("#tahun_mulai_sekolah").select2();
        $("#tahun_selesai_sekolah").select2();
        $("#ket_sekolah").select2();

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data riwayat pendidikan
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            // cek apakah riwayat pendidikan sudah dimasukan
            if ($("input[name='jenjang_pendidikan[]']").length > 0) {
                var pendidikan_tinggi = $("[name=pendidikan_tinggi]").val();
                cek_diploma = $("input[name=cek_diploma]:checked").val();
                jenjang_pendidikan = $("input[name='jenjang_pendidikan[]']").map(function() {
                    return this.value;
                }).get();
                nama_sekolah_pendidikan = $("input[name='nama_sekolah_pendidikan[]']").map(function() {
                    return this.value;
                }).get();
                jurusan_pendidikan = $("input[name='jurusan_pendidikan[]']").map(function() {
                    return this.value;
                }).get();
                tahun_mulai_pendidikan = $("input[name='tahun_mulai_pendidikan[]']").map(function() {
                    return this.value;
                }).get();
                tahun_selesai_pendidikan = $("input[name='tahun_selesai_pendidikan[]']").map(function() {
                    return this.value;
                }).get();
                ket_pendidikan = $("input[name='ket_pendidikan[]']").map(function() {
                    return this.value;
                }).get();

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanRiwayatPendidikan",
                            data: {
                                pendidikan_tinggi: pendidikan_tinggi,
                                cek_diploma: cek_diploma,
                                jenjang_pendidikan: jenjang_pendidikan,
                                nama_sekolah_pendidikan: nama_sekolah_pendidikan,
                                jurusan_pendidikan: jurusan_pendidikan,
                                tahun_mulai_pendidikan: tahun_mulai_pendidikan,
                                tahun_selesai_pendidikan: tahun_selesai_pendidikan,
                                ket_pendidikan: ket_pendidikan,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-pelatihan-kursus.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            } else {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan riwayat pendidikan terlebih dahulu') ?>.",
                    buttons: false,
                    timer: 1600
                });
            }
        });

        // auto input
        var pendidikan_tinggi = '<?= $pendidikan_tinggi ?>';
        $("#pendidikan_tinggi").val(pendidikan_tinggi).trigger('change');

        var cek_diploma = '<?= $cek_diploma ?>';
        $("input[name='cek_diploma'][value='" + cek_diploma + "']").prop("checked", true);
        // end auto input
    });

    function tambahRiwayatPendidikan() {
        var temp_jenjang = htmlspecialchars($("#temp_jenjang").val());
        temp_nama_sekolah = htmlspecialchars($("#temp_nama_sekolah").val());
        temp_jurusan_sekolah = htmlspecialchars($("#temp_jurusan_sekolah").val());
        tahun_mulai_sekolah = htmlspecialchars($("#tahun_mulai_sekolah").val());
        tahun_selesai_sekolah = htmlspecialchars($("#tahun_selesai_sekolah").val());
        ket_sekolah = htmlspecialchars($("#ket_sekolah").val());

        if (temp_jenjang == "" || temp_jenjang === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih jenjang pendidikan terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if (temp_nama_sekolah == "" || temp_nama_sekolah === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan nama sekolah terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if ((temp_jenjang != "SD" && temp_jenjang != "SMP") && (temp_jurusan_sekolah == "" || temp_jurusan_sekolah === null)) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan masukan jurusan terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if (tahun_mulai_sekolah == "" || tahun_mulai_sekolah === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih tahun mulai terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if (tahun_selesai_sekolah == "" || tahun_selesai_sekolah === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih tahun selesai terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if (tahun_mulai_sekolah > tahun_selesai_sekolah) {
            swal({
                icon: "warning",
                text: "<?= translate('Tahun mulai tidak boleh lebih besar dari tahun selesai') ?>.",
                buttons: false,
                timer: 1600
            });
        } else if (ket_sekolah == "" || ket_sekolah === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih keterangan sekolah terlebih dahulu') ?>.",
                buttons: false,
                timer: 1600
            });
        } else {
            $("#konten-info-detail-pendidikan").hide();

            var id_input = Math.floor(Math.random() * 900) + 100;
            var content = $("#konten-detail-pendidikan").html();

            if (temp_jurusan_sekolah === null) {
                temp_jurusan_sekolah = "";
            }

            newContent = content + '<div class="col-md-4" id="detailPendidikan-' + id_input + '">' +
                '<div class="card">' +
                '<div class="card-body">' +
                '<span class="card-title" style="font-size: 14px;">' + temp_nama_sekolah + '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailPendidikan(' + id_input + ')"></i></span>' +
                '<p class="card-text mt-2" style="font-size: 12px;">' +
                '<?= translate('Tahun') ?> : ' + tahun_mulai_sekolah + ' - ' + tahun_selesai_sekolah + '<br>' +
                '<?= translate('Jurusan') ?> : ' + temp_jurusan_sekolah + '<br>' +
                '<?= translate('Keterangan') ?> : ' + $('#ket_sekolah option:selected').text() + '' +
                '</p>' +
                '<input type="hidden" name="jenjang_pendidikan[]" value="' + temp_jenjang + '">' +
                '<input type="hidden" name="nama_sekolah_pendidikan[]" value="' + temp_nama_sekolah + '">' +
                '<input type="hidden" name="jurusan_pendidikan[]" value="' + temp_jurusan_sekolah + '">' +
                '<input type="hidden" name="tahun_mulai_pendidikan[]" value="' + tahun_mulai_sekolah + '">' +
                '<input type="hidden" name="tahun_selesai_pendidikan[]" value="' + tahun_selesai_sekolah + '">' +
                '<input type="hidden" name="ket_pendidikan[]" value="' + ket_sekolah + '">' +
                '</div>' +
                '</div>' +
                '</div>';

            $("#konten-detail-pendidikan").html(newContent);

            $("#temp_jenjang").val('').trigger('change');

            $("#temp_nama_sekolah").select2('destroy');
            $('#temp_nama_sekolah').append('');
            $('#temp_nama_sekolah').append('<option></option>');
            $('#temp_nama_sekolah').select2({
                placeholder: "<?= translate('Cari Sekolah') ?>...",
                allowClear: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getSekolah',
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data sekolah
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 4, // Mulai mencari setelah mengetik minimal 4 huruf
                tags: true
            });

            $("#temp_jurusan_sekolah").select2('destroy');
            $('#temp_jurusan_sekolah').append('');
            $('#temp_jurusan_sekolah').append('<option></option>');
            $('#temp_jurusan_sekolah').select2({
                placeholder: "<?= translate('Cari Jurusan') ?>...",
                allowClear: true,
                tags: true,
                ajax: {
                    url: "../../controller/controller.php",
                    dataType: "json",
                    delay: 250, // Mencegah terlalu banyak request
                    data: function(params) {
                        return {
                            func: 'getJurusan',
                            pendidikan: $('#temp_jenjang').val(), // Ambil jenjang pendidikan yang dipilih
                            q: params.term, // Kata kunci pencarian
                            page: params.page || 1 // Paginasi
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.results, // Data sekolah
                            pagination: {
                                more: data.more // Jika masih ada data, aktifkan paginasi
                            }
                        };
                    }
                },
                minimumInputLength: 3, // Mulai mencari setelah mengetik minimal 4 huruf
            });

            $("#temp_nama_sekolah").val(null).trigger('change');
            $("#temp_jurusan_sekolah").val(null).trigger('change');
            $("#tahun_mulai_sekolah").val('').trigger('change');
            $("#tahun_selesai_sekolah").val('').trigger('change');
            $("#ket_sekolah").val('').trigger('change');
        }
    }

    function hapusDetailPendidikan(id) {
        $("#detailPendidikan-" + id).remove();

        // cek apakah html kosong atau tidak
        if ($("#konten-detail-pendidikan").text().trim().length === 0) {
            $("#konten-info-detail-pendidikan").show();
        }
    }
</script>