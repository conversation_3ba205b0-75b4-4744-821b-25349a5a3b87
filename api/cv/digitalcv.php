<?php
ini_set('max_execution_time', 300);
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include "../../model/database.php";
include '../../api/s3.php';

function decrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $data = strtr($data, '-_,', '+/=');
    $decoded = base64_decode($data);
    $iv = substr($decoded, 0, 16);
    $encrypted = substr($decoded, 16);
    return openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
}

function tgl_indo($tanggal)
{
    $bulan = array(
        1 => 'Januari',
        'Febru<PERSON>',
        'Mare<PERSON>',
        'April',
        '<PERSON>',
        '<PERSON>i',
        '<PERSON>i',
        '<PERSON><PERSON><PERSON>',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecah = explode('-', $tanggal);
    $tahun = $pecah[0];
    $bulan = translate($bulan[(int)$pecah[1]]);
    $hari = ltrim($pecah[2], '0'); // hapus angka 0 di depan (misal 01 jadi 1)

    return $hari . ' ' . $bulan . ' ' . $tahun;
}

$q = 'false';
$tempAkses = "true";
$temp_info = "true";
if ((isset($_SESSION['users-pic']) || isset($_SESSION['users'])) && isset($_GET['q'])) {
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
    $q = decrypt_url($_GET['q'], $key);

    if ($q != "") {
        // cek apakah q terdaftar
        $get = $conn->prepare("SELECT
                                uk.nama_lengkap as nama,
                                uk.foto,
                                rh.jenis_kelamin,
                                rh.tempat_lahir,
                                rh.tgl_lahir,
                                rh.ktp,
                                rh.no_telepon,
                                rh.sim,
                                rh.email,
                                rh.status_pernikahan,
                                rh.alamat,
                                rh.rt,
                                rh.rw,
                                rh.kecamatan,
                                rh.kota,
                                rh.kode_pos,
                                rh.pengalaman_kerja,
                                rh.minat_gaji,
                                rh.minat_lokasi_kerja,
                                rh.perjalanan_dinas,
                                rh.bahasa_asing,
                                rh.kelebihan,
                                rh.kekurangan,
                                rh.ilmu_komputerisasi,
                                rh.memimpin_tim,
                                rh.kemampuan_presentasi,
                                rh.lingkup_pekerjaan,
                                rh.organisasi
                            FROM
                                users_kandidat	uk
                                JOIN rh ON uk.pin = rh.id
                            WHERE
                                rh.id = ?");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            $row = mysqli_fetch_array($result);
            $nama = htmlspecialchars(strtoupper($row['nama']));
            if ($row['foto'] != '') {
                if ($s3->doesObjectExist($bucket, 'kandidat/foto-profil/' . $row['foto'])) {
                    $cmd = $s3->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => 'kandidat/foto-profil/' . $row['foto']
                    ]);

                    $request = $s3->createPresignedRequest($cmd, '+24 hours');
                    $img = (string) $request->getUri();

                    $pas_foto = '<img width="70" height="80" src="' . $img . '" class="user-image" alt=".">';
                } else {
                    $pas_foto = '<img width="70" height="80" src="' . $row['foto'] . '" referrerpolicy="no-referrer" onerror="this.src=\'../../assets/images/content/img-avatar-default.png\';" class="user-image" alt=".">';
                }
            } else {
                $pas_foto = '<img width="70" height="80" src="../../assets/images/content/img-avatar-default.png" class="user-image" alt=".">';
            }

            $jenis_kelamin = translate($row['jenis_kelamin']);
            $tempat_lahir = htmlspecialchars($row['tempat_lahir']);
            $tgl_lahir = tgl_indo($row['tgl_lahir']);
            $ktp = htmlspecialchars($row['ktp']);
            $no_telepon = htmlspecialchars($row['no_telepon']);
            $sim = str_replace(",", ", ", htmlspecialchars($row['sim']));
            $email = htmlspecialchars($row['email']);
            $status_pernikahan = translate($row['status_pernikahan']);
            $alamat = htmlspecialchars($row['alamat']);
            $rt_rw = htmlspecialchars($row['rt'] . "/" . $row['rw']);
            $kecamatan  = htmlspecialchars(ucwords(strtolower($row['kecamatan'])));
            $kota = htmlspecialchars(ucwords(strtolower($row['kota'])));
            $kode_pos = htmlspecialchars($row['kode_pos']);
            $pengalaman_kerja = htmlspecialchars($row['pengalaman_kerja']);
            if ($row['minat_gaji'] > 0) {
                $minat_gaji = number_format($row['minat_gaji'], 0, ".", ".");
            } else {
                $minat_gaji = 0;
            }

            $minat_lokasi_kerja = htmlspecialchars(str_replace("Dimana Saja", translate("Dimana Saja"), ucwords(strtolower($row['minat_lokasi_kerja']))));
            $perjalanan_dinas = htmlspecialchars($row['perjalanan_dinas']);
            $bahasa_asing = htmlspecialchars($row['bahasa_asing']);
            $kelebihan = htmlspecialchars(str_replace(",", ", ", $row['kelebihan']));
            $kekurangan = htmlspecialchars(str_replace(",", ", ", $row['kekurangan']));
            $ilmu_komputerisasi = explode(",", htmlspecialchars($row['ilmu_komputerisasi']));
            $arr_memimpin = array("1" => 'Tidak Pernah', "2" => '1 - 3 Orang', "3" => '4 - 10 Orang', "4" => '11 - 50 Orang', "5" => 'Lebih dari 50 Orang');
            $memimpin_tim = translate($arr_memimpin[$row['memimpin_tim']]);
            if ($row['kemampuan_presentasi'] == '1') {
                $kemampuan_presentasi = 'Kurang';
            } else if ($row['kemampuan_presentasi'] == '2') {
                $kemampuan_presentasi = 'Cukup';
            } else if ($row['kemampuan_presentasi'] == '3') {
                $kemampuan_presentasi = 'Sangat Baik';
            } else {
                $kemampuan_presentasi = '-';
            }
            $lingkup_pekerjaan = htmlspecialchars($row['lingkup_pekerjaan']);
            $organisasi = htmlspecialchars($row['organisasi']);

            $user_id = "-";
            if (isset($_SESSION['users-pic']['id'])) {
                $user_id = addslashes($_SESSION['users-pic']['id']);
                $extra_info = "HRD";
            } elseif (isset($_SESSION['users']['pin'])) {
                $user_id = addslashes($_SESSION['users']['pin']);
                $extra_info = "Kandidat";
            } elseif (isset($_SESSION['users']['id'])) {
                $user_id = addslashes($_SESSION['users']['id']);
                $extra_info = "Super Admin";
            }

            // simpan log aktivitas
            $messages = 'Menampilkan digital cv kandidat ' . $q . ' atas nama ' . $nama . '.';
            $level = "INFO";
            $path = $_SERVER['REQUEST_URI'];
            logActivity($conn, $user_id, $level, $messages, $extra_info);
        } else {
            $tempAkses = "false";
        }
    } else {
        $tempAkses = "false";
    }
} else {
    $tempAkses = "false";
}

if ($tempAkses == 'false') {
    echo '<script>window.close();</script>';
}

$paket = "-";
if ($extra_info == "HRD") {
    $id_koordinator = $_SESSION['users-pic']['id_koordinator'];
    // cek apakah q terdaftar
    $get = $conn->prepare("SELECT paket
                            FROM
                                koordinator
                            WHERE
                                id_koordinator = ? ");
    $get->bind_param("s", $id_koordinator);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
        $row = mysqli_fetch_array($result);
        $paket = $row['paket'];
    }
}
?>

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?= $nama ?></title>
    <!-- Icon -->
    <link rel="icon" type="image/png" href="../../assets/images/logo/logo.png" />
    <style>
        .row {
            display: flex;
            flex-wrap: wrap;
            align-items: baseline;
            max-width: 1000px;
        }

        .long-text {
            width: 50%;
            text-align: justify;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
        }

        .col-0 {
            width: 1%;
        }

        .col-1 {
            width: 8.333333%;
        }

        .col-2 {
            width: 16.666667%;
        }

        .col-3 {
            width: 25%;
        }

        .col-4 {
            width: 33.333333%;
        }

        .col-5 {
            width: 41.666667%;
        }

        .col-6 {
            width: 50%;
        }

        .col-7 {
            width: 58.333333%;
        }

        .col-8 {
            width: 66.666667%;
        }

        .col-9 {
            width: 75%;
        }

        .col-10 {
            width: 83.333333%;
        }

        .col-11 {
            width: 91.666667%;
        }

        .col-12 {
            width: 100%;
            max-width: 1000px;
        }

        .table-container {
            overflow: visible;
            word-wrap: break-word;
        }

        .page_break {
            page-break-before: always !important;
            page-break-after: always !important;

        }

        .page_break_avoid {
            page-break-before: avoid;
        }

        .data-table {
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            -moz-border-collapse: collapse;
            box-sizing: border-box;
            background: white !important;
        }

        .long-text {
            text-align: justify;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .data-table thead tr {
            background-color: #2c3e50 !important;
            color: #ffffff !important;
        }

        .no-right-border {
            border-right: none;
        }

        .p-5 {
            padding: 4px;
        }

        .p-2 {
            padding: 2px;
        }

        .data-table th,
        .data-table td {
            font-family: Times New Roman, sans-serif;
            text-transform: capitalize;
            word-wrap: break-word;
        }

        .data-table th {
            font-size: 16px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .data-table tbody tr {
            border-bottom: 0px solid #000;
        }

        .border-right {
            border-right: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-right {
            border-top: 1px #000000 solid !important;
            border-left: 1px #000000 solid !important;
            border-bottom: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-left {
            border-top: 1px #000000 solid !important;
            border-right: 1px #000000 solid !important;
            border-bottom: 1px #000000 solid !important;
            box-sizing: border-box;
        }

        .border-no-bottom {
            border-bottom: 0px !important;
            box-sizing: border-box;
        }


        .border {
            box-sizing: border-box;
            border: 1px #000000 solid !important;
            -moz-box-sizing: border-box;
        }

        .no-border {
            border: 0px solid;
        }

        .qa-item {
            display: grid;
            grid-template-columns: 15px 1fr;
            width: 100%;
            /* Full width */
            page-break-inside: avoid;
        }

        .qa-content {
            display: flex;
            flex-direction: column;
        }

        @media print {
            .footer {
                position: absolute;
                right: 0;
                font-size: 12px;
                background-color: #DBDBDB;
                color: black;
                padding: 5px 10px;
                border-radius: 5px;
            }


            div {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
                page-break-before: auto !important;
            }


            * {
                @include box-shadow(none);
                @include text-shadow(none);
                clear: both;
            }


            @page {
                size: A4 portrait;
                margin: 7mm;
                font-size: 10px;

                @bottom-right {

                    font-weight: bold;
                    font-size: 10px;
                    content: "Halaman " counter(page) " dari " counter(pages);
                    position: relative;
                }

            }



            div {
                page-break-inside: avoid;
                page-break-after: auto;
                page-break-before: auto;
            }

            body {
                margin: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                background: white !important;
            }

            .avoid-break {
                break-inside: avoid;
                /* Prevents breaking inside an element */
            }

            .page_break {
                page-break-before: always !important;
                page-break-after: always !important;
                break-before: always !important;
                /* Modern alternative */
                break-after: always !important;
                /* Modern alternative */
            }

            .page_break_avoid {
                page-break-before: avoid;
                break-inside: avoid !important;
                /* Modern alternative */
                page-break-after: avoid;
            }

            .table-container {
                overflow: visible;
                border-collapse: collapse;
                -moz-border-collapse: collapse;
            }

            .data-table {
                width: 100% !important;
            }

            .border-no-right {
                -moz-box-sizing: border-box;
                border-top: 1px #000000 solid !important;
                border-left: 1px #000000 solid !important;
                border-bottom: 1px #000000 solid !important;
                box-sizing: border-box !important;

            }

            .border-no-left {
                -moz-box-sizing: border-box;
                border-top: 1px #000000 solid !important;
                border-right: 1px #000000 solid !important;
                border-bottom: 1px #000000 solid !important;
                box-sizing: border-box !important;
            }

            .data-table th {
                text-transform: capitalize;
            }

            .data-table th tr td {
                vertical-align: middle;
                overflow-wrap: break-word !important;
                white-space: normal;
                color: black !important;
            }

            .number {
                float: left;
                margin-right: 10px;
            }

            .question-text {
                margin-left: 25px;
            }

            .answer {
                margin-left: 25px;
                font-style: italic;
                color: #444;
            }

            .spacer {
                height: 10px;
                page-break-after: auto;
            }


            .col-0 {
                width: 1%;
            }

            .col-1 {
                width: 8.333333%;
            }

            .col-2 {
                width: 16.666667%;
            }

            .col-3 {
                width: 25%;
            }

            .col-4 {
                width: 33.333333%;
            }

            .col-5 {
                width: 41.666667%;
            }

            .col-6 {
                width: 50%;
            }

            .col-7 {
                width: 58.333333%;
            }

            .col-8 {
                width: 66.666667%;
            }

            .col-9 {
                width: 75%;
            }

            .col-10 {
                width: 83.333333%;
            }

            .col-11 {
                width: 91.666667%;
            }

            .col-12 {
                width: 100%;
            }

            .p-5 {
                padding: 4px;
            }

            .p-2 {
                padding: 2px;
            }


            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }

            border {
                -moz-box-sizing: border-box !important;
                page-break-before: always !important;
                box-sizing: border-box !important;
            }

            td {
                page-break-inside: avoid;
                word-wrap: break-word;
            }
        }


        .blurry-wrapper {
            position: relative;
            display: inline-block;
            cursor: help;
            padding: 2px 0;
        }

        .blurry-text {
            filter: blur(4px);
            transition: filter 0.3s ease-out;
            color: #000;
        }

        .info-text {
            position: absolute;
            top: 100%;
            left: 0;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
            background-color: rgba(0, 0, 0, 0.85);
            color: #fff;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            z-index: 10;
            pointer-events: none;
            transform: translateY(5px);
        }

        .blurry-wrapper:hover .info-text {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .link-example .blurry-text {
            color: #007bff;
            font-weight: bold;
        }

        .link-example .info-text {
            background-color: #007bff;
        }

        .blurry-wrapper .info-text.info-top {
            top: auto;
            bottom: 100%;
            transform: translateY(-5px);
        }

        .blurry-wrapper:hover .info-text.info-top {
            transform: translateY(0);
        }

        a.blurry-wrapper {
            text-decoration: none;
        }
    </style>
</head>

<body>
    <div class="table-container">
        <div class="row" style="margin-bottom:10px;">
            <div class="col-9 border-no-right">
                <div class="row" style="align-items :center; padding-top: 5px; padding-bottom:5px;">
                    <div class="col-6">
                        <img width="80" height="80" src="../../assets/images/logo/logoDcv2.png" class="user-image" alt="." style="margin-left: 20px; text-align:center;">
                    </div>
                    <div class="col-6">
                        <h3 style="margin: 0;">DIGITAL CV</h3>
                    </div>
                </div>
            </div>
            <div class="col-3 border">
                <div class="row" style="text-align :center; padding-top: 5px; padding-bottom:5px;">
                    <div class="col-12"><?= $pas_foto ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="table-container">
        <table class="data-table">
            <tr>
                <p class="col-12 p-2" style="background-color: #0d3b72;color:white;padding:5px;"><strong>I. <?= strtoupper(translate('Identitas')) ?></strong></p>
            </tr>
            <tr>
                <div class="row border " style="border-bottom:0px !important">
                    <div class="col-6 border-right ">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Nama Lengkap') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($nama) ?></div>
                        </div>
                    </div>
                    <div class="col-6 ">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Jenis Kelamin') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($jenis_kelamin) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-bottom:0px !important">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Tempat Lahir') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($tempat_lahir) ?></div>
                        </div>
                    </div>
                    <div class="col-6 ">
                        <div class="row  p-2">
                            <div class="col-5"><?= translate('Tanggal Lahir') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($tgl_lahir) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-bottom:0px !important">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('SIM') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($sim) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Nomor Handphone') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <?php
                            if ($paket == 'C1') {
                                echo '<span class="blurry-wrapper">
                                        <span class="blurry-text">
                                            ????????????????
                                        </span>
                                        <span class="info-text info-top">
                                            ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                        </span>
                                    </span>';
                            } else {
                                echo '<div class="col-6">' . htmlspecialchars($no_telepon) . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border">
                    <div class="col-6 border-right">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Status Pernikahan') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($status_pernikahan) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Alamat Email') ?></div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <?php
                            if ($paket == 'C1') {
                                echo '<span class="blurry-wrapper">
                                        <span class="blurry-text">
                                            ????????????????
                                        </span>
                                        <span class="info-text info-top">
                                            ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                        </span>
                                    </span>';
                            } else {
                                echo '<div class="col-6">' . htmlspecialchars($email) . '</div>';
                            }
                            ?>

                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-top: 0px !important;border-bottom: 0px !important;">
                    <div class="col-12">
                        <div class="row p-2">
                            <div class="col-11"><?= translate('Alamat Tinggal (Alamat Surat)') ?></div>
                            <div class="col-1"></div>
                        </div>
                    </div>
                </div>
            <tr>
                <div class="row border" style="border-top: 0px !important;border-bottom: 0px !important;">
                    <div class="col-12">
                        <div class="row p-2" style="margin-top:5px;">
                            <div class="col-12"><?= htmlspecialchars($alamat) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <div class="row border" style="border-top: 0px !important;">
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5" style="margin-top: 10px;"><?= translate('RT / RW') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6" style="margin-top: 10px;"><?= htmlspecialchars($rt_rw) ?></div>
                            <div class="col-5"><?= translate('Kec.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kecamatan) ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row p-2">
                            <div class="col-5"><?= translate('Kota / Kab.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kota) ?></div>
                            <div class="col-5"><?= translate('Kode Pos.') ?> </div>
                            <div class="col-1" style="width: 2% !important;">:</div>
                            <div class="col-6"><?= htmlspecialchars($kode_pos) ?></div>
                        </div>
                    </div>
                </div>
            </tr>
            <tr>
                <td class="p-5"></td>
            </tr>
        </table>
    </div>

    <?php
    // cek apakah ada data pendidikan formal
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_pendidikan rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY FIELD(rp.jenjang, 'SD', 'SMP', 'SMA', 'Diploma', 'S1', 'S2', 'S3')");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($result->num_rows > 0) {
    ?>
        <div class="table-container">
            <div>
                <p class="col-12 p-2" style="background-color: #0d3b72;color:white;padding:5px;"><strong>II. <?= strtoupper(translate('Pendidikan')) ?></strong></p>
            </div>

            <div>
                <td class=" p-2" colspan="9" style="height: 10%;">1. <?= translate('Pendidikan Formal') ?></td>
            </div>
            <table class="data-table">
                <tr>
                    <th class="border" style="width: 10%;"><?= translate('Jenjang') ?></th>
                    <th class="border" style="width: 25%;"><?= translate('Nama & Tempat (Kota)') ?></th>
                    <th class="border" style="width: 20%;"><?= translate('Prog. Studi / Jurusan') ?></th>
                    <th class="border" style="width: 20%;"><?= translate('Tahun') ?></th>
                    <th class="border" style="width: 10%;"><?= translate('Keterangan') ?></th>
                </tr>
                <?php
                while ($row = mysqli_fetch_array($result)) {
                    echo '<tr>
                            <td class="border" align="center">' . translate($row['jenjang']) . '</td>
                            <td class="border" align="center">' . htmlspecialchars($row['nama_sekolah']) . '</td>
                            <td class="border" align="center">' . htmlspecialchars($row['jurusan']) . '</td>
                            <td class="border" align="center">' . htmlspecialchars($row['tahun_mulai']) . ' ' . translate('s/d') . ' ' . htmlspecialchars($row['tahun_selesai']) . '</td>
                            <td class="border" align="center">' . translate($row['ket']) . '</td>
                        </tr>';
                }
                ?>
                <div>
                    <td class=" p-2" colspan="9" style="height: 10%;"></td>
                </div>
            </table>
        </div>
    <?php
    }

    // cek apakah ada data pendidikan formal
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_kursus rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY rp.tgl_mulai");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    echo '<div class="table-container">
            <table  class="data-table">';
    if ($result->num_rows > 0) {
        echo '<tr>
                <td class="p-5" colspan="9" style="height: 10%;"></td>
            </tr>
            <tr>
                <td class="p-2" colspan="9">2. ' . translate('Pendidikan Non Formal (Pelatihan / Training)') . '</td>
            </tr>
            <tr>
                <th class="border p-2" width="30%">' . translate('Pelatihan / Training') . '</th>
                <th class="border p-2" width="20%">' . translate('Tahun') . '</th>
                <th class="border p-2" width="20%">' . translate('Kota') . '</th>
                <th class="border p-2" width="10%" >' . translate('Sertifikat') . '</th>
            </tr>';
        while ($row = mysqli_fetch_array($result)) {
            echo '<tr>
                    <td class="border" align="center" width="30%">' . htmlspecialchars($row['nama']) . '</td>
                    <td class="border" align="center" width="20%" >' . date('m-Y', strtotime($row['tgl_mulai'])) . ' / ' . date('m-Y', strtotime($row['tgl_selesai'])) . '</td>
                    <td class="border" align="center" width="20%">' . htmlspecialchars($row['tempat']) . '</td>
                    <td class="border" align="center" width="10%" >' . translate($row['sertifikat']) . '</td>
                </tr>';
        }
    } else {
        echo '<tr>
                <td class="p-2" colspan="9" style="height: 10%;">2. ' . translate('Pendidikan Non Formal (Pelatihan / Training)') . ' : <strong>' . translate('Tidak Ada') . '</strong></td>
            </tr>';
    }
    echo '</table>
        </div>';

    // cek apakah riwayat pekerjaan ada
    $get = $conn->prepare("SELECT
                                rp.*
                            FROM
                                users_kandidat uk
                                JOIN riwayat_pekerjaan rp ON rp.id = uk.pin
                            WHERE
                                uk.pin = ?
                            ORDER BY CONCAT(SUBSTRING(rp.tahun_mulai, 4, 4), '-', SUBSTRING(rp.tahun_mulai, 1, 2)) ASC");
    $get->bind_param("s", $q);
    $get->execute();
    $result = $get->get_result();
    $get->close();

    if ($pengalaman_kerja == 'Tidak') {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. ' . strtoupper(translate('Riwayat pekerjaan dan aktivitas pekerjaan')) . '</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. ' . translate('Riwayat Pekerjaan') . '?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak Ada') . '</i></strong></div>
            </div>';
    } elseif ($pengalaman_kerja == 'Fresh Graduate') {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. ' . strtoupper(translate('Riwayat pekerjaan dan aktivitas pekerjaan')) . '</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. ' . translate('Riwayat Pekerjaan') . '?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Fresh Graduate</i></strong></div>
            </div>';
    } elseif ($pengalaman_kerja == 'Ya' && $result->num_rows > 0) {
        echo ' <div style="margin-top: 10px;">
                    <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. ' . strtoupper(translate('Riwayat pekerjaan dan aktivitas pekerjaan')) . '</strong></p>
                </div>

                <div style="margin-top: 10px;">
                    <p class="p-2">1. ' . translate('Riwayat Pekerjaan') . ' </p>
                </div>';
        $temp = 1;
        $arr_riwayat_pekerjaan = array();
        $last_row = $result->num_rows;
        $no = 1;
        while ($row = mysqli_fetch_array($result)) {
            if ($row['gaji'] > 0) {
                $temp_gaji = number_format($row['gaji'], 0, ".", ".");
            } else {
                $temp_gaji = 0;
            }

            if ($temp == 1) {
                $temp_array = array();
                $temp_array[] = array('nama_perusahaan' => $row['nama_perusahaan'], 'jabatan' => $row['jabatan'], 'gaji' => $temp_gaji, 'tahun' => $row['tahun_mulai'] . ' ' . translate('s/d') . ' ' . $row['tahun_selesai'], 'alasan_berhenti' => $row['alasan_berhenti']);
                $temp = 2;
            } else {
                $temp_array[] = array('nama_perusahaan' => $row['nama_perusahaan'], 'jabatan' => $row['jabatan'], 'gaji' => $temp_gaji, 'tahun' => $row['tahun_mulai'] . ' ' . translate('s/d') . ' ' . $row['tahun_selesai'], 'alasan_berhenti' => $row['alasan_berhenti']);
                $arr_riwayat_pekerjaan[] = $temp_array;
                $temp = 1;
            }

            if ($last_row == $no && $last_row % 2 != 0) {
                $arr_riwayat_pekerjaan[] = $temp_array;
            }

            $no++;
        }

        if (count($arr_riwayat_pekerjaan) > 0) {
            for ($i = 0; $i < count($arr_riwayat_pekerjaan); $i++) {
                $arr = $arr_riwayat_pekerjaan[$i];
                if (isset($arr[0])) {
                    if (isset($arr[1])) {
                        $nama_perusahaan2 = '<td style="width: 50%;  margin-top:20px;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-5">' . translate('Nama Perusahaan') . '</div>
                                                            <div class="col-1" style="width: 2% !important;">:</div>
                                                            <div class="col-6">' . htmlspecialchars($arr[1]['nama_perusahaan']) . '</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                        $jabatan2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">' . translate('Jabatan') . '</div>
                                                    <div class="col-1" style="width: 2% !important;">:</div>
                                                    <div class="col-6">' . htmlspecialchars($arr[1]['jabatan']) . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>';
                        if ($paket == 'C1') {
                            $blur_gaji = '<span class="blurry-wrapper">
                                                    <span class="blurry-text">
                                                        ????????????????
                                                    </span>
                                                    <span class="info-text info-top">
                                                        ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                                    </span>
                                                </span>';
                        } else {
                            $blur_gaji = '<div class="col-6">Rp. ' . htmlspecialchars($arr[1]['gaji']) . '</div>';
                        }
                        $gaji2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5">' . translate('Gaji Net') . '</div>
                                                <div class="col-1" style="width: 2% !important;">:</div>
                                                ' . $blur_gaji . '
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $tahun2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5">' . translate('Tahun') . ' (..<span style="text-transform: lowercase !important">' . translate('s/d') . '</span>..)</div>
                                                <div class="col-1" style="width: 2% !important;">:</div>
                                                <div class="col-6">' . htmlspecialchars($arr[1]['tahun']) . '</div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        if ($paket == 'C1') {
                            $blur_alasan2 = '<span class="blurry-wrapper">
                                                    <span class="blurry-text">
                                                        ????????????????
                                                    </span>
                                                    <span class="info-text info-top">
                                                        ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                                    </span>
                                                </span>';
                        } else {
                            $blur_alasan2 = '<div class="col-12">' . htmlspecialchars($arr[1]['alasan_berhenti']) . '</div>';
                        }
                        $alasan_berhenti2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-12">' . translate('Alasan Berhenti') . ' </div>
                                                            ' . $blur_alasan2 . '
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                    } else {
                        $nama_perusahaan2 = '<td style="width: 50%;  margin-top:20px;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-5"></div>
                                                            <div class="col-1" style="width: 2% !important;"></div>
                                                            <div class="col-6"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                        $jabatan2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5"></div>
                                                    <div class="col-1" style="width: 2% !important;"></div>
                                                    <div class="col-6"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>';
                        $gaji2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5"></div>
                                                <div class="col-1" style="width: 2% !important;"></div>
                                                <div class="col-6"></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $tahun2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-5"></div>
                                                <div class="col-1" style="width: 2% !important;"></div>
                                                <div class="col-6"></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>';
                        $alasan_berhenti2 = '<td style="width: 50%;" class="border-no-left p-2"  colspan="2"  align="left" valign="middle">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="row">
                                                            <div class="col-12"></div>
                                                            <div class="col-12"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>';
                    }

                    echo '<div class="table-container" style="margin-top:20px;">
                            <table  class="data-table">
                                <tr>
                                    <td style="width: 50%; " class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">' . translate('Nama Perusahaan') . '</div>
                                                    <div class="col-1" style="width: 2% !important;">:</div>
                                                    <div class="col-6">' . htmlspecialchars($arr[0]['nama_perusahaan']) . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $nama_perusahaan2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">' . translate('Jabatan') . '</div>
                                                    <div class="col-1" style="width: 2% !important;">:</div>
                                                    <div class="col-6">' . htmlspecialchars($arr[0]['jabatan']) . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $jabatan2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">' . translate('Gaji Net') . '</div>
                                                    <div class="col-1" style="width: 2% !important;">:</div>';
                    if ($paket == 'C1') {
                        echo '<span class="blurry-wrapper">
                                                    <span class="blurry-text">
                                                        ????????????????
                                                    </span>
                                                    <span class="info-text info-top">
                                                        ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                                    </span>
                                                </span>';
                    } else {
                        echo '<div class="col-6">Rp. ' . htmlspecialchars($arr[0]['gaji']) . '</div>';
                    }

                    echo '</div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $gaji2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-5">' . translate('Tahun') . ' (..<span style="text-transform: lowercase !important">' . translate('s/d') . '</span>..)</div>
                                                    <div class="col-1" style="width: 2% !important;">:</div>
                                                    <div class="col-6">' . htmlspecialchars($arr[0]['tahun']) . '</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $tahun2 . '
                                </tr>
                                <tr>
                                    <td style="width: 50%;" class="border p-2"  colspan="2"  align="left" valign="middle">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-12">' . translate('Alasan Berhenti') . ' </div>';
                    if ($paket == 'C1') {
                        echo '
                                            <span class="blurry-wrapper">
                                                <span class="blurry-text">
                                                    ????????????????
                                                </span>
                                                <span class="info-text info-top">
                                                    ' . translate("Tidak dapat melihat informasi, untuk membuka informasi silakan upgrade layanan anda") . '.
                                                </span>
                                            </span>';
                    } else {
                        echo '<div class="col-12">' . htmlspecialchars($arr[0]['alasan_berhenti']) . '</div>';
                    }
                    echo '</div>
                                            </div>
                                        </div>
                                    </td>
                                    ' . $alasan_berhenti2 . '
                                </tr>
                            </table>
                        </div>';
                }
            }
        } else {
            echo '<div style="margin-top: 10px;">
                    <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. ' . strtoupper(translate('Riwayat pekerjaan dan aktivitas pekerjaan')) . '</strong></p>
                </div>
                <div style="margin-top: 10px;">
                    <p class="p-2">1. ' . translate('Riwayat Pekerjaan') . ' : <strong><i>-</i></strong></p>
                </div>';
        }
    } else {
        echo '<div style="margin-top: 10px;">
                <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>III. ' . strtoupper(translate('Riwayat pekerjaan dan aktivitas pekerjaan')) . '</strong></p>
            </div>
            <div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. ' . translate('Riwayat Pekerjaan') . '?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak Ada') . '</i></strong></div>
            </div>';
    }
    ?>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">2. <?= translate('Sebutkan gaji yang Anda inginkan') ?>?<br>&nbsp;&nbsp;&nbsp;&nbsp;<strong><i>Rp. <?= htmlspecialchars($minat_gaji) ?></i></strong></div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">3. <?= translate('Lokasi kerja yang diminati') ?>?<br> &nbsp;&nbsp;&nbsp;&nbsp;<strong><i><?= htmlspecialchars($minat_lokasi_kerja) ?></i></strong></div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="col-12">4. <?= translate('Apakah anda bersedia melakukan perjalanan dinas') ?>?<br> &nbsp;&nbsp;&nbsp;&nbsp;<strong><i><?= translate($perjalanan_dinas) ?></i></strong></div>
    </div>

    <div classs="" style="margin-top: 20px;">
        <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>IV. <?= strtoupper(translate('Minat dan Konsep Pribadi')) ?></strong></p>
    </div>

    <?php
    if ($bahasa_asing == 'Ya') {
        // cek apakah riwayat pekerjaan ada
        $get = $conn->prepare("SELECT
            rp.*
        FROM
            users_kandidat uk
            JOIN penguasaan_bahasa rp ON rp.id = uk.pin
        WHERE
            uk.pin = ?
        ORDER BY bahasa ASC");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            echo '<div class="row "  style="margin-top: 10px;">
                    <div class="col-12">1. ' . translate('Penguasaan Bahasa Asing') . ' </div>
                </div>
                <div class="table-container" style="margin-top: 10px;">
                    <table  class="data-table">
                    <tr>
                        <th class="border p-2" align="center" >' . translate('Bahasa asing yang dikuasai') . '</th>
                        <th class="border p-2" align="center" >' . translate('Membaca') . '</th>
                        <th class="border p-2" align="center" >' . translate('Menulis') . '</th>
                        <th class="border p-2" align="center" >' . translate('Mendengar') . '</th>
                        <th class="border p-2" align="center" >' . translate('Berbicara') . '</th>
                    </tr>';
            while ($row = mysqli_fetch_array($result)) {
                echo '<tr>
                        <td class="border p-2" align="center" >' . htmlspecialchars($row['bahasa']) . '</td>
                        <td class="border p-2" align="center" >' . htmlspecialchars($row['membaca']) . '</td>
                        <td class="border p-2" align="center" >' . htmlspecialchars($row['menulis']) . '</td>
                        <td class="border p-2" align="center" >' . htmlspecialchars($row['mendengar']) . '</td>
                        <td class="border p-2" align="center" >' . htmlspecialchars($row['berbicara']) . '</td>
                    </tr>';
            }

            echo '</table>
                </div>
                <div">
                    <div class="col-12">' . translate('Keterangan : BS (Baik Sekali), B (Baik), C (Cukup), K (Kurang), KS (Kurang sekali)') . '</div>
                </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="col-12">1. ' . translate('Penguasaan Bahasa Asing') . ' :<br>  &nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak Menguasai Bahasa Asing') . '</i></strong></div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. ' . translate('Penguasaan Bahasa Asing') . ' :<br>  &nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak Menguasai Bahasa Asing') . '</i></strong></div>
            </div>';
    }
    ?>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>2.</span>
            <div class="qa-content">
                <div class="qa-question"><?= translate('Menurut Anda, apa kelebihan yang Anda miliki') ?>? </div>
                <div class="qa-question"><strong><i><?= $kelebihan ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>3.</span>
            <div class="qa-content">
                <div class="qa-question"><?= translate('Menurut Anda, apa yang berpotensi menjadi hambatan/kekurangan Anda') ?>? </div>
                <div class="qa-question"><strong><i><?= $kekurangan ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>4.</span>
            <div class="qa-content">
                <div class="qa-question"><?= translate('Bagaimana pengetahuan Anda dibidang komputerisasi') ?>? </div>
                <?php
                if (count($ilmu_komputerisasi) > 0) {
                    $arr_kk = array(
                        '1' => 'Mengetahui dasar dan pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)',
                        '2' => 'Mengerti dan Menguasai pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)',
                        '3' => 'Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya',
                        '4' => 'Menguasai Pemograman dan Analist Pemograman',
                        '5' => 'Spesialis Komputer Sains',
                        '6' => 'Tidak menguasai'
                    );

                    for ($i = 0; $i < count($ilmu_komputerisasi); $i++) {
                        $temp = $ilmu_komputerisasi[$i];
                        echo '<div class="qa-question"><strong><i> - ' . translate($arr_kk[$temp]) . '</i></strong> </div>';
                    }
                } else {
                    echo '<div class="qa-question"><strong><i>-</i></strong> </div>';
                }
                ?>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>5.</span>
            <div class="qa-content">
                <div class="qa-question"><?= translate('Berapa jumlah terbanyak yang pernah anda pimpin dalam perusahaan') ?>? </div>
                <div class="qa-question"><strong><i><?= translate($memimpin_tim) ?></i></strong> </div>
            </div>
        </div>
    </div>
    <div class="row border" style="margin-top: 10px;">
        <div class="qa-item">
            <span>6.</span>
            <div class="qa-content">
                <div class="qa-question"><?= translate('Berikan penilaian terhadap kemampuan Anda dalam presentasi/berbicara di depan umum') ?>? </div>
                <div class="qa-question"><strong><i><?= translate($kemampuan_presentasi) ?></i></strong> </div>
            </div>
        </div>
    </div>
    <?php
    // cek apakah ruang lingkup pekerjaan ada
    if ($lingkup_pekerjaan != "") {
        $get = $conn->query("SELECT
            *
        FROM
            ruang_lingkup_pekerjaan
        WHERE
            id IN ($lingkup_pekerjaan)
        ORDER BY id ASC");

        if ($get->num_rows > 0) {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="qa-item">
                        <span>7.</span>
                        <div class="qa-content">
                            <div class="qa-question">' . translate('Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai') . '? </div>';
            while ($row = mysqli_fetch_array($get)) {
                echo '<div class="qa-question"><strong><i> - ' . translate($row['name']) . '</i></strong> </div>';
            }
            echo '</div>
                </div>
            </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="qa-item">
                        <span>7.</span>
                        <div class="qa-content">
                            <div class="qa-question">' . translate('Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai') . '? </div>
                            <div class="qa-question"><strong><i>-</i></strong> </div>
                        </div>
                    </div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="qa-item">
                    <span>7.</span>
                    <div class="qa-content">
                        <div class="qa-question">' . translate('Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai') . '? </div>
                        <div class="qa-question"><strong><i>-</i></strong> </div>
                    </div>
                </div>
            </div>';
    }
    ?>
    <div style="margin-top: 10px;">
        <p class="col-12 p-2" colspan="9" style="background-color: #0d3b72;color:white;padding:5px; "><strong>V. <?= strtoupper(translate('Aktivitas sosial')) ?></strong></p>
    </div>
    <?php
    if ($organisasi == 'Ya') {
        // cek apakah riwayat organisasi ada
        $get = $conn->prepare("SELECT
            rp.*
        FROM
            users_kandidat uk
            JOIN riwayat_organisasi rp ON rp.id = uk.pin
        WHERE
            uk.pin = ?
        ORDER BY tahun ASC");
        $get->bind_param("s", $q);
        $get->execute();
        $result = $get->get_result();
        $get->close();

        if ($result->num_rows > 0) {
            echo '<div class="row" style="margin-top: 10px;">
                    <div class="col-12">1. ' . translate('Sebutkan pengalaman organisasi Anda! (Organisasi Sosial, Politik, Kemasyarakatan, Serikat Pekerja)') . ' </div>
                </div>
                <div class="table-container" style="margin-top: 10px;">
                    <table  class="data-table">
                        <tr>
                            <th class="border p-2" width="25%">' . translate('Nama Organisasi') . '</th>
                            <th class="border p-2"  width="25%">' . translate('Tempat') . '</th>
                            <th class="border p-2"  width="25%">' . translate('Jabatan') . '</th>
                            <th class="border p-2"  width="25%">' . translate('Tahun') . '</th>
                        </tr>';
            while ($row = mysqli_fetch_array($result)) {
                echo '<tr>
                        <td class="border p-2" align="center">' . htmlspecialchars($row['nama']) . '</td>
                        <td class="border p-2" align="center">' . htmlspecialchars($row['tempat']) . '</td>
                        <td class="border p-2" align="center">' . htmlspecialchars($row['jabatan']) . '</td>
                        <td class="border p-2" align="center">' . htmlspecialchars($row['tahun']) . '</td>
                    </tr>';
            }

            echo '</table>
                </div>';
        } else {
            echo '<div class="row border" style="margin-top: 10px;">
                    <div class="col-12">1. ' . translate('Sebutkan pengalaman organisasi Anda') . '  :  &nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak mengikuti Organisasi') . '</i></strong></div>
                </div>';
        }
    } else {
        echo '<div class="row border" style="margin-top: 10px;">
                <div class="col-12">1. ' . translate('Sebutkan pengalaman organisasi Anda') . '  :  &nbsp;&nbsp;&nbsp;<strong><i>' . translate('Tidak mengikuti Organisasi') . '</i></strong></div>
            </div>';
    }
    ?>
</body>
<!-- <script>
    window.onload = function() {
        window.print();
    };
</script> -->

</html>