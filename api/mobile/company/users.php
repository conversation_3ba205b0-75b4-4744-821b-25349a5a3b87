<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';
include '../../jwt_helper.php';


// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$email_perusahaan = $userData->email;
$divisi = $userData->divisi;
$company = $userData->id_koordinator;
$id_pegawai = $userData->id;

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function get_email_from_lamar($id_lamar, $conn)
{
    $sql = "SELECT
                uk.email
            FROM
                `users_lamar`  ul
                JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
            WHERE
                id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $email = $row['email'];
    } else {
        $email = null;
    }
    return $email;
}

function get_posisi($id_req, $conn)
{
    $sql = "SELECT posisi FROM list_request WHERE id_req = '$id_req'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $posisi = $row['posisi'];
    } else {
        $posisi = null;
    }
    return $posisi;
}

function get_nama_kandidat($id_lamar, $conn)
{
    $sql = "SELECT
        uk.nama_lengkap
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $nama_lengkap = $row['nama_lengkap'];
    } else {
        $nama_lengkap = null;
    }
    return $nama_lengkap;
}

function update_proses_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "On Proccess Digitalcv";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "On Proccess Interview";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "On Proccess Psikotes";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "On Proccess Validasi";
    } elseif ($text == 'VALIDASI') {
        $status_update = "On Proccess Offering";
    } elseif ($text == 'OFFERING') {
        $status_update = "On Proccess Medical";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Sudah Diterima";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_close_status_lowongan($id_lamar, $conn, $text)
{
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    }
    $sql = "UPDATE users_lamar SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_terima_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Terima Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Terima Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Terima Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Terima Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Terima Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Terima Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Terima Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Terima Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function update_tolak_history_lowongan($id_lamar, $conn, $text, $id_req, $company)
{
    $tgl = date('Y-m-d H:i:s');
    $pincode = get_pin($id_lamar, $conn);
    if ($text == 'SCREENING') {
        $status_update = "Tolak Screening";
    } elseif ($text == 'DIGITALCV') {
        $status_update = "Tolak Digitalcv";
    } elseif ($text == 'INTERVIEW') {
        $status_update = "Tolak Interview";
    } elseif ($text == 'PSIKOTES') {
        $status_update = "Tolak Psikotes";
    } elseif ($text == 'VALIDASI') {
        $status_update = "Tolak Validasi";
    } elseif ($text == 'OFFERING') {
        $status_update = "Tolak Offering";
    } elseif ($text == 'MEDICAL') {
        $status_update = "Tolak Medical";
    } elseif ($text == 'REKRUTMEN') {
        $status_update = "Tolak Rekrutmen";
    }
    //cek data
    $sql_cek = "SELECT * FROM users_lamar_history WHERE id_lamar = '$id_lamar' AND `status` LIKE '%$text%'";
    $result_cek = $conn->query($sql_cek);
    if (mysqli_num_rows($result_cek) > 0) {
        $sql = "UPDATE users_lamar_history SET status = '$status_update' WHERE id_lamar = '$id_lamar'";
    } else {
        $sql = "INSERT INTO `users_lamar_history`
                    (`id_lamar`, `id_gestalt`, `id_req`, `id_koordinator`, `status`, `tgl`) 
                    VALUES
                    ('$id_lamar', '$pincode', '$id_req', '$company', '$status_update', '$tgl')";
    }

    $result = $conn->query($sql);
    if ($result === true) {
        return 'Ok';
    } else {
        return 'No';
    }
}

function get_pin($id_lamar, $conn)
{
    $sql = "SELECT
        uk.pin
    FROM
        `users_lamar`  ul
        JOIN `users_kandidat` uk ON ul.id_gestalt = uk.pin
    WHERE
        id_lamar = '$id_lamar'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $pin = $row['pin'];
    } else {
        $pin = null;
    }
    return $pin;
}

function get_company($company, $conn)
{
    $data = [];
    $sql = "SELECT * FROM koordinator WHERE id_koordinator = '$company'";
    $result = $conn->query($sql);
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $data['nama_perusahaan'] = $row['label'];
        $data['img'] = $row['img'];
        $data['alamat'] = $row['alamat'];
    } else {
        $data = null;
    }
    return $data;
}


function cek_feedback_hrd($conn, $id_lamar)
{
    //cek review = HRD
    $sql = "SELECT approval FROM feedback WHERE id_lamar = '$id_lamar' AND review = 'HRD'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['approval'];
}

function getInitials($name)
{
    $words = explode(' ', strtoupper($name));
    $initials = '';
    foreach ($words as $w) {
        if ($w !== '') {
            $initials .= $w[0];
        }
    }
    return substr($initials, 0, 2); // maksimal 2 huruf
}

function selisihTahunBulan($tanggal_awal, $tanggal_akhir)
{
    $tanggal_awal = DateTime::createFromFormat('m-Y', $tanggal_awal);
    $tanggal_awal = $tanggal_awal->format('Y-m-d');

    $tanggal_akhir = DateTime::createFromFormat('m-Y', $tanggal_akhir);
    $tanggal_akhir = $tanggal_akhir->format('Y-m-d');

    $awal  = new DateTime($tanggal_awal);
    $akhir = new DateTime($tanggal_akhir);

    if ($awal > $akhir) {
        // Tukar jika tanggal awal lebih besar dari akhir
        $temp = $awal;
        $awal = $akhir;
        $akhir = $temp;
    }

    $selisih = $awal->diff($akhir);

    $tahun = $selisih->y;
    $bulan = $selisih->m;

    $output = "";

    if ($tahun > 0) {
        $output .= $tahun . " Tahun";
    }

    if ($bulan > 0) {
        if ($output !== "") {
            $output .= " ";
        }
        $output .= $bulan . " Bulan";
    }

    return $output === "" ? "0 Bulan" : $output;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}

// fungsi untuk validasi password
function validatePassword($password)
{
    $errors = [];

    // Cek minimal 8 karakter
    if (strlen($password) < 8) {
        $errors[] = "Password minimal 8 karakter";
    }

    // Cek mengandung minimal 1 huruf besar
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 huruf besar";
    }

    // Cek mengandung minimal 1 angka
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 angka";
    }

    // Cek mengandung minimal 1 karakter khusus
    if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
        $errors[] = "Password harus mengandung minimal 1 karakter khusus (!@#$%^&*()_+-=[]{}|;:,.<>?)";
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

// fungsi untuk melakukan hash password
function hashPassword($password)
{
    return password_hash($password, PASSWORD_BCRYPT);
}

function verifyPassword($inputPassword, $hashedPassword)
{
    return password_verify($inputPassword, $hashedPassword);
}

// function buat id unik
function generateUUID()
{
    return strtoupper(bin2hex(random_bytes(8))); // 16 karakter unik
}

if (isset($_POST['akses'])) {
    $akses = $_POST['akses'];
} else {
    $akses = "";
}

$func = $_GET['func'];

if ($func == 'getDataUsers') {
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch .= " AND (
            nama LIKE '%$q%' OR 
            id_koordinator LIKE '%$q%'  OR 
            email LIKE '%$q%' 
        )";
    }

    $data = array();

    // Hitung total data
    $sqlCount = $conn->prepare("SELECT COUNT(DISTINCT id_pic) AS total FROM koordinator_pic 
                 WHERE id_koordinator = ? AND `role` != 'master' $querySearch");
    $sqlCount->bind_param("s", $company);
    $sqlCount->execute();
    $resultCount = $sqlCount->get_result();
    $sqlCount->close();

    $rowCount = mysqli_fetch_array($resultCount);
    $totalData = intval($rowCount['total']);
    $totalPage = $totalData > 0 ? ceil($totalData / $pageSize) : 1;

    // Ambil data sesuai pagination
    $sqlData = $conn->prepare("SELECT * FROM koordinator_pic 
                WHERE id_koordinator = ? AND `role` != 'master' $querySearch
                GROUP BY id_pic
                ORDER BY nama
                LIMIT ?, ?");
    $sqlData->bind_param("sii", $company, $offset, $pageSize);
    $sqlData->execute();
    $queryData = $sqlData->get_result();
    $sqlData->close();

    $no = $offset;

    while ($rowData = mysqli_fetch_assoc($queryData)) {
        $no++;

        $data[] = array(
            "no" => $no,
            "id_pic" => $rowData['id_pic'],
            "id_koordinator" => $rowData['id_koordinator'],
            "nama" => $rowData['nama'],
            "email" => $rowData['email'],
            "no_hp" => $rowData['no_hp'],
            "posisi" => $rowData['posisi'],
            "img" => $rowData['img'],
            "role" => $rowData['role'],
            "fitur" => $rowData['fitur'],
        );
    }

    $output = array(
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    );

    echo json_encode(utf8ize($output), JSON_PRETTY_PRINT);
}


if ($func == 'penambahanUser') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        $getKoor = $conn->prepare("SELECT * FROM koordinator WHERE id_koordinator = ?");
        $getKoor->bind_param("s", $id_koordinator);
        $getKoor->execute();
        $result = $getKoor->get_result();
        $getKoor->close();

        $rowKoor = mysqli_fetch_array($result);
        $paket = preg_replace("/[^0-9]/", "", $rowKoor['paket']);

        // cek apakah limit penambahan user sudah terpenuhi
        if ($paket > 2) {
            // get jumlah user yang sudah terdaftar
            $getJmlUser = $conn->prepare("SELECT * FROM koordinator_pic WHERE id_koordinator = ?");
            $getJmlUser->bind_param("s", $id_koordinator);
            $getJmlUser->execute();
            $jmlUser = $getJmlUser->get_result();
            $getJmlUser->close();

            if (($paket == 3 && $jmlUser->num_rows >= 3) || ($paket == 4 && $jmlUser->num_rows >= 10)) {
                throw new Exception("Tidak dapat melakukan penambahan user, karena limit sudah terpenuhi.");
            }
        } else {
            throw new Exception("Akun anda tidak dapan melakukan penambahan user.");
        }

        if (!isset($_POST['nama']) || !isset($_POST['email']) || !isset($_POST['no_hp']) || !isset($_POST['password']) || !isset($_POST['kon_password'])) {
            throw new Exception("Tidak dapat menyimpan data.");
        }

        $nama = $_POST['nama'];
        $email = $_POST['email'];
        $no_hp = $_POST['no_hp'];
        $otp = $_POST['otp'];
        $password = $_POST['password'];
        $kon_password = $_POST['kon_password'];

        //cek otp
        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
        $sql->bind_param("ss", $otp, $email);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            // Validasi waktu expired OTP (5 menit)
            $row = mysqli_fetch_array($result);
            $otpDate = new DateTime($row['date']);
            $currentDate = new DateTime();
            $interval = $currentDate->diff($otpDate);
            $minutesDiff = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

            if ($minutesDiff > 5) {
                throw new Exception("OTP sudah kadaluarsa. Silakan minta OTP baru.");
            }
            // Cek apakah OTP sudah pernah digunakan
            if ($row['status'] == 'Finish') {
                throw new Exception("OTP sudah pernah digunakan. Silakan minta OTP baru.");
            }
            // Update Status OTP
            $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
            $update->bind_param("ss", $otp, $email);

            if ($update->execute()) {
                $update->close();
            } else {
                throw new Exception("Pendaftaran Gagal.");
            }
        } else {
            throw new Exception("OTP Salah.");
        }

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data.");
        }

        // cek apakah kata sandi sudah sama
        if ($password != $kon_password) {
            throw new Exception("Kata sandi tidak sama!");
        }

        // Validasi password sebelum hashing
        $passwordValidation = validatePassword($password);
        if (!$passwordValidation['valid']) {
            throw new Exception("Password tidak memenuhi syarat: " . implode(", ", $passwordValidation['errors']));
        }

        $password = hashPassword($_POST['password']);

        // cek apakah email sudah terdaftar atau belum
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            email = ?");
        $sql->bind_param("s", $email);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows > 0) {
            throw new Exception("Email sudah terdaftar!");
        }

        // generate id pic
        $sqlCek = "SELECT id_pic FROM koordinator_pic";
        $queryCek = $conn->query($sqlCek);

        do {
            $id_pic = "PIC-" . generateUUID();
        } while ($id_pic == $queryCek);

        // get fitur default untuk user
        $getFitur = $conn->query("SELECT id_fitur FROM list_fitur LIMIT 1");
        $rowFitur = mysqli_fetch_array($getFitur);
        $fitur_default = $rowFitur['id_fitur'];

        // Proses penyimpanan data koordinator pic
        $insert = $conn->prepare("INSERT INTO `koordinator_pic` (`id_pic`, `id_koordinator`, `nama`, `email`, `password`, `no_hp`, `status`, `create_at`, `role`, `fitur`) 
        VALUES (?, ?, ?, ?, ?, ?, 'Active', ?, 'normal', ?)");
        $insert->bind_param("ssssssss", $id_pic, $id_koordinator, $nama, $email, $password, $no_hp, $created_at, $fitur_default);

        if ($insert->execute()) {
            $insert->close();

            // simpan log aktivitas
            $aktivitas = 'Input user baru dengan id user ' . $id_pic . '.';
            $extra_info = "HRD";
            $level = "INFO";
            logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Penambahan user berhasil.";
        } else {
            throw new Exception("Tidak dapat menyimpan data.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'getDataPengaturanUsers') {

    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch .= " AND (
            nama LIKE '%$q%' OR 
            id_koordinator LIKE '%$q%'  OR 
            email LIKE '%$q%' 
        )";
    }

    $data = array();

    // Hitung total data
    $sqlCount = $conn->prepare("SELECT COUNT(DISTINCT id_pic) AS total 
                 FROM koordinator_pic 
                 WHERE id_koordinator = ? 
                   AND `role` != 'master' 
                   AND id_pic != ?
                   $querySearch");
    $sqlCount->bind_param("ss", $company, $id_pegawai);
    $sqlCount->execute();
    $resultCount = $sqlCount->get_result();
    $sqlCount->close();

    $rowCount = mysqli_fetch_array($resultCount);
    $totalData = intval($rowCount['total']);
    $totalPage = $totalData > 0 ? ceil($totalData / $pageSize) : 1;

    // Ambil data sesuai pagination
    $sqlData = $conn->prepare("SELECT * FROM koordinator_pic 
                WHERE id_koordinator = ?
                  AND `role` != 'master' 
                  AND id_pic != ?
                  $querySearch
                GROUP BY id_pic
                ORDER BY nama
                LIMIT ?, ?");
    $sqlData->bind_param("ssii", $company, $id_pegawai, $offset, $pageSize);
    $sqlData->execute();
    $resultData = $sqlData->get_result();
    $sqlData->close();

    $no = $offset;

    while ($rowData = mysqli_fetch_assoc($resultData)) {
        $no++;
        $data[] = array(
            "no" => $no,
            "id_pic" => $rowData['id_pic'],
            "nama" => $rowData['nama'],
            "email" => $rowData['email'],
            "no_hp" => $rowData['no_hp'],
        );
    }

    $output = array(
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    );

    echo json_encode(utf8ize($output), JSON_PRETTY_PRINT);
}


if ($func == 'updateDataUser') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['nama']) || !isset($_POST['email']) || !isset($_POST['no_hp']) || !isset($_POST['id_pic'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $nama = $_POST['nama'];
        $email = $_POST['email'];
        $otp = $_POST['otp'];
        $no_hp = $_POST['no_hp'];
        $id_pic = base64_decode($_POST['id_pic']);

        //cek otp
        // cek apakah email berubah, jika berubah maka perlu verifikasi OTP
        $sql_check_email = $conn->prepare("SELECT email FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $sql_check_email->bind_param("ss", $id_pic, $id_koordinator);
        $sql_check_email->execute();
        $result_check = $sql_check_email->get_result();
        $sql_check_email->close();
        
        $row_email = mysqli_fetch_array($result_check);
        $email_lama_check = $row_email['email'];
        
        // jika email berubah, maka perlu verifikasi OTP
        if ($email != $email_lama_check) {
            $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
            $sql->bind_param("ss", $otp, $email);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                // Update Status OTP
                $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                $update->bind_param("ss", $otp, $email);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception("Pendaftaran Gagal.");
                }
            } else {
                throw new Exception("OTP Salah.");
            }
        }

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah id pic ada
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pic, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan diubah tidak terdaftar.");
        }

        $row = mysqli_fetch_array($result);
        $email_lama = $row['email'];

        if ($email != $email_lama) {
            // cek apakah email sudah terdaftar atau belum
            $sql = $conn->prepare("SELECT
                *
            FROM
                koordinator_pic
            WHERE
                email = ?");
            $sql->bind_param("s", $email);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                throw new Exception("Email sudah terdaftar!");
            }
        }

        // Proses update data koordinator pic
        $update = $conn->prepare("UPDATE 
        koordinator_pic 
        SET
            nama = ?,
            email = ?,
            no_hp = ?,
            updated_at = ?
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $update->bind_param("ssssss", $nama, $email, $no_hp, $created_at, $id_pic, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan log aktivitas
            $aktivitas = 'Update data user atas id user ' . $id_pic . '.';
            $extra_info = "HRD";
            $level = "INFO";
            logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Update data user berhasil.";
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'hapusUser') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['id_pic'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $id_pic = base64_decode($_POST['id_pic']);

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah id pic ada
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pic, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan dihapus tidak terdaftar.");
        }

        // simpan data pic yang akan dihapus ke histori
        $insert = $conn->prepare("INSERT INTO `hist_delete_pic`(`id_pic`, `id_koordinator`, `nama`, `email`, `password`, `posisi`, `no_hp`, `img`, `status`, `role`, `create_at`, `updated_at`, `deleted_at`) 
        SELECT id_pic, id_koordinator, nama, email, `password`, posisi, no_hp, img, `status`, `role`, create_at, updated_at, '$created_at' as deleted_at FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $insert->bind_param("ss", $id_pic, $id_koordinator);

        if ($insert->execute()) {
            $insert->close();

            // Proses hapus data koordinator pic
            $delete = $conn->prepare("DELETE FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
            $delete->bind_param("ss", $id_pic, $id_koordinator);

            if ($delete->execute()) {
                $delete->close();

                // simpan log aktivitas
                $aktivitas = 'Menghapus data user atas id user ' . $id_pic . '.';
                $extra_info = "HRD";
                $level = "INFO";
                logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

                // Jika semua query berhasil, commit transaksi
                $conn->commit();

                $status = "success";
                $message = "Hapus data user berhasil.";
            } else {
                throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
            }
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'ubahDataAkun') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['nama']) || !isset($_POST['email']) || !isset($_POST['no_hp'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $nama = $_POST['nama'];
        $email = $_POST['email'];
        $no_hp = $_POST['no_hp'];
        $otp = $_POST['otp'];

        //cek otp
        $timezone = "SET time_zone = '+07:00'";
        $conn->query($timezone);

        // $sql_check_email_lama = $conn->prepare("SELECT * FROM kode_verifikasi WHERE email_lama = ?");
        // $sql_check_email_lama->bind_param("s", $email_perusahaan);
        // $sql_check_email_lama->execute();
        // $result_check_email_lama = $sql_check_email_lama->get_result();
        // $sql_check_email_lama->close();

        // if ($result_check_email_lama->num_rows > 0) {
            // cek apakah email berubah, jika berubah maka perlu verifikasi OTP
            $sql_check_email = $conn->prepare("SELECT email FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
            $sql_check_email->bind_param("ss", $id_pegawai, $id_koordinator);
            $sql_check_email->execute();
            $result_check = $sql_check_email->get_result();
            $sql_check_email->close();
            
            $row_email = mysqli_fetch_array($result_check);
            $email_lama_check = $row_email['email'];
            
        
            if ($email != $email_lama_check) {
                $sql_check_email_lama = $conn->prepare("SELECT * FROM kode_verifikasi WHERE email_lama = ?");
                $sql_check_email_lama->bind_param("s", $email_perusahaan);
                $sql_check_email_lama->execute();
                $result_check_email_lama = $sql_check_email_lama->get_result();
                $sql_check_email_lama->close();

                if ($result_check_email_lama->num_rows > 0) {
                    $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
                    $sql->bind_param("ss", $otp, $email);
                    $sql->execute();
                    $result = $sql->get_result();
                    $sql->close();

                    if ($result->num_rows > 0) {
                        // Update Status OTP
                        $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                        $update->bind_param("ss", $otp, $email);

                        if ($update->execute()) {
                            $update->close();
                        } else {
                            throw new Exception("Pendaftaran Gagal.");
                        }
                    } else {
                        throw new Exception("OTP Salah.");
                    }
                } else {
                    throw new Exception("OTP Salah.");
                }
            } else {
                if (!empty($otp)) {
                    $sql_check_email_lama = $conn->prepare("SELECT * FROM kode_verifikasi WHERE email_lama = ?");
                    $sql_check_email_lama->bind_param("s", $email_perusahaan);
                    $sql_check_email_lama->execute();
                    $result_check_email_lama = $sql_check_email_lama->get_result();
                    $sql_check_email_lama->close();

                    if ($result_check_email_lama->num_rows > 0) {
                        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
                        $sql->bind_param("ss", $otp, $email);
                        $sql->execute();
                        $result = $sql->get_result();
                        $sql->close();

                        if ($result->num_rows > 0) {
                            // Update Status OTP
                            $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                            $update->bind_param("ss", $otp, $email);

                            if ($update->execute()) {
                                $update->close();
                            } else {
                                throw new Exception("Pendaftaran Gagal.");
                            }
                        } else {
                            throw new Exception("OTP Salah.");
                        }
                    } else {
                        throw new Exception("OTP Salah.");
                    }
                }
            }
        // } else {
        //     throw new Exception("OTP Salah.");
        // }

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah id pic ada
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan diubah tidak terdaftar.");
        }

        $row = mysqli_fetch_array($result);
        $email_lama = $row['email'];

        if ($email != $email_lama) {
            // cek apakah email sudah terdaftar atau belum
            $sql = $conn->prepare("SELECT
                *
            FROM
                koordinator_pic
            WHERE
                email = ?");
            $sql->bind_param("s", $email);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                throw new Exception("Email sudah terdaftar!");
            }
        }

        // Proses update data koordinator pic
        $update = $conn->prepare("UPDATE 
        koordinator_pic 
        SET
            nama = ?,
            email = ?,
            no_hp = ?,
            updated_at = ?
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $update->bind_param("ssssss", $nama, $email, $no_hp, $created_at, $id_pegawai, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan log aktivitas
            $aktivitas = 'Update data user atas id user ' . $id_pegawai . '.';
            $extra_info = "HRD";
            $level = "INFO";
            logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Update data user berhasil.";
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'ubahKataSandiAkun') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['password_lama']) || !isset($_POST['password']) || !isset($_POST['kon_password'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah password baru sudah sama
        if ($_POST['password'] != $_POST['kon_password']) {
            throw new Exception("Kata sandi tidak sama. Silakan sesuaikan kembali.");
        }

        // Validasi password baru sebelum hashing
        $passwordValidation = validatePassword($_POST['password']);
        if (!$passwordValidation['valid']) {
            throw new Exception("Password baru tidak memenuhi syarat: " . implode(", ", $passwordValidation['errors']));
        }

        $password_lama = $_POST['password_lama'];
        $password = hashPassword($_POST['password']);
        $kon_password = $_POST['kon_password'];
        $otp = $_POST['otp'];

        // cek otp
        $timezone = "SET time_zone = '+07:00'";
        $conn->query($timezone);

        $sql = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? and date >= NOW() - INTERVAL 5 MINUTE GROUP BY code");
        $sql->bind_param("ss", $otp, $email_perusahaan);
        $sql->execute();
        $resultOtp = $sql->get_result();
        $sql->close();

        if ($resultOtp->num_rows > 0) {
            $sql_cek = $conn->prepare("SELECT * FROM kode_verifikasi WHERE code = ? AND email = ? AND status = 'Finish'");
            $sql_cek->bind_param("ss", $otp, $email_perusahaan);
            $sql_cek->execute();
            $result_cek = $sql_cek->get_result();
            $sql_cek->close();
            
            if ($result_cek->num_rows == 0) {
                // Update Status OTP
                $update = $conn->prepare("UPDATE kode_verifikasi SET `status` = 'Finish' WHERE code = ? AND email = ?");
                $update->bind_param("ss", $otp, $email_perusahaan);

                if ($update->execute()) {
                    $update->close();
                } else {
                    throw new Exception(translate('Update kata sandi gagal dilakukan.'));
                }
            } else {
                throw new Exception(translate('OTP sudah pernah digunakan.'));
            }
            
        } else {
            throw new Exception(translate('OTP Salah.'));
        }

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah id pic ada
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan diubah tidak terdaftar.");
        }

        $row = mysqli_fetch_array($result);
        $storedHash = $row['password'];

        // cek apakah password lama sudah sesuai
        if (!verifyPassword($password_lama, $storedHash)) {
            throw new Exception("Kata sandi lama salah. Silakan sesuaikan kembali.");
        }

        // Proses update data koordinator pic
        $update = $conn->prepare("UPDATE 
        koordinator_pic 
        SET
            `password` = ?,
            updated_at = ?
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $update->bind_param("ssss", $password, $created_at, $id_pegawai, $id_koordinator);

        if ($update->execute()) {
            $update->close();

            // simpan log aktivitas
            $aktivitas = 'Melakukan proses ubah kata sandi.';
            $extra_info = "HRD";
            $level = "INFO";
            logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Ubah kata sandi berhasil.";
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'getDataPengaturanAkses') {

    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 5;
    $offset = ($page - 1) * $pageSize;
    $q = isset($_GET['q']) ? $conn->real_escape_string($_GET['q']) : ""; // Tambahkan filter search ke total jika ada
    $q = substr($q, 0, 50); // batasi max 50 karakter
    $querySearch = "";
    if (!empty($q)) {
        $querySearch .= " AND (
            nama LIKE '%$q%' OR 
            id_koordinator LIKE '%$q%'  OR 
            email LIKE '%$q%' 
        )";
    }

    $data = array();

    // Hitung total data
    $sqlCount = $conn->prepare("SELECT COUNT(DISTINCT id_pic) AS total 
                 FROM koordinator_pic 
                 WHERE id_koordinator = ? 
                   AND `role` != 'master' 
                   AND id_pic != ?
                   $querySearch
                   ");
    $sqlCount->bind_param("ss", $company, $id_pegawai);
    $sqlCount->execute();
    $resultCount = $sqlCount->get_result();
    $sqlCount->close();

    $rowCount = mysqli_fetch_array($resultCount);
    $totalData = intval($rowCount['total']);
    $totalPage = $totalData > 0 ? ceil($totalData / $pageSize) : 1;

    // Ambil data dengan pagination
    $sqlData = $conn->prepare("SELECT * FROM koordinator_pic 
                WHERE id_koordinator = ?
                  AND `role` != 'master' 
                  AND id_pic != ?
                  $querySearch
                GROUP BY id_pic
                ORDER BY nama
                LIMIT ?, ?");
    $sqlData->bind_param("ssii", $company, $id_pegawai, $offset, $pageSize);
    $sqlData->execute();
    $resultData = $sqlData->get_result();
    $sqlData->close();

    $no = $offset;

    while ($rowData = mysqli_fetch_assoc($resultData)) {
        $no++;
        $data[] = array(
            "no" => $no,
            "id_pic" => $rowData['id_pic'],
            "nama" => $rowData['nama'],
            "email" => $rowData['email'],
            "no_hp" => $rowData['no_hp'],
        );
    }

    $output = array(
        "status" => true,
        "message" => "Data berhasil diambil!",
        "data" => $data,
        "page" => $page,
        "page_size" => $pageSize,
        "total_page" => $totalPage,
        "total_data" => $totalData
    );

    echo json_encode(utf8ize($output), JSON_PRETTY_PRINT);
}


if ($func == 'getDataAkses') {
    $data = [];
    $status = false;
    $message = "Data tidak ditemukan.";

    try {
        // Validasi dan sanitasi input
        if (!isset($_GET['id']) || empty($_GET['id'])) {
            throw new Exception("ID user tidak ditemukan.");
        }

        $id_pic = base64_decode($_GET['id'], true);
        if ($id_pic === false || !preg_match('/^PIC-[A-F0-9]{16}$/', $id_pic)) {
            throw new Exception("ID user tidak valid.");
        }

        // Gunakan prepared statement untuk query
        $sql = $conn->prepare("SELECT fitur FROM koordinator_pic WHERE id_pic = ? AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pic, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan diubah tidak terdaftar.");
        }

        $row = $result->fetch_assoc();
        $fitur_user = explode("|", $row['fitur']);

        // Ambil semua fitur dengan prepared statement
        $get = $conn->prepare("SELECT id_fitur, menu, sub_menu, status FROM list_fitur ORDER BY `no`, menu");
        $get->execute();
        $resultFitur = $get->get_result();
        $get->close();

        $dataFitur = [];
        while ($rowFitur = $resultFitur->fetch_assoc()) {
            $dataFitur[] = [
                "id_fitur" => $rowFitur['id_fitur'],
                "menu" => htmlspecialchars($rowFitur['menu'], ENT_QUOTES, 'UTF-8'),
                "sub_menu" => strip_tags(str_replace("<br>", "", $rowFitur['sub_menu'])),
                "akses" => in_array($rowFitur['id_fitur'], $fitur_user),
                "status" => $rowFitur['status']
            ];
        }

        $status = true;
        $message = "Data berhasil diambil!";

        $output = [
            "status" => $status,
            "message" => $message,
            "data" => $dataFitur,
            "page" => 1,
            "page_size" => count($dataFitur),
            "total_page" => 1,
            "total_data" => count($dataFitur)
        ];
    } catch (Exception $e) {
        $output = [
            "status" => false,
            "message" => $e->getMessage(),
            "data" => [],
            "page" => 1,
            "page_size" => 0,
            "total_page" => 1,
            "total_data" => 0
        ];
    }

    echo json_encode(utf8ize($output), JSON_PRETTY_PRINT);
}

if ($func == 'updateAksesUser') {
    $data = array();

    $status = "gagal";
    $message = "Tidak dapat menyimpan data.";

    $created_at = date("Y-m-d H:i:s");

    try {
        // Mulai proses
        $conn->begin_transaction();

        if (!isset($_POST['id_pic']) || !isset($_POST['akses'])) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        $id_pic = base64_decode($_POST['id_pic']);
        $akses = explode(",", $_POST['akses']);

        // cek jika akses kosong
        if (count($akses) == 0) {
            throw new Exception("Akses tidak boleh kosong. Silakan pilih akses terlebih dahulu.");
        }

        // cek apakah fitur yang dipilih terdaftar
        $temp = array();
        for ($i = 0; $i < count($akses); $i++) {
            $temp[] = "'" . $akses[$i] . "'";
        }
        $temp = implode(",", $temp);

        $cek = $conn->query("SELECT * FROM list_fitur WHERE id_fitur IN ($temp)");
        if ($cek->num_rows != count($akses)) {
            throw new Exception("Akses yang dipilih tidak terdaftar. Silakan hubungi administrator.");
        }

        $akses = implode("|", $akses);

        // cek apakah user dan id_perusahaan terdaftar atau tidak
        $sql = $conn->prepare("SELECT
            kp.id_pic,
            k.img,
            k.img_banner
        FROM
            koordinator k
            JOIN koordinator_pic kp ON k.id_koordinator = kp.id_koordinator
        WHERE
            kp.id_pic = ? 
            AND k.id_koordinator = ?
        GROUP BY
            k.id_koordinator");
        $sql->bind_param("ss", $id_pegawai, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("Tidak dapat menyimpan data. Silakan untuk login kembali.");
        }

        // cek apakah id pic ada
        $sql = $conn->prepare("SELECT
            *
        FROM
            koordinator_pic
        WHERE
            id_pic = ?
            AND id_koordinator = ?");
        $sql->bind_param("ss", $id_pic, $id_koordinator);
        $sql->execute();
        $result = $sql->get_result();
        $sql->close();

        if ($result->num_rows == 0) {
            throw new Exception("User yang akan diubah tidak terdaftar.");
        }

        // update akses user
        $update = $conn->prepare("UPDATE koordinator_pic SET fitur = ?, updated_at = ? WHERE id_pic = ? AND id_koordinator = ?");
        $update->bind_param("ssss", $akses, $created_at, $id_pic, $id_koordinator);
        if ($update->execute()) {
            $update->close();

            // simpan log aktivitas
            $aktivitas = 'Update akses user atas id user ' . $id_pegawai . '.';
            $extra_info = "HRD";
            $level = "INFO";
            logActivity($conn, $id_pegawai, $level, $aktivitas, $extra_info);

            // Jika semua query berhasil, commit transaksi
            $conn->commit();

            $status = "success";
            $message = "Update akses user berhasil.";
        } else {
            throw new Exception("Tidak dapat menyimpan data. Silakan hubungi administrator.");
        }
    } catch (Exception $e) {
        // Jika ada error, rollback proses
        $conn->rollback();

        $status = "gagal";
        $message = $e->getMessage();
    }

    $data['status'] = $status === "success";
    $data['message'] = $message;
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

$conn->close();
exit;
