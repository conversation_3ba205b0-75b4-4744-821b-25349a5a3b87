<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Secure query execution function
function executeSecureQuery($conn, $sql, $params = [], $types = "")
{
    try {
        if (!empty($params)) {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types)) {
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        } else {
            return $conn->query($sql);
        }
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Input validation function
function validateInput($input, $type = 'string', $maxLength = 255)
{
    if (empty($input) && $input !== '0') return '';

    switch ($type) {
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT) !== false ? (int)$input : 0;
        case 'search':
            // Allow letters, numbers, spaces, and common punctuation for search
            return preg_match('/^[a-zA-Z0-9\s\.\-_,]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
        default:
            return preg_match('/^[a-zA-Z0-9\s\.\-_,]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
    }
}

// Output sanitization function with enhanced URL protection
function sanitizeOutput($data, $key = '')
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $k => $v) {
            $sanitized[$k] = sanitizeOutput($v, $k);
        }
        return $sanitized;
    }

    // Don't sanitize specific fields that contain URLs or encoded data
    $protected_fields = ['logoURL', 'link', 'link_perusahaan', 'temp_kode', 'img'];
    if (in_array($key, $protected_fields)) {
        return $data;
    }

    // Don't sanitize URLs (especially S3 presigned URLs)
    if (is_string($data)) {
        // Check if it's a URL (starts with http/https) or contains S3-like structure
        if (
            filter_var($data, FILTER_VALIDATE_URL) !== false ||
            (strpos($data, 'http') === 0 && (strpos($data, 'amazonaws.com') !== false || strpos($data, '?') !== false))
        ) {
            return $data;
        }
        return $data;
    }

    return $data;
}

// Validate and sanitize input parameters
$limit = validateInput($_POST['page_size'] ?? 6, 'int');
$limit = ($limit > 0 && $limit <= 100) ? $limit : 6; // Max 100 items per page

$page = validateInput($_POST['page'] ?? 1, 'int');
$page = ($page > 0 && $page <= 10000) ? $page : 1; // Max 10000 pages to prevent DOS

$start = ($page - 1) * $limit;

// Validate search parameters
// $pencarian_value = validateInput($_GET['pencarian'] ?? '', 'search', 100);
// $lokasi_value = validateInput($_GET['lokasi'] ?? '', 'search', 100);

// Build secure WHERE conditions and parameters
$where_conditions_main = ["lr.status = 'On Proccess'", "lf.id = ?"];
$where_conditions_union = ["lr.status = 'Selesai'", "lf.id = ?"];
$params = [$pin, $pin]; // For both UNION queries
$types = "ss";

// Handle search parameter for both UNION queries
$pencarian = "";
if (isset($_POST['pencarian'])) {
    $temp_pencarian = "'%" . $_POST['pencarian'] . "%'";
    $pencarian = "(lr.perusahaan LIKE $temp_pencarian OR lr.posisi LIKE $temp_pencarian)";
}

// Handle location filter for both UNION queries
if (isset($_POST['lokasi'])) {
    $location_condition = "lr.lokasi_kerja LIKE ?";
    $where_conditions_main[] = $location_condition;
    $where_conditions_union[] = $location_condition;
    $location_param = "%" . $lokasi_value . "%";
    $params[] = $location_param;
    $params[] = $location_param; // For UNION query
    $types .= "ss";
}

// Build the main query with secure parameterized statements
$sql = "SELECT
                                lr.*,
                                k.img,
                                k.alamat,
                                k.tipe,
                                k.link,
                                k.link_perusahaan,
                                k.deskripsi,
                                lk.kk,
                                lk.kb,
                                lk.km,
                                lk.kbdu,
                                lk.rlp,
                                lf.created_at AS fav_created_at,
                                lf.lokasi AS lokasi
                            FROM
                                list_request lr
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                JOIN list_favorite lf ON lf.id_req = lr.id_req
                                JOIN list_kriteria lk ON lr.id_req = lk.id_req
                            WHERE
                                lr.`status` = 'On Proccess'
                                AND lf.id = ?
                                AND $pencarian
                            GROUP BY
                                lr.id_req
                            UNION
                            SELECT
                                lr.*,
                                k.img,
                                k.alamat,
                                k.tipe,
                                k.link,
                                k.link_perusahaan,
                                k.deskripsi,
                                lk.kk,
                                lk.kb,
                                lk.km,
                                lk.kbdu,
                                lk.rlp,
                                lf.created_at AS fav_created_at,
                                lf.lokasi AS lokasi
                            FROM
                                list_request lr
                                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                                JOIN list_favorite lf ON lf.id_req = lr.id_req
                                JOIN list_kriteria lk ON lr.id_req = lk.id_req
                            WHERE
                                lr.`status` = 'Selesai'
                                AND lf.id = ?
                                AND $pencarian
                            GROUP BY
                                lr.id_req
                            LIMIT ? , ?";
// Add limit parameters
$params[] = $start;
$params[] = $limit;
$types .= "ii";

$result = executeSecureQuery($conn, $sql, $params, $types);

// Calculate total data with secure query
$count_sql = "SELECT
                        COUNT(h.id) AS total
                    FROM
                        (
                        SELECT
                            lr.id
                        FROM
                            list_request lr
                            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                            JOIN list_favorite lf ON lf.id_req = lr.id_req
                            JOIN list_kriteria lk ON lr.id_req = lk.id_req
                        WHERE 
                         lr.`status` = 'On Proccess'
                                AND lf.id = ?
                        AND $pencarian
                        GROUP BY
                            lr.id_req
                        UNION
                        SELECT
                            lr.id
                        FROM
                            list_request lr
                            JOIN koordinator k ON lr.id_koordinator = k.id_koordinator 
                            JOIN list_favorite lf ON lf.id_req = lr.id_req
                            JOIN list_kriteria lk ON lr.id_req = lk.id_req
                        WHERE  lr.`status` = 'Selesai'
                                AND lf.id = ?
                                AND $pencarian
                        GROUP BY
                            lr.id_req,lf.lokasi
                        ) h";

// Remove LIMIT parameters for count query
$count_params = array_slice($params, 0, -2);
$count_types = substr($types, 0, -2);

$total_result = executeSecureQuery($conn, $count_sql, $count_params, $count_types);

if ($total_result && $total_result->num_rows > 0) {
    $total_row = $total_result->fetch_assoc();
    $total_data = $total_row['total'];
} else {
    $total_data = 0;
}

$total_pages = ceil($total_data / $limit);
$response['data'] = [];

if ($result && $result->num_rows > 0) {

    while ($row = $result->fetch_assoc()) {
        $id_req = $row['id_req'];
        $waktu_lalu = waktuLalu($row['create_at']);
        $tgl_posting = formatTanggalIndonesia($row['create_at']);
        $arr_lokasi_kerja = explode(",", $row['lokasi']);
        $logoURL = null; // Default jika tidak ada gambar
        $is_favorit = false; // Default jika bukan favorit
        $is_lamar = false;

        // Cek apakah ada gambar logo di S3
        if (!empty($row['img'])) {
            if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $row['img']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();
            }
        }

        // Loop melalui setiap lokasi_kerja dan buat entry baru untuk masing-masing lokasi
        $lokasi = isset($arr_lokasi_kerja[0]) ? $arr_lokasi_kerja[0] : "-";
        $temp_kode = base64_encode($id_req . "|" . $lokasi);

        if (isset($lokasi)) {
            $lokasi_kerja = str_replace("KOTA ", "", str_replace("KABUPATEN ", "KAB. ", $lokasi));
        } else {
            $lokasi_kerja = "-";
        }

        if ($pin != '-') {
            // Cek apakah data ini termasuk favorit
            $cekFav = $conn->prepare("SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?");
            $cekFav->bind_param("sss", $pin, $id_req, $lokasi);
            $cekFav->execute();
            $resultFav = $cekFav->get_result();

            if ($resultFav->num_rows > 0) {
                $is_favorit = true;
            }
            $cekFav->close();

            // Cek apakah data ini sudah dilamar
            $cekLamar = $conn->prepare("SELECT
            ul.id_lamar
            FROM
            users_lamar ul
            JOIN list_request lr ON lr.id_req = ul.id_req
            WHERE
            ul.id_gestalt = ?
            AND ul.id_req = ?
            LIMIT ?, ?");
            $lamar_start = 0;
            $lamar_limit = 1;
            $cekLamar->bind_param("ssii", $pin, $id_req, $lamar_start, $lamar_limit);
            $cekLamar->execute();
            $resultLamar = $cekLamar->get_result();

            if ($resultLamar->num_rows > 0) {
                $is_lamar = true;
            }
            $cekLamar->close();
        }

        // Tambahkan semua data asli + tambahan ke dalam array sebagai entri tunggal
        $row_copy = $row; // Buat salinan dari row asli
        $row_copy['lokasi_kerja2'] = $lokasi_kerja;
        $row_copy['waktu_lalu'] = $waktu_lalu;
        $row_copy['tgl_posting'] = $tgl_posting;
        $row_copy['logoURL'] = $logoURL;
        $row_copy['is_favorit'] = $is_favorit;
        $row_copy['is_lamar'] = $is_lamar;
        $row_copy['temp_kode'] = $temp_kode;
        $row_copy['arr_lokasi'] = [$lokasi]; // Ubah menjadi array dengan satu lokasi

        // Sanitize output data while protecting URLs and encoded fields
        $response['data'][] = sanitizeOutput($row_copy);
    }
}

// Handle query execution errors
if ($result === false) {
    $response = [
        "success" => false,
        "message" => "Terjadi kesalahan dalam mengambil data",
        "data" => [],
        "page" => $page,
        "page_size" => $limit,
        "total_page" => 0,
        "total_data" => 0
    ];
} else {
    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data'],
        "page" => $page,
        "page_size" => $limit,
        "total_page" => $total_pages,
        "total_data" => $total_data
    ];
}

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);


$conn->close();
exit;
