<?php
header("Content-Type: application/json");

include '../../../model/database.php';
include '../../jwt_helper.php';
require '../../../vendor/autoload.php';
include '../../../api/ses.php';

// Verifikasi access token dan dapatkan data user
$userData = requireAuth();
$id_pic = $userData->id ?? "";

if (isset($_POST['fcm_token'])) {
    $fcmToken = $_POST['fcm_token'];
} else {
    $fcmToken = "";
}

$data = array();

$status = 'false';
$message = 'Update fcm token gagal';

if ($fcmToken != "") {
    $update = $conn->prepare("UPDATE koordinator_pic SET `fcm_token` = ? WHERE `id_pic` = ? ");
    $update->bind_param("ss", $fcmToken, $id_pic);
    if ($update->execute()) {
        $update->close();

        $status = 'true';
        $message = 'Update fcm token berhasil';
    }

    $data['status'] = $status === "true";
    $data['message'] = $message;
    $data['data'] = [];
} else {
    $data['status'] = $status === "false";
    $data['message'] = "Gagal update fcm token!";
    $data['data'] = [];
}


$output = json_encode($data, JSON_PRETTY_PRINT);
echo $output;


$conn->close();
exit;
