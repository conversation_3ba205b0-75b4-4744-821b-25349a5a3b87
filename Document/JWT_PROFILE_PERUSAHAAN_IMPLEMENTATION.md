# JWT Implementation for Profile Perusahaan API

## Overview

This document details the JWT authentication implementation for the `profile_perusahaan.php` endpoint in the company recruitment system.

## Endpoint Information

- **File**: `api/mobile/company/profile_perusahaan.php`
- **Purpose**: Manages company profile data (view and update)
- **Authentication**: JWT Bearer Token required

## Migration Changes

### 1. Authentication Updates

- **Added**: JWT helper inclusion and authentication requirement
- **Removed**: Session-based authentication and POST parameter validation
- **Added**: `requireAuth()` function call to validate JWT tokens

### 2. User Data Extraction

**Before (Parameter-based)**:

```php
if (isset($_POST['id_koordinator']) && isset($_POST['divisi']) && isset($_POST['id'])) {
    $id_koordinator = $_POST['id_koordinator'];
    $divisi = $_POST['divisi'];
    $id_pegawai = $_POST['id'];
} else {
    // Error response
}
```

**After (JWT-based)**:

```php
// Require authentication for all endpoints
$userData = requireAuth();

// Extract user data from JWT
$id_koordinator = $userData->id_koordinator;
$divisi = $userData->divisi;
$company = $userData->company;
$id_pegawai = $id_koordinator;
```

### 3. SQL Query Updates

- All queries now use data extracted from JWT tokens
- Removed dependency on POST/GET parameters for sensitive data
- Updated prepared statements to use JWT-derived values

### 4. Activity Logging

- Added comprehensive activity logging for all operations
- Logs user access and data modifications
- Includes IP address, user agent, and timestamp

## API Functions

### 1. getProfilPerusahaan

**Purpose**: Retrieve company profile information with pagination

**Authentication**: JWT token required in Authorization header

**Request**:

```http
GET /api/mobile/company/profile_perusahaan.php?func=getProfilPerusahaan&page=1&page_size=10
Authorization: Bearer <jwt_token>
```

**Response**:

```json
{
  "status": true,
  "message": "Data perusahaan berhasil ditemukan.",
  "data": [
    {
      "nama_perusahaan": "PT Example",
      "alamat": "Jakarta",
      "tipe": "Technology",
      "link_perusahaan": "https://example.com",
      "tentang_perusahaan": "Company description",
      "tlp": "021-1234567",
      "email": "<EMAIL>",
      "logo_perusahaan_url": "https://s3-presigned-url",
      "banner_perusahaan_url": "https://s3-presigned-url"
    }
  ],
  "page": 1,
  "page_size": 10,
  "total_page": 1,
  "total_data": 1
}
```

### 2. updateProfilPerusahaan

**Purpose**: Update company profile information

**Authentication**: JWT token required in Authorization header

**Request**:

```http
POST /api/mobile/company/profile_perusahaan.php?func=updateProfilPerusahaan
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

Parameters:
- nama_perusahaan: string
- no_tlp_perusahaan: string
- email_perusahaan: string
- alamat_perusahaan: string
- website_perusahaan: string
- industri_perusahaan: string
- deskripsi_perusahaan: string
- logo: file (optional)
- banner: file (optional)
```

**Response**:

```json
{
  "status": true,
  "message": "Data perusahaan berhasil diupdate.",
  "data": []
}
```

## Security Improvements

### 1. Token-Based Authentication

- JWT tokens contain encrypted user data
- Tokens have expiration times
- Invalid or expired tokens are automatically rejected

### 2. Eliminated Parameter Injection

- No longer accepts sensitive data via POST/GET parameters
- All user identification comes from verified JWT payload
- Prevents unauthorized access attempts

### 3. Enhanced Logging

- All access attempts are logged with detailed information
- Failed authentication attempts are tracked
- Activity logs include user context and timestamps

## Error Handling

### Authentication Errors

```json
{
  "error": "Authentication required",
  "message": "No token provided or invalid token"
}
```

### Authorization Errors

```json
{
  "error": "Access denied",
  "message": "Insufficient permissions for this resource"
}
```

## Implementation Notes

### 1. File Upload Security

- S3 integration maintained for logo and banner uploads
- File validation and security checks preserved
- Temporary file handling secured

### 2. Database Transactions

- Transaction-based updates for data consistency
- Rollback capability on errors
- Atomicity for complex operations

### 3. Backward Compatibility

- JWT implementation replaces session-based authentication
- Response format maintained for client compatibility
- Error handling improved with detailed messages

## Testing

### Test Authentication

```bash
# Get JWT token first from login endpoint
curl -X POST "http://localhost/digitalcv/api/mobile/company/login.php" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use token for profile access
curl -X GET "http://localhost/digitalcv/api/mobile/company/profile_perusahaan.php?func=getProfilPerusahaan" \
  -H "Authorization: Bearer <jwt_token>"
```

### Test Profile Update

```bash
curl -X POST "http://localhost/digitalcv/api/mobile/company/profile_perusahaan.php?func=updateProfilPerusahaan" \
  -H "Authorization: Bearer <jwt_token>" \
  -F "nama_perusahaan=Updated Company Name" \
  -F "email_perusahaan=<EMAIL>"
```

## Migration Checklist

- [x] JWT helper integration
- [x] Authentication requirement added
- [x] Parameter-based authentication removed
- [x] User data extraction from JWT
- [x] SQL queries updated
- [x] Activity logging implemented
- [x] Error handling improved
- [x] File upload security maintained
- [x] Database transactions preserved
- [x] Response format compatibility maintained

## Next Steps

1. Update client applications to use JWT authentication
2. Remove any remaining session dependencies
3. Monitor logs for authentication issues
4. Update API documentation for consumers
