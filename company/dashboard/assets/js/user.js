const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000
});

function getData() {
    $.get("../../controller/userController.php", { func: 'getData' },
        function(data) {
            var obj = JSON.parse(data);
            for (var i = 0; i < obj.length; i++) {
                if (i == 0) {
                    $('#jmlh-sedang-test').html(obj[i]);
                }

                if (i == 1) {
                    $('#jmlh-sudah-test').html(obj[i]);
                }

                if (i == 2) {
                    $('#jmlh-belum-test').html(obj[i]);
                }

                if (i == 3) {
                    $('#jmlh-sudah-didownload').html(obj[i]);
                }

                if (i == 4) {
                    $('#jmlh-sudah-dihapus').html(obj[i]);
                }

                if (i == 5) {
                    $('#jmlh-sudah-approve').html(obj[i]);
                }

                if (i == 6) {
                    $('#jmlh-sudah-gagal-approve').html(obj[i]);
                }

                if (i == 7) {
                    $('#jmlh-sudah-lulus-psikotes').html(obj[i]);
                }

                if (i == 8) {
                    $('#jmlh-sudah-gagal-psikotes').html(obj[i]);
                }

                if (i == 9) {
                    $('#jmlh-sudah-lulus-interview').html(obj[i]);
                }

                if (i == 10) {
                    $('#jmlh-sudah-gagal-interview').html(obj[i]);
                }

                if (i == 11) {
                    $('#jmlh-sudah-lolos-medis').html(obj[i]);
                }

                if (i == 12) {
                    $('#jmlh-sudah-gagal-medis').html(obj[i]);
                }
            }
        });

    $("#table-sedang-test").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataSedangTest',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-test").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataSudahTest',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-belum-test").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataBelumTest',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-didownload").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataDidownload',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-dihapus").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataDihapus',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-approve").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataApprove',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-gagal-approve").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataGagalApprove',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-lulus-psikotes").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataLulusPsikotes',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-gagal-psikotes").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataGagalPsikotes',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-lulus-interview").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataLulusInterview',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-gagal-interview").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataGagalInterview',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-lolos-medis").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataLolosMedis',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

    $("#table-sudah-gagal-medis").DataTable({
        "ordering": false,
        "serverSide": true,
        "ajax": {
            "url": '../../controller/userController.php?func=getDataGagalMedis',
            "dataSrc": "data",
            complete: function(data) {
                console.log(data);
            },
        },
        "lengthChange": false
    });

}