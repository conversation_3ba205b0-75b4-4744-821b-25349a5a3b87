<?php
header("Content-Type: application/json");
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

// Security functions
function executeSecureQuery($conn, $query, $types, $params)
{
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            error_log("Prepare failed: " . $conn->error);
            return false;
        }

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        if (!$stmt->execute()) {
            error_log("Execute failed: " . $stmt->error);
            $stmt->close();
            return false;
        }

        $result = $stmt->get_result();
        $stmt->close();
        return $result;
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

function validateInput($input, $type, $options = [])
{
    switch ($type) {
        case 'function':
            $allowed = ['cekNotif', 'countNewNotif', 'updateNotif', 'updateAllNotif'];
            return in_array($input, $allowed) ? $input : false;
        case 'int':
            $min = $options['min'] ?? 1;
            $max = $options['max'] ?? PHP_INT_MAX;
            $value = filter_var($input, FILTER_VALIDATE_INT);
            return ($value !== false && $value >= $min && $value <= $max) ? $value : false;
        case 'string':
            $maxLength = $options['max_length'] ?? 255;
            $cleaned = trim($input);
            return (strlen($cleaned) <= $maxLength) ? $cleaned : false;
        case 'datetime':
            return preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $input) ? $input : false;
        default:
            return false;
    }
}

function sanitizeOutput($data)
{
    if (is_array($data)) {
        $protected_fields = ['logo_url', 'link', 'img', 'foto', 'avatar', 'logoURL'];
        $result = [];
        foreach ($data as $key => $value) {
            if (in_array($key, $protected_fields)) {
                // Protect S3 URLs and other URLs from HTML encoding
                if (is_string($value) && (strpos($value, 'amazonaws.com') !== false ||
                    strpos($value, 'http') === 0 || filter_var($value, FILTER_VALIDATE_URL))) {
                    $result[$key] = $value;
                } else {
                    $result[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                }
            } else {
                $result[$key] = sanitizeOutput($value);
            }
        }
        return $result;
    } elseif (is_string($data)) {
        return $data;
    }
    return $data;
}

function sendErrorResponse($message = "Invalid request")
{
    echo json_encode([
        'status' => false,
        'message' => $message,
        'data' => []
    ], JSON_PRETTY_PRINT);
    exit;
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Validate function parameter
$func = validateInput($_GET['func'] ?? '', 'function');
if (!$func) {
    sendErrorResponse("Invalid function parameter");
}

if ($func == "cekNotif") {
    $data = array();

    // Validate and sanitize pagination parameters
    $page = validateInput($_GET['page'] ?? 1, 'int', ['min' => 1, 'max' => 10000]);
    $page_size = validateInput($_GET['page_size'] ?? 5, 'int', ['min' => 1, 'max' => 100]);

    if (!$page || !$page_size) {
        sendErrorResponse("Invalid pagination parameters");
    }

    $offset = ($page - 1) * $page_size;

    $status = "false";
    $message = "Notifikasi gagal";
    $konten = array(
        'notification' => 0,
        'titleNotif' => 'Anda tidak memiliki notifikasi',
        'items' => []
    );

    // Hitung total data dengan secure query
    $countResult = executeSecureQuery(
        $conn,
        "SELECT COUNT(*) as total FROM notif_summary WHERE penerima = ?",
        "s",
        [$pin]
    );

    if (!$countResult) {
        sendErrorResponse("Database error occurred");
    }

    $total_data = $countResult->fetch_assoc()['total'];
    $total_page = ceil($total_data / $page_size);

    // Ambil data dengan pagination menggunakan secure query
    $result = executeSecureQuery(
        $conn,
        "SELECT ns.*, k.label, k.img
         FROM notif_summary ns
         LEFT JOIN koordinator k ON k.id_koordinator = ns.pengirim
         WHERE ns.penerima = ? 
         ORDER BY ns.create_at DESC
         LIMIT ?, ?",
        "sii",
        [$pin, $offset, $page_size]
    );

    if (!$result) {
        sendErrorResponse("Database error occurred");
    }

    if ($result->num_rows > 0) {
        $notifList = [];
        $jml_notif = 0;

        while ($row = mysqli_fetch_array($result)) {
            // Secure Logo URL handling
            $logoURL = "";
            if (!empty($row['img'])) {
                // Sanitize filename to prevent path traversal
                $filename = basename($row['img']);
                if (preg_match('/^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|gif|svg)$/i', $filename)) {
                    $s3Key = 'perusahaan/logo/' . $filename;

                    try {
                        if ($s3->doesObjectExist($bucket, $s3Key)) {
                            $cmd = $s3->getCommand('GetObject', [
                                'Bucket' => $bucket,
                                'Key'    => $s3Key
                            ]);
                            $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                            $logoURL = (string) $request->getUri();
                        }
                    } catch (Exception $e) {
                        error_log("S3 error: " . $e->getMessage());
                        $logoURL = "";
                    }
                }
            }

            // Waktu relatif
            $waktuawal  = date_create($row['create_at']);
            $waktuakhir = date_create();
            $diff  = date_diff($waktuawal, $waktuakhir);

            if ($diff->d != '0') {
                $waktu = $diff->d . ' hari yang lalu';
            } elseif ($diff->h != '0') {
                $waktu = $diff->h . ' jam yang lalu';
            } elseif ($diff->i != '0') {
                $waktu = $diff->i . ' menit yang lalu';
            } else {
                $waktu = $diff->s . ' detik yang lalu';
            }

            // Secure link generation
            $link = "";
            if ($row['judul'] == 'Pengumuman Hasil Seleksi' && !empty($row['id_referensi'])) {
                // Sanitize id_referensi to prevent XSS
                $clean_id = preg_replace('/[^a-zA-Z0-9_-]/', '', $row['id_referensi']);
                if (!empty($clean_id)) {
                    $link = $baseURL . "candidate/dashboard/riwayat/timeline?q=" . urlencode($clean_id);
                }
            }

            // Tambahkan ke list dengan sanitization
            $notifData = array(
                'logo_url' => $logoURL,
                'label' => $row['label'] ?? '',
                'isi' => $row['isi'] ?? '',
                'waktu' => $waktu,
                'link' => $link,
                'penerima' => $row['penerima'] ?? '',
                'pengirim' => $row['pengirim'] ?? '',
                'tgl_kirim' => $row['create_at'] ?? '',
                'status' => $row['status'] ?? '',
                'id_req' => $row['id_referensi'] ?? '',
            );

            // Apply output sanitization while preserving URLs
            $notifList[] = sanitizeOutput($notifData);
            $jml_notif++;
        }

        $status = "true";
        $message = "Berhasil mengambil data";
        $konten = array(
            'notification' => $total_data,
            'titleNotif' => 'Anda memiliki ' . $total_data . ' notifikasi baru',
            'items' => $notifList
        );
    }

    // Susun data akhir dengan sanitization
    $data = array(
        'status' => $status === "true",
        'message' => htmlspecialchars($message, ENT_QUOTES, 'UTF-8'),
        'notification' => (int)$konten['notification'],
        'titleNotif' => htmlspecialchars($konten['titleNotif'], ENT_QUOTES, 'UTF-8'),
        'data' => $konten['items'], // Already sanitized above
        'page' => (int)$page,
        'page_size' => (int)$page_size,
        'total_page' => (int)$total_page,
        'total_data' => (int)$total_data
    );

    echo json_encode($data, JSON_PRETTY_PRINT);
}

if ($func == "countNewNotif") {
    $data = array();

    $status = "false";
    $message = "Gagal mengambil jumlah notifikasi";

    // Query untuk mengambil total notifikasi yang belum dibaca dengan secure query
    $result = executeSecureQuery(
        $conn,
        "SELECT COUNT(*) as total 
         FROM notif_summary ns
         LEFT JOIN koordinator k ON k.id_koordinator = ns.pengirim
         WHERE ns.penerima = ? 
         AND ns.status <> 'Dibaca'",
        "s",
        [$pin]
    );

    if (!$result) {
        sendErrorResponse("Database error occurred");
    }

    $row = $result->fetch_assoc();
    $total_notif = (int)(isset($row['total']) ? $row['total'] : 0);

    $status = "true";
    $message = "Berhasil mengambil jumlah notifikasi";

    // Susun data akhir dengan sanitization
    $data = array(
        'status' => $status === "true",
        'message' => htmlspecialchars($message, ENT_QUOTES, 'UTF-8'),
        'data' => [],
        'total_notif' => $total_notif,
    );

    echo json_encode($data, JSON_PRETTY_PRINT);
}


if ($func == 'updateNotif') {
    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    // Validate and sanitize POST parameters
    if (isset($_POST['penerima']) && isset($_POST['pengirim']) && isset($_POST['tgl'])) {
        $penerima = validateInput($_POST['penerima'], 'string', ['max_length' => 50]);
        $pengirim = validateInput($_POST['pengirim'], 'string', ['max_length' => 50]);
        $tgl = validateInput($_POST['tgl'], 'datetime');

        if (!$penerima || !$pengirim || !$tgl) {
            sendErrorResponse("Invalid input parameters");
        }

        // Verify the penerima matches the authenticated user
        if ($penerima !== $pin) {
            sendErrorResponse("Unauthorized access");
        }

        // Use secure query for update
        $result = executeSecureQuery(
            $conn,
            "UPDATE notif_summary SET `status` = 'Dibaca' 
             WHERE penerima = ? AND pengirim = ? AND create_at = ?",
            "sss",
            [$penerima, $pengirim, $tgl]
        );

        if ($result !== false) {
            $status = 'true';
            $message = 'Update notifikasi berhasil';
        }
    }

    $data['status'] = $status === "true";
    $data['message'] = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}

if ($func == 'updateAllNotif') {
    $data = array();

    $status = 'false';
    $message = 'Update notifikasi gagal';

    // Use secure query for bulk update
    $result = executeSecureQuery(
        $conn,
        "UPDATE notif_summary SET `status` = 'Dibaca' 
         WHERE penerima = ? AND `status` = 'Dikirim'",
        "s",
        [$pin]
    );

    if ($result !== false) {
        $status = 'true';
        $message = 'Update notifikasi berhasil';
    }

    $data['status'] = $status === "true";
    $data['message'] = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    $data['data'] = [];

    $output = json_encode($data, JSON_PRETTY_PRINT);
    echo $output;
}


$conn->close();
exit;
