<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../../../model/database.php';

$temp_pin = "null";
// get data user
if (isset($_SESSION['users']['pin'])) {
    $temp_pin = addslashes($_SESSION['users']['pin']);
    $key = 'gestalt#pass_+*209285<>oZKKrq*9-'; // 32 karakter
}

// get data minat dan konsep jika sudah ada
$sql = $conn->prepare("SELECT * FROM rh WHERE id = ?");
$sql->bind_param("s", $temp_pin);
$sql->execute();
$result = $sql->get_result();
$sql->close();

$bahasa_asing = "";
$kelebihan = array();
$kekurangan = array();
$ilmu_komputerisasi = "";
$memimpin_tim = "";
$kemampuan_presentasi = 0;
$lingkup_pekerjaan = array();

if ($result->num_rows > 0) {
    $row = mysqli_fetch_assoc($result);
    $bahasa_asing = htmlspecialchars($row['bahasa_asing']);
    $kelebihan = explode(",", htmlspecialchars($row['kelebihan']));
    $kekurangan = explode(",", htmlspecialchars($row['kekurangan']));
    $ilmu_komputerisasi = htmlspecialchars($row['ilmu_komputerisasi']);
    $memimpin_tim = htmlspecialchars($row['memimpin_tim']);
    $kemampuan_presentasi = htmlspecialchars($row['kemampuan_presentasi']);
    $lingkup_pekerjaan = explode(",", htmlspecialchars($row['lingkup_pekerjaan']));
}

$awal_kelebihan = "";
if (isset($kelebihan[0])) {
    $awal_kelebihan = $kelebihan[0];
}

$awal_kekurangan = "";
if (isset($kekurangan[0])) {
    $awal_kekurangan = $kekurangan[0];
}

function encrypt_url($data, $key)
{
    $method = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    $result = base64_encode($iv . $encrypted);
    return strtr($result, '+/=', '-_,');  // bikin URL-safe
}
?>
<form id="myForm" class="needs-validation" novalidate>
    <div class="row">
        <div class="col-md-6">
            <h5><?= translate('Minat dan Konsep Pribadi') ?></h5>
        </div>
        <div class="col-md-6">
            <a href="../../../api/cv/digitalcv.php?q=<?= encrypt_url($temp_pin, $key) ?>" target="_blank" class="btn btn-info btn-sm" style="float:right"><?= translate('Cetak CV') ?></a>
        </div>
    </div>
    <hr>

    <div class="row">
        <div class="col-md-6 mt-2">
            <label class="form-label"><?= translate('Apakah anda menguasai bahasa asing') ?>? <a style="color: red;">(*)</a></label>
            <table style="width: 100%;">
                <tr>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="penguasaan_bhs_y" name="penguasaan_bhs" value="Ya" required class="form-check-input" required>
                            <label class="form-check-label" for="penguasaan_bhs_y">
                                <?= translate('Ya') ?>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="penguasaan_bhs_t" name="penguasaan_bhs" value="Tidak" required class="form-check-input" required>
                            <label class="form-check-label" for="penguasaan_bhs_t">
                                <?= translate('Tidak') ?>
                            </label>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div id="konten-penguasaan-bhs" style="display: none;">
        <div class="row">
            <div class="col-md-4 mt-2">
                <label for="temp_bahasa" class="form-label"><?= translate('Bahasa asing yang dikuasai') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_bahasa" name="temp_bahasa" style="width: 100%">
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 mt-2">
                <label for="temp_membaca" class="form-label"><?= translate('Membaca') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_membaca" name="temp_membaca" style="width: 100%">
                    <option value="" selected disabled><?= translate('Pilih') ?> ..</option>
                    <option value="BS"><?= translate('Baik Sekali') ?></option>
                    <option value="B"><?= translate('Baik') ?></option>
                    <option value="C"><?= translate('Cukup') ?></option>
                    <option value="K"><?= translate('Kurang') ?></option>
                    <option value="KS"><?= translate('Kurang Sekali') ?></option>
                </select>
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_menulis" class="form-label"><?= translate('Menulis') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_menulis" name="temp_menulis" style="width: 100%">
                    <option value="" selected disabled><?= translate('Pilih') ?> ..</option>
                    <option value="BS"><?= translate('Baik Sekali') ?></option>
                    <option value="B"><?= translate('Baik') ?></option>
                    <option value="C"><?= translate('Cukup') ?></option>
                    <option value="K"><?= translate('Kurang') ?></option>
                    <option value="KS"><?= translate('Kurang Sekali') ?></option>
                </select>
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_mendengar" class="form-label"><?= translate('Mendengar') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_mendengar" name="temp_mendengar" style="width: 100%">
                    <option value="" selected disabled><?= translate('Pilih') ?> ..</option>
                    <option value="BS"><?= translate('Baik Sekali') ?></option>
                    <option value="B"><?= translate('Baik') ?></option>
                    <option value="C"><?= translate('Cukup') ?></option>
                    <option value="K"><?= translate('Kurang') ?></option>
                    <option value="KS"><?= translate('Kurang Sekali') ?></option>
                </select>
            </div>
            <div class="col-md-3 mt-2">
                <label for="temp_berbicara" class="form-label"><?= translate('Berbicara') ?> <span style="color: red;">(*)</span></label>
                <select class="form-select" id="temp_berbicara" name="temp_berbicara" style="width: 100%">
                    <option value="" selected disabled><?= translate('Pilih') ?> ..</option>
                    <option value="BS"><?= translate('Baik Sekali') ?></option>
                    <option value="B"><?= translate('Baik') ?></option>
                    <option value="C"><?= translate('Cukup') ?></option>
                    <option value="K"><?= translate('Kurang') ?></option>
                    <option value="KS"><?= translate('Kurang Sekali') ?></option>
                </select>
            </div>
            <div class="col-md-12 mt-3">
                <label class="form-label" style="display: none;">#</label>
                <button type="button" class="btn btn-primary btn-sm" style="float: right;" onclick="tambahBahasa()"><i class="fas fa-plus-square"></i> <?= translate('Simpan') ?></button>
            </div>
        </div>

        <div class="row mt-3">
            <span><?= translate('Detail Penguasaan Bahasa Asing') ?></span>
            <?php
            // get penguasaan bahasa
            $sql = $conn->prepare("SELECT * FROM penguasaan_bahasa WHERE id = ?");
            $sql->bind_param("s", $temp_pin);
            $sql->execute();
            $result = $sql->get_result();
            $sql->close();

            if ($result->num_rows > 0) {
                $temp_arr = array("BS" => translate('Baik Sekali'), "B" => translate('Baik'), "C" => translate('Cukup'), "K" => translate('Kurang'), "KS" => translate('Kurang Sekali'));

                echo '<div class="col-md-12" id="konten-info-detail-bahasa" style="display: none;">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Detail penguasaan bahasa asing belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-bahasa">';
                $no = 0;
                while ($row = mysqli_fetch_array($result)) {
                    echo '<div class="col-md-4" id="detailBahasa-' . $no . '">
                        <div class="card">
                        <div class="card-body">
                        <span class="card-title" style="font-size: 14px;">' . $row['bahasa'] . '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailBahasa(' . $no . ')"></i></span>
                        <p class="card-text mt-2" style="font-size: 12px;">
                        ' . translate('Membaca') . ' : ' . htmlspecialchars($temp_arr[$row['membaca']]) . '<br>
                        ' . translate('Menulis') . ' : ' . htmlspecialchars($temp_arr[$row['menulis']]) . '<br>
                        ' . translate('Mendengar') . ' : ' . htmlspecialchars($temp_arr[$row['mendengar']]) . '<br>
                        ' . translate('Berbicara') . ' : ' . htmlspecialchars($temp_arr[$row['berbicara']]) . '
                        </p>
                        <input type="hidden" name="bahasa[]" value="' . htmlspecialchars($row['bahasa']) . '">
                        <input type="hidden" name="membaca_bhs[]" value="' . htmlspecialchars($row['membaca']) . '">
                        <input type="hidden" name="menulis_bhs[]" value="' . htmlspecialchars($row['menulis']) . '">
                        <input type="hidden" name="mendengar_bhs[]" value="' . htmlspecialchars($row['mendengar']) . '">
                        <input type="hidden" name="berbicara_bhs[]" value="' . htmlspecialchars($row['berbicara']) . '">
                        </div>
                        </div>
                        </div>';
                    $no++;
                }
                echo '</div>';
            } else {
                echo '<div class="col-md-12" id="konten-info-detail-bahasa">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i> ' . translate('Detail penguasaan bahasa asing belum ditambahkan.') . '
                        </div>
                    </div>
                    <div class="row mt-2" id="konten-detail-bahasa">
                    </div>';
            }
            ?>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-6 mt-2">
            <label class="form-label"><?= translate('Sebutkan hal - hal yang menjadi kelebihan Anda') ?> <a style="color: red;">(*)</a></label>
            <div class="input-group mb-2" id="kelebihan0">
                <input type="text" class="form-control" name="kelebihan[]" value="<?= $awal_kelebihan ?>" placeholder="<?= translate('Silakan tuliskan kelebihan Anda') ?>" required>
                <span class="input-group-text" style="cursor: pointer;" onclick="tambahInput('kelebihan')"><i class="fas fa-plus-square" style="color: #1b7bf5;"></i></span>
            </div>
            <div id="konten-kelebihan">
                <?php
                if (count($kelebihan) > 0) {
                    $no = 1;
                    for ($i = 1; $i < count($kelebihan); $i++) {
                        echo '<div class="input-group mb-2" id="kelebihan' . $no . '">
                                <input type="text" class="form-control" id="tempkelebihan' . $no . '" name="kelebihan[]" value="' . $kelebihan[$i] . '" placeholder="' . translate('Silakan tuliskan kelebihan Anda') . '" required>
                                <span class="input-group-text" style="cursor: pointer;" onclick="hapusInput(\'kelebihan' . $no . '\')"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;"></i></span>
                            </div>';
                        $no++;
                    }
                }
                ?>
            </div>
        </div>
        <div class="col-md-6 mt-2">
            <label class="form-label"><?= translate('Sebutkan hal - hal yang menjadi hambatan/kekurangan Anda') ?> <a style="color: red;">(*)</a></label>
            <div class="input-group mb-2" id="kekurangan0">
                <input type="text" class="form-control" name="kekurangan[]" value="<?= $awal_kekurangan ?>" placeholder="<?= translate('Silakan tuliskan kekurangan Anda') ?>" required>
                <span class="input-group-text" style="cursor: pointer;" onclick="tambahInput('kekurangan')"><i class="fas fa-plus-square" style="color: #1b7bf5;"></i></span>
            </div>
            <div id="konten-kekurangan">
                <?php
                if (count($kekurangan) > 0) {
                    $no = 1;
                    for ($i = 1; $i < count($kekurangan); $i++) {
                        echo '<div class="input-group mb-2" id="kekurangan' . $no . '">
                                <input type="text" class="form-control" id="tempkekurangan' . $no . '" name="kekurangan[]" value="' . $kekurangan[$i] . '" placeholder="' . translate('Silakan tuliskan kekurangan Anda') . '" required>
                                <span class="input-group-text" style="cursor: pointer;" onclick="hapusInput(\'kekurangan' . $no . '\')"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;"></i></span>
                            </div>';
                        $no++;
                    }
                }
                ?>
            </div>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-12">
            <label><?= translate('Bagaimana pengetahuan Anda dibidang komputerisasi') ?>? <a style="color: red;">(*)</a></label>
            <table class="mt-2" style="width: 100%;">
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk6" name="kk" required value="6"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk6"><?= translate('Tidak menguasai') ?></label></td>
                </tr>
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk1" name="kk" required value="1"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk1"><?= translate('Mengetahui dasar dan pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)') ?></label></td>
                </tr>
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk2" name="kk" required value="2"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk2"><?= translate('Mengerti dan Menguasai pengoperasian Ms. Office (Ms. Word, Ms. Excel, Ms. PPT, dll)') ?></label></td>
                </tr>
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk3" name="kk" required value="3"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk3"><?= translate('Menguasai Operating Sistem Microsoft, Apple Operating Sistem dan App Lainnya') ?></label></td>
                </tr>
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk4" name="kk" required value="4"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk4"><?= translate('Menguasai Pemograman dan Analist Pemograman') ?></label></td>
                </tr>
                <tr>
                    <td style="vertical-align: top;"><input class="form-check-input" type="radio" id="kk5" name="kk" required value="5"></td>
                    <td></td>
                    <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="kk5"><?= translate('Spesialis Komputer Sains') ?></label></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row mt-2">
        <label><?= translate('Berapa orang yang pernah dipimpin dalam tim kerja Anda') ?>? <a style="color: red;">(*)</a></label>
        <div class="col-md-3">
            <div class="form-check" style="white-space: nowrap;">
                <input class="form-check-input" type="radio" name="pimpin_tim" id="pimpin_tim1" value="1" required>
                <label class="form-check-label" for="pimpin_tim1">
                    <?= translate('Tidak pernah/Tidak Ada') ?>
                </label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="pimpin_tim" id="pimpin_tim2" value="2">
                <label class="form-check-label" for="pimpin_tim2">
                    <?= translate('1 - 3 orang') ?>
                </label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="pimpin_tim" id="pimpin_tim3" value="3">
                <label class="form-check-label" for="pimpin_tim3">
                    <?= translate('4 - 10 orang') ?>
                </label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="pimpin_tim" id="pimpin_tim4" value="4">
                <label class="form-check-label" for="pimpin_tim4">
                    <?= translate('11 - 50 orang') ?>
                </label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="pimpin_tim" id="pimpin_tim5" value="5">
                <label class="form-check-label" for="pimpin_tim5">
                    <?= translate('Lebih dari 50 orang') ?>
                </label>
            </div>
        </div>
    </div>

    <div class="row mt-2">
        <label><?= translate('Berikan penilaian terhadap kemampuan Anda dalam presentasi/berbicara di depan umum') ?>? <a style="color: red;">(*)</a></label>
        <div class="col-md-6 mt-2">
            <table style="width: 100%;">
                <tr>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="kemampuan_persentasi_1" name="kemampuan_persentasi" value="1" required class="form-check-input" required>
                            <label class="form-check-label" for="kemampuan_persentasi_1">
                                <?= translate('Kurang') ?>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="kemampuan_persentasi_2" name="kemampuan_persentasi" value="2" required class="form-check-input" required>
                            <label class="form-check-label" for="kemampuan_persentasi_2">
                                <?= translate('Cukup') ?>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div class="form-check">
                            <input type="radio" id="kemampuan_persentasi_3" name="kemampuan_persentasi" value="3" required class="form-check-input" required>
                            <label class="form-check-label" for="kemampuan_persentasi_3">
                                <?= translate('Sangat Baik') ?>
                            </label>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-12">
            <label><?= translate('Sebutkan ruang lingkup pekerjaan yang Anda sukai dan kuasai') ?>? <a style="color: red;">(*)</a> (<?= translate('bisa pilih lebih dari satu') ?>)</label>
            <table class="mt-2" style="width: 100%; border-spacing: 0 5px; border-collapse: separtate;">
                <?php
                // Get data ruang lingkup pekerjaan
                $get = $conn->query("SELECT * FROM ruang_lingkup_pekerjaan");
                $temp = 1;
                while ($row = mysqli_fetch_array($get)) {
                    if ($temp == 1) {
                        echo '<tr>
                                <td style="vertical-align: top;"><input class="form-check-input" type="checkbox" id="rlp' . $row['id'] . '" name="rlp[]" value="' . $row['value'] . '"></td>
                                <td></td>
                                <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="rlp' . $row['id'] . '">' . translate($row['name']) . '</label></td>';
                        $temp = 2;
                    } else {
                        echo '<td style="vertical-align: top;"><input class="form-check-input" type="checkbox" id="rlp' . $row['id'] . '" name="rlp[]" value="' . $row['value'] . '"></td>
                              <td></td>
                              <td style="vertical-align: top; text-align: left;"><label class="form-check-label" for="rlp' . $row['id'] . '">' . translate($row['name']) . '</label></td>
                            </tr>';
                        $temp = 1;
                    }
                }
                ?>
            </table>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-8 mt-2">
            <span><?= translate('Progres Pengisian') ?></span>
            <div class="progress" role="progressbar" aria-label="Animated striped example" aria-valuemin="0" aria-valuemax="100">
                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="progress-pengisian">0%</div>
            </div>
        </div>
        <div class="col-md-4 mt-3" style="text-align: right;">
            <button type="button" class="btn btn-light btn-sm" style="border-color: #00000038;" onclick="loadPage('form-informasi-pekerjaan.php')"><?= translate('Kembali') ?></button>
            <button type="submit" class="btn btn-primary btn-sm" id="btn-simpan"><?= translate('Selanjutnya') ?></button>
        </div>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('#temp_bahasa').select2({
            placeholder: "<?= translate('Contoh') ?>: Arab/China/Inggris",
            allowClear: true,
            ajax: {
                url: "../../controller/controller.php",
                dataType: "json",
                delay: 250, // Mencegah terlalu banyak request
                data: function(params) {
                    return {
                        func: 'getBahasa',
                        q: params.term, // Kata kunci pencarian
                        page: params.page || 1 // Paginasi
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results, // Data Loaksi
                        pagination: {
                            more: data.more // Jika masih ada data, aktifkan paginasi
                        }
                    };
                }
            },
            minimumInputLength: 2, // Mulai mencari setelah mengetik minimal 4 huruf
        });
        $("#temp_membaca").select2();
        $("#temp_menulis").select2();
        $("#temp_mendengar").select2();
        $("#temp_berbicara").select2();

        // Fungsi onchange bisa menguasai bahasa asing
        $('input[name="penguasaan_bhs"]').on('change', function() {
            var value = $(this).val(); // Ambil value dari radio yang dipilih

            if (value == 'Ya') {
                $("#konten-penguasaan-bhs").show();
            } else {
                $("#konten-penguasaan-bhs").hide();
            }

            $("#temp_bahasa").val(null).trigger('change');
            $("#temp_membaca").val('').trigger('change');
            $("#temp_menulis").val('').trigger('change');
            $("#temp_mendengar").val('').trigger('change');
            $("#temp_berbicara").val('').trigger('change');
        });
        // End Fungsi onchange bisa menguasai bahasa asing

        // Get progress pengisian
        $.ajax({
            type: "post",
            url: "../../controller/controller.php?func=getProgresPengisian",
            success: function(result) {
                var obj = JSON.parse(JSON.stringify(result));

                if (obj.status == "success") {
                    $("#progress-pengisian")
                        .css("width", obj.value + "%") // Update lebar progress bar
                        .text(obj.value + "%"); // Menampilkan nilai di dalam elemen
                } else {
                    $("#progress-pengisian")
                        .css("width", "0%") // Update lebar progress bar
                        .text("0%"); // Menampilkan nilai di dalam elemen
                }
            },
        });
        // End Get progress pengisian

        // Fungsi simpan data minat dan konsep pribadi
        $("#myForm").submit(function(event) {
            event.preventDefault(); // Mencegah reload form
            event.stopPropagation(); // Menghentikan event bubbling

            if (this.checkValidity() === false) {
                $(this).addClass("was-validated");
                return;
            }

            var penguasaan_bhs = $("input[name='penguasaan_bhs']:checked").val();
            bahasa = [];
            membaca_bhs = [];
            menulis_bhs = [];
            mendengar_bhs = [];
            berbicara_bhs = [];
            kelebihan = $("input[name='kelebihan[]']").map(function() {
                return this.value;
            }).get();
            kekurangan = $("input[name='kekurangan[]']").map(function() {
                return this.value;
            }).get();
            kk = $("input[name='kk']:checked").val();
            pimpin_tim = $("input[name='pimpin_tim']:checked").val();
            kemampuan_persentasi = $("input[name='kemampuan_persentasi']:checked").val();
            rlp = $("input[name='rlp[]']:checked").map(function() {
                return this.value;
            }).get();

            if (penguasaan_bhs == "Ya" && $("input[name='bahasa[]']").length == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan masukan detail bahasa asing yang dikuasai terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600,
                    className: "swal-text-center" // Tambahkan class kustom
                });
            } else if (kemampuan_persentasi == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan beri penilaian terhadap kemampuan presentasi/berbicara anda terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600,
                    className: "swal-text-center" // Tambahkan class kustom
                });
            } else if (rlp.length == 0) {
                swal({
                    icon: "warning",
                    text: "<?= translate('Silakan pilih ruang lingkup pekerjaan yang anda sukai dan kuasai terlebih dahulu.') ?>",
                    buttons: false,
                    timer: 1600,
                    className: "swal-text-center" // Tambahkan class kustom
                });
            } else {
                if (penguasaan_bhs == "Ya") {
                    bahasa = $("input[name='bahasa[]']").map(function() {
                        return this.value;
                    }).get();
                    membaca_bhs = $("input[name='membaca_bhs[]']").map(function() {
                        return this.value;
                    }).get();
                    menulis_bhs = $("input[name='menulis_bhs[]']").map(function() {
                        return this.value;
                    }).get();
                    mendengar_bhs = $("input[name='mendengar_bhs[]']").map(function() {
                        return this.value;
                    }).get();
                    berbicara_bhs = $("input[name='berbicara_bhs[]']").map(function() {
                        return this.value;
                    }).get();
                }

                grecaptcha.ready(function() {
                    grecaptcha.execute('6LdFFMMrAAAAAFE2woQv0yLoGzy6jpGQvLf0LzVF', {
                        action: 'submit'
                    }).then(function(token) {
                        $("#btn-simpan").html("<?= translate('Harap Tunggu') ?>....");
                        $("#btn-simpan").attr("disabled", true);

                        $.ajax({
                            type: "post",
                            url: "../../controller/controller.php?func=simpanMinatKonsep",
                            data: {
                                penguasaan_bhs: penguasaan_bhs,
                                bahasa: bahasa,
                                membaca_bhs: membaca_bhs,
                                menulis_bhs: menulis_bhs,
                                mendengar_bhs: mendengar_bhs,
                                berbicara_bhs: berbicara_bhs,
                                kelebihan: kelebihan,
                                kekurangan: kekurangan,
                                kk: kk,
                                pimpin_tim: pimpin_tim,
                                kemampuan_persentasi: kemampuan_persentasi,
                                rlp: rlp,
                                recaptcha_response: token
                            },
                            success: function(result) {
                                var obj = JSON.parse(JSON.stringify(result));

                                if (obj.status == "success") {
                                    swal({
                                        icon: "success",
                                        text: obj.message,
                                        buttons: false,
                                        buttons: {
                                            confirm: {
                                                text: "<?= translate('Selanjutnya') ?>!",
                                                className: "btn btn-primary",
                                            }
                                        },
                                    }).then((confirm) => {
                                        loadPage('form-riwayat-organisasi.php');
                                    });
                                } else {
                                    swal({
                                        icon: "error",
                                        text: obj.message,
                                        buttons: false,
                                        timer: 2000
                                    });
                                    $("#btn-simpan").html("<?= translate('Selanjutnya') ?>");
                                    $("#btn-simpan").prop("disabled", false);
                                }
                            },
                        });
                    });
                });
            }


        });
        // End Fungsi simpan data minat dan konsep pribadi

        // Auto input
        var bahasa_asing = '<?= $bahasa_asing ?>';
        $("input[name='penguasaan_bhs'][value='" + bahasa_asing + "']").prop("checked", true);
        if (bahasa_asing == "Ya") {
            $("#konten-penguasaan-bhs").show();
        }

        var ilmu_komputerisasi = '<?= $ilmu_komputerisasi ?>';
        $("input[name='kk'][value='" + ilmu_komputerisasi + "']").prop("checked", true);

        var memimpin_tim = '<?= $memimpin_tim ?>';
        $("input[name='pimpin_tim'][value='" + memimpin_tim + "']").prop("checked", true);

        var lingkup_pekerjaan = JSON.parse('<?= json_encode($lingkup_pekerjaan) ?>');
        if (lingkup_pekerjaan.length > 0) {
            for (let index = 0; index < lingkup_pekerjaan.length; index++) {
                const val = lingkup_pekerjaan[index];
                $("input[name='rlp[]'][value='" + val + "']").prop("checked", true);
            }
        }

        var kemampuan_presentasi = '<?= $kemampuan_presentasi ?>';
        $("input[name='kemampuan_persentasi'][value='" + kemampuan_presentasi + "']").prop("checked", true);
        // End Auto input
    });

    function tambahBahasa() {
        var temp_bahasa = htmlspecialchars($("#temp_bahasa").val());
        temp_membaca = htmlspecialchars($("#temp_membaca").val());
        temp_menulis = htmlspecialchars($("#temp_menulis").val());
        temp_mendengar = htmlspecialchars($("#temp_mendengar").val());
        temp_berbicara = htmlspecialchars($("#temp_berbicara").val());

        if (temp_bahasa == "" || temp_bahasa === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih bahasa asing terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_membaca == "" || temp_membaca === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih kemampuan membaca bahasa asing terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_menulis == "" || temp_menulis === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih kemampuan menulis bahasa asing terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_mendengar == "" || temp_mendengar === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih kemampuan mendengar bahasa asing terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else if (temp_berbicara == "" || temp_berbicara === null) {
            swal({
                icon: "warning",
                text: "<?= translate('Silakan pilih kemampuan berbicara bahasa asing terlebih dahulu.') ?>",
                buttons: false,
                timer: 1600
            });
        } else {
            $("#konten-info-detail-bahasa").hide();

            var id_input = Math.floor(Math.random() * 900) + 100;
            var content = $("#konten-detail-bahasa").html();

            let arr_kemampuan = [{
                    id: "BS",
                    ket: "<?= translate('Baik Sekali') ?>"
                },
                {
                    id: "B",
                    ket: "<?= translate('Baik') ?>"
                },
                {
                    id: "C",
                    ket: "<?= translate('Cukup') ?>"
                },
                {
                    id: "K",
                    ket: "<?= translate('Kurang') ?>"
                },
                {
                    id: "KS",
                    ket: "<?= translate('Kurang Sekali') ?>"
                },
            ];


            var result = arr_kemampuan.find(item => item.id === temp_membaca);
            membaca = result.ket;
            var result = arr_kemampuan.find(item => item.id === temp_menulis);
            menulis = result.ket;
            var result = arr_kemampuan.find(item => item.id === temp_mendengar);
            mendengar = result.ket;
            var result = arr_kemampuan.find(item => item.id === temp_berbicara);
            berbicara = result.ket;

            newContent = content + '<div class="col-md-4" id="detailBahasa-' + id_input + '">' +
                '<div class="card">' +
                '<div class="card-body">' +
                '<span class="card-title" style="font-size: 14px;">' + temp_bahasa + '</span><span style="float: right;"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;" onclick="hapusDetailBahasa(' + id_input + ')"></i></span>' +
                '<p class="card-text mt-2" style="font-size: 12px;">' +
                '<?= translate('Membaca') ?> : ' + membaca + '<br>' +
                '<?= translate('Menulis') ?> : ' + menulis + '<br>' +
                '<?= translate('Mendengar') ?> : ' + mendengar + '<br>' +
                '<?= translate('Berbicara') ?> : ' + berbicara + '' +
                '</p>' +
                '<input type="hidden" name="bahasa[]" value="' + temp_bahasa + '">' +
                '<input type="hidden" name="membaca_bhs[]" value="' + temp_membaca + '">' +
                '<input type="hidden" name="menulis_bhs[]" value="' + temp_menulis + '">' +
                '<input type="hidden" name="mendengar_bhs[]" value="' + temp_mendengar + '">' +
                '<input type="hidden" name="berbicara_bhs[]" value="' + temp_berbicara + '">' +
                '</div>' +
                '</div>' +
                '</div>';

            $("#konten-detail-bahasa").html(newContent);

            $("#temp_bahasa").val(null).trigger('change');
            $("#temp_membaca").val('').trigger('change');
            $("#temp_menulis").val('').trigger('change');
            $("#temp_mendengar").val('').trigger('change');
            $("#temp_berbicara").val('').trigger('change');
        }
    }

    function hapusDetailBahasa(id) {
        $("#detailBahasa-" + id).remove();

        // cek apakah html kosong atau tidak
        if ($("#konten-detail-bahasa").text().trim().length === 0) {
            $("#konten-info-detail-bahasa").show();
        }
    }

    function tambahInput(ket) {
        if (ket == 'kelebihan') {
            placeholder = "<?= translate('Silakan tuliskan kelebihan Anda') ?>";
        } else {
            placeholder = "<?= translate('Silakan tuliskan kekurangan Anda') ?>";
        }

        var cekIDInput = false;
        do {
            var id_input = Math.floor(Math.random() * 900) + 100;
            cekIDInput = !$("#" + ket + id_input).length;
        } while (!cekIDInput);

        newContent = '<div class="input-group mb-2" id="' + ket + id_input + '">' +
            '<input type="text" class="form-control" id="temp' + ket + id_input + '" name="' + ket + '[]" placeholder="' + placeholder + '" required>' +
            '<span class="input-group-text" style="cursor: pointer;" onclick="hapusInput(\'' + ket + id_input + '\')"><i class="fas fa-trash-alt" style="color: red; cursor: pointer;"></i></span>' +
            '</div>';

        $("#konten-" + ket).before(newContent);
    }

    function hapusInput(ket) {
        $("#" + ket).remove();
    }
</script>