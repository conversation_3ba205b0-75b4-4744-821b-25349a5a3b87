<?php
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();
include '../model/database.php';
$data = array();

if (isset($_SESSION['users-pic'])) {
    $company = $_SESSION['users-pic']['id_koordinator'];
    $divisi = $_SESSION['users-pic']['divisi'];
    $user_id = $_SESSION['users-pic']['id'];
}

$sqlData = "SELECT * FROM notif_summary WHERE penerima = '$company' AND status = 'Dikirim' GROUP BY create_at";
$queryData = $conn->query($sqlData);
while ($rowData = mysqli_fetch_assoc($queryData)) {
    $judul = $rowData['judul'];
    $link = $rowData['link'];
    $logo = $baseURL . 'assets/images/logo/logo.png';

    $dataArr = array('judul' => $judul, 'link' => $link, 'logo' => $logo);
    array_push($data, $dataArr);
}

$sql_update = "UPDATE notif_summary set status = 'Diterima' where penerima = '$company' AND status = 'Dikirim'";
$conn->query($sql_update);



$output = array("data" => $data);
$output = json_encode($output, JSON_PRETTY_PRINT);
echo $output;
