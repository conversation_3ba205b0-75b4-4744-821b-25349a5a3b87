<?php
header("Content-Type: application/json");
date_default_timezone_set('Asia/Jakarta');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Lax'
]);
session_start();

include '../../../model/database.php';
include '../../../api/s3.php';
include '../../jwt_helper.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';

$userData = requireAuth();
$pin = $userData->pin ?? "-";
$nama_user = $userData->nama ?? "-";
$email_user = $userData->email ?? "-";

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }
    return $mixed;
}

function waktuLalu($tanggal)
{
    $tgl_awal = new DateTime($tanggal);
    $tgl_sekarang = new DateTime();
    $diff = $tgl_sekarang->diff($tgl_awal);

    if ($diff->y > 0) {
        return $diff->y . " tahun yang lalu";
    } elseif ($diff->m > 0) {
        return $diff->m . " bulan yang lalu";
    } elseif ($diff->d > 0) {
        return $diff->d . " hari yang lalu";
    } elseif ($diff->h > 0) {
        return $diff->h . " jam yang lalu";
    } elseif ($diff->i > 0) {
        return $diff->i . " menit yang lalu";
    } else {
        return "Baru saja";
    }
}

function formatTanggalIndonesia($tanggal)
{
    $bulanIndo = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "Mei",
        "Jun",
        "Jul",
        "Agu",
        "Sep",
        "Okt",
        "Nov",
        "Des"
    ];

    $tanggalObj = new DateTime($tanggal);
    $tgl = $tanggalObj->format('d'); // Ambil tanggal
    $bln = $bulanIndo[(int)$tanggalObj->format('m') - 1]; // Ambil bulan
    $thn = $tanggalObj->format('y'); // Ambil tahun dua digit

    return "$tgl $bln $thn";
}

// Secure query execution function
function executeSecureQuery($conn, $sql, $params = [], $types = "")
{
    try {
        if (!empty($params)) {
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types)) {
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
            return $result;
        } else {
            return $conn->query($sql);
        }
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

// Input validation function
function validateInput($input, $type = 'string', $maxLength = 255)
{
    if (empty($input) && $input !== '0') return '';

    switch ($type) {
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT) !== false ? (int)$input : 0;
        case 'search':
            // Allow letters, numbers, spaces, and common punctuation for search
            return preg_match('/^[a-zA-Z0-9\s\.\-_,]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
        case 'array':
            if (!is_array($input)) return [];
            return array_filter(array_map(function ($item) use ($maxLength) {
                return preg_match('/^[a-zA-Z0-9\s\.\-_]+$/u', $item) ? substr(trim($item), 0, $maxLength) : null;
            }, $input));
        case 'date_filter':
            $allowed = ["Hari Ini", "3 Hari Terakhir", "7 Hari Terakhir", "14 Hari Terakhir", "30 Hari Terakhir", "Kapan Saja"];
            return in_array($input, $allowed) ? $input : "Kapan Saja";
        default:
            return preg_match('/^[a-zA-Z0-9\s\.\-_,]+$/u', $input) ? substr(trim($input), 0, $maxLength) : '';
    }
}

// Output sanitization function with URL protection
function sanitizeOutput($data, $key = '')
{
    if (is_array($data)) {
        $sanitized = [];
        foreach ($data as $k => $v) {
            $sanitized[$k] = sanitizeOutput($v, $k);
        }
        return $sanitized;
    }

    // Don't sanitize specific fields that contain URLs or encoded data
    $protected_fields = ['logoURL', 'link', 'link_perusahaan', 'temp_kode'];
    if (in_array($key, $protected_fields)) {
        return $data;
    }

    // Don't sanitize URLs (especially S3 presigned URLs)
    if (is_string($data)) {
        return $data;
    }

    return $data;
}


// Validate and sanitize input parameters
$limit = validateInput($_POST['page_size'] ?? 6, 'int');
$limit = ($limit > 0 && $limit <= 100) ? $limit : 6; // Max 100 items per page

$page = validateInput($_POST['page'] ?? 1, 'int');
$page = ($page > 0 && $page <= 10000) ? $page : 1; // Max 10000 pages to prevent DOS

$start = ($page - 1) * $limit;

// Validate search parameters
$pencarian_value = validateInput($_POST['pencarian'] ?? '', 'search', 100);
$lokasi_value = $_POST['lokasi'];

// Validate array parameters
$jenis_pekerjaan_array = validateInput($_POST['jenis_pekerjaan'] ?? [], 'array');
$spesialisasi_array = validateInput($_POST['spesialisasi'] ?? [], 'array');
$pendidikan_array = validateInput($_POST['pendidikan'] ?? [], 'array');
$waktu_posting_value = validateInput($_POST['waktu_posting'] ?? 'Kapan Saja', 'date_filter');

$response['data'] = [];

// Build secure WHERE conditions and parameters
$where_conditions = ["lr.status = 'On Proccess'"];
$params = [];
$types = "";

// Handle search parameter
if (!empty($pencarian_value)) {
    $where_conditions[] = "(lr.perusahaan LIKE ? OR lr.posisi LIKE ?)";
    $search_param = "%" . $pencarian_value . "%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ss";
}

// Handle location filter (will be applied in PHP logic later)

if (!empty($lokasi_value)) {
    $where_conditions[] = "(lr.lokasi_kerja LIKE '%" . addslashes($lokasi_value) . "%' )";
}

$lokasi_filter = !empty($lokasi_value) ? $lokasi_value : null;

// Handle job type filter
if (isset($_POST['jenis_pekerjaan']) && count($_POST['jenis_pekerjaan']) > 0) {
    // $placeholders = str_repeat('?,', count($jenis_pekerjaan_array) - 1) . '?';
    $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['jenis_pekerjaan']);
    $where_conditions[] = "lr.tipe_pekerjaan IN (" . implode(",", $quotedArray) . ")";
}

// Handle specialization filter
if (isset($_POST['spesialisasi']) && count($_POST['spesialisasi']) > 0) {
    $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['spesialisasi']);
    $where_conditions[] = "k.tipe IN (" . implode(",", $quotedArray) . ")";
}

// Handle education filter
if (isset($_POST['pendidikan']) && count($_POST['pendidikan']) > 0) {
    $quotedArray = array_map(function ($item) {
            return "'" . addslashes($item) . "'";
        }, $_POST['pendidikan']);
    $where_conditions[] = "lr.k_pendidikan IN (" . implode(",", $quotedArray) . ")";
}

// Handle time posting filter
if ($waktu_posting_value !== "Kapan Saja") {
    $temp_tgl_awal = "";
    $temp_tgl_akhir = "";

    if ($waktu_posting_value == "Hari Ini") {
        $temp_tgl_awal = date("Y-m-d") . " 00:00:00";
        $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
    } elseif ($waktu_posting_value == "3 Hari Terakhir") {
        $temp_tgl = new DateTime();
        $temp_tgl->modify("-3 days");
        $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
        $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
    } elseif ($waktu_posting_value == "7 Hari Terakhir") {
        $temp_tgl = new DateTime();
        $temp_tgl->modify("-7 days");
        $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
        $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
    } elseif ($waktu_posting_value == "14 Hari Terakhir") {
        $temp_tgl = new DateTime();
        $temp_tgl->modify("-14 days");
        $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
        $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
    } elseif ($waktu_posting_value == "30 Hari Terakhir") {
        $temp_tgl = new DateTime();
        $temp_tgl->modify("-30 days");
        $temp_tgl_awal = $temp_tgl->format("Y-m-d") . " 00:00:00";
        $temp_tgl_akhir = date("Y-m-d") . " 23:59:59";
    }

    if (!empty($temp_tgl_awal) && !empty($temp_tgl_akhir)) {
        $where_conditions[] = "lr.create_at BETWEEN ? AND ?";
        $params[] = $temp_tgl_awal;
        $params[] = $temp_tgl_akhir;
        $types .= "ss";
    }
}

// Build the main query with secure parameterized statements
$base_sql = "SELECT
                lr.*,
                k.img,
                k.alamat,
                k.tipe,
                k.link,
                k.link_perusahaan,
                k.deskripsi,
                lk.kk,
                lk.kb,
                lk.km,
                lk.kbdu,
                lk.rlp
            FROM
                list_request lr
                JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
                JOIN list_kriteria lk ON lr.id_req = lk.id_req
            WHERE " . implode(" AND ", $where_conditions) . "
            ORDER BY lr.create_at DESC
            LIMIT ?, ?";

// Add limit parameters
$params[] = $start;
$params[] = $limit;
$types .= "ii";

$result = executeSecureQuery($conn, $base_sql, $params, $types);

// Calculate total data with secure query
$count_sql = "SELECT lr.lokasi_kerja FROM list_request lr
    JOIN koordinator k ON lr.id_koordinator = k.id_koordinator
    JOIN list_kriteria lk ON lr.id_req = lk.id_req
    WHERE " . implode(" AND ", $where_conditions);

// Remove LIMIT parameters for count query
$count_params = array_slice($params, 0, -2);
$count_types = substr($types, 0, -2);

$lokasi_result = executeSecureQuery($conn, $count_sql, $count_params, $count_types);

$total_data = 0;

if ($lokasi_result) {
    while ($row = $lokasi_result->fetch_assoc()) {
        $arr_lokasi = explode(",", $row['lokasi_kerja']);

        if (is_array($arr_lokasi)) {
            // Apply location filter if specified
            if ($lokasi_filter) {
                $filtered_locations = array_filter($arr_lokasi, function ($loc) use ($lokasi_filter) {
                    return stripos(trim($loc), $lokasi_filter) !== false;
                });
                $total_data += count($filtered_locations);
            } else {
                $total_data += count($arr_lokasi);
            }
        } else {
            if (!$lokasi_filter || stripos(trim($row['lokasi_kerja']), $lokasi_filter) !== false) {
                $total_data += 1;
            }
        }
    }
}

$total_pages = ceil($total_data / $limit);

// Fetch Ultrajaya jobs if filter is Full-Time
$dataLokerUltra = [];
$total_dataLokerUltra = 0;
$limitLokerUltra = $limit;
$startLokerUltra = $start;


// get daftar lowongan dari ultrajaya
$token = "FAJB87548LKGDL";
$api_filter_pencarian = $pencarian_value;
$api_filter_pendidikan = !empty($pendidikan_array) ? implode(',', $pendidikan_array) : '';
$api_filter_lokasi = $lokasi_filter ?? '';

$ch = curl_init("https://ultrajaya.digitalcv.id/recruitment/api/recruitment.php?nama_perusahaan=" . urlencode($api_filter_pencarian) . "&posisi=" . urlencode($api_filter_pencarian) . "&pendidikan=" . urlencode($api_filter_pendidikan) . "&tanggal_posting&lokasi=" . urlencode($api_filter_lokasi) . "&limit=" . urlencode($limitLokerUltra) . "&offset=" . urlencode($startLokerUltra));

curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $token",
    "Content-Type: application/json"
]);

curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);


if (!curl_errno($ch)) {
    if (json_decode(curl_exec($ch))->status === 'success') {
        $dataLokerUltra = json_decode(curl_exec($ch))->data;
        $total_dataLokerUltra = json_decode(curl_exec($ch))->total;
    }
}
curl_close($ch);

// Add Ultrajaya's total to our total
$total_data += $total_dataLokerUltra;
$total_pages = ceil($total_data / $limit);

// Add Ultrajaya job data if available
if (!empty($dataLokerUltra)) {

    foreach ($dataLokerUltra as $ultraJob) {

        try {
            // Parse and normalize the date from Ultrajaya API
            $normalized_date = date('Y-m-d H:i:s');
            if (isset($ultraJob->create_at) && !empty($ultraJob->create_at)) {
                try {
                    // Clean the date string by removing extra spaces and fixing format
                    $clean_date = preg_replace('/\s+/', ' ', trim($ultraJob->create_at));
                    $clean_date = str_replace(['/', ' PM', ' AM'], ['-', '', ''], $clean_date);
                    
                    // Try to parse the cleaned date
                    $date_obj = DateTime::createFromFormat('Y-m-d H:i:s', $clean_date);
                    if ($date_obj === false) {
                        // If that fails, try other common formats
                        $date_obj = DateTime::createFromFormat('Y-m-d', substr($clean_date, 0, 10));
                    }
                    
                    if ($date_obj !== false) {
                        $normalized_date = $date_obj->format('Y-m-d H:i:s');
                    }
                } catch (Exception $e) {
                    // Use current date if parsing fails
                    $normalized_date = date('Y-m-d H:i:s');
                }
            }

            $row_ultra = [
            'id_req' => $ultraJob->id_req ?? 'ultra_' . uniqid(),
            'perusahaan' => $ultraJob->nama_perusahaan ?? 'PT Ultrajaya Milk Industry & Trading Company Tbk',
            'posisi' => $ultraJob->posisi ?? '-',
            'tipe_pekerjaan' => 'Full-Time',
            'k_pendidikan' => $ultraJob->k_pendidikan ?? '-',
            'create_at' => $normalized_date,
            'img' => null,
            'alamat' => $ultraJob->lokasi_kerja ?? '-',
            'link' => $ultraJob->link ?? '-',
            'link_perusahaan' => 'https://ultrajaya.co.id',
            'deskripsi' => $ultraJob->deskripsi ?? '-',
            'lokasi_kerja2' => $ultraJob->lokasi_kerja ?? '-',
            'waktu_lalu' => waktuLalu($normalized_date),
            'tgl_posting' => formatTanggalIndonesia($normalized_date),
            'logoURL' => $ultraJob->logo ?? null,
            'is_favorit' => false,
            'is_lamar' => false,
            'temp_kode' => base64_encode('ultra_' . ($ultraJob->id_req ?? 'unknown') . '|' . ($ultraJob->lokasi_kerja ?? '-')),
            'arr_lokasi' => [$ultraJob->lokasi_kerja ?? '-'],
            'kk' => '-',
            'kb' => '-',
            'km' => '-',
            'kbdu' => '-',
            'rlp' => '-',
            'status' => 'ultra'
            ];
        } catch (Exception $e) {
            echo "Error processing Ultrajaya job data: " . $e->getMessage();
            continue; // Skip this job and continue with the next one
        }

        // Only add Ultrajaya jobs if Full-Time is in the filter or no job type filter is applied
        if (empty($jenis_pekerjaan_array) || in_array('Full-Time', $jenis_pekerjaan_array)) {
            $response['data'][] = sanitizeOutput($row_ultra);
        }
    }
}

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $id_req = $row['id_req'];
        $waktu_lalu = waktuLalu($row['create_at']);
        $tgl_posting = formatTanggalIndonesia($row['create_at']);
        $arr_lokasi_kerja = explode(",", $row['lokasi_kerja']);
        $logoURL = null; // Default jika tidak ada gambar
        $is_favorit = false; // Default jika bukan favorit
        $is_lamar = false;

        // Cek apakah ada gambar logo di S3
        if (!empty($row['img'])) {
            if ($s3->doesObjectExist($bucket, 'perusahaan/logo/' . $row['img'])) {
                $cmd = $s3->getCommand('GetObject', [
                    'Bucket' => $bucket,
                    'Key'    => 'perusahaan/logo/' . $row['img']
                ]);

                $request = $s3->createPresignedRequest($cmd, '+10 minutes');
                $logoURL = (string) $request->getUri();
            }
        }

        for ($i = 0; $i < count($arr_lokasi_kerja); $i++) {
            $lokasi = trim($arr_lokasi_kerja[$i]);
            $is_lamar = false;

            // Apply location filter if specified
            if ($lokasi_filter && stripos($lokasi, $lokasi_filter) === false) {
                continue;
            }

            $temp_kode = base64_encode($id_req . "|" . $lokasi);

            if (isset($lokasi) && !empty($lokasi)) {
                $lokasi_kerja = str_replace("KOTA ", "", str_replace("KABUPATEN ", "KAB. ", $lokasi));
            } else {
                $lokasi_kerja = "-";
            }

            if ($pin != '-') {
                // Cek apakah data ini termasuk favorit
                $cekFav = $conn->prepare("SELECT id FROM list_favorite WHERE id = ? AND id_req = ? AND lokasi = ?");
                $cekFav->bind_param("sss", $pin, $id_req, $lokasi);
                $cekFav->execute();
                $resultFav = $cekFav->get_result();

                if ($resultFav->num_rows > 0) {
                    $is_favorit = true;
                } else {
                    $is_favorit = false;
                }

                $cekFav->close();

                $whare_lokasi = "%" . $lokasi . "%";

                // Cek apakah data ini sudah dilamar
                $cekLamar = $conn->prepare("SELECT
                    ul.id_lamar
                FROM
                    users_lamar ul
                WHERE
                    ul.id_gestalt = ?
                    AND ul.id_req = ?
                    AND ul.lokasi LIKE ?");
                $cekLamar->bind_param("sss", $pin, $id_req,  $whare_lokasi);
                $cekLamar->execute();
                $resultLamar = $cekLamar->get_result();

                if ($resultLamar->num_rows > 0) {
                    $is_lamar = true;
                }
                $cekLamar->close();
            }

            // Tambahkan semua data asli + tambahan ke dalam array sebagai entri terpisah
            $row_copy = $row; // Buat salinan dari row asli
            $row_copy['lokasi_kerja2'] = $lokasi_kerja;
            $row_copy['waktu_lalu'] = $waktu_lalu;
            $row_copy['tgl_posting'] = $tgl_posting;
            $row_copy['logoURL'] = $logoURL;
            $row_copy['is_favorit'] = $is_favorit;
            $row_copy['is_lamar'] = $is_lamar;
            $row_copy['temp_kode'] = $temp_kode;
            $row_copy['arr_lokasi'] = [$lokasi]; // Ubah menjadi array dengan satu lokasi

            // Sanitize output data while protecting URLs and encoded fields
            $response['data'][] = sanitizeOutput($row_copy);
        }
    }
}

// Handle query execution errors
if ($result === false) {
    $response = [
        "success" => false,
        "message" => "Terjadi kesalahan dalam mengambil data",
        "data" => [],
        "page" => $page,
        "total_page" => 0,
        "page_size" => $limit,
        "total_data" => 0
    ];
} else {
    // Format JSON response
    $response = [
        "success" => true,
        "message" => "fetch data berhasil",
        "data" => $response['data'],
        "page" => $page,
        "total_page" => $total_pages,
        "page_size" => $limit,
        "total_data" => $total_data
    ];
}

header('Content-Type: application/json');
echo json_encode($response, JSON_PRETTY_PRINT);

$conn->close();
exit;
